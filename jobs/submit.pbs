#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-gcnn-unified
#PBS -j oe

# ==================== 任务选择配置 ====================
# 设置要执行的任务类型，可以选择一个或多个：
# TRAIN     - 基础训练任务
# FINETUNE  - 链式微调任务  
# ANALYZE   - 分析任务
# 
# 示例：
# TASKS="TRAIN"                    # 只执行训练
# TASKS="TRAIN FINETUNE"           # 先训练后微调
# TASKS="ANALYZE"                  # 只执行分析
# TASKS="TRAIN FINETUNE ANALYZE"   # 执行全流程

TASKS="ANALYZE FINETUNE"

# ==================== 读取配置文件 ====================
# 根据任务类型读取对应的配置文件
load_config() {
    local task_type=$1
    local config_file=""
    
    case $task_type in
        TRAIN)
            config_file="jobs/config/train.conf"
            ;;
        FINETUNE)
            config_file="jobs/config/finetune.conf"
            ;;
        ANALYZE)
            config_file="jobs/config/analyze.conf"
            ;;
        *)
            echo "错误: 未知任务类型: $task_type"
            return 1
            ;;
    esac
    
    if [ ! -f "$config_file" ]; then
        echo "错误: 配置文件 $config_file 不存在！"
        return 1
    fi
    
    echo "读取配置文件: $config_file"
    source "$config_file"
    return 0
}

# 进入工作目录（必须在读取配置文件之前）
cd $PBS_O_WORKDIR || exit $?

# 预加载所有可能用到的配置文件
echo "预加载配置文件..."
echo "当前工作目录: $(pwd)"
for task in $TASKS; do
    if ! load_config "$task"; then
        exit 1
    fi
done

# 记录作业开始时间和节点信息
echo "==================== 统一任务提交系统 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Selected tasks: $TASKS"
echo "GPU Information:"
nvidia-smi

# ==================== 环境配置 ====================
# conda环境名称
CONDA_ENV="netket"

# GPU设备
export CUDA_VISIBLE_DEVICES="0"

# 模块加载配置
ANACONDA_MODULE="anaconda2025/2025"
UNLOAD_CUDA_MODULE="cuda/12.2"


# 加载必要的模块
echo "Loading modules..."
module load $ANACONDA_MODULE
# 注意：anaconda2025会自动加载cuda/12.2作为依赖
# 如果需要其他CUDA版本，请先卸载cuda/12.2再加载所需版本
module unload $UNLOAD_CUDA_MODULE 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
echo "Using GPU device: $CUDA_VISIBLE_DEVICES"

# 验证环境
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 通用函数定义 ====================

# 函数：检查checkpoint是否存在
check_checkpoint() {
    local L=$1
    local J2=$2
    local J1=$3
    local num_layers=$4
    local num_features=$5
    local model_dir="model_L${num_layers}F${num_features}"
    local checkpoint_path="results/L=$L/J2=$J2/J1=$J1/$model_dir/training/checkpoints/final_GCNN.pkl"

    if [ -f "$checkpoint_path" ]; then
        echo "$checkpoint_path"
        return 0
    else
        return 1
    fi
}

# 函数：构建checkpoint参数
build_checkpoint_args() {
    local enable=$1
    local interval=$2
    local keep_history=$3
    local resume_from=$4
    
    local args=""
    if [ "$enable" = "true" ]; then
        args="--enable_checkpoint --save_interval $interval"
        
        if [ "$keep_history" = "true" ]; then
            args="$args --keep_history"
        fi
        
        if [ -n "$resume_from" ]; then
            args="$args --resume_from $resume_from"
        fi
    fi
    
    echo "$args"
}

# 函数：运行单个模型配置的所有训练任务
run_train_model() {
    local num_layers=$1
    local num_features=$2
    local checkpoint_args=$3
    
    echo "==================== 模型参数: Layers=$num_layers, Features=$num_features ===================="

    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            for J1 in $TRAIN_J1_VALUES; do
                echo "Starting training L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features at: $(date)"

                # 顺序执行训练任务
                python scripts/train.py $L $J2 $J1 \
                    --n_samples $TRAIN_N_SAMPLES \
                    --chunk_size $TRAIN_CHUNK_SIZE \
                    --n_cycles $TRAIN_N_CYCLES \
                    --initial_period $TRAIN_INITIAL_PERIOD \
                    --period_mult $TRAIN_PERIOD_MULT \
                    --max_lr $TRAIN_MAX_LR \
                    --min_lr $TRAIN_MIN_LR \
                    --num_features $num_features \
                    --num_layers $num_layers \
                    --diag_shift $TRAIN_DIAG_SHIFT \
                    --grad_clip $TRAIN_GRAD_CLIP \
                    $checkpoint_args

                echo "Completed training job L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features at: $(date)"
            done
        done
    done
    
    echo "模型 Layers=$num_layers, Features=$num_features 的所有训练任务已完成！"
}

# 函数：运行训练任务
run_train() {
    echo "==================== 执行训练任务 ===================="

    # 重新加载训练配置（确保使用最新配置）
    load_config "TRAIN"

    # 构建checkpoint参数
    local checkpoint_args=$(build_checkpoint_args \
        "$TRAIN_ENABLE_CHECKPOINT" \
        "$TRAIN_CHECKPOINT_INTERVAL" \
        "$TRAIN_KEEP_CHECKPOINT_HISTORY" \
        "$TRAIN_RESUME_FROM_CHECKPOINT")

    echo "训练参数配置:"
    echo "L values: $L_VALUES"
    echo "J2 values: $J2_VALUES"
    echo "J1 values: $TRAIN_J1_VALUES"
    echo "Model configs: $TRAIN_MODEL_CONFIGS"
    echo "Model parallel: 串行执行（已移除并行）"
    echo "Learning rate: $TRAIN_LEARNING_RATE"
    echo "Samples: $TRAIN_N_SAMPLES"
    echo "Checkpoint args: $checkpoint_args"

    # 将模型配置转换为数组
    model_configs_array=($TRAIN_MODEL_CONFIGS)
    total_models=${#model_configs_array[@]}
    
    echo "总共 $total_models 个模型配置，串行执行"
    
    # 串行处理所有模型配置
    for model_config in "${model_configs_array[@]}"; do
        # 解析层数和特征数（格式：layers,features）
        IFS=',' read -r num_layers num_features <<< "$model_config"
        
        echo "==================== 开始模型 Layers=$num_layers, Features=$num_features 的训练任务 ===================="
        
        # 串行运行该模型的所有训练任务
        run_train_model $num_layers $num_features "$checkpoint_args"
        
        echo "模型 Layers=$num_layers, Features=$num_features 的训练任务已完成"
    done
    
    echo "所有训练任务已完成！"
}

# 函数：生成J1扩张层次
generate_j1_layers() {
    local start=$1
    local left_bound=$2
    local right_bound=$3
    local step=$4
    
    # 使用Python生成分层序列
    python3 -c "
import numpy as np

start = float('$start')
left_bound = float('$left_bound')
right_bound = float('$right_bound')
step = float('$step')

# 生成向左的序列
left_seq = []
current = start - step
while current >= left_bound - 1e-10:  # 修改为减去容差，确保边界值被包含
    left_seq.append(current)
    current -= step

# 生成向右的序列
right_seq = []
current = start + step
while current <= right_bound + 1e-10:  # 添加小的容差避免浮点精度问题
    right_seq.append(current)
    current += step

# 按层次输出，每层包含左右两个点（如果存在）
max_len = max(len(left_seq), len(right_seq))
for i in range(max_len):
    layer = []
    if i < len(left_seq):
        layer.append(f'{left_seq[i]:.2f}')
    if i < len(right_seq):
        layer.append(f'{right_seq[i]:.2f}')
    
    if layer:
        print(' '.join(layer))
"
}

# 函数：运行单个微调任务
run_finetune_task() {
    local L=$1
    local J2=$2
    local J1=$3
    local num_layers=$4
    local num_features=$5
    local checkpoint_path=$6
    local checkpoint_args=$7

    echo "Starting fine-tuning L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features from checkpoint: $checkpoint_path at: $(date)"

    # 运行微调
    python scripts/train.py $L $J2 $J1 \
        --n_samples $FINETUNE_N_SAMPLES \
        --chunk_size $FINETUNE_CHUNK_SIZE \
        --n_cycles $FINETUNE_N_CYCLES \
        --initial_period $FINETUNE_INITIAL_PERIOD \
        --period_mult $FINETUNE_PERIOD_MULT \
        --max_lr $FINETUNE_MAX_LR \
        --min_lr $FINETUNE_MIN_LR \
        --num_features $num_features \
        --num_layers $num_layers \
        --diag_shift $FINETUNE_DIAG_SHIFT \
        --grad_clip $FINETUNE_GRAD_CLIP \
        --resume_from "$checkpoint_path" \
        $checkpoint_args

    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo "Fine-tuning completed for L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features at: $(date)"
        return 0
    else
        echo "Fine-tuning failed for L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features at: $(date)"
        return 1
    fi
}

# 函数：运行单个模型配置的链式微调任务
run_finetune_model() {
    local num_layers=$1
    local num_features=$2
    local checkpoint_args=$3
    
    echo "==================== 模型参数: Layers=$num_layers, Features=$num_features ===================="

    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            echo "Processing L=$L, J2=$J2, L=$num_layers, F=$num_features"

            # 检查起始checkpoint是否存在
            start_checkpoint=$(check_checkpoint $L $J2 $FINETUNE_START_J1 $num_layers $num_features)
            if [ $? -ne 0 ]; then
                model_dir="model_L${num_layers}F${num_features}"
                echo "错误: 起始checkpoint不存在: results/L=$L/J2=$J2/J1=$FINETUNE_START_J1/$model_dir/training/checkpoints/final_GCNN.pkl"
                echo "请先运行训练任务训练 L=$L, J2=$J2, J1=$FINETUNE_START_J1, L=$num_layers, F=$num_features"
                continue
            fi

            echo "找到起始checkpoint: $start_checkpoint"
            
            # 生成J1扩张层次（每层包含左右点）
            j1_layers=$(generate_j1_layers $FINETUNE_START_J1 $FINETUNE_J1_LEFT_BOUND $FINETUNE_J1_RIGHT_BOUND $FINETUNE_J1_STEP)
            
            echo "J1扩张层次（串行处理）:"
            echo "$j1_layers"
            
            # 逐层进行微调，串行处理每层的所有任务
            layer_num=1
            echo "$j1_layers" | while IFS= read -r layer; do
                if [ -z "$layer" ]; then
                    continue
                fi

                echo "==================== 处理第 $layer_num 层: $layer ===================="

                # 解析当前层的J1值（空格分隔）
                layer_j1_values=($layer)

                # 为当前层的每个J1值串行执行微调任务
                for target_j1 in "${layer_j1_values[@]}"; do
                    echo "准备串行微调 J1=$target_j1, L=$num_layers, F=$num_features"

                    # 检查目标checkpoint是否已存在
                    if check_checkpoint $L $J2 $target_j1 $num_layers $num_features >/dev/null 2>&1; then
                        echo "J1=$target_j1, L=$num_layers, F=$num_features 的checkpoint已存在，跳过"
                        continue
                    fi

                    # 寻找最近的已有checkpoint作为源
                    source_checkpoint=""
                    min_distance=999999

                    # 检查起始点
                    if check_checkpoint $L $J2 $FINETUNE_START_J1 $num_layers $num_features >/dev/null 2>&1; then
                        distance=$(python3 -c "print(abs($target_j1 - $FINETUNE_START_J1))")
                        if [ $(python3 -c "print($distance < $min_distance)") = "True" ]; then
                            min_distance=$distance
                            source_checkpoint=$(check_checkpoint $L $J2 $FINETUNE_START_J1 $num_layers $num_features)
                        fi
                    fi

                    # 检查所有可能的已训练J1值（在当前目标点周围搜索）
                    search_range=$(python3 -c "
import numpy as np
target = float('$target_j1')
step = float('$FINETUNE_J1_STEP')
left_bound = float('$FINETUNE_J1_LEFT_BOUND')
right_bound = float('$FINETUNE_J1_RIGHT_BOUND')

# 生成搜索范围（目标点周围的所有可能J1值）
candidates = []
for offset in [-3, -2, -1, 1, 2, 3]:  # 搜索范围
    candidate = target + offset * step
    if left_bound <= candidate <= right_bound:
        candidates.append(f'{candidate:.2f}')

print(' '.join(candidates))
")

                    for candidate_j1 in $search_range; do
                        if check_checkpoint $L $J2 $candidate_j1 $num_layers $num_features >/dev/null 2>&1; then
                            potential_checkpoint=$(check_checkpoint $L $J2 $candidate_j1 $num_layers $num_features)
                            distance=$(python3 -c "print(abs($target_j1 - $candidate_j1))")
                            if [ $(python3 -c "print($distance < $min_distance)") = "True" ]; then
                                min_distance=$distance
                                source_checkpoint=$potential_checkpoint
                            fi
                        fi
                    done

                    if [ -z "$source_checkpoint" ]; then
                        echo "错误: 找不到合适的源checkpoint用于微调 J1=$target_j1, L=$num_layers, F=$num_features"
                        continue
                    fi
                    
                    echo "J1=$target_j1, L=$num_layers, F=$num_features 使用源checkpoint: $source_checkpoint"

                    # 串行执行微调任务
                    echo "开始串行微调 J1=$target_j1, L=$num_layers, F=$num_features at $(date)"
                    if run_finetune_task $L $J2 $target_j1 $num_layers $num_features "$source_checkpoint" "$checkpoint_args"; then
                        echo "成功完成 J1=$target_j1, L=$num_layers, F=$num_features 的串行微调 at $(date)"
                    else
                        echo "串行微调失败: J1=$target_j1, L=$num_layers, F=$num_features at $(date)"
                    fi
                done

                echo "第 $layer_num 层的所有任务已完成"

                layer_num=$((layer_num + 1))
            done

            echo "完成 L=$L, J2=$J2, L=$num_layers, F=$num_features 的链式微调"
        done
    done
    
    echo "模型 Layers=$num_layers, Features=$num_features 的链式微调已完成！"
}

# 函数：运行链式微调任务
run_finetune() {
    echo "==================== 执行链式微调任务 ===================="
    
    # 重新加载微调配置（确保使用最新配置）
    load_config "FINETUNE"
    
    # 构建checkpoint参数
    local checkpoint_args=$(build_checkpoint_args \
        "$FINETUNE_ENABLE_CHECKPOINT" \
        "$FINETUNE_CHECKPOINT_INTERVAL" \
        "$FINETUNE_KEEP_CHECKPOINT_HISTORY" \
        "")
    
    echo "链式微调参数配置:"
    echo "Start J1: $FINETUNE_START_J1"
    echo "J1 bounds: [$FINETUNE_J1_LEFT_BOUND, $FINETUNE_J1_RIGHT_BOUND]"
    echo "J1 step: $FINETUNE_J1_STEP"
    echo "Model configs: $FINETUNE_MODEL_CONFIGS"
    echo "Model parallel: 串行执行（已移除并行）"
    echo "Learning rate: $FINETUNE_LEARNING_RATE"
    echo "Samples: $FINETUNE_N_SAMPLES"

    # 将模型配置转换为数组
    model_configs_array=($FINETUNE_MODEL_CONFIGS)
    total_models=${#model_configs_array[@]}
    
    echo "总共 $total_models 个模型配置，串行执行"
    
    # 串行处理所有模型配置
    for model_config in "${model_configs_array[@]}"; do
        # 解析层数和特征数（格式：layers,features）
        IFS=',' read -r num_layers num_features <<< "$model_config"
        
        echo "==================== 开始模型 Layers=$num_layers, Features=$num_features 的链式微调任务 ===================="
        
        # 串行运行该模型的所有微调任务
        run_finetune_model $num_layers $num_features "$checkpoint_args"
        
        echo "模型 Layers=$num_layers, Features=$num_features 的链式微调任务已完成"
    done

    echo "所有链式微调任务已完成！"
}

# 函数：运行单个模型配置的所有分析任务
run_analyze_model() {
    local num_layers=$1
    local num_features=$2
    
    echo "==================== 模型参数: Layers=$num_layers, Features=$num_features ===================="
    model_dir="model_L${num_layers}F${num_features}"

    # 遍历所有参数组合并运行分析
    for params in "${ANALYZE_PARAM_SETS[@]}"; do
        # 提取参数
        read -r L J2 J1 <<< "$params"

        echo "Starting analysis: L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features at: $(date)"

        # 获取checkpoint目录
        checkpoint_dir="results/L=$L/J2=$J2/J1=$J1/$model_dir/training/checkpoints"

        # 检查checkpoint目录是否存在
        if [ ! -d "$checkpoint_dir" ]; then
            echo "Warning: Checkpoint directory $checkpoint_dir does not exist, skipping..."
            continue
        fi

        # 根据配置决定分析哪些checkpoint
        if [ "$ANALYZE_ALL_CHECKPOINTS" = "true" ]; then
            # 获取所有checkpoint文件
            checkpoint_files=($(find "$checkpoint_dir" -name "*.pkl" | sort))

            if [ ${#checkpoint_files[@]} -eq 0 ]; then
                echo "Warning: No checkpoint files found in $checkpoint_dir, skipping..."
                continue
            fi

            echo "Found ${#checkpoint_files[@]} checkpoint files for L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features"
        else
            # 只分析final_GCNN.pkl
            final_checkpoint="$checkpoint_dir/final_GCNN.pkl"
            if [ ! -f "$final_checkpoint" ]; then
                echo "Warning: Final checkpoint $final_checkpoint does not exist, skipping..."
                continue
            fi
            checkpoint_files=("$final_checkpoint")
            echo "Analyzing only final checkpoint for L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features"
        fi

        # 为每个checkpoint顺序运行分析
        for checkpoint_file in "${checkpoint_files[@]}"; do
            # 提取checkpoint名称（不含路径和扩展名）
            checkpoint_name=$(basename "$checkpoint_file" .pkl)

            echo "  Processing checkpoint: $checkpoint_name"

            # 顺序运行分析脚本，传递模型参数
            python scripts/analyze.py --L $L --J2 $J2 --J1 $J1 \
                --num_features $num_features --num_layers $num_layers \
                --checkpoint "$checkpoint_name" --n_samples $ANALYZE_N_SAMPLES

            echo "  Completed checkpoint: $checkpoint_name"
        done

        echo "Completed analysis jobs for L=$L, J2=$J2, J1=$J1, L=$num_layers, F=$num_features (${#checkpoint_files[@]} checkpoints)"
        echo "---------------------------------------"
    done
    
    echo "模型 Layers=$num_layers, Features=$num_features 的所有分析任务已完成！"
}

# 函数：运行分析任务
run_analyze() {
    echo "==================== 执行分析任务 ===================="

    # 重新加载分析配置（确保使用最新配置）
    load_config "ANALYZE"

    echo "分析参数组合:"
    printf '%s\n' "${ANALYZE_PARAM_SETS[@]}"
    echo "Model configs: $ANALYZE_MODEL_CONFIGS"
    echo "Model parallel: 串行执行（已移除并行）"
    echo "Analyze all checkpoints: $ANALYZE_ALL_CHECKPOINTS"

    # 将模型配置转换为数组
    model_configs_array=($ANALYZE_MODEL_CONFIGS)
    total_models=${#model_configs_array[@]}
    
    echo "总共 $total_models 个模型配置，串行执行"
    
    # 串行处理所有模型配置
    for model_config in "${model_configs_array[@]}"; do
        # 解析层数和特征数（格式：layers,features）
        IFS=',' read -r num_layers num_features <<< "$model_config"
        
        echo "==================== 开始模型 Layers=$num_layers, Features=$num_features 的分析任务 ===================="
        
        # 串行运行该模型的所有分析任务
        run_analyze_model $num_layers $num_features
        
        echo "模型 Layers=$num_layers, Features=$num_features 的分析任务已完成"
    done

    # 整理结果
    echo "Organizing results..."

    # 记录磁盘使用情况
    echo "Disk usage for results:"
    du -sh results/

    echo "所有分析任务已完成！"
}

# ==================== 主执行逻辑 ====================

# 串行执行选定的任务
for task in $TASKS; do
    case $task in
        TRAIN)
            echo "开始训练任务..."
            run_train
            echo "训练任务已完成"
            ;;
        FINETUNE)
            echo "开始微调任务..."
            run_finetune
            echo "微调任务已完成"
            ;;
        ANALYZE)
            echo "开始分析任务..."
            run_analyze
            echo "分析任务已完成"
            ;;
        *)
            echo "错误: 未知任务类型: $task"
            echo "可用任务类型: TRAIN, FINETUNE, ANALYZE"
            exit 1
            ;;
    esac
done

echo "==================== 所有任务完成 ===================="
echo "执行的任务: $TASKS"
echo "Job finished at: $(date)"
