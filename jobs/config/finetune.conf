# ==================== 链式微调任务配置文件 ====================
# 此文件包含链式微调任务的所有参数配置

# ==================== 系统参数配置 ====================
# 晶格尺寸
L_VALUES="4 5"

# J2耦合强度
J2_VALUES="1.00"

# ==================== 链式微调起始和边界参数 ====================
FINETUNE_START_J1="0.76"
FINETUNE_J1_LEFT_BOUND="0.76"
FINETUNE_J1_RIGHT_BOUND="0.84"
FINETUNE_J1_STEP="0.01"

# ==================== 微调超参数 ====================
# 通常比训练用更少的样本和周期
# 学习率调度（余弦退火+热重启）
FINETUNE_MAX_LR=0.03         # 最大学习率（重启时的学习率）
FINETUNE_MIN_LR=0.005        # 最小学习率（周期结束时的学习率）
FINETUNE_INITIAL_PERIOD=150   # 初始退火周期长度
FINETUNE_PERIOD_MULT=2.0      # 周期倍增因子
FINETUNE_N_CYCLES=3           # 重启周期数

# 采样参数
FINETUNE_N_SAMPLES=4096
FINETUNE_CHUNK_SIZE=4096

# ==================== 模型参数 ====================
# 模型参数组合（格式：每个组合为"层数,特征数"，空格分隔）
# 例如："2,4" 表示 2层4特征，挨个模型执行微调链
# 示例：FINETUNE_MODEL_CONFIGS="2,4 4,5 6,8"
FINETUNE_MODEL_CONFIGS="4,4 6,4 8,4"

# 模型并行数已移除，所有模型将串行执行

# 其他模型参数
FINETUNE_DIAG_SHIFT=0.15
FINETUNE_GRAD_CLIP=1.0

# ==================== Checkpoint配置 ====================
FINETUNE_ENABLE_CHECKPOINT=true
FINETUNE_CHECKPOINT_INTERVAL=100
FINETUNE_KEEP_CHECKPOINT_HISTORY=true

