[2025-10-02 16:26:50] ==================================================
[2025-10-02 16:26:50] GCNN for Shastry-Sutherland Model
[2025-10-02 16:26:50] ==================================================
[2025-10-02 16:26:50] System parameters:
[2025-10-02 16:26:50]   - System size: L=5, N=100
[2025-10-02 16:26:50]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-02 16:26:50] --------------------------------------------------
[2025-10-02 16:26:50] Model parameters:
[2025-10-02 16:26:50]   - Number of layers = 4
[2025-10-02 16:26:50]   - Number of features = 4
[2025-10-02 16:26:50]   - Total parameters = 19628
[2025-10-02 16:26:50] --------------------------------------------------
[2025-10-02 16:26:50] Training parameters:
[2025-10-02 16:26:50]   - Total iterations: 2250
[2025-10-02 16:26:50]   - Annealing cycles: 4
[2025-10-02 16:26:50]   - Initial period: 150
[2025-10-02 16:26:50]   - Period multiplier: 2.0
[2025-10-02 16:26:50]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-02 16:26:50]   - Samples: 4096
[2025-10-02 16:26:50]   - Discarded samples: 0
[2025-10-02 16:26:50]   - Chunk size: 4096
[2025-10-02 16:26:50]   - Diagonal shift: 0.15
[2025-10-02 16:26:51]   - Gradient clipping: 1.0
[2025-10-02 16:26:51]   - Checkpoint enabled: interval=200
[2025-10-02 16:26:51]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.76/model_L4F4/training/checkpoints
[2025-10-02 16:26:51] --------------------------------------------------
[2025-10-02 16:26:51] Device status:
[2025-10-02 16:26:51]   - Devices model: NVIDIA H200 NVL
[2025-10-02 16:26:51]   - Number of devices: 1
[2025-10-02 16:26:51]   - Sharding: True
[2025-10-02 16:27:21] [Iter    1/2250] R0[0/150]    | LR: 0.030000 | E:  50.486668 | E_var:     0.0576 | E_err:   0.003751
[2025-10-02 16:27:26] [Iter    2/2250] R0[2/150]    | LR: 0.029989 | E:  50.482162 | E_var:     0.0831 | E_err:   0.004503
[2025-10-02 16:27:31] [Iter    3/2250] R0[4/150]    | LR: 0.029956 | E:  50.478326 | E_var:     0.1239 | E_err:   0.005500
[2025-10-02 16:27:36] [Iter    4/2250] R0[6/150]    | LR: 0.029901 | E:  50.469094 | E_var:     0.1848 | E_err:   0.006716
[2025-10-02 16:27:41] [Iter    5/2250] R0[8/150]    | LR: 0.029825 | E:  50.449521 | E_var:     0.3087 | E_err:   0.008682
[2025-10-02 16:27:46] [Iter    6/2250] R0[10/150]   | LR: 0.029727 | E:  50.410657 | E_var:     0.5305 | E_err:   0.011380
[2025-10-02 16:27:52] [Iter    7/2250] R0[12/150]   | LR: 0.029607 | E:  50.334424 | E_var:     0.9529 | E_err:   0.015253
[2025-10-02 16:27:57] [Iter    8/2250] R0[14/150]   | LR: 0.029466 | E:  50.224302 | E_var:     1.7211 | E_err:   0.020499
[2025-10-02 16:28:02] [Iter    9/2250] R0[16/150]   | LR: 0.029305 | E:  49.909679 | E_var:     3.5317 | E_err:   0.029364
[2025-10-02 16:28:07] [Iter   10/2250] R0[18/150]   | LR: 0.029122 | E:  49.360171 | E_var:     8.0751 | E_err:   0.044401
[2025-10-02 16:28:12] [Iter   11/2250] R0[20/150]   | LR: 0.028919 | E:  47.795992 | E_var:    22.7101 | E_err:   0.074461
[2025-10-02 16:28:17] [Iter   12/2250] R0[22/150]   | LR: 0.028696 | E:  43.821780 | E_var:    57.4759 | E_err:   0.118458
[2025-10-02 16:28:23] [Iter   13/2250] R0[24/150]   | LR: 0.028454 | E:  35.911152 | E_var:    88.2101 | E_err:   0.146750
[2025-10-02 16:28:28] [Iter   14/2250] R0[26/150]   | LR: 0.028192 | E:  28.077356 | E_var:    66.2503 | E_err:   0.127179
[2025-10-02 16:28:33] [Iter   15/2250] R0[28/150]   | LR: 0.027912 | E:  22.253440 | E_var:    59.3232 | E_err:   0.120346
[2025-10-02 16:28:38] [Iter   16/2250] R0[30/150]   | LR: 0.027613 | E:  17.174872 | E_var:    57.0787 | E_err:   0.118048
[2025-10-02 16:28:43] [Iter   17/2250] R0[32/150]   | LR: 0.027296 | E:  12.249225 | E_var:    60.5026 | E_err:   0.121537
[2025-10-02 16:28:48] [Iter   18/2250] R0[34/150]   | LR: 0.026962 | E:   7.621872 | E_var:    68.0109 | E_err:   0.128857
[2025-10-02 16:28:54] [Iter   19/2250] R0[36/150]   | LR: 0.026612 | E:   3.561412 | E_var:    55.4141 | E_err:   0.116314
[2025-10-02 16:28:59] [Iter   20/2250] R0[38/150]   | LR: 0.026246 | E:  -0.145377 | E_var:    47.3527 | E_err:   0.107521
[2025-10-02 16:29:04] [Iter   21/2250] R0[40/150]   | LR: 0.025864 | E:  -2.941828 | E_var:    42.0058 | E_err:   0.101269
[2025-10-02 16:29:09] [Iter   22/2250] R0[42/150]   | LR: 0.025468 | E:  -5.391186 | E_var:    39.7457 | E_err:   0.098507
[2025-10-02 16:29:14] [Iter   23/2250] R0[44/150]   | LR: 0.025057 | E:  -7.364836 | E_var:    45.3485 | E_err:   0.105221
[2025-10-02 16:29:19] [Iter   24/2250] R0[46/150]   | LR: 0.024634 | E:  -9.156683 | E_var:    35.3088 | E_err:   0.092846
[2025-10-02 16:29:25] [Iter   25/2250] R0[48/150]   | LR: 0.024198 | E: -10.674083 | E_var:    31.4722 | E_err:   0.087656
[2025-10-02 16:29:30] [Iter   26/2250] R0[50/150]   | LR: 0.023750 | E: -11.975133 | E_var:    38.3562 | E_err:   0.096769
[2025-10-02 16:29:35] [Iter   27/2250] R0[52/150]   | LR: 0.023291 | E: -13.167966 | E_var:    27.0476 | E_err:   0.081261
[2025-10-02 16:29:40] [Iter   28/2250] R0[54/150]   | LR: 0.022822 | E: -14.360180 | E_var:    28.5125 | E_err:   0.083433
[2025-10-02 16:29:45] [Iter   29/2250] R0[56/150]   | LR: 0.022344 | E: -15.184756 | E_var:    26.9295 | E_err:   0.081084
[2025-10-02 16:29:50] [Iter   30/2250] R0[58/150]   | LR: 0.021857 | E: -16.162061 | E_var:    27.5581 | E_err:   0.082025
[2025-10-02 16:29:56] [Iter   31/2250] R0[60/150]   | LR: 0.021363 | E: -17.025616 | E_var:    29.0541 | E_err:   0.084222
[2025-10-02 16:30:01] [Iter   32/2250] R0[62/150]   | LR: 0.020861 | E: -17.777349 | E_var:    33.1145 | E_err:   0.089914
[2025-10-02 16:30:06] [Iter   33/2250] R0[64/150]   | LR: 0.020354 | E: -18.486064 | E_var:    29.0151 | E_err:   0.084165
[2025-10-02 16:30:11] [Iter   34/2250] R0[66/150]   | LR: 0.019842 | E: -19.131270 | E_var:    24.5492 | E_err:   0.077417
[2025-10-02 16:30:16] [Iter   35/2250] R0[68/150]   | LR: 0.019326 | E: -19.846107 | E_var:    24.1458 | E_err:   0.076779
[2025-10-02 16:30:21] [Iter   36/2250] R0[70/150]   | LR: 0.018807 | E: -20.545699 | E_var:    25.9437 | E_err:   0.079586
[2025-10-02 16:30:27] [Iter   37/2250] R0[72/150]   | LR: 0.018285 | E: -21.113140 | E_var:    22.2221 | E_err:   0.073657
[2025-10-02 16:30:32] [Iter   38/2250] R0[74/150]   | LR: 0.017762 | E: -21.533238 | E_var:    21.5071 | E_err:   0.072462
[2025-10-02 16:30:37] [Iter   39/2250] R0[76/150]   | LR: 0.017238 | E: -22.158251 | E_var:    23.7805 | E_err:   0.076196
[2025-10-02 16:30:42] [Iter   40/2250] R0[78/150]   | LR: 0.016715 | E: -22.643690 | E_var:    24.5359 | E_err:   0.077396
[2025-10-02 16:30:47] [Iter   41/2250] R0[80/150]   | LR: 0.016193 | E: -23.053400 | E_var:    24.4505 | E_err:   0.077262
[2025-10-02 16:30:52] [Iter   42/2250] R0[82/150]   | LR: 0.015674 | E: -23.412091 | E_var:    19.8573 | E_err:   0.069627
[2025-10-02 16:30:58] [Iter   43/2250] R0[84/150]   | LR: 0.015158 | E: -23.942636 | E_var:    21.6666 | E_err:   0.072730
[2025-10-02 16:31:03] [Iter   44/2250] R0[86/150]   | LR: 0.014646 | E: -24.189992 | E_var:    18.9421 | E_err:   0.068004
[2025-10-02 16:31:08] [Iter   45/2250] R0[88/150]   | LR: 0.014139 | E: -24.639147 | E_var:    20.0305 | E_err:   0.069930
[2025-10-02 16:31:13] [Iter   46/2250] R0[90/150]   | LR: 0.013637 | E: -24.877316 | E_var:    20.5280 | E_err:   0.070794
[2025-10-02 16:31:18] [Iter   47/2250] R0[92/150]   | LR: 0.013143 | E: -25.178794 | E_var:    18.2214 | E_err:   0.066698
[2025-10-02 16:31:23] [Iter   48/2250] R0[94/150]   | LR: 0.012656 | E: -25.600169 | E_var:    19.4573 | E_err:   0.068922
[2025-10-02 16:31:29] [Iter   49/2250] R0[96/150]   | LR: 0.012178 | E: -25.915426 | E_var:    18.0157 | E_err:   0.066320
[2025-10-02 16:31:34] [Iter   50/2250] R0[98/150]   | LR: 0.011709 | E: -26.109078 | E_var:    20.0380 | E_err:   0.069943
[2025-10-02 16:31:39] [Iter   51/2250] R0[100/150]  | LR: 0.011250 | E: -26.548033 | E_var:    18.5987 | E_err:   0.067385
[2025-10-02 16:31:44] [Iter   52/2250] R0[102/150]  | LR: 0.010802 | E: -26.784067 | E_var:    19.0119 | E_err:   0.068129
[2025-10-02 16:31:49] [Iter   53/2250] R0[104/150]  | LR: 0.010366 | E: -26.977803 | E_var:    17.7870 | E_err:   0.065898
[2025-10-02 16:31:55] [Iter   54/2250] R0[106/150]  | LR: 0.009943 | E: -27.211823 | E_var:    17.5325 | E_err:   0.065425
[2025-10-02 16:32:00] [Iter   55/2250] R0[108/150]  | LR: 0.009532 | E: -27.540236 | E_var:    17.4015 | E_err:   0.065180
[2025-10-02 16:32:05] [Iter   56/2250] R0[110/150]  | LR: 0.009136 | E: -27.675397 | E_var:    17.2708 | E_err:   0.064935
[2025-10-02 16:32:10] [Iter   57/2250] R0[112/150]  | LR: 0.008754 | E: -27.861354 | E_var:    16.4766 | E_err:   0.063424
[2025-10-02 16:32:15] [Iter   58/2250] R0[114/150]  | LR: 0.008388 | E: -28.058987 | E_var:    17.8763 | E_err:   0.066063
[2025-10-02 16:32:20] [Iter   59/2250] R0[116/150]  | LR: 0.008038 | E: -28.410123 | E_var:    16.6787 | E_err:   0.063812
[2025-10-02 16:32:26] [Iter   60/2250] R0[118/150]  | LR: 0.007704 | E: -28.529444 | E_var:    15.2577 | E_err:   0.061033
[2025-10-02 16:32:31] [Iter   61/2250] R0[120/150]  | LR: 0.007387 | E: -28.756936 | E_var:    17.8176 | E_err:   0.065955
[2025-10-02 16:32:36] [Iter   62/2250] R0[122/150]  | LR: 0.007088 | E: -28.897225 | E_var:    16.5213 | E_err:   0.063510
[2025-10-02 16:32:41] [Iter   63/2250] R0[124/150]  | LR: 0.006808 | E: -29.124644 | E_var:    15.6235 | E_err:   0.061760
[2025-10-02 16:32:46] [Iter   64/2250] R0[126/150]  | LR: 0.006546 | E: -29.404832 | E_var:    16.8422 | E_err:   0.064124
[2025-10-02 16:32:51] [Iter   65/2250] R0[128/150]  | LR: 0.006304 | E: -29.567694 | E_var:    15.8599 | E_err:   0.062226
[2025-10-02 16:32:57] [Iter   66/2250] R0[130/150]  | LR: 0.006081 | E: -29.752747 | E_var:    15.8238 | E_err:   0.062155
[2025-10-02 16:33:02] [Iter   67/2250] R0[132/150]  | LR: 0.005878 | E: -29.914973 | E_var:    15.5636 | E_err:   0.061642
[2025-10-02 16:33:07] [Iter   68/2250] R0[134/150]  | LR: 0.005695 | E: -30.085590 | E_var:    14.6849 | E_err:   0.059876
[2025-10-02 16:33:12] [Iter   69/2250] R0[136/150]  | LR: 0.005534 | E: -30.232495 | E_var:    14.7006 | E_err:   0.059908
[2025-10-02 16:33:17] [Iter   70/2250] R0[138/150]  | LR: 0.005393 | E: -30.386540 | E_var:    14.1014 | E_err:   0.058675
[2025-10-02 16:33:22] [Iter   71/2250] R0[140/150]  | LR: 0.005273 | E: -30.553493 | E_var:    14.9069 | E_err:   0.060327
[2025-10-02 16:33:27] [Iter   72/2250] R0[142/150]  | LR: 0.005175 | E: -30.770913 | E_var:    14.2476 | E_err:   0.058978
[2025-10-02 16:33:33] [Iter   73/2250] R0[144/150]  | LR: 0.005099 | E: -30.907964 | E_var:    14.2013 | E_err:   0.058882
[2025-10-02 16:33:38] [Iter   74/2250] R0[146/150]  | LR: 0.005044 | E: -31.018999 | E_var:    14.3715 | E_err:   0.059234
[2025-10-02 16:33:43] [Iter   75/2250] R0[148/150]  | LR: 0.005011 | E: -31.258124 | E_var:    15.3556 | E_err:   0.061228
[2025-10-02 16:33:43] 🔄 RESTART #1 | Period: 300
[2025-10-02 16:33:48] [Iter   76/2250] R1[0/300]    | LR: 0.030000 | E: -31.406789 | E_var:    14.6420 | E_err:   0.059789
[2025-10-02 16:33:53] [Iter   77/2250] R1[2/300]    | LR: 0.029997 | E: -31.545097 | E_var:    14.2038 | E_err:   0.058887
[2025-10-02 16:33:59] [Iter   78/2250] R1[4/300]    | LR: 0.029989 | E: -31.787073 | E_var:    13.5423 | E_err:   0.057500
[2025-10-02 16:34:04] [Iter   79/2250] R1[6/300]    | LR: 0.029975 | E: -31.870277 | E_var:    14.7384 | E_err:   0.059985
[2025-10-02 16:34:09] [Iter   80/2250] R1[8/300]    | LR: 0.029956 | E: -32.080838 | E_var:    13.4851 | E_err:   0.057378
[2025-10-02 16:34:14] [Iter   81/2250] R1[10/300]   | LR: 0.029932 | E: -32.287214 | E_var:    13.3202 | E_err:   0.057026
[2025-10-02 16:34:19] [Iter   82/2250] R1[12/300]   | LR: 0.029901 | E: -32.347215 | E_var:    13.5672 | E_err:   0.057553
[2025-10-02 16:34:24] [Iter   83/2250] R1[14/300]   | LR: 0.029866 | E: -32.465779 | E_var:    14.6488 | E_err:   0.059803
[2025-10-02 16:34:29] [Iter   84/2250] R1[16/300]   | LR: 0.029825 | E: -32.627433 | E_var:    13.0364 | E_err:   0.056416
[2025-10-02 16:34:35] [Iter   85/2250] R1[18/300]   | LR: 0.029779 | E: -32.779120 | E_var:    12.8743 | E_err:   0.056064
[2025-10-02 16:34:40] [Iter   86/2250] R1[20/300]   | LR: 0.029727 | E: -32.928088 | E_var:    12.6846 | E_err:   0.055649
[2025-10-02 16:34:45] [Iter   87/2250] R1[22/300]   | LR: 0.029670 | E: -33.051171 | E_var:    12.9883 | E_err:   0.056311
[2025-10-02 16:34:50] [Iter   88/2250] R1[24/300]   | LR: 0.029607 | E: -33.163541 | E_var:    13.0936 | E_err:   0.056539
[2025-10-02 16:34:55] [Iter   89/2250] R1[26/300]   | LR: 0.029540 | E: -33.264451 | E_var:    12.0486 | E_err:   0.054236
[2025-10-02 16:35:00] [Iter   90/2250] R1[28/300]   | LR: 0.029466 | E: -33.396587 | E_var:    11.5095 | E_err:   0.053009
[2025-10-02 16:35:06] [Iter   91/2250] R1[30/300]   | LR: 0.029388 | E: -33.584181 | E_var:    12.2162 | E_err:   0.054612
[2025-10-02 16:35:11] [Iter   92/2250] R1[32/300]   | LR: 0.029305 | E: -33.739977 | E_var:    11.8772 | E_err:   0.053849
[2025-10-02 16:35:16] [Iter   93/2250] R1[34/300]   | LR: 0.029216 | E: -33.742773 | E_var:    11.4242 | E_err:   0.052812
[2025-10-02 16:35:21] [Iter   94/2250] R1[36/300]   | LR: 0.029122 | E: -33.962588 | E_var:    11.9555 | E_err:   0.054026
[2025-10-02 16:35:26] [Iter   95/2250] R1[38/300]   | LR: 0.029023 | E: -33.981458 | E_var:    11.1730 | E_err:   0.052228
[2025-10-02 16:35:31] [Iter   96/2250] R1[40/300]   | LR: 0.028919 | E: -34.193468 | E_var:    10.5761 | E_err:   0.050814
[2025-10-02 16:35:37] [Iter   97/2250] R1[42/300]   | LR: 0.028810 | E: -34.252229 | E_var:    11.6763 | E_err:   0.053392
[2025-10-02 16:35:42] [Iter   98/2250] R1[44/300]   | LR: 0.028696 | E: -34.379627 | E_var:    11.6585 | E_err:   0.053351
[2025-10-02 16:35:47] [Iter   99/2250] R1[46/300]   | LR: 0.028578 | E: -34.516475 | E_var:    10.9765 | E_err:   0.051767
[2025-10-02 16:35:52] [Iter  100/2250] R1[48/300]   | LR: 0.028454 | E: -34.636178 | E_var:    11.0261 | E_err:   0.051884
[2025-10-02 16:35:57] [Iter  101/2250] R1[50/300]   | LR: 0.028325 | E: -34.678850 | E_var:    10.9078 | E_err:   0.051605
[2025-10-02 16:36:02] [Iter  102/2250] R1[52/300]   | LR: 0.028192 | E: -34.805259 | E_var:    10.0130 | E_err:   0.049443
[2025-10-02 16:36:08] [Iter  103/2250] R1[54/300]   | LR: 0.028054 | E: -34.949220 | E_var:    10.2720 | E_err:   0.050078
[2025-10-02 16:36:13] [Iter  104/2250] R1[56/300]   | LR: 0.027912 | E: -35.093098 | E_var:    10.2435 | E_err:   0.050009
[2025-10-02 16:36:18] [Iter  105/2250] R1[58/300]   | LR: 0.027764 | E: -35.109956 | E_var:    10.0873 | E_err:   0.049626
[2025-10-02 16:36:23] [Iter  106/2250] R1[60/300]   | LR: 0.027613 | E: -35.150468 | E_var:     9.7432 | E_err:   0.048772
[2025-10-02 16:36:28] [Iter  107/2250] R1[62/300]   | LR: 0.027457 | E: -35.310193 | E_var:     9.6749 | E_err:   0.048601
[2025-10-02 16:36:33] [Iter  108/2250] R1[64/300]   | LR: 0.027296 | E: -35.384125 | E_var:    10.1826 | E_err:   0.049860
[2025-10-02 16:36:39] [Iter  109/2250] R1[66/300]   | LR: 0.027131 | E: -35.528254 | E_var:     9.3410 | E_err:   0.047755
[2025-10-02 16:36:44] [Iter  110/2250] R1[68/300]   | LR: 0.026962 | E: -35.601479 | E_var:     9.5308 | E_err:   0.048237
[2025-10-02 16:36:49] [Iter  111/2250] R1[70/300]   | LR: 0.026789 | E: -35.685251 | E_var:     9.3793 | E_err:   0.047853
[2025-10-02 16:36:54] [Iter  112/2250] R1[72/300]   | LR: 0.026612 | E: -35.745170 | E_var:     8.7376 | E_err:   0.046187
[2025-10-02 16:36:59] [Iter  113/2250] R1[74/300]   | LR: 0.026431 | E: -35.861012 | E_var:     8.7190 | E_err:   0.046137
[2025-10-02 16:37:05] [Iter  114/2250] R1[76/300]   | LR: 0.026246 | E: -35.950850 | E_var:     8.9438 | E_err:   0.046728
[2025-10-02 16:37:10] [Iter  115/2250] R1[78/300]   | LR: 0.026057 | E: -36.004460 | E_var:     9.1015 | E_err:   0.047139
[2025-10-02 16:37:15] [Iter  116/2250] R1[80/300]   | LR: 0.025864 | E: -36.058800 | E_var:     8.7797 | E_err:   0.046298
[2025-10-02 16:37:20] [Iter  117/2250] R1[82/300]   | LR: 0.025668 | E: -36.187409 | E_var:     8.0176 | E_err:   0.044243
[2025-10-02 16:37:25] [Iter  118/2250] R1[84/300]   | LR: 0.025468 | E: -36.271882 | E_var:     8.4843 | E_err:   0.045512
[2025-10-02 16:37:30] [Iter  119/2250] R1[86/300]   | LR: 0.025264 | E: -36.322643 | E_var:     8.1289 | E_err:   0.044549
[2025-10-02 16:37:35] [Iter  120/2250] R1[88/300]   | LR: 0.025057 | E: -36.380664 | E_var:     7.9734 | E_err:   0.044121
[2025-10-02 16:37:41] [Iter  121/2250] R1[90/300]   | LR: 0.024847 | E: -36.428151 | E_var:     7.8498 | E_err:   0.043777
[2025-10-02 16:37:46] [Iter  122/2250] R1[92/300]   | LR: 0.024634 | E: -36.501345 | E_var:     7.9783 | E_err:   0.044134
[2025-10-02 16:37:51] [Iter  123/2250] R1[94/300]   | LR: 0.024417 | E: -36.584951 | E_var:     7.7809 | E_err:   0.043585
[2025-10-02 16:37:56] [Iter  124/2250] R1[96/300]   | LR: 0.024198 | E: -36.644498 | E_var:     7.3063 | E_err:   0.042235
[2025-10-02 16:38:01] [Iter  125/2250] R1[98/300]   | LR: 0.023975 | E: -36.732767 | E_var:     7.3358 | E_err:   0.042320
[2025-10-02 16:38:06] [Iter  126/2250] R1[100/300]  | LR: 0.023750 | E: -36.854271 | E_var:     7.6940 | E_err:   0.043341
[2025-10-02 16:38:12] [Iter  127/2250] R1[102/300]  | LR: 0.023522 | E: -36.896907 | E_var:     7.3256 | E_err:   0.042290
[2025-10-02 16:38:17] [Iter  128/2250] R1[104/300]  | LR: 0.023291 | E: -36.962768 | E_var:     7.4382 | E_err:   0.042614
[2025-10-02 16:38:22] [Iter  129/2250] R1[106/300]  | LR: 0.023058 | E: -36.986375 | E_var:     7.1888 | E_err:   0.041894
[2025-10-02 16:38:27] [Iter  130/2250] R1[108/300]  | LR: 0.022822 | E: -37.041495 | E_var:     7.0733 | E_err:   0.041556
[2025-10-02 16:38:32] [Iter  131/2250] R1[110/300]  | LR: 0.022584 | E: -37.040072 | E_var:     7.1386 | E_err:   0.041747
[2025-10-02 16:38:37] [Iter  132/2250] R1[112/300]  | LR: 0.022344 | E: -37.183708 | E_var:     6.7945 | E_err:   0.040728
[2025-10-02 16:38:43] [Iter  133/2250] R1[114/300]  | LR: 0.022102 | E: -37.176546 | E_var:     6.9151 | E_err:   0.041088
[2025-10-02 16:38:48] [Iter  134/2250] R1[116/300]  | LR: 0.021857 | E: -37.277425 | E_var:     7.7290 | E_err:   0.043439
[2025-10-02 16:38:53] [Iter  135/2250] R1[118/300]  | LR: 0.021611 | E: -37.315298 | E_var:     6.6155 | E_err:   0.040188
[2025-10-02 16:38:58] [Iter  136/2250] R1[120/300]  | LR: 0.021363 | E: -37.370657 | E_var:     6.4324 | E_err:   0.039628
[2025-10-02 16:39:03] [Iter  137/2250] R1[122/300]  | LR: 0.021113 | E: -37.408143 | E_var:     6.2278 | E_err:   0.038993
[2025-10-02 16:39:09] [Iter  138/2250] R1[124/300]  | LR: 0.020861 | E: -37.430152 | E_var:     6.7402 | E_err:   0.040566
[2025-10-02 16:39:14] [Iter  139/2250] R1[126/300]  | LR: 0.020609 | E: -37.499179 | E_var:     6.3571 | E_err:   0.039396
[2025-10-02 16:39:19] [Iter  140/2250] R1[128/300]  | LR: 0.020354 | E: -37.561747 | E_var:     6.4530 | E_err:   0.039692
[2025-10-02 16:39:24] [Iter  141/2250] R1[130/300]  | LR: 0.020099 | E: -37.633074 | E_var:     6.2721 | E_err:   0.039131
[2025-10-02 16:39:29] [Iter  142/2250] R1[132/300]  | LR: 0.019842 | E: -37.633534 | E_var:     6.3146 | E_err:   0.039264
[2025-10-02 16:39:34] [Iter  143/2250] R1[134/300]  | LR: 0.019585 | E: -37.695713 | E_var:     7.2565 | E_err:   0.042090
[2025-10-02 16:39:40] [Iter  144/2250] R1[136/300]  | LR: 0.019326 | E: -37.697958 | E_var:     5.5765 | E_err:   0.036898
[2025-10-02 16:39:45] [Iter  145/2250] R1[138/300]  | LR: 0.019067 | E: -37.739800 | E_var:     6.2047 | E_err:   0.038921
[2025-10-02 16:39:50] [Iter  146/2250] R1[140/300]  | LR: 0.018807 | E: -37.747491 | E_var:     5.9303 | E_err:   0.038050
[2025-10-02 16:39:55] [Iter  147/2250] R1[142/300]  | LR: 0.018546 | E: -37.814966 | E_var:     5.8310 | E_err:   0.037731
[2025-10-02 16:40:00] [Iter  148/2250] R1[144/300]  | LR: 0.018285 | E: -37.859833 | E_var:     5.9078 | E_err:   0.037978
[2025-10-02 16:40:05] [Iter  149/2250] R1[146/300]  | LR: 0.018023 | E: -37.911194 | E_var:     5.5447 | E_err:   0.036793
[2025-10-02 16:40:11] [Iter  150/2250] R1[148/300]  | LR: 0.017762 | E: -37.861058 | E_var:     6.0322 | E_err:   0.038376
[2025-10-02 16:40:16] [Iter  151/2250] R1[150/300]  | LR: 0.017500 | E: -37.915286 | E_var:     5.5835 | E_err:   0.036921
[2025-10-02 16:40:21] [Iter  152/2250] R1[152/300]  | LR: 0.017238 | E: -37.957251 | E_var:     5.0983 | E_err:   0.035280
[2025-10-02 16:40:26] [Iter  153/2250] R1[154/300]  | LR: 0.016977 | E: -38.020439 | E_var:     5.3905 | E_err:   0.036277
[2025-10-02 16:40:31] [Iter  154/2250] R1[156/300]  | LR: 0.016715 | E: -38.036205 | E_var:     5.5632 | E_err:   0.036854
[2025-10-02 16:40:36] [Iter  155/2250] R1[158/300]  | LR: 0.016454 | E: -38.065532 | E_var:     5.2293 | E_err:   0.035731
[2025-10-02 16:40:42] [Iter  156/2250] R1[160/300]  | LR: 0.016193 | E: -38.144595 | E_var:     5.0682 | E_err:   0.035176
[2025-10-02 16:40:47] [Iter  157/2250] R1[162/300]  | LR: 0.015933 | E: -38.145808 | E_var:     5.1599 | E_err:   0.035493
[2025-10-02 16:40:52] [Iter  158/2250] R1[164/300]  | LR: 0.015674 | E: -38.160313 | E_var:     5.2111 | E_err:   0.035669
[2025-10-02 16:40:57] [Iter  159/2250] R1[166/300]  | LR: 0.015415 | E: -38.225148 | E_var:     5.4393 | E_err:   0.036441
[2025-10-02 16:41:02] [Iter  160/2250] R1[168/300]  | LR: 0.015158 | E: -38.253259 | E_var:     4.9120 | E_err:   0.034630
[2025-10-02 16:41:07] [Iter  161/2250] R1[170/300]  | LR: 0.014901 | E: -38.300436 | E_var:     4.8432 | E_err:   0.034386
[2025-10-02 16:41:13] [Iter  162/2250] R1[172/300]  | LR: 0.014646 | E: -38.267242 | E_var:     4.9520 | E_err:   0.034771
[2025-10-02 16:41:18] [Iter  163/2250] R1[174/300]  | LR: 0.014391 | E: -38.368912 | E_var:     4.7048 | E_err:   0.033892
[2025-10-02 16:41:23] [Iter  164/2250] R1[176/300]  | LR: 0.014139 | E: -38.361582 | E_var:     4.7216 | E_err:   0.033952
[2025-10-02 16:41:28] [Iter  165/2250] R1[178/300]  | LR: 0.013887 | E: -38.332053 | E_var:     5.6615 | E_err:   0.037178
[2025-10-02 16:41:33] [Iter  166/2250] R1[180/300]  | LR: 0.013637 | E: -38.368949 | E_var:     4.6418 | E_err:   0.033664
[2025-10-02 16:41:38] [Iter  167/2250] R1[182/300]  | LR: 0.013389 | E: -38.430336 | E_var:     4.6633 | E_err:   0.033742
[2025-10-02 16:41:44] [Iter  168/2250] R1[184/300]  | LR: 0.013143 | E: -38.459030 | E_var:     4.5479 | E_err:   0.033322
[2025-10-02 16:41:49] [Iter  169/2250] R1[186/300]  | LR: 0.012898 | E: -38.518791 | E_var:     4.3691 | E_err:   0.032660
[2025-10-02 16:41:54] [Iter  170/2250] R1[188/300]  | LR: 0.012656 | E: -38.544627 | E_var:     4.6255 | E_err:   0.033605
[2025-10-02 16:41:59] [Iter  171/2250] R1[190/300]  | LR: 0.012416 | E: -38.556535 | E_var:     4.4320 | E_err:   0.032894
[2025-10-02 16:42:04] [Iter  172/2250] R1[192/300]  | LR: 0.012178 | E: -38.593784 | E_var:     4.3835 | E_err:   0.032714
[2025-10-02 16:42:09] [Iter  173/2250] R1[194/300]  | LR: 0.011942 | E: -38.601671 | E_var:     4.3124 | E_err:   0.032448
[2025-10-02 16:42:15] [Iter  174/2250] R1[196/300]  | LR: 0.011709 | E: -38.647016 | E_var:     4.1393 | E_err:   0.031790
[2025-10-02 16:42:20] [Iter  175/2250] R1[198/300]  | LR: 0.011478 | E: -38.662879 | E_var:     4.6099 | E_err:   0.033548
[2025-10-02 16:42:25] [Iter  176/2250] R1[200/300]  | LR: 0.011250 | E: -38.664944 | E_var:     4.2405 | E_err:   0.032176
[2025-10-02 16:42:30] [Iter  177/2250] R1[202/300]  | LR: 0.011025 | E: -38.701022 | E_var:     4.3138 | E_err:   0.032453
[2025-10-02 16:42:35] [Iter  178/2250] R1[204/300]  | LR: 0.010802 | E: -38.698578 | E_var:     4.3886 | E_err:   0.032733
[2025-10-02 16:42:40] [Iter  179/2250] R1[206/300]  | LR: 0.010583 | E: -38.745599 | E_var:     4.1055 | E_err:   0.031659
[2025-10-02 16:42:46] [Iter  180/2250] R1[208/300]  | LR: 0.010366 | E: -38.728364 | E_var:     5.1776 | E_err:   0.035554
[2025-10-02 16:42:51] [Iter  181/2250] R1[210/300]  | LR: 0.010153 | E: -38.765293 | E_var:     4.0913 | E_err:   0.031605
[2025-10-02 16:42:56] [Iter  182/2250] R1[212/300]  | LR: 0.009943 | E: -38.790979 | E_var:     4.0929 | E_err:   0.031611
[2025-10-02 16:43:01] [Iter  183/2250] R1[214/300]  | LR: 0.009736 | E: -38.826688 | E_var:     4.0072 | E_err:   0.031278
[2025-10-02 16:43:06] [Iter  184/2250] R1[216/300]  | LR: 0.009532 | E: -38.838792 | E_var:     3.8416 | E_err:   0.030625
[2025-10-02 16:43:11] [Iter  185/2250] R1[218/300]  | LR: 0.009332 | E: -38.891910 | E_var:     3.8748 | E_err:   0.030757
[2025-10-02 16:43:17] [Iter  186/2250] R1[220/300]  | LR: 0.009136 | E: -38.973789 | E_var:     4.0298 | E_err:   0.031366
[2025-10-02 16:43:22] [Iter  187/2250] R1[222/300]  | LR: 0.008943 | E: -38.939982 | E_var:     3.7029 | E_err:   0.030067
[2025-10-02 16:43:27] [Iter  188/2250] R1[224/300]  | LR: 0.008754 | E: -38.961050 | E_var:     3.9285 | E_err:   0.030969
[2025-10-02 16:43:32] [Iter  189/2250] R1[226/300]  | LR: 0.008569 | E: -38.963111 | E_var:     3.5767 | E_err:   0.029550
[2025-10-02 16:43:37] [Iter  190/2250] R1[228/300]  | LR: 0.008388 | E: -38.976479 | E_var:     3.6511 | E_err:   0.029856
[2025-10-02 16:43:42] [Iter  191/2250] R1[230/300]  | LR: 0.008211 | E: -39.023941 | E_var:     3.5853 | E_err:   0.029586
[2025-10-02 16:43:48] [Iter  192/2250] R1[232/300]  | LR: 0.008038 | E: -39.038117 | E_var:     3.6181 | E_err:   0.029721
[2025-10-02 16:43:53] [Iter  193/2250] R1[234/300]  | LR: 0.007869 | E: -39.036772 | E_var:     3.8499 | E_err:   0.030658
[2025-10-02 16:43:58] [Iter  194/2250] R1[236/300]  | LR: 0.007704 | E: -39.058268 | E_var:     3.7016 | E_err:   0.030062
[2025-10-02 16:44:03] [Iter  195/2250] R1[238/300]  | LR: 0.007543 | E: -39.067302 | E_var:     4.9887 | E_err:   0.034899
[2025-10-02 16:44:08] [Iter  196/2250] R1[240/300]  | LR: 0.007387 | E: -39.147437 | E_var:     3.1540 | E_err:   0.027749
[2025-10-02 16:44:13] [Iter  197/2250] R1[242/300]  | LR: 0.007236 | E: -39.168839 | E_var:     3.4964 | E_err:   0.029217
[2025-10-02 16:44:18] [Iter  198/2250] R1[244/300]  | LR: 0.007088 | E: -39.183896 | E_var:     3.4244 | E_err:   0.028914
[2025-10-02 16:44:24] [Iter  199/2250] R1[246/300]  | LR: 0.006946 | E: -39.209738 | E_var:     3.2739 | E_err:   0.028272
[2025-10-02 16:44:29] [Iter  200/2250] R1[248/300]  | LR: 0.006808 | E: -39.227013 | E_var:     3.8044 | E_err:   0.030476
[2025-10-02 16:44:29] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-02 16:44:34] [Iter  201/2250] R1[250/300]  | LR: 0.006675 | E: -39.254868 | E_var:     3.4075 | E_err:   0.028843
[2025-10-02 16:44:39] [Iter  202/2250] R1[252/300]  | LR: 0.006546 | E: -39.293526 | E_var:     3.6242 | E_err:   0.029746
[2025-10-02 16:44:44] [Iter  203/2250] R1[254/300]  | LR: 0.006422 | E: -39.289881 | E_var:     3.4592 | E_err:   0.029061
[2025-10-02 16:44:49] [Iter  204/2250] R1[256/300]  | LR: 0.006304 | E: -39.351193 | E_var:     3.4455 | E_err:   0.029003
[2025-10-02 16:44:55] [Iter  205/2250] R1[258/300]  | LR: 0.006190 | E: -39.346209 | E_var:     3.4903 | E_err:   0.029191
[2025-10-02 16:45:00] [Iter  206/2250] R1[260/300]  | LR: 0.006081 | E: -39.412192 | E_var:     3.1913 | E_err:   0.027913
[2025-10-02 16:45:05] [Iter  207/2250] R1[262/300]  | LR: 0.005977 | E: -39.412165 | E_var:     3.4459 | E_err:   0.029005
[2025-10-02 16:45:10] [Iter  208/2250] R1[264/300]  | LR: 0.005878 | E: -39.429623 | E_var:     3.2152 | E_err:   0.028017
[2025-10-02 16:45:15] [Iter  209/2250] R1[266/300]  | LR: 0.005784 | E: -39.467190 | E_var:     3.5745 | E_err:   0.029541
[2025-10-02 16:45:20] [Iter  210/2250] R1[268/300]  | LR: 0.005695 | E: -39.525568 | E_var:     3.5423 | E_err:   0.029408
[2025-10-02 16:45:26] [Iter  211/2250] R1[270/300]  | LR: 0.005612 | E: -39.538183 | E_var:     3.3722 | E_err:   0.028693
[2025-10-02 16:45:31] [Iter  212/2250] R1[272/300]  | LR: 0.005534 | E: -39.661188 | E_var:     3.4544 | E_err:   0.029040
[2025-10-02 16:45:36] [Iter  213/2250] R1[274/300]  | LR: 0.005460 | E: -39.649527 | E_var:     3.7672 | E_err:   0.030327
[2025-10-02 16:45:41] [Iter  214/2250] R1[276/300]  | LR: 0.005393 | E: -39.741579 | E_var:     3.6931 | E_err:   0.030027
[2025-10-02 16:45:46] [Iter  215/2250] R1[278/300]  | LR: 0.005330 | E: -39.780414 | E_var:     3.5456 | E_err:   0.029422
[2025-10-02 16:45:51] [Iter  216/2250] R1[280/300]  | LR: 0.005273 | E: -39.852481 | E_var:     3.5357 | E_err:   0.029380
[2025-10-02 16:45:57] [Iter  217/2250] R1[282/300]  | LR: 0.005221 | E: -39.911176 | E_var:     3.5071 | E_err:   0.029261
[2025-10-02 16:46:02] [Iter  218/2250] R1[284/300]  | LR: 0.005175 | E: -40.017870 | E_var:     3.9467 | E_err:   0.031041
[2025-10-02 16:46:07] [Iter  219/2250] R1[286/300]  | LR: 0.005134 | E: -40.080232 | E_var:     3.7099 | E_err:   0.030095
[2025-10-02 16:46:12] [Iter  220/2250] R1[288/300]  | LR: 0.005099 | E: -40.195477 | E_var:     3.4338 | E_err:   0.028954
[2025-10-02 16:46:17] [Iter  221/2250] R1[290/300]  | LR: 0.005068 | E: -40.308143 | E_var:     4.0667 | E_err:   0.031509
[2025-10-02 16:46:22] [Iter  222/2250] R1[292/300]  | LR: 0.005044 | E: -40.441961 | E_var:     3.5837 | E_err:   0.029579
[2025-10-02 16:46:27] [Iter  223/2250] R1[294/300]  | LR: 0.005025 | E: -40.580609 | E_var:     3.7643 | E_err:   0.030315
[2025-10-02 16:46:33] [Iter  224/2250] R1[296/300]  | LR: 0.005011 | E: -40.694648 | E_var:     3.4961 | E_err:   0.029215
[2025-10-02 16:46:38] [Iter  225/2250] R1[298/300]  | LR: 0.005003 | E: -40.796238 | E_var:     3.6060 | E_err:   0.029671
[2025-10-02 16:46:38] 🔄 RESTART #2 | Period: 600
[2025-10-02 16:46:43] [Iter  226/2250] R2[0/600]    | LR: 0.030000 | E: -41.003797 | E_var:     3.4771 | E_err:   0.029136
[2025-10-02 16:46:48] [Iter  227/2250] R2[2/600]    | LR: 0.029999 | E: -41.090243 | E_var:     4.5877 | E_err:   0.033467
[2025-10-02 16:46:53] [Iter  228/2250] R2[4/600]    | LR: 0.029997 | E: -41.219233 | E_var:     4.3491 | E_err:   0.032585
[2025-10-02 16:46:58] [Iter  229/2250] R2[6/600]    | LR: 0.029994 | E: -41.326970 | E_var:     3.8113 | E_err:   0.030504
[2025-10-02 16:47:04] [Iter  230/2250] R2[8/600]    | LR: 0.029989 | E: -41.462481 | E_var:     2.5066 | E_err:   0.024738
[2025-10-02 16:47:09] [Iter  231/2250] R2[10/600]   | LR: 0.029983 | E: -41.551195 | E_var:     2.1266 | E_err:   0.022785
[2025-10-02 16:47:14] [Iter  232/2250] R2[12/600]   | LR: 0.029975 | E: -41.664468 | E_var:     2.2295 | E_err:   0.023330
[2025-10-02 16:47:19] [Iter  233/2250] R2[14/600]   | LR: 0.029966 | E: -41.684029 | E_var:     1.8829 | E_err:   0.021440
[2025-10-02 16:47:24] [Iter  234/2250] R2[16/600]   | LR: 0.029956 | E: -41.700921 | E_var:     3.5088 | E_err:   0.029268
[2025-10-02 16:47:29] [Iter  235/2250] R2[18/600]   | LR: 0.029945 | E: -41.757945 | E_var:     1.7724 | E_err:   0.020802
[2025-10-02 16:47:34] [Iter  236/2250] R2[20/600]   | LR: 0.029932 | E: -41.831243 | E_var:     1.6085 | E_err:   0.019817
[2025-10-02 16:47:40] [Iter  237/2250] R2[22/600]   | LR: 0.029917 | E: -41.883622 | E_var:     1.3814 | E_err:   0.018365
[2025-10-02 16:47:45] [Iter  238/2250] R2[24/600]   | LR: 0.029901 | E: -41.887671 | E_var:     1.3615 | E_err:   0.018232
[2025-10-02 16:47:50] [Iter  239/2250] R2[26/600]   | LR: 0.029884 | E: -41.914983 | E_var:     1.2689 | E_err:   0.017601
[2025-10-02 16:47:55] [Iter  240/2250] R2[28/600]   | LR: 0.029866 | E: -41.908834 | E_var:     1.4850 | E_err:   0.019040
[2025-10-02 16:48:00] [Iter  241/2250] R2[30/600]   | LR: 0.029846 | E: -41.960371 | E_var:     1.2748 | E_err:   0.017642
[2025-10-02 16:48:05] [Iter  242/2250] R2[32/600]   | LR: 0.029825 | E: -41.896753 | E_var:     1.3726 | E_err:   0.018306
[2025-10-02 16:48:10] [Iter  243/2250] R2[34/600]   | LR: 0.029802 | E: -41.972675 | E_var:     1.0440 | E_err:   0.015965
[2025-10-02 16:48:16] [Iter  244/2250] R2[36/600]   | LR: 0.029779 | E: -41.968179 | E_var:     1.1282 | E_err:   0.016596
[2025-10-02 16:48:21] [Iter  245/2250] R2[38/600]   | LR: 0.029753 | E: -41.991625 | E_var:     1.0072 | E_err:   0.015681
[2025-10-02 16:48:26] [Iter  246/2250] R2[40/600]   | LR: 0.029727 | E: -42.001077 | E_var:     1.1864 | E_err:   0.017019
[2025-10-02 16:48:31] [Iter  247/2250] R2[42/600]   | LR: 0.029699 | E: -41.976291 | E_var:     3.1125 | E_err:   0.027566
[2025-10-02 16:48:36] [Iter  248/2250] R2[44/600]   | LR: 0.029670 | E: -42.003121 | E_var:     1.1527 | E_err:   0.016776
[2025-10-02 16:48:41] [Iter  249/2250] R2[46/600]   | LR: 0.029639 | E: -42.012055 | E_var:     1.0296 | E_err:   0.015855
[2025-10-02 16:48:47] [Iter  250/2250] R2[48/600]   | LR: 0.029607 | E: -42.019983 | E_var:     0.9851 | E_err:   0.015508
[2025-10-02 16:48:52] [Iter  251/2250] R2[50/600]   | LR: 0.029574 | E: -42.005924 | E_var:     0.9160 | E_err:   0.014954
[2025-10-02 16:48:57] [Iter  252/2250] R2[52/600]   | LR: 0.029540 | E: -42.036701 | E_var:     1.0702 | E_err:   0.016164
[2025-10-02 16:49:02] [Iter  253/2250] R2[54/600]   | LR: 0.029504 | E: -42.029431 | E_var:     1.1572 | E_err:   0.016808
[2025-10-02 16:49:07] [Iter  254/2250] R2[56/600]   | LR: 0.029466 | E: -42.017981 | E_var:     0.9471 | E_err:   0.015206
[2025-10-02 16:49:12] [Iter  255/2250] R2[58/600]   | LR: 0.029428 | E: -42.025714 | E_var:     0.9484 | E_err:   0.015217
[2025-10-02 16:49:17] [Iter  256/2250] R2[60/600]   | LR: 0.029388 | E: -42.023862 | E_var:     1.3497 | E_err:   0.018153
[2025-10-02 16:49:23] [Iter  257/2250] R2[62/600]   | LR: 0.029347 | E: -42.033391 | E_var:     0.9608 | E_err:   0.015316
[2025-10-02 16:49:28] [Iter  258/2250] R2[64/600]   | LR: 0.029305 | E: -42.047569 | E_var:     0.8997 | E_err:   0.014820
[2025-10-02 16:49:33] [Iter  259/2250] R2[66/600]   | LR: 0.029261 | E: -42.037074 | E_var:     0.8963 | E_err:   0.014792
[2025-10-02 16:49:38] [Iter  260/2250] R2[68/600]   | LR: 0.029216 | E: -42.036290 | E_var:     0.9225 | E_err:   0.015008
[2025-10-02 16:49:43] [Iter  261/2250] R2[70/600]   | LR: 0.029170 | E: -42.074513 | E_var:     0.8369 | E_err:   0.014294
[2025-10-02 16:49:48] [Iter  262/2250] R2[72/600]   | LR: 0.029122 | E: -42.059634 | E_var:     0.9130 | E_err:   0.014930
[2025-10-02 16:49:53] [Iter  263/2250] R2[74/600]   | LR: 0.029073 | E: -42.065934 | E_var:     0.8526 | E_err:   0.014428
[2025-10-02 16:49:59] [Iter  264/2250] R2[76/600]   | LR: 0.029023 | E: -42.037961 | E_var:     1.0231 | E_err:   0.015805
[2025-10-02 16:50:04] [Iter  265/2250] R2[78/600]   | LR: 0.028972 | E: -42.051885 | E_var:     0.8194 | E_err:   0.014144
[2025-10-02 16:50:09] [Iter  266/2250] R2[80/600]   | LR: 0.028919 | E: -42.059476 | E_var:     0.7941 | E_err:   0.013924
[2025-10-02 16:50:14] [Iter  267/2250] R2[82/600]   | LR: 0.028865 | E: -42.072542 | E_var:     1.0724 | E_err:   0.016181
[2025-10-02 16:50:19] [Iter  268/2250] R2[84/600]   | LR: 0.028810 | E: -42.053217 | E_var:     0.8438 | E_err:   0.014353
[2025-10-02 16:50:24] [Iter  269/2250] R2[86/600]   | LR: 0.028754 | E: -42.046231 | E_var:     2.3369 | E_err:   0.023886
[2025-10-02 16:50:29] [Iter  270/2250] R2[88/600]   | LR: 0.028696 | E: -42.064293 | E_var:     0.8727 | E_err:   0.014596
[2025-10-02 16:50:35] [Iter  271/2250] R2[90/600]   | LR: 0.028638 | E: -42.067596 | E_var:     0.9962 | E_err:   0.015595
[2025-10-02 16:50:40] [Iter  272/2250] R2[92/600]   | LR: 0.028578 | E: -42.087008 | E_var:     0.8309 | E_err:   0.014242
[2025-10-02 16:50:45] [Iter  273/2250] R2[94/600]   | LR: 0.028516 | E: -42.075794 | E_var:     0.8645 | E_err:   0.014528
[2025-10-02 16:50:50] [Iter  274/2250] R2[96/600]   | LR: 0.028454 | E: -42.071449 | E_var:     0.8334 | E_err:   0.014264
[2025-10-02 16:50:55] [Iter  275/2250] R2[98/600]   | LR: 0.028390 | E: -42.098272 | E_var:     0.7568 | E_err:   0.013593
[2025-10-02 16:51:00] [Iter  276/2250] R2[100/600]  | LR: 0.028325 | E: -42.074356 | E_var:     0.8288 | E_err:   0.014225
[2025-10-02 16:51:06] [Iter  277/2250] R2[102/600]  | LR: 0.028259 | E: -42.059160 | E_var:     2.1016 | E_err:   0.022651
[2025-10-02 16:51:11] [Iter  278/2250] R2[104/600]  | LR: 0.028192 | E: -42.081301 | E_var:     0.9469 | E_err:   0.015204
[2025-10-02 16:51:16] [Iter  279/2250] R2[106/600]  | LR: 0.028124 | E: -42.113456 | E_var:     0.7039 | E_err:   0.013109
[2025-10-02 16:51:21] [Iter  280/2250] R2[108/600]  | LR: 0.028054 | E: -42.091134 | E_var:     0.6813 | E_err:   0.012897
[2025-10-02 16:51:26] [Iter  281/2250] R2[110/600]  | LR: 0.027983 | E: -42.092428 | E_var:     0.7621 | E_err:   0.013640
[2025-10-02 16:51:31] [Iter  282/2250] R2[112/600]  | LR: 0.027912 | E: -42.109402 | E_var:     3.2151 | E_err:   0.028017
[2025-10-02 16:51:36] [Iter  283/2250] R2[114/600]  | LR: 0.027839 | E: -42.099155 | E_var:     0.8123 | E_err:   0.014082
[2025-10-02 16:51:42] [Iter  284/2250] R2[116/600]  | LR: 0.027764 | E: -42.109511 | E_var:     0.7238 | E_err:   0.013293
[2025-10-02 16:51:47] [Iter  285/2250] R2[118/600]  | LR: 0.027689 | E: -42.084116 | E_var:     0.9189 | E_err:   0.014978
[2025-10-02 16:51:52] [Iter  286/2250] R2[120/600]  | LR: 0.027613 | E: -42.105100 | E_var:     0.7472 | E_err:   0.013506
[2025-10-02 16:51:57] [Iter  287/2250] R2[122/600]  | LR: 0.027535 | E: -42.112386 | E_var:     0.8548 | E_err:   0.014446
[2025-10-02 16:52:02] [Iter  288/2250] R2[124/600]  | LR: 0.027457 | E: -42.098049 | E_var:     0.8052 | E_err:   0.014021
[2025-10-02 16:52:07] [Iter  289/2250] R2[126/600]  | LR: 0.027377 | E: -42.095020 | E_var:     0.6994 | E_err:   0.013067
[2025-10-02 16:52:12] [Iter  290/2250] R2[128/600]  | LR: 0.027296 | E: -42.087575 | E_var:     0.6999 | E_err:   0.013072
[2025-10-02 16:52:18] [Iter  291/2250] R2[130/600]  | LR: 0.027214 | E: -42.090543 | E_var:     0.7444 | E_err:   0.013481
[2025-10-02 16:52:23] [Iter  292/2250] R2[132/600]  | LR: 0.027131 | E: -42.135525 | E_var:     0.7158 | E_err:   0.013220
[2025-10-02 16:52:28] [Iter  293/2250] R2[134/600]  | LR: 0.027047 | E: -42.102388 | E_var:     0.7792 | E_err:   0.013793
[2025-10-02 16:52:33] [Iter  294/2250] R2[136/600]  | LR: 0.026962 | E: -42.091276 | E_var:     0.6975 | E_err:   0.013049
[2025-10-02 16:52:38] [Iter  295/2250] R2[138/600]  | LR: 0.026876 | E: -42.105365 | E_var:     0.8781 | E_err:   0.014642
[2025-10-02 16:52:43] [Iter  296/2250] R2[140/600]  | LR: 0.026789 | E: -42.117656 | E_var:     0.7319 | E_err:   0.013367
[2025-10-02 16:52:48] [Iter  297/2250] R2[142/600]  | LR: 0.026701 | E: -42.121519 | E_var:     0.7582 | E_err:   0.013605
[2025-10-02 16:52:54] [Iter  298/2250] R2[144/600]  | LR: 0.026612 | E: -42.124320 | E_var:     0.8728 | E_err:   0.014597
[2025-10-02 16:52:59] [Iter  299/2250] R2[146/600]  | LR: 0.026522 | E: -42.095319 | E_var:     0.8280 | E_err:   0.014218
[2025-10-02 16:53:04] [Iter  300/2250] R2[148/600]  | LR: 0.026431 | E: -42.099017 | E_var:     0.9888 | E_err:   0.015537
[2025-10-02 16:53:09] [Iter  301/2250] R2[150/600]  | LR: 0.026339 | E: -42.101550 | E_var:     2.5173 | E_err:   0.024791
[2025-10-02 16:53:14] [Iter  302/2250] R2[152/600]  | LR: 0.026246 | E: -42.095748 | E_var:     0.9440 | E_err:   0.015181
[2025-10-02 16:53:19] [Iter  303/2250] R2[154/600]  | LR: 0.026152 | E: -42.117671 | E_var:     0.7549 | E_err:   0.013576
[2025-10-02 16:53:24] [Iter  304/2250] R2[156/600]  | LR: 0.026057 | E: -42.124289 | E_var:     0.7811 | E_err:   0.013810
[2025-10-02 16:53:30] [Iter  305/2250] R2[158/600]  | LR: 0.025961 | E: -42.094448 | E_var:     0.8379 | E_err:   0.014303
[2025-10-02 16:53:35] [Iter  306/2250] R2[160/600]  | LR: 0.025864 | E: -42.103996 | E_var:     0.7162 | E_err:   0.013224
[2025-10-02 16:53:40] [Iter  307/2250] R2[162/600]  | LR: 0.025766 | E: -42.132633 | E_var:     0.6810 | E_err:   0.012894
[2025-10-02 16:53:45] [Iter  308/2250] R2[164/600]  | LR: 0.025668 | E: -42.128390 | E_var:     0.8912 | E_err:   0.014750
[2025-10-02 16:53:50] [Iter  309/2250] R2[166/600]  | LR: 0.025568 | E: -42.127953 | E_var:     0.6424 | E_err:   0.012524
[2025-10-02 16:53:55] [Iter  310/2250] R2[168/600]  | LR: 0.025468 | E: -42.131375 | E_var:     1.0071 | E_err:   0.015680
[2025-10-02 16:54:00] [Iter  311/2250] R2[170/600]  | LR: 0.025367 | E: -42.141206 | E_var:     0.6639 | E_err:   0.012732
[2025-10-02 16:54:06] [Iter  312/2250] R2[172/600]  | LR: 0.025264 | E: -42.113563 | E_var:     0.6474 | E_err:   0.012572
[2025-10-02 16:54:11] [Iter  313/2250] R2[174/600]  | LR: 0.025161 | E: -42.151168 | E_var:     0.8599 | E_err:   0.014489
[2025-10-02 16:54:16] [Iter  314/2250] R2[176/600]  | LR: 0.025057 | E: -42.133520 | E_var:     1.8223 | E_err:   0.021093
[2025-10-02 16:54:21] [Iter  315/2250] R2[178/600]  | LR: 0.024953 | E: -42.134154 | E_var:     0.7757 | E_err:   0.013761
[2025-10-02 16:54:26] [Iter  316/2250] R2[180/600]  | LR: 0.024847 | E: -42.123082 | E_var:     0.7966 | E_err:   0.013946
[2025-10-02 16:54:31] [Iter  317/2250] R2[182/600]  | LR: 0.024741 | E: -42.127661 | E_var:     0.7488 | E_err:   0.013521
[2025-10-02 16:54:37] [Iter  318/2250] R2[184/600]  | LR: 0.024634 | E: -42.130648 | E_var:     0.6861 | E_err:   0.012942
[2025-10-02 16:54:42] [Iter  319/2250] R2[186/600]  | LR: 0.024526 | E: -42.123482 | E_var:     0.7646 | E_err:   0.013663
[2025-10-02 16:54:47] [Iter  320/2250] R2[188/600]  | LR: 0.024417 | E: -42.147182 | E_var:     0.7366 | E_err:   0.013410
[2025-10-02 16:54:52] [Iter  321/2250] R2[190/600]  | LR: 0.024308 | E: -42.136525 | E_var:     0.7134 | E_err:   0.013197
[2025-10-02 16:54:57] [Iter  322/2250] R2[192/600]  | LR: 0.024198 | E: -42.118039 | E_var:     0.6854 | E_err:   0.012936
[2025-10-02 16:55:02] [Iter  323/2250] R2[194/600]  | LR: 0.024087 | E: -42.127951 | E_var:     0.6557 | E_err:   0.012652
[2025-10-02 16:55:07] [Iter  324/2250] R2[196/600]  | LR: 0.023975 | E: -42.132485 | E_var:     0.9516 | E_err:   0.015242
[2025-10-02 16:55:13] [Iter  325/2250] R2[198/600]  | LR: 0.023863 | E: -42.127146 | E_var:     0.7081 | E_err:   0.013148
[2025-10-02 16:55:18] [Iter  326/2250] R2[200/600]  | LR: 0.023750 | E: -42.130528 | E_var:     0.7188 | E_err:   0.013247
[2025-10-02 16:55:23] [Iter  327/2250] R2[202/600]  | LR: 0.023636 | E: -42.116564 | E_var:     0.7515 | E_err:   0.013545
[2025-10-02 16:55:28] [Iter  328/2250] R2[204/600]  | LR: 0.023522 | E: -42.148390 | E_var:     0.7020 | E_err:   0.013092
[2025-10-02 16:55:33] [Iter  329/2250] R2[206/600]  | LR: 0.023407 | E: -42.140061 | E_var:     0.7935 | E_err:   0.013918
[2025-10-02 16:55:38] [Iter  330/2250] R2[208/600]  | LR: 0.023291 | E: -42.148860 | E_var:     0.7044 | E_err:   0.013113
[2025-10-02 16:55:43] [Iter  331/2250] R2[210/600]  | LR: 0.023175 | E: -42.136004 | E_var:     0.7659 | E_err:   0.013674
[2025-10-02 16:55:49] [Iter  332/2250] R2[212/600]  | LR: 0.023058 | E: -42.167904 | E_var:     0.7342 | E_err:   0.013388
[2025-10-02 16:55:54] [Iter  333/2250] R2[214/600]  | LR: 0.022940 | E: -42.156072 | E_var:     0.6884 | E_err:   0.012964
[2025-10-02 16:55:59] [Iter  334/2250] R2[216/600]  | LR: 0.022822 | E: -42.134877 | E_var:     0.8033 | E_err:   0.014004
[2025-10-02 16:56:04] [Iter  335/2250] R2[218/600]  | LR: 0.022704 | E: -42.144838 | E_var:     0.7015 | E_err:   0.013087
[2025-10-02 16:56:09] [Iter  336/2250] R2[220/600]  | LR: 0.022584 | E: -42.131102 | E_var:     1.1632 | E_err:   0.016852
[2025-10-02 16:56:14] [Iter  337/2250] R2[222/600]  | LR: 0.022464 | E: -42.134258 | E_var:     2.3601 | E_err:   0.024004
[2025-10-02 16:56:19] [Iter  338/2250] R2[224/600]  | LR: 0.022344 | E: -42.131095 | E_var:     0.7405 | E_err:   0.013446
[2025-10-02 16:56:25] [Iter  339/2250] R2[226/600]  | LR: 0.022223 | E: -42.164182 | E_var:     0.5811 | E_err:   0.011911
[2025-10-02 16:56:30] [Iter  340/2250] R2[228/600]  | LR: 0.022102 | E: -42.147983 | E_var:     0.7729 | E_err:   0.013736
[2025-10-02 16:56:35] [Iter  341/2250] R2[230/600]  | LR: 0.021980 | E: -42.142778 | E_var:     0.6863 | E_err:   0.012944
[2025-10-02 16:56:40] [Iter  342/2250] R2[232/600]  | LR: 0.021857 | E: -42.163702 | E_var:     0.6131 | E_err:   0.012235
[2025-10-02 16:56:45] [Iter  343/2250] R2[234/600]  | LR: 0.021734 | E: -42.167571 | E_var:     0.6895 | E_err:   0.012974
[2025-10-02 16:56:50] [Iter  344/2250] R2[236/600]  | LR: 0.021611 | E: -42.132968 | E_var:     0.6454 | E_err:   0.012553
[2025-10-02 16:56:55] [Iter  345/2250] R2[238/600]  | LR: 0.021487 | E: -42.167261 | E_var:     1.0350 | E_err:   0.015896
[2025-10-02 16:57:01] [Iter  346/2250] R2[240/600]  | LR: 0.021363 | E: -42.167579 | E_var:     0.7156 | E_err:   0.013218
[2025-10-02 16:57:06] [Iter  347/2250] R2[242/600]  | LR: 0.021238 | E: -42.178128 | E_var:     0.6847 | E_err:   0.012929
[2025-10-02 16:57:11] [Iter  348/2250] R2[244/600]  | LR: 0.021113 | E: -42.149529 | E_var:     0.8602 | E_err:   0.014492
[2025-10-02 16:57:16] [Iter  349/2250] R2[246/600]  | LR: 0.020987 | E: -42.150532 | E_var:     1.7949 | E_err:   0.020933
[2025-10-02 16:57:21] [Iter  350/2250] R2[248/600]  | LR: 0.020861 | E: -42.123972 | E_var:     2.3155 | E_err:   0.023776
[2025-10-02 16:57:26] [Iter  351/2250] R2[250/600]  | LR: 0.020735 | E: -42.161792 | E_var:     0.7701 | E_err:   0.013712
[2025-10-02 16:57:32] [Iter  352/2250] R2[252/600]  | LR: 0.020609 | E: -42.137101 | E_var:     0.7420 | E_err:   0.013459
[2025-10-02 16:57:37] [Iter  353/2250] R2[254/600]  | LR: 0.020482 | E: -42.159572 | E_var:     0.6094 | E_err:   0.012197
[2025-10-02 16:57:42] [Iter  354/2250] R2[256/600]  | LR: 0.020354 | E: -42.148441 | E_var:     0.7010 | E_err:   0.013082
[2025-10-02 16:57:47] [Iter  355/2250] R2[258/600]  | LR: 0.020227 | E: -42.159285 | E_var:     0.5712 | E_err:   0.011809
[2025-10-02 16:57:52] [Iter  356/2250] R2[260/600]  | LR: 0.020099 | E: -42.149113 | E_var:     0.7081 | E_err:   0.013149
[2025-10-02 16:57:57] [Iter  357/2250] R2[262/600]  | LR: 0.019971 | E: -42.162273 | E_var:     0.8053 | E_err:   0.014022
[2025-10-02 16:58:02] [Iter  358/2250] R2[264/600]  | LR: 0.019842 | E: -42.155892 | E_var:     0.6903 | E_err:   0.012981
[2025-10-02 16:58:08] [Iter  359/2250] R2[266/600]  | LR: 0.019714 | E: -42.167744 | E_var:     0.5541 | E_err:   0.011630
[2025-10-02 16:58:13] [Iter  360/2250] R2[268/600]  | LR: 0.019585 | E: -42.173819 | E_var:     0.5275 | E_err:   0.011348
[2025-10-02 16:58:18] [Iter  361/2250] R2[270/600]  | LR: 0.019455 | E: -42.165612 | E_var:     0.6293 | E_err:   0.012395
[2025-10-02 16:58:23] [Iter  362/2250] R2[272/600]  | LR: 0.019326 | E: -42.162499 | E_var:     0.7179 | E_err:   0.013239
[2025-10-02 16:58:28] [Iter  363/2250] R2[274/600]  | LR: 0.019196 | E: -42.154388 | E_var:     0.6767 | E_err:   0.012854
[2025-10-02 16:58:33] [Iter  364/2250] R2[276/600]  | LR: 0.019067 | E: -42.159942 | E_var:     0.7002 | E_err:   0.013074
[2025-10-02 16:58:39] [Iter  365/2250] R2[278/600]  | LR: 0.018937 | E: -42.171449 | E_var:     0.6072 | E_err:   0.012176
[2025-10-02 16:58:44] [Iter  366/2250] R2[280/600]  | LR: 0.018807 | E: -42.186964 | E_var:     0.7493 | E_err:   0.013525
[2025-10-02 16:58:49] [Iter  367/2250] R2[282/600]  | LR: 0.018676 | E: -42.177682 | E_var:     0.7264 | E_err:   0.013317
[2025-10-02 16:58:54] [Iter  368/2250] R2[284/600]  | LR: 0.018546 | E: -42.175076 | E_var:     0.6965 | E_err:   0.013040
[2025-10-02 16:58:59] [Iter  369/2250] R2[286/600]  | LR: 0.018415 | E: -42.161163 | E_var:     0.6542 | E_err:   0.012638
[2025-10-02 16:59:04] [Iter  370/2250] R2[288/600]  | LR: 0.018285 | E: -42.180521 | E_var:     0.9089 | E_err:   0.014896
[2025-10-02 16:59:09] [Iter  371/2250] R2[290/600]  | LR: 0.018154 | E: -42.174429 | E_var:     0.6531 | E_err:   0.012628
[2025-10-02 16:59:15] [Iter  372/2250] R2[292/600]  | LR: 0.018023 | E: -42.184829 | E_var:     0.5835 | E_err:   0.011935
[2025-10-02 16:59:20] [Iter  373/2250] R2[294/600]  | LR: 0.017893 | E: -42.201344 | E_var:     0.7056 | E_err:   0.013125
[2025-10-02 16:59:25] [Iter  374/2250] R2[296/600]  | LR: 0.017762 | E: -42.165258 | E_var:     0.6398 | E_err:   0.012498
[2025-10-02 16:59:30] [Iter  375/2250] R2[298/600]  | LR: 0.017631 | E: -42.178852 | E_var:     0.7094 | E_err:   0.013161
[2025-10-02 16:59:35] [Iter  376/2250] R2[300/600]  | LR: 0.017500 | E: -42.170425 | E_var:     0.6122 | E_err:   0.012225
[2025-10-02 16:59:40] [Iter  377/2250] R2[302/600]  | LR: 0.017369 | E: -42.178641 | E_var:     0.5863 | E_err:   0.011964
[2025-10-02 16:59:45] [Iter  378/2250] R2[304/600]  | LR: 0.017238 | E: -42.180434 | E_var:     0.9988 | E_err:   0.015615
[2025-10-02 16:59:51] [Iter  379/2250] R2[306/600]  | LR: 0.017107 | E: -42.184176 | E_var:     0.6109 | E_err:   0.012213
[2025-10-02 16:59:56] [Iter  380/2250] R2[308/600]  | LR: 0.016977 | E: -42.170220 | E_var:     0.6286 | E_err:   0.012388
[2025-10-02 17:00:01] [Iter  381/2250] R2[310/600]  | LR: 0.016846 | E: -42.193542 | E_var:     0.5976 | E_err:   0.012078
[2025-10-02 17:00:06] [Iter  382/2250] R2[312/600]  | LR: 0.016715 | E: -42.159437 | E_var:     0.6378 | E_err:   0.012479
[2025-10-02 17:00:11] [Iter  383/2250] R2[314/600]  | LR: 0.016585 | E: -42.180658 | E_var:     0.7109 | E_err:   0.013174
[2025-10-02 17:00:16] [Iter  384/2250] R2[316/600]  | LR: 0.016454 | E: -42.188517 | E_var:     0.6455 | E_err:   0.012554
[2025-10-02 17:00:21] [Iter  385/2250] R2[318/600]  | LR: 0.016324 | E: -42.158204 | E_var:     0.7151 | E_err:   0.013213
[2025-10-02 17:00:27] [Iter  386/2250] R2[320/600]  | LR: 0.016193 | E: -42.176690 | E_var:     0.6563 | E_err:   0.012658
[2025-10-02 17:00:32] [Iter  387/2250] R2[322/600]  | LR: 0.016063 | E: -42.173159 | E_var:     0.7806 | E_err:   0.013805
[2025-10-02 17:00:37] [Iter  388/2250] R2[324/600]  | LR: 0.015933 | E: -42.184671 | E_var:     0.8148 | E_err:   0.014104
[2025-10-02 17:00:42] [Iter  389/2250] R2[326/600]  | LR: 0.015804 | E: -42.192452 | E_var:     0.5760 | E_err:   0.011858
[2025-10-02 17:00:47] [Iter  390/2250] R2[328/600]  | LR: 0.015674 | E: -42.188727 | E_var:     0.7272 | E_err:   0.013324
[2025-10-02 17:00:52] [Iter  391/2250] R2[330/600]  | LR: 0.015545 | E: -42.196915 | E_var:     0.6401 | E_err:   0.012501
[2025-10-02 17:00:57] [Iter  392/2250] R2[332/600]  | LR: 0.015415 | E: -42.179086 | E_var:     0.6428 | E_err:   0.012527
[2025-10-02 17:01:03] [Iter  393/2250] R2[334/600]  | LR: 0.015286 | E: -42.189036 | E_var:     0.5738 | E_err:   0.011836
[2025-10-02 17:01:08] [Iter  394/2250] R2[336/600]  | LR: 0.015158 | E: -42.178480 | E_var:     0.5433 | E_err:   0.011517
[2025-10-02 17:01:13] [Iter  395/2250] R2[338/600]  | LR: 0.015029 | E: -42.191051 | E_var:     0.6328 | E_err:   0.012430
[2025-10-02 17:01:18] [Iter  396/2250] R2[340/600]  | LR: 0.014901 | E: -42.193727 | E_var:     0.5585 | E_err:   0.011677
[2025-10-02 17:01:23] [Iter  397/2250] R2[342/600]  | LR: 0.014773 | E: -42.178277 | E_var:     0.6330 | E_err:   0.012432
[2025-10-02 17:01:28] [Iter  398/2250] R2[344/600]  | LR: 0.014646 | E: -42.180637 | E_var:     0.6051 | E_err:   0.012155
[2025-10-02 17:01:33] [Iter  399/2250] R2[346/600]  | LR: 0.014518 | E: -42.173878 | E_var:     0.6680 | E_err:   0.012770
[2025-10-02 17:01:39] [Iter  400/2250] R2[348/600]  | LR: 0.014391 | E: -42.173842 | E_var:     0.5996 | E_err:   0.012099
[2025-10-02 17:01:39] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-02 17:01:44] [Iter  401/2250] R2[350/600]  | LR: 0.014265 | E: -42.166646 | E_var:     0.6137 | E_err:   0.012240
[2025-10-02 17:01:49] [Iter  402/2250] R2[352/600]  | LR: 0.014139 | E: -42.171988 | E_var:     0.6029 | E_err:   0.012132
[2025-10-02 17:01:54] [Iter  403/2250] R2[354/600]  | LR: 0.014013 | E: -42.189171 | E_var:     0.5873 | E_err:   0.011974
[2025-10-02 17:01:59] [Iter  404/2250] R2[356/600]  | LR: 0.013887 | E: -42.180898 | E_var:     1.8142 | E_err:   0.021046
[2025-10-02 17:02:05] [Iter  405/2250] R2[358/600]  | LR: 0.013762 | E: -42.200767 | E_var:     0.4765 | E_err:   0.010786
[2025-10-02 17:02:10] [Iter  406/2250] R2[360/600]  | LR: 0.013637 | E: -42.180059 | E_var:     0.5459 | E_err:   0.011545
[2025-10-02 17:02:15] [Iter  407/2250] R2[362/600]  | LR: 0.013513 | E: -42.173772 | E_var:     0.8657 | E_err:   0.014538
[2025-10-02 17:02:20] [Iter  408/2250] R2[364/600]  | LR: 0.013389 | E: -42.195876 | E_var:     0.5713 | E_err:   0.011810
[2025-10-02 17:02:25] [Iter  409/2250] R2[366/600]  | LR: 0.013266 | E: -42.173512 | E_var:     0.6516 | E_err:   0.012613
[2025-10-02 17:02:30] [Iter  410/2250] R2[368/600]  | LR: 0.013143 | E: -42.195353 | E_var:     0.5803 | E_err:   0.011903
[2025-10-02 17:02:35] [Iter  411/2250] R2[370/600]  | LR: 0.013020 | E: -42.191753 | E_var:     0.7038 | E_err:   0.013108
[2025-10-02 17:02:41] [Iter  412/2250] R2[372/600]  | LR: 0.012898 | E: -42.191810 | E_var:     0.5793 | E_err:   0.011893
[2025-10-02 17:02:46] [Iter  413/2250] R2[374/600]  | LR: 0.012777 | E: -42.183898 | E_var:     0.8996 | E_err:   0.014820
[2025-10-02 17:02:51] [Iter  414/2250] R2[376/600]  | LR: 0.012656 | E: -42.209600 | E_var:     0.6101 | E_err:   0.012205
[2025-10-02 17:02:56] [Iter  415/2250] R2[378/600]  | LR: 0.012536 | E: -42.207601 | E_var:     0.7147 | E_err:   0.013209
[2025-10-02 17:03:01] [Iter  416/2250] R2[380/600]  | LR: 0.012416 | E: -42.172929 | E_var:     0.7549 | E_err:   0.013576
[2025-10-02 17:03:06] [Iter  417/2250] R2[382/600]  | LR: 0.012296 | E: -42.188736 | E_var:     0.7209 | E_err:   0.013266
[2025-10-02 17:03:11] [Iter  418/2250] R2[384/600]  | LR: 0.012178 | E: -42.189601 | E_var:     0.7288 | E_err:   0.013339
[2025-10-02 17:03:17] [Iter  419/2250] R2[386/600]  | LR: 0.012060 | E: -42.198997 | E_var:     0.5586 | E_err:   0.011678
[2025-10-02 17:03:22] [Iter  420/2250] R2[388/600]  | LR: 0.011942 | E: -42.198313 | E_var:     0.5819 | E_err:   0.011919
[2025-10-02 17:03:27] [Iter  421/2250] R2[390/600]  | LR: 0.011825 | E: -42.193640 | E_var:     0.6796 | E_err:   0.012881
[2025-10-02 17:03:32] [Iter  422/2250] R2[392/600]  | LR: 0.011709 | E: -42.206979 | E_var:     0.4770 | E_err:   0.010792
[2025-10-02 17:03:37] [Iter  423/2250] R2[394/600]  | LR: 0.011593 | E: -42.194937 | E_var:     0.5725 | E_err:   0.011822
[2025-10-02 17:03:42] [Iter  424/2250] R2[396/600]  | LR: 0.011478 | E: -42.197982 | E_var:     0.6881 | E_err:   0.012961
[2025-10-02 17:03:47] [Iter  425/2250] R2[398/600]  | LR: 0.011364 | E: -42.203241 | E_var:     0.6185 | E_err:   0.012289
[2025-10-02 17:03:53] [Iter  426/2250] R2[400/600]  | LR: 0.011250 | E: -42.209368 | E_var:     0.7347 | E_err:   0.013393
[2025-10-02 17:03:58] [Iter  427/2250] R2[402/600]  | LR: 0.011137 | E: -42.196507 | E_var:     0.5725 | E_err:   0.011822
[2025-10-02 17:04:03] [Iter  428/2250] R2[404/600]  | LR: 0.011025 | E: -42.216397 | E_var:     0.5775 | E_err:   0.011874
[2025-10-02 17:04:08] [Iter  429/2250] R2[406/600]  | LR: 0.010913 | E: -42.172411 | E_var:     0.5918 | E_err:   0.012020
[2025-10-02 17:04:13] [Iter  430/2250] R2[408/600]  | LR: 0.010802 | E: -42.189910 | E_var:     0.4755 | E_err:   0.010774
[2025-10-02 17:04:18] [Iter  431/2250] R2[410/600]  | LR: 0.010692 | E: -42.180832 | E_var:     0.6235 | E_err:   0.012338
[2025-10-02 17:04:23] [Iter  432/2250] R2[412/600]  | LR: 0.010583 | E: -42.219978 | E_var:     0.6389 | E_err:   0.012490
[2025-10-02 17:04:29] [Iter  433/2250] R2[414/600]  | LR: 0.010474 | E: -42.211746 | E_var:     0.5854 | E_err:   0.011955
[2025-10-02 17:04:34] [Iter  434/2250] R2[416/600]  | LR: 0.010366 | E: -42.198955 | E_var:     0.5663 | E_err:   0.011758
[2025-10-02 17:04:39] [Iter  435/2250] R2[418/600]  | LR: 0.010259 | E: -42.202029 | E_var:     0.5426 | E_err:   0.011510
[2025-10-02 17:04:44] [Iter  436/2250] R2[420/600]  | LR: 0.010153 | E: -42.177397 | E_var:     0.5820 | E_err:   0.011920
[2025-10-02 17:04:49] [Iter  437/2250] R2[422/600]  | LR: 0.010047 | E: -42.199251 | E_var:     0.5772 | E_err:   0.011871
[2025-10-02 17:04:54] [Iter  438/2250] R2[424/600]  | LR: 0.009943 | E: -42.202658 | E_var:     0.6378 | E_err:   0.012478
[2025-10-02 17:04:59] [Iter  439/2250] R2[426/600]  | LR: 0.009839 | E: -42.205979 | E_var:     0.5842 | E_err:   0.011943
[2025-10-02 17:05:05] [Iter  440/2250] R2[428/600]  | LR: 0.009736 | E: -42.190065 | E_var:     0.5075 | E_err:   0.011131
[2025-10-02 17:05:10] [Iter  441/2250] R2[430/600]  | LR: 0.009633 | E: -42.197819 | E_var:     0.7329 | E_err:   0.013377
[2025-10-02 17:05:15] [Iter  442/2250] R2[432/600]  | LR: 0.009532 | E: -42.195014 | E_var:     0.5491 | E_err:   0.011578
[2025-10-02 17:05:20] [Iter  443/2250] R2[434/600]  | LR: 0.009432 | E: -42.202874 | E_var:     0.6376 | E_err:   0.012477
[2025-10-02 17:05:25] [Iter  444/2250] R2[436/600]  | LR: 0.009332 | E: -42.215889 | E_var:     0.6127 | E_err:   0.012230
[2025-10-02 17:05:30] [Iter  445/2250] R2[438/600]  | LR: 0.009234 | E: -42.188074 | E_var:     0.6219 | E_err:   0.012322
[2025-10-02 17:05:36] [Iter  446/2250] R2[440/600]  | LR: 0.009136 | E: -42.209158 | E_var:     0.6059 | E_err:   0.012163
[2025-10-02 17:05:41] [Iter  447/2250] R2[442/600]  | LR: 0.009039 | E: -42.195493 | E_var:     0.4954 | E_err:   0.010997
[2025-10-02 17:05:46] [Iter  448/2250] R2[444/600]  | LR: 0.008943 | E: -42.210598 | E_var:     0.5435 | E_err:   0.011519
[2025-10-02 17:05:51] [Iter  449/2250] R2[446/600]  | LR: 0.008848 | E: -42.200028 | E_var:     0.6926 | E_err:   0.013003
[2025-10-02 17:05:56] [Iter  450/2250] R2[448/600]  | LR: 0.008754 | E: -42.205539 | E_var:     0.6360 | E_err:   0.012461
[2025-10-02 17:06:01] [Iter  451/2250] R2[450/600]  | LR: 0.008661 | E: -42.197161 | E_var:     0.6497 | E_err:   0.012595
[2025-10-02 17:06:06] [Iter  452/2250] R2[452/600]  | LR: 0.008569 | E: -42.208771 | E_var:     0.7787 | E_err:   0.013788
[2025-10-02 17:06:12] [Iter  453/2250] R2[454/600]  | LR: 0.008478 | E: -42.216703 | E_var:     0.8809 | E_err:   0.014665
[2025-10-02 17:06:17] [Iter  454/2250] R2[456/600]  | LR: 0.008388 | E: -42.203755 | E_var:     0.5372 | E_err:   0.011452
[2025-10-02 17:06:22] [Iter  455/2250] R2[458/600]  | LR: 0.008299 | E: -42.181423 | E_var:     0.7572 | E_err:   0.013596
[2025-10-02 17:06:27] [Iter  456/2250] R2[460/600]  | LR: 0.008211 | E: -42.225549 | E_var:     0.6241 | E_err:   0.012344
[2025-10-02 17:06:32] [Iter  457/2250] R2[462/600]  | LR: 0.008124 | E: -42.199467 | E_var:     0.5501 | E_err:   0.011589
[2025-10-02 17:06:37] [Iter  458/2250] R2[464/600]  | LR: 0.008038 | E: -42.206059 | E_var:     0.9616 | E_err:   0.015322
[2025-10-02 17:06:42] [Iter  459/2250] R2[466/600]  | LR: 0.007953 | E: -42.183082 | E_var:     0.6256 | E_err:   0.012359
[2025-10-02 17:06:48] [Iter  460/2250] R2[468/600]  | LR: 0.007869 | E: -42.203141 | E_var:     0.6018 | E_err:   0.012121
[2025-10-02 17:06:53] [Iter  461/2250] R2[470/600]  | LR: 0.007786 | E: -42.217453 | E_var:     0.7314 | E_err:   0.013363
[2025-10-02 17:06:58] [Iter  462/2250] R2[472/600]  | LR: 0.007704 | E: -42.202187 | E_var:     0.6041 | E_err:   0.012144
[2025-10-02 17:07:03] [Iter  463/2250] R2[474/600]  | LR: 0.007623 | E: -42.203651 | E_var:     0.7932 | E_err:   0.013916
[2025-10-02 17:07:08] [Iter  464/2250] R2[476/600]  | LR: 0.007543 | E: -42.206399 | E_var:     0.5771 | E_err:   0.011870
[2025-10-02 17:07:13] [Iter  465/2250] R2[478/600]  | LR: 0.007465 | E: -42.204232 | E_var:     0.5954 | E_err:   0.012057
[2025-10-02 17:07:18] [Iter  466/2250] R2[480/600]  | LR: 0.007387 | E: -42.217067 | E_var:     0.5554 | E_err:   0.011645
[2025-10-02 17:07:24] [Iter  467/2250] R2[482/600]  | LR: 0.007311 | E: -42.199401 | E_var:     1.0379 | E_err:   0.015918
[2025-10-02 17:07:29] [Iter  468/2250] R2[484/600]  | LR: 0.007236 | E: -42.213124 | E_var:     0.5254 | E_err:   0.011325
[2025-10-02 17:07:34] [Iter  469/2250] R2[486/600]  | LR: 0.007161 | E: -42.208517 | E_var:     0.9021 | E_err:   0.014840
[2025-10-02 17:07:39] [Iter  470/2250] R2[488/600]  | LR: 0.007088 | E: -42.235353 | E_var:     0.6929 | E_err:   0.013006
[2025-10-02 17:07:44] [Iter  471/2250] R2[490/600]  | LR: 0.007017 | E: -42.220301 | E_var:     0.5881 | E_err:   0.011983
[2025-10-02 17:07:49] [Iter  472/2250] R2[492/600]  | LR: 0.006946 | E: -42.201225 | E_var:     0.5108 | E_err:   0.011167
[2025-10-02 17:07:54] [Iter  473/2250] R2[494/600]  | LR: 0.006876 | E: -42.216705 | E_var:     0.6367 | E_err:   0.012468
[2025-10-02 17:08:00] [Iter  474/2250] R2[496/600]  | LR: 0.006808 | E: -42.220082 | E_var:     0.6465 | E_err:   0.012564
[2025-10-02 17:08:05] [Iter  475/2250] R2[498/600]  | LR: 0.006741 | E: -42.210519 | E_var:     1.0728 | E_err:   0.016184
[2025-10-02 17:08:10] [Iter  476/2250] R2[500/600]  | LR: 0.006675 | E: -42.185761 | E_var:     2.3049 | E_err:   0.023722
[2025-10-02 17:08:15] [Iter  477/2250] R2[502/600]  | LR: 0.006610 | E: -42.192804 | E_var:     0.5310 | E_err:   0.011386
[2025-10-02 17:08:20] [Iter  478/2250] R2[504/600]  | LR: 0.006546 | E: -42.188109 | E_var:     0.5423 | E_err:   0.011506
[2025-10-02 17:08:25] [Iter  479/2250] R2[506/600]  | LR: 0.006484 | E: -42.220535 | E_var:     0.6726 | E_err:   0.012815
[2025-10-02 17:08:31] [Iter  480/2250] R2[508/600]  | LR: 0.006422 | E: -42.205089 | E_var:     0.5679 | E_err:   0.011775
[2025-10-02 17:08:36] [Iter  481/2250] R2[510/600]  | LR: 0.006362 | E: -42.224518 | E_var:     0.5524 | E_err:   0.011613
[2025-10-02 17:08:41] [Iter  482/2250] R2[512/600]  | LR: 0.006304 | E: -42.213044 | E_var:     0.7261 | E_err:   0.013314
[2025-10-02 17:08:46] [Iter  483/2250] R2[514/600]  | LR: 0.006246 | E: -42.195719 | E_var:     0.6119 | E_err:   0.012222
[2025-10-02 17:08:51] [Iter  484/2250] R2[516/600]  | LR: 0.006190 | E: -42.200718 | E_var:     0.6602 | E_err:   0.012695
[2025-10-02 17:08:56] [Iter  485/2250] R2[518/600]  | LR: 0.006135 | E: -42.216895 | E_var:     0.7853 | E_err:   0.013846
[2025-10-02 17:09:01] [Iter  486/2250] R2[520/600]  | LR: 0.006081 | E: -42.200950 | E_var:     0.5297 | E_err:   0.011372
[2025-10-02 17:09:07] [Iter  487/2250] R2[522/600]  | LR: 0.006028 | E: -42.194552 | E_var:     0.5404 | E_err:   0.011486
[2025-10-02 17:09:12] [Iter  488/2250] R2[524/600]  | LR: 0.005977 | E: -42.201217 | E_var:     0.5470 | E_err:   0.011557
[2025-10-02 17:09:17] [Iter  489/2250] R2[526/600]  | LR: 0.005927 | E: -42.196409 | E_var:     0.5370 | E_err:   0.011450
[2025-10-02 17:09:22] [Iter  490/2250] R2[528/600]  | LR: 0.005878 | E: -42.204195 | E_var:     0.5577 | E_err:   0.011669
[2025-10-02 17:09:27] [Iter  491/2250] R2[530/600]  | LR: 0.005830 | E: -42.218642 | E_var:     0.5493 | E_err:   0.011580
[2025-10-02 17:09:32] [Iter  492/2250] R2[532/600]  | LR: 0.005784 | E: -42.202895 | E_var:     0.6147 | E_err:   0.012251
[2025-10-02 17:09:37] [Iter  493/2250] R2[534/600]  | LR: 0.005739 | E: -42.207027 | E_var:     0.4896 | E_err:   0.010933
[2025-10-02 17:09:43] [Iter  494/2250] R2[536/600]  | LR: 0.005695 | E: -42.210921 | E_var:     0.4689 | E_err:   0.010700
[2025-10-02 17:09:48] [Iter  495/2250] R2[538/600]  | LR: 0.005653 | E: -42.218863 | E_var:     0.5207 | E_err:   0.011275
[2025-10-02 17:09:53] [Iter  496/2250] R2[540/600]  | LR: 0.005612 | E: -42.223043 | E_var:     0.5995 | E_err:   0.012098
[2025-10-02 17:09:58] [Iter  497/2250] R2[542/600]  | LR: 0.005572 | E: -42.222695 | E_var:     0.4860 | E_err:   0.010893
[2025-10-02 17:10:03] [Iter  498/2250] R2[544/600]  | LR: 0.005534 | E: -42.210485 | E_var:     0.6628 | E_err:   0.012721
[2025-10-02 17:10:08] [Iter  499/2250] R2[546/600]  | LR: 0.005496 | E: -42.234196 | E_var:     0.5280 | E_err:   0.011354
[2025-10-02 17:10:13] [Iter  500/2250] R2[548/600]  | LR: 0.005460 | E: -42.208552 | E_var:     0.7369 | E_err:   0.013413
[2025-10-02 17:10:19] [Iter  501/2250] R2[550/600]  | LR: 0.005426 | E: -42.204953 | E_var:     0.7803 | E_err:   0.013802
[2025-10-02 17:10:24] [Iter  502/2250] R2[552/600]  | LR: 0.005393 | E: -42.219384 | E_var:     0.5248 | E_err:   0.011319
[2025-10-02 17:10:29] [Iter  503/2250] R2[554/600]  | LR: 0.005361 | E: -42.206272 | E_var:     0.6978 | E_err:   0.013052
[2025-10-02 17:10:34] [Iter  504/2250] R2[556/600]  | LR: 0.005330 | E: -42.200663 | E_var:     0.5783 | E_err:   0.011882
[2025-10-02 17:10:39] [Iter  505/2250] R2[558/600]  | LR: 0.005301 | E: -42.197889 | E_var:     0.4972 | E_err:   0.011018
[2025-10-02 17:10:44] [Iter  506/2250] R2[560/600]  | LR: 0.005273 | E: -42.220989 | E_var:     0.5994 | E_err:   0.012097
[2025-10-02 17:10:49] [Iter  507/2250] R2[562/600]  | LR: 0.005247 | E: -42.232850 | E_var:     0.6670 | E_err:   0.012761
[2025-10-02 17:10:55] [Iter  508/2250] R2[564/600]  | LR: 0.005221 | E: -42.233278 | E_var:     0.7516 | E_err:   0.013546
[2025-10-02 17:11:00] [Iter  509/2250] R2[566/600]  | LR: 0.005198 | E: -42.236164 | E_var:     0.9029 | E_err:   0.014847
[2025-10-02 17:11:05] [Iter  510/2250] R2[568/600]  | LR: 0.005175 | E: -42.215145 | E_var:     0.5366 | E_err:   0.011446
[2025-10-02 17:11:10] [Iter  511/2250] R2[570/600]  | LR: 0.005154 | E: -42.215236 | E_var:     0.5456 | E_err:   0.011541
[2025-10-02 17:11:15] [Iter  512/2250] R2[572/600]  | LR: 0.005134 | E: -42.225647 | E_var:     0.5361 | E_err:   0.011441
[2025-10-02 17:11:20] [Iter  513/2250] R2[574/600]  | LR: 0.005116 | E: -42.220079 | E_var:     0.6176 | E_err:   0.012280
[2025-10-02 17:11:25] [Iter  514/2250] R2[576/600]  | LR: 0.005099 | E: -42.220193 | E_var:     0.6413 | E_err:   0.012513
[2025-10-02 17:11:31] [Iter  515/2250] R2[578/600]  | LR: 0.005083 | E: -42.214940 | E_var:     0.5258 | E_err:   0.011330
[2025-10-02 17:11:36] [Iter  516/2250] R2[580/600]  | LR: 0.005068 | E: -42.233195 | E_var:     0.5613 | E_err:   0.011706
[2025-10-02 17:11:41] [Iter  517/2250] R2[582/600]  | LR: 0.005055 | E: -42.223477 | E_var:     0.5630 | E_err:   0.011724
[2025-10-02 17:11:46] [Iter  518/2250] R2[584/600]  | LR: 0.005044 | E: -42.203941 | E_var:     0.5520 | E_err:   0.011609
[2025-10-02 17:11:51] [Iter  519/2250] R2[586/600]  | LR: 0.005034 | E: -42.226328 | E_var:     0.4878 | E_err:   0.010913
[2025-10-02 17:11:56] [Iter  520/2250] R2[588/600]  | LR: 0.005025 | E: -42.228535 | E_var:     0.4986 | E_err:   0.011033
[2025-10-02 17:12:02] [Iter  521/2250] R2[590/600]  | LR: 0.005017 | E: -42.214575 | E_var:     0.5488 | E_err:   0.011576
[2025-10-02 17:12:07] [Iter  522/2250] R2[592/600]  | LR: 0.005011 | E: -42.220902 | E_var:     0.5499 | E_err:   0.011587
[2025-10-02 17:12:12] [Iter  523/2250] R2[594/600]  | LR: 0.005006 | E: -42.219401 | E_var:     0.5683 | E_err:   0.011780
[2025-10-02 17:12:17] [Iter  524/2250] R2[596/600]  | LR: 0.005003 | E: -42.238858 | E_var:     0.6722 | E_err:   0.012811
[2025-10-02 17:12:22] [Iter  525/2250] R2[598/600]  | LR: 0.005001 | E: -42.211254 | E_var:     0.5909 | E_err:   0.012011
[2025-10-02 17:12:22] 🔄 RESTART #3 | Period: 1200
[2025-10-02 17:12:27] [Iter  526/2250] R3[0/1200]   | LR: 0.030000 | E: -42.229836 | E_var:     0.4887 | E_err:   0.010923
[2025-10-02 17:12:32] [Iter  527/2250] R3[2/1200]   | LR: 0.030000 | E: -42.240333 | E_var:     0.6088 | E_err:   0.012191
[2025-10-02 17:12:38] [Iter  528/2250] R3[4/1200]   | LR: 0.029999 | E: -42.226189 | E_var:     0.5960 | E_err:   0.012063
[2025-10-02 17:12:43] [Iter  529/2250] R3[6/1200]   | LR: 0.029998 | E: -42.221747 | E_var:     0.5413 | E_err:   0.011496
[2025-10-02 17:12:48] [Iter  530/2250] R3[8/1200]   | LR: 0.029997 | E: -42.227458 | E_var:     0.5078 | E_err:   0.011134
[2025-10-02 17:12:53] [Iter  531/2250] R3[10/1200]  | LR: 0.029996 | E: -42.220979 | E_var:     0.5131 | E_err:   0.011192
[2025-10-02 17:12:58] [Iter  532/2250] R3[12/1200]  | LR: 0.029994 | E: -42.242425 | E_var:     0.8200 | E_err:   0.014149
[2025-10-02 17:13:03] [Iter  533/2250] R3[14/1200]  | LR: 0.029992 | E: -42.224184 | E_var:     0.6914 | E_err:   0.012992
[2025-10-02 17:13:08] [Iter  534/2250] R3[16/1200]  | LR: 0.029989 | E: -42.211985 | E_var:     0.7469 | E_err:   0.013503
[2025-10-02 17:13:14] [Iter  535/2250] R3[18/1200]  | LR: 0.029986 | E: -42.219499 | E_var:     0.6353 | E_err:   0.012454
[2025-10-02 17:13:19] [Iter  536/2250] R3[20/1200]  | LR: 0.029983 | E: -42.217476 | E_var:     0.5281 | E_err:   0.011355
[2025-10-02 17:13:24] [Iter  537/2250] R3[22/1200]  | LR: 0.029979 | E: -42.203622 | E_var:     0.5496 | E_err:   0.011584
[2025-10-02 17:13:29] [Iter  538/2250] R3[24/1200]  | LR: 0.029975 | E: -42.221199 | E_var:     0.4664 | E_err:   0.010670
[2025-10-02 17:13:34] [Iter  539/2250] R3[26/1200]  | LR: 0.029971 | E: -42.225914 | E_var:     0.6300 | E_err:   0.012402
[2025-10-02 17:13:39] [Iter  540/2250] R3[28/1200]  | LR: 0.029966 | E: -42.210270 | E_var:     0.5820 | E_err:   0.011920
[2025-10-02 17:13:44] [Iter  541/2250] R3[30/1200]  | LR: 0.029961 | E: -42.227330 | E_var:     0.5216 | E_err:   0.011285
[2025-10-02 17:13:50] [Iter  542/2250] R3[32/1200]  | LR: 0.029956 | E: -42.218391 | E_var:     0.5306 | E_err:   0.011381
[2025-10-02 17:13:55] [Iter  543/2250] R3[34/1200]  | LR: 0.029951 | E: -42.239360 | E_var:     0.5765 | E_err:   0.011864
[2025-10-02 17:14:00] [Iter  544/2250] R3[36/1200]  | LR: 0.029945 | E: -42.181569 | E_var:     3.1524 | E_err:   0.027742
[2025-10-02 17:14:05] [Iter  545/2250] R3[38/1200]  | LR: 0.029938 | E: -42.214950 | E_var:     2.1762 | E_err:   0.023050
[2025-10-02 17:14:10] [Iter  546/2250] R3[40/1200]  | LR: 0.029932 | E: -42.224475 | E_var:     0.6920 | E_err:   0.012998
[2025-10-02 17:14:15] [Iter  547/2250] R3[42/1200]  | LR: 0.029925 | E: -42.226235 | E_var:     0.5414 | E_err:   0.011496
[2025-10-02 17:14:21] [Iter  548/2250] R3[44/1200]  | LR: 0.029917 | E: -42.237538 | E_var:     0.5508 | E_err:   0.011597
[2025-10-02 17:14:26] [Iter  549/2250] R3[46/1200]  | LR: 0.029909 | E: -42.238927 | E_var:     0.6223 | E_err:   0.012326
[2025-10-02 17:14:31] [Iter  550/2250] R3[48/1200]  | LR: 0.029901 | E: -42.231499 | E_var:     0.5532 | E_err:   0.011622
[2025-10-02 17:14:36] [Iter  551/2250] R3[50/1200]  | LR: 0.029893 | E: -42.244317 | E_var:     0.6111 | E_err:   0.012215
[2025-10-02 17:14:41] [Iter  552/2250] R3[52/1200]  | LR: 0.029884 | E: -42.239474 | E_var:     0.5194 | E_err:   0.011261
[2025-10-02 17:14:46] [Iter  553/2250] R3[54/1200]  | LR: 0.029875 | E: -42.225586 | E_var:     0.6096 | E_err:   0.012199
[2025-10-02 17:14:51] [Iter  554/2250] R3[56/1200]  | LR: 0.029866 | E: -42.231667 | E_var:     0.7589 | E_err:   0.013612
[2025-10-02 17:14:57] [Iter  555/2250] R3[58/1200]  | LR: 0.029856 | E: -42.231505 | E_var:     0.6659 | E_err:   0.012751
[2025-10-02 17:15:02] [Iter  556/2250] R3[60/1200]  | LR: 0.029846 | E: -42.234743 | E_var:     0.4395 | E_err:   0.010359
[2025-10-02 17:15:07] [Iter  557/2250] R3[62/1200]  | LR: 0.029836 | E: -42.219686 | E_var:     0.5358 | E_err:   0.011437
[2025-10-02 17:15:12] [Iter  558/2250] R3[64/1200]  | LR: 0.029825 | E: -42.226827 | E_var:     0.4899 | E_err:   0.010936
[2025-10-02 17:15:17] [Iter  559/2250] R3[66/1200]  | LR: 0.029814 | E: -42.242520 | E_var:     0.5750 | E_err:   0.011848
[2025-10-02 17:15:22] [Iter  560/2250] R3[68/1200]  | LR: 0.029802 | E: -42.225392 | E_var:     0.5417 | E_err:   0.011500
[2025-10-02 17:15:27] [Iter  561/2250] R3[70/1200]  | LR: 0.029791 | E: -42.226272 | E_var:     0.5568 | E_err:   0.011659
[2025-10-02 17:15:33] [Iter  562/2250] R3[72/1200]  | LR: 0.029779 | E: -42.218990 | E_var:     0.5213 | E_err:   0.011281
[2025-10-02 17:15:38] [Iter  563/2250] R3[74/1200]  | LR: 0.029766 | E: -42.247055 | E_var:     0.5132 | E_err:   0.011193
[2025-10-02 17:15:43] [Iter  564/2250] R3[76/1200]  | LR: 0.029753 | E: -42.235942 | E_var:     0.5629 | E_err:   0.011723
[2025-10-02 17:15:48] [Iter  565/2250] R3[78/1200]  | LR: 0.029740 | E: -42.240212 | E_var:     0.6016 | E_err:   0.012119
[2025-10-02 17:15:53] [Iter  566/2250] R3[80/1200]  | LR: 0.029727 | E: -42.223740 | E_var:     0.6568 | E_err:   0.012663
[2025-10-02 17:15:58] [Iter  567/2250] R3[82/1200]  | LR: 0.029713 | E: -42.218391 | E_var:     0.4655 | E_err:   0.010660
[2025-10-02 17:16:03] [Iter  568/2250] R3[84/1200]  | LR: 0.029699 | E: -42.236372 | E_var:     0.4937 | E_err:   0.010979
[2025-10-02 17:16:09] [Iter  569/2250] R3[86/1200]  | LR: 0.029685 | E: -42.213656 | E_var:     0.5314 | E_err:   0.011390
[2025-10-02 17:16:14] [Iter  570/2250] R3[88/1200]  | LR: 0.029670 | E: -42.228257 | E_var:     0.6991 | E_err:   0.013064
[2025-10-02 17:16:19] [Iter  571/2250] R3[90/1200]  | LR: 0.029655 | E: -42.231312 | E_var:     0.5951 | E_err:   0.012053
[2025-10-02 17:16:24] [Iter  572/2250] R3[92/1200]  | LR: 0.029639 | E: -42.228711 | E_var:     0.6820 | E_err:   0.012903
[2025-10-02 17:16:29] [Iter  573/2250] R3[94/1200]  | LR: 0.029623 | E: -42.226518 | E_var:     0.4907 | E_err:   0.010946
[2025-10-02 17:16:34] [Iter  574/2250] R3[96/1200]  | LR: 0.029607 | E: -42.239583 | E_var:     0.5757 | E_err:   0.011855
[2025-10-02 17:16:39] [Iter  575/2250] R3[98/1200]  | LR: 0.029591 | E: -42.222500 | E_var:     0.5209 | E_err:   0.011277
[2025-10-02 17:16:45] [Iter  576/2250] R3[100/1200] | LR: 0.029574 | E: -42.230358 | E_var:     0.4849 | E_err:   0.010880
[2025-10-02 17:16:50] [Iter  577/2250] R3[102/1200] | LR: 0.029557 | E: -42.221585 | E_var:     0.6418 | E_err:   0.012517
[2025-10-02 17:16:55] [Iter  578/2250] R3[104/1200] | LR: 0.029540 | E: -42.231536 | E_var:     0.4939 | E_err:   0.010981
[2025-10-02 17:17:00] [Iter  579/2250] R3[106/1200] | LR: 0.029522 | E: -42.221531 | E_var:     0.5460 | E_err:   0.011546
[2025-10-02 17:17:05] [Iter  580/2250] R3[108/1200] | LR: 0.029504 | E: -42.222767 | E_var:     0.5287 | E_err:   0.011361
[2025-10-02 17:17:10] [Iter  581/2250] R3[110/1200] | LR: 0.029485 | E: -42.226960 | E_var:     0.6463 | E_err:   0.012561
[2025-10-02 17:17:15] [Iter  582/2250] R3[112/1200] | LR: 0.029466 | E: -42.234493 | E_var:     0.5124 | E_err:   0.011184
[2025-10-02 17:17:21] [Iter  583/2250] R3[114/1200] | LR: 0.029447 | E: -42.228065 | E_var:     0.5746 | E_err:   0.011844
[2025-10-02 17:17:26] [Iter  584/2250] R3[116/1200] | LR: 0.029428 | E: -42.247513 | E_var:     0.5144 | E_err:   0.011206
[2025-10-02 17:17:31] [Iter  585/2250] R3[118/1200] | LR: 0.029408 | E: -42.229447 | E_var:     0.5511 | E_err:   0.011600
[2025-10-02 17:17:36] [Iter  586/2250] R3[120/1200] | LR: 0.029388 | E: -42.245206 | E_var:     0.5632 | E_err:   0.011726
[2025-10-02 17:17:41] [Iter  587/2250] R3[122/1200] | LR: 0.029368 | E: -42.217410 | E_var:     0.6280 | E_err:   0.012382
[2025-10-02 17:17:46] [Iter  588/2250] R3[124/1200] | LR: 0.029347 | E: -42.233220 | E_var:     0.5851 | E_err:   0.011952
[2025-10-02 17:17:51] [Iter  589/2250] R3[126/1200] | LR: 0.029326 | E: -42.220232 | E_var:     0.6474 | E_err:   0.012572
[2025-10-02 17:17:57] [Iter  590/2250] R3[128/1200] | LR: 0.029305 | E: -42.224820 | E_var:     0.6114 | E_err:   0.012218
[2025-10-02 17:18:02] [Iter  591/2250] R3[130/1200] | LR: 0.029283 | E: -42.233800 | E_var:     0.6268 | E_err:   0.012371
[2025-10-02 17:18:07] [Iter  592/2250] R3[132/1200] | LR: 0.029261 | E: -42.240073 | E_var:     0.5662 | E_err:   0.011758
[2025-10-02 17:18:12] [Iter  593/2250] R3[134/1200] | LR: 0.029239 | E: -42.231457 | E_var:     0.5979 | E_err:   0.012081
[2025-10-02 17:18:17] [Iter  594/2250] R3[136/1200] | LR: 0.029216 | E: -42.236875 | E_var:     0.6662 | E_err:   0.012753
[2025-10-02 17:18:22] [Iter  595/2250] R3[138/1200] | LR: 0.029193 | E: -42.246837 | E_var:     0.5029 | E_err:   0.011080
[2025-10-02 17:18:27] [Iter  596/2250] R3[140/1200] | LR: 0.029170 | E: -42.234601 | E_var:     0.5464 | E_err:   0.011550
[2025-10-02 17:18:33] [Iter  597/2250] R3[142/1200] | LR: 0.029146 | E: -42.227443 | E_var:     0.6441 | E_err:   0.012540
[2025-10-02 17:18:38] [Iter  598/2250] R3[144/1200] | LR: 0.029122 | E: -42.246678 | E_var:     0.5351 | E_err:   0.011430
[2025-10-02 17:18:43] [Iter  599/2250] R3[146/1200] | LR: 0.029098 | E: -42.239612 | E_var:     0.4524 | E_err:   0.010509
[2025-10-02 17:18:48] [Iter  600/2250] R3[148/1200] | LR: 0.029073 | E: -42.235540 | E_var:     0.5623 | E_err:   0.011717
[2025-10-02 17:18:48] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-02 17:18:53] [Iter  601/2250] R3[150/1200] | LR: 0.029048 | E: -42.233901 | E_var:     0.4891 | E_err:   0.010928
[2025-10-02 17:18:58] [Iter  602/2250] R3[152/1200] | LR: 0.029023 | E: -42.235935 | E_var:     0.4950 | E_err:   0.010993
[2025-10-02 17:19:04] [Iter  603/2250] R3[154/1200] | LR: 0.028998 | E: -42.221837 | E_var:     0.4838 | E_err:   0.010868
[2025-10-02 17:19:09] [Iter  604/2250] R3[156/1200] | LR: 0.028972 | E: -42.243816 | E_var:     0.6927 | E_err:   0.013005
[2025-10-02 17:19:14] [Iter  605/2250] R3[158/1200] | LR: 0.028946 | E: -42.217784 | E_var:     0.4660 | E_err:   0.010667
[2025-10-02 17:19:19] [Iter  606/2250] R3[160/1200] | LR: 0.028919 | E: -42.225521 | E_var:     0.6312 | E_err:   0.012414
[2025-10-02 17:19:24] [Iter  607/2250] R3[162/1200] | LR: 0.028893 | E: -42.229322 | E_var:     0.5546 | E_err:   0.011637
[2025-10-02 17:19:29] [Iter  608/2250] R3[164/1200] | LR: 0.028865 | E: -42.227363 | E_var:     0.5875 | E_err:   0.011976
[2025-10-02 17:19:34] [Iter  609/2250] R3[166/1200] | LR: 0.028838 | E: -42.250068 | E_var:     0.6094 | E_err:   0.012198
[2025-10-02 17:19:40] [Iter  610/2250] R3[168/1200] | LR: 0.028810 | E: -42.211569 | E_var:     0.5707 | E_err:   0.011804
[2025-10-02 17:19:45] [Iter  611/2250] R3[170/1200] | LR: 0.028782 | E: -42.222643 | E_var:     0.4985 | E_err:   0.011032
[2025-10-02 17:19:50] [Iter  612/2250] R3[172/1200] | LR: 0.028754 | E: -42.216172 | E_var:     0.5611 | E_err:   0.011704
[2025-10-02 17:19:55] [Iter  613/2250] R3[174/1200] | LR: 0.028725 | E: -42.234405 | E_var:     0.4916 | E_err:   0.010956
[2025-10-02 17:20:00] [Iter  614/2250] R3[176/1200] | LR: 0.028696 | E: -42.240557 | E_var:     0.4851 | E_err:   0.010883
[2025-10-02 17:20:05] [Iter  615/2250] R3[178/1200] | LR: 0.028667 | E: -42.232446 | E_var:     0.5816 | E_err:   0.011916
[2025-10-02 17:20:10] [Iter  616/2250] R3[180/1200] | LR: 0.028638 | E: -42.239842 | E_var:     0.4826 | E_err:   0.010854
[2025-10-02 17:20:16] [Iter  617/2250] R3[182/1200] | LR: 0.028608 | E: -42.244328 | E_var:     0.4922 | E_err:   0.010962
[2025-10-02 17:20:21] [Iter  618/2250] R3[184/1200] | LR: 0.028578 | E: -42.229238 | E_var:     0.5829 | E_err:   0.011930
[2025-10-02 17:20:26] [Iter  619/2250] R3[186/1200] | LR: 0.028547 | E: -42.234272 | E_var:     0.4718 | E_err:   0.010733
[2025-10-02 17:20:31] [Iter  620/2250] R3[188/1200] | LR: 0.028516 | E: -42.222401 | E_var:     0.5310 | E_err:   0.011386
[2025-10-02 17:20:36] [Iter  621/2250] R3[190/1200] | LR: 0.028485 | E: -42.234517 | E_var:     0.5427 | E_err:   0.011510
[2025-10-02 17:20:41] [Iter  622/2250] R3[192/1200] | LR: 0.028454 | E: -42.244950 | E_var:     0.5009 | E_err:   0.011059
[2025-10-02 17:20:47] [Iter  623/2250] R3[194/1200] | LR: 0.028422 | E: -42.238055 | E_var:     0.5097 | E_err:   0.011155
[2025-10-02 17:20:52] [Iter  624/2250] R3[196/1200] | LR: 0.028390 | E: -42.215169 | E_var:     0.6460 | E_err:   0.012558
[2025-10-02 17:20:57] [Iter  625/2250] R3[198/1200] | LR: 0.028358 | E: -42.246430 | E_var:     0.5318 | E_err:   0.011395
[2025-10-02 17:21:02] [Iter  626/2250] R3[200/1200] | LR: 0.028325 | E: -42.220151 | E_var:     0.4672 | E_err:   0.010680
[2025-10-02 17:21:07] [Iter  627/2250] R3[202/1200] | LR: 0.028292 | E: -42.248373 | E_var:     0.4838 | E_err:   0.010868
[2025-10-02 17:21:12] [Iter  628/2250] R3[204/1200] | LR: 0.028259 | E: -42.242849 | E_var:     0.5114 | E_err:   0.011173
[2025-10-02 17:21:17] [Iter  629/2250] R3[206/1200] | LR: 0.028226 | E: -42.227973 | E_var:     0.4384 | E_err:   0.010345
[2025-10-02 17:21:23] [Iter  630/2250] R3[208/1200] | LR: 0.028192 | E: -42.232198 | E_var:     0.5402 | E_err:   0.011484
[2025-10-02 17:21:28] [Iter  631/2250] R3[210/1200] | LR: 0.028158 | E: -42.254392 | E_var:     0.4345 | E_err:   0.010300
[2025-10-02 17:21:33] [Iter  632/2250] R3[212/1200] | LR: 0.028124 | E: -42.252467 | E_var:     0.5299 | E_err:   0.011374
[2025-10-02 17:21:38] [Iter  633/2250] R3[214/1200] | LR: 0.028089 | E: -42.256432 | E_var:     0.5802 | E_err:   0.011902
[2025-10-02 17:21:43] [Iter  634/2250] R3[216/1200] | LR: 0.028054 | E: -42.260500 | E_var:     0.4645 | E_err:   0.010649
[2025-10-02 17:21:48] [Iter  635/2250] R3[218/1200] | LR: 0.028019 | E: -42.232512 | E_var:     0.6836 | E_err:   0.012918
[2025-10-02 17:21:53] [Iter  636/2250] R3[220/1200] | LR: 0.027983 | E: -42.220839 | E_var:     0.4983 | E_err:   0.011030
[2025-10-02 17:21:59] [Iter  637/2250] R3[222/1200] | LR: 0.027948 | E: -42.251121 | E_var:     0.4909 | E_err:   0.010948
[2025-10-02 17:22:04] [Iter  638/2250] R3[224/1200] | LR: 0.027912 | E: -42.237464 | E_var:     0.5861 | E_err:   0.011962
[2025-10-02 17:22:09] [Iter  639/2250] R3[226/1200] | LR: 0.027875 | E: -42.254480 | E_var:     0.5197 | E_err:   0.011264
[2025-10-02 17:22:14] [Iter  640/2250] R3[228/1200] | LR: 0.027839 | E: -42.235227 | E_var:     0.5547 | E_err:   0.011637
[2025-10-02 17:22:19] [Iter  641/2250] R3[230/1200] | LR: 0.027802 | E: -42.241206 | E_var:     0.6230 | E_err:   0.012333
[2025-10-02 17:22:24] [Iter  642/2250] R3[232/1200] | LR: 0.027764 | E: -42.256173 | E_var:     0.5948 | E_err:   0.012051
[2025-10-02 17:22:29] [Iter  643/2250] R3[234/1200] | LR: 0.027727 | E: -42.247945 | E_var:     0.6571 | E_err:   0.012666
[2025-10-02 17:22:35] [Iter  644/2250] R3[236/1200] | LR: 0.027689 | E: -42.243536 | E_var:     0.5294 | E_err:   0.011369
[2025-10-02 17:22:40] [Iter  645/2250] R3[238/1200] | LR: 0.027651 | E: -42.241668 | E_var:     0.4576 | E_err:   0.010569
[2025-10-02 17:22:45] [Iter  646/2250] R3[240/1200] | LR: 0.027613 | E: -42.225293 | E_var:     0.4958 | E_err:   0.011002
[2025-10-02 17:22:50] [Iter  647/2250] R3[242/1200] | LR: 0.027574 | E: -42.252720 | E_var:     0.5361 | E_err:   0.011441
[2025-10-02 17:22:55] [Iter  648/2250] R3[244/1200] | LR: 0.027535 | E: -42.254687 | E_var:     0.5487 | E_err:   0.011574
[2025-10-02 17:23:00] [Iter  649/2250] R3[246/1200] | LR: 0.027496 | E: -42.249138 | E_var:     0.4395 | E_err:   0.010359
[2025-10-02 17:23:05] [Iter  650/2250] R3[248/1200] | LR: 0.027457 | E: -42.253502 | E_var:     0.5038 | E_err:   0.011090
[2025-10-02 17:23:11] [Iter  651/2250] R3[250/1200] | LR: 0.027417 | E: -42.236580 | E_var:     0.4617 | E_err:   0.010617
[2025-10-02 17:23:16] [Iter  652/2250] R3[252/1200] | LR: 0.027377 | E: -42.260763 | E_var:     0.5086 | E_err:   0.011143
[2025-10-02 17:23:21] [Iter  653/2250] R3[254/1200] | LR: 0.027337 | E: -42.242576 | E_var:     0.5739 | E_err:   0.011837
[2025-10-02 17:23:26] [Iter  654/2250] R3[256/1200] | LR: 0.027296 | E: -42.232359 | E_var:     0.6633 | E_err:   0.012726
[2025-10-02 17:23:31] [Iter  655/2250] R3[258/1200] | LR: 0.027255 | E: -42.216133 | E_var:     0.4405 | E_err:   0.010370
[2025-10-02 17:23:36] [Iter  656/2250] R3[260/1200] | LR: 0.027214 | E: -42.258659 | E_var:     0.6001 | E_err:   0.012104
[2025-10-02 17:23:41] [Iter  657/2250] R3[262/1200] | LR: 0.027173 | E: -42.225266 | E_var:     0.5076 | E_err:   0.011132
[2025-10-02 17:23:47] [Iter  658/2250] R3[264/1200] | LR: 0.027131 | E: -42.232571 | E_var:     0.4481 | E_err:   0.010459
[2025-10-02 17:23:52] [Iter  659/2250] R3[266/1200] | LR: 0.027090 | E: -42.248484 | E_var:     0.4731 | E_err:   0.010747
[2025-10-02 17:23:57] [Iter  660/2250] R3[268/1200] | LR: 0.027047 | E: -42.223837 | E_var:     1.0903 | E_err:   0.016316
[2025-10-02 17:24:02] [Iter  661/2250] R3[270/1200] | LR: 0.027005 | E: -42.241935 | E_var:     0.4311 | E_err:   0.010259
[2025-10-02 17:24:07] [Iter  662/2250] R3[272/1200] | LR: 0.026962 | E: -42.239347 | E_var:     0.4952 | E_err:   0.010995
[2025-10-02 17:24:12] [Iter  663/2250] R3[274/1200] | LR: 0.026920 | E: -42.259818 | E_var:     0.5147 | E_err:   0.011210
[2025-10-02 17:24:17] [Iter  664/2250] R3[276/1200] | LR: 0.026876 | E: -42.254083 | E_var:     0.4984 | E_err:   0.011031
[2025-10-02 17:24:23] [Iter  665/2250] R3[278/1200] | LR: 0.026833 | E: -42.258517 | E_var:     0.5100 | E_err:   0.011159
[2025-10-02 17:24:28] [Iter  666/2250] R3[280/1200] | LR: 0.026789 | E: -42.268629 | E_var:     0.6064 | E_err:   0.012168
[2025-10-02 17:24:33] [Iter  667/2250] R3[282/1200] | LR: 0.026745 | E: -42.240703 | E_var:     0.4089 | E_err:   0.009992
[2025-10-02 17:24:38] [Iter  668/2250] R3[284/1200] | LR: 0.026701 | E: -42.251417 | E_var:     0.5343 | E_err:   0.011422
[2025-10-02 17:24:43] [Iter  669/2250] R3[286/1200] | LR: 0.026657 | E: -42.226376 | E_var:     0.6536 | E_err:   0.012632
[2025-10-02 17:24:48] [Iter  670/2250] R3[288/1200] | LR: 0.026612 | E: -42.230995 | E_var:     0.7984 | E_err:   0.013961
[2025-10-02 17:24:53] [Iter  671/2250] R3[290/1200] | LR: 0.026567 | E: -42.248145 | E_var:     0.5458 | E_err:   0.011544
[2025-10-02 17:24:59] [Iter  672/2250] R3[292/1200] | LR: 0.026522 | E: -42.241972 | E_var:     0.6336 | E_err:   0.012438
[2025-10-02 17:25:04] [Iter  673/2250] R3[294/1200] | LR: 0.026477 | E: -42.239869 | E_var:     0.5089 | E_err:   0.011146
[2025-10-02 17:25:09] [Iter  674/2250] R3[296/1200] | LR: 0.026431 | E: -42.255240 | E_var:     0.4300 | E_err:   0.010246
[2025-10-02 17:25:14] [Iter  675/2250] R3[298/1200] | LR: 0.026385 | E: -42.226265 | E_var:     0.4874 | E_err:   0.010908
[2025-10-02 17:25:19] [Iter  676/2250] R3[300/1200] | LR: 0.026339 | E: -42.223558 | E_var:     0.5960 | E_err:   0.012063
[2025-10-02 17:25:24] [Iter  677/2250] R3[302/1200] | LR: 0.026292 | E: -42.242437 | E_var:     0.5630 | E_err:   0.011724
[2025-10-02 17:25:29] [Iter  678/2250] R3[304/1200] | LR: 0.026246 | E: -42.231312 | E_var:     0.6543 | E_err:   0.012639
[2025-10-02 17:25:35] [Iter  679/2250] R3[306/1200] | LR: 0.026199 | E: -42.229731 | E_var:     0.6521 | E_err:   0.012617
[2025-10-02 17:25:40] [Iter  680/2250] R3[308/1200] | LR: 0.026152 | E: -42.243540 | E_var:     0.9518 | E_err:   0.015244
[2025-10-02 17:25:45] [Iter  681/2250] R3[310/1200] | LR: 0.026104 | E: -42.268868 | E_var:     0.4252 | E_err:   0.010188
[2025-10-02 17:25:50] [Iter  682/2250] R3[312/1200] | LR: 0.026057 | E: -42.237984 | E_var:     0.5513 | E_err:   0.011601
[2025-10-02 17:25:55] [Iter  683/2250] R3[314/1200] | LR: 0.026009 | E: -42.228134 | E_var:     0.5340 | E_err:   0.011418
[2025-10-02 17:26:00] [Iter  684/2250] R3[316/1200] | LR: 0.025961 | E: -42.249172 | E_var:     0.4640 | E_err:   0.010644
[2025-10-02 17:26:05] [Iter  685/2250] R3[318/1200] | LR: 0.025913 | E: -42.237465 | E_var:     0.6797 | E_err:   0.012882
[2025-10-02 17:26:11] [Iter  686/2250] R3[320/1200] | LR: 0.025864 | E: -42.252162 | E_var:     0.4994 | E_err:   0.011041
[2025-10-02 17:26:16] [Iter  687/2250] R3[322/1200] | LR: 0.025815 | E: -42.251020 | E_var:     0.5263 | E_err:   0.011336
[2025-10-02 17:26:21] [Iter  688/2250] R3[324/1200] | LR: 0.025766 | E: -42.229735 | E_var:     0.5560 | E_err:   0.011650
[2025-10-02 17:26:26] [Iter  689/2250] R3[326/1200] | LR: 0.025717 | E: -42.230362 | E_var:     0.6857 | E_err:   0.012938
[2025-10-02 17:26:31] [Iter  690/2250] R3[328/1200] | LR: 0.025668 | E: -42.255084 | E_var:     0.4932 | E_err:   0.010974
[2025-10-02 17:26:36] [Iter  691/2250] R3[330/1200] | LR: 0.025618 | E: -42.265726 | E_var:     0.4491 | E_err:   0.010471
[2025-10-02 17:26:42] [Iter  692/2250] R3[332/1200] | LR: 0.025568 | E: -42.263063 | E_var:     0.4664 | E_err:   0.010671
[2025-10-02 17:26:47] [Iter  693/2250] R3[334/1200] | LR: 0.025518 | E: -42.222235 | E_var:     0.4920 | E_err:   0.010960
[2025-10-02 17:26:52] [Iter  694/2250] R3[336/1200] | LR: 0.025468 | E: -42.253207 | E_var:     0.5724 | E_err:   0.011822
[2025-10-02 17:26:57] [Iter  695/2250] R3[338/1200] | LR: 0.025417 | E: -42.259235 | E_var:     0.4832 | E_err:   0.010862
[2025-10-02 17:27:02] [Iter  696/2250] R3[340/1200] | LR: 0.025367 | E: -42.241066 | E_var:     0.5324 | E_err:   0.011400
[2025-10-02 17:27:07] [Iter  697/2250] R3[342/1200] | LR: 0.025316 | E: -42.232950 | E_var:     0.5572 | E_err:   0.011663
[2025-10-02 17:27:12] [Iter  698/2250] R3[344/1200] | LR: 0.025264 | E: -42.265719 | E_var:     0.4923 | E_err:   0.010963
[2025-10-02 17:27:18] [Iter  699/2250] R3[346/1200] | LR: 0.025213 | E: -42.234388 | E_var:     0.4102 | E_err:   0.010007
[2025-10-02 17:27:23] [Iter  700/2250] R3[348/1200] | LR: 0.025161 | E: -42.260707 | E_var:     0.4977 | E_err:   0.011023
[2025-10-02 17:27:28] [Iter  701/2250] R3[350/1200] | LR: 0.025110 | E: -42.212127 | E_var:     0.5204 | E_err:   0.011271
[2025-10-02 17:27:33] [Iter  702/2250] R3[352/1200] | LR: 0.025057 | E: -42.236933 | E_var:     0.5222 | E_err:   0.011291
[2025-10-02 17:27:38] [Iter  703/2250] R3[354/1200] | LR: 0.025005 | E: -42.216223 | E_var:     0.4659 | E_err:   0.010665
[2025-10-02 17:27:43] [Iter  704/2250] R3[356/1200] | LR: 0.024953 | E: -42.243792 | E_var:     0.4821 | E_err:   0.010849
[2025-10-02 17:27:48] [Iter  705/2250] R3[358/1200] | LR: 0.024900 | E: -42.220763 | E_var:     1.0457 | E_err:   0.015978
[2025-10-02 17:27:54] [Iter  706/2250] R3[360/1200] | LR: 0.024847 | E: -42.249812 | E_var:     0.4831 | E_err:   0.010860
[2025-10-02 17:27:59] [Iter  707/2250] R3[362/1200] | LR: 0.024794 | E: -42.233427 | E_var:     0.6521 | E_err:   0.012618
[2025-10-02 17:28:04] [Iter  708/2250] R3[364/1200] | LR: 0.024741 | E: -42.256438 | E_var:     0.5623 | E_err:   0.011717
[2025-10-02 17:28:09] [Iter  709/2250] R3[366/1200] | LR: 0.024688 | E: -42.245074 | E_var:     0.5812 | E_err:   0.011912
[2025-10-02 17:28:14] [Iter  710/2250] R3[368/1200] | LR: 0.024634 | E: -42.243549 | E_var:     0.4587 | E_err:   0.010583
[2025-10-02 17:28:19] [Iter  711/2250] R3[370/1200] | LR: 0.024580 | E: -42.250368 | E_var:     0.5311 | E_err:   0.011387
[2025-10-02 17:28:24] [Iter  712/2250] R3[372/1200] | LR: 0.024526 | E: -42.255529 | E_var:     0.4325 | E_err:   0.010276
[2025-10-02 17:28:30] [Iter  713/2250] R3[374/1200] | LR: 0.024472 | E: -42.250039 | E_var:     0.4199 | E_err:   0.010125
[2025-10-02 17:28:35] [Iter  714/2250] R3[376/1200] | LR: 0.024417 | E: -42.250540 | E_var:     0.5496 | E_err:   0.011584
[2025-10-02 17:28:40] [Iter  715/2250] R3[378/1200] | LR: 0.024363 | E: -42.252465 | E_var:     0.5560 | E_err:   0.011651
[2025-10-02 17:28:45] [Iter  716/2250] R3[380/1200] | LR: 0.024308 | E: -42.265223 | E_var:     0.4658 | E_err:   0.010664
[2025-10-02 17:28:50] [Iter  717/2250] R3[382/1200] | LR: 0.024253 | E: -42.250705 | E_var:     0.5366 | E_err:   0.011445
[2025-10-02 17:28:55] [Iter  718/2250] R3[384/1200] | LR: 0.024198 | E: -42.253952 | E_var:     0.4228 | E_err:   0.010159
[2025-10-02 17:29:00] [Iter  719/2250] R3[386/1200] | LR: 0.024142 | E: -42.250871 | E_var:     0.3922 | E_err:   0.009785
[2025-10-02 17:29:06] [Iter  720/2250] R3[388/1200] | LR: 0.024087 | E: -42.226825 | E_var:     0.5329 | E_err:   0.011406
[2025-10-02 17:29:11] [Iter  721/2250] R3[390/1200] | LR: 0.024031 | E: -42.246101 | E_var:     0.4835 | E_err:   0.010865
[2025-10-02 17:29:16] [Iter  722/2250] R3[392/1200] | LR: 0.023975 | E: -42.249132 | E_var:     0.4509 | E_err:   0.010492
[2025-10-02 17:29:21] [Iter  723/2250] R3[394/1200] | LR: 0.023919 | E: -42.240746 | E_var:     0.5363 | E_err:   0.011442
[2025-10-02 17:29:26] [Iter  724/2250] R3[396/1200] | LR: 0.023863 | E: -42.252133 | E_var:     0.5328 | E_err:   0.011405
[2025-10-02 17:29:31] [Iter  725/2250] R3[398/1200] | LR: 0.023807 | E: -42.233176 | E_var:     0.5033 | E_err:   0.011085
[2025-10-02 17:29:36] [Iter  726/2250] R3[400/1200] | LR: 0.023750 | E: -42.252655 | E_var:     0.5373 | E_err:   0.011453
[2025-10-02 17:29:42] [Iter  727/2250] R3[402/1200] | LR: 0.023693 | E: -42.255628 | E_var:     0.6585 | E_err:   0.012679
[2025-10-02 17:29:47] [Iter  728/2250] R3[404/1200] | LR: 0.023636 | E: -42.224848 | E_var:     0.8726 | E_err:   0.014596
[2025-10-02 17:29:52] [Iter  729/2250] R3[406/1200] | LR: 0.023579 | E: -42.260601 | E_var:     0.5482 | E_err:   0.011568
[2025-10-02 17:29:57] [Iter  730/2250] R3[408/1200] | LR: 0.023522 | E: -42.250667 | E_var:     0.4617 | E_err:   0.010616
[2025-10-02 17:30:02] [Iter  731/2250] R3[410/1200] | LR: 0.023464 | E: -42.245598 | E_var:     0.4875 | E_err:   0.010910
[2025-10-02 17:30:07] [Iter  732/2250] R3[412/1200] | LR: 0.023407 | E: -42.255272 | E_var:     0.4188 | E_err:   0.010111
[2025-10-02 17:30:13] [Iter  733/2250] R3[414/1200] | LR: 0.023349 | E: -42.260593 | E_var:     0.5133 | E_err:   0.011195
[2025-10-02 17:30:18] [Iter  734/2250] R3[416/1200] | LR: 0.023291 | E: -42.238919 | E_var:     0.4588 | E_err:   0.010583
[2025-10-02 17:30:23] [Iter  735/2250] R3[418/1200] | LR: 0.023233 | E: -42.251933 | E_var:     0.4648 | E_err:   0.010652
[2025-10-02 17:30:28] [Iter  736/2250] R3[420/1200] | LR: 0.023175 | E: -42.232660 | E_var:     0.5379 | E_err:   0.011459
[2025-10-02 17:30:33] [Iter  737/2250] R3[422/1200] | LR: 0.023116 | E: -42.273087 | E_var:     0.8255 | E_err:   0.014197
[2025-10-02 17:30:38] [Iter  738/2250] R3[424/1200] | LR: 0.023058 | E: -42.251652 | E_var:     0.5451 | E_err:   0.011536
[2025-10-02 17:30:43] [Iter  739/2250] R3[426/1200] | LR: 0.022999 | E: -42.250250 | E_var:     0.4598 | E_err:   0.010595
[2025-10-02 17:30:49] [Iter  740/2250] R3[428/1200] | LR: 0.022940 | E: -42.254597 | E_var:     0.5558 | E_err:   0.011649
[2025-10-02 17:30:54] [Iter  741/2250] R3[430/1200] | LR: 0.022881 | E: -42.246411 | E_var:     0.8467 | E_err:   0.014378
[2025-10-02 17:30:59] [Iter  742/2250] R3[432/1200] | LR: 0.022822 | E: -42.249629 | E_var:     0.5125 | E_err:   0.011186
[2025-10-02 17:31:04] [Iter  743/2250] R3[434/1200] | LR: 0.022763 | E: -42.256920 | E_var:     0.5280 | E_err:   0.011354
[2025-10-02 17:31:09] [Iter  744/2250] R3[436/1200] | LR: 0.022704 | E: -42.258806 | E_var:     0.5174 | E_err:   0.011239
[2025-10-02 17:31:14] [Iter  745/2250] R3[438/1200] | LR: 0.022644 | E: -42.254687 | E_var:     0.4377 | E_err:   0.010337
[2025-10-02 17:31:19] [Iter  746/2250] R3[440/1200] | LR: 0.022584 | E: -42.253269 | E_var:     0.5427 | E_err:   0.011510
[2025-10-02 17:31:25] [Iter  747/2250] R3[442/1200] | LR: 0.022524 | E: -42.251421 | E_var:     0.4857 | E_err:   0.010889
[2025-10-02 17:31:30] [Iter  748/2250] R3[444/1200] | LR: 0.022464 | E: -42.233764 | E_var:     0.4729 | E_err:   0.010745
[2025-10-02 17:31:35] [Iter  749/2250] R3[446/1200] | LR: 0.022404 | E: -42.259924 | E_var:     0.4784 | E_err:   0.010807
[2025-10-02 17:31:40] [Iter  750/2250] R3[448/1200] | LR: 0.022344 | E: -42.251889 | E_var:     0.4716 | E_err:   0.010730
[2025-10-02 17:31:45] [Iter  751/2250] R3[450/1200] | LR: 0.022284 | E: -42.262197 | E_var:     0.4846 | E_err:   0.010878
[2025-10-02 17:31:50] [Iter  752/2250] R3[452/1200] | LR: 0.022223 | E: -42.261869 | E_var:     0.7843 | E_err:   0.013838
[2025-10-02 17:31:55] [Iter  753/2250] R3[454/1200] | LR: 0.022162 | E: -42.238436 | E_var:     0.5851 | E_err:   0.011952
[2025-10-02 17:32:01] [Iter  754/2250] R3[456/1200] | LR: 0.022102 | E: -42.245118 | E_var:     0.5158 | E_err:   0.011222
[2025-10-02 17:32:06] [Iter  755/2250] R3[458/1200] | LR: 0.022041 | E: -42.247052 | E_var:     0.6863 | E_err:   0.012944
[2025-10-02 17:32:11] [Iter  756/2250] R3[460/1200] | LR: 0.021980 | E: -42.245757 | E_var:     0.6138 | E_err:   0.012242
[2025-10-02 17:32:16] [Iter  757/2250] R3[462/1200] | LR: 0.021918 | E: -42.234956 | E_var:     0.5206 | E_err:   0.011274
[2025-10-02 17:32:21] [Iter  758/2250] R3[464/1200] | LR: 0.021857 | E: -42.252811 | E_var:     0.6743 | E_err:   0.012830
[2025-10-02 17:32:26] [Iter  759/2250] R3[466/1200] | LR: 0.021796 | E: -42.252684 | E_var:     0.5335 | E_err:   0.011412
[2025-10-02 17:32:31] [Iter  760/2250] R3[468/1200] | LR: 0.021734 | E: -42.245084 | E_var:     0.5187 | E_err:   0.011253
[2025-10-02 17:32:37] [Iter  761/2250] R3[470/1200] | LR: 0.021673 | E: -42.249478 | E_var:     0.5907 | E_err:   0.012009
[2025-10-02 17:32:42] [Iter  762/2250] R3[472/1200] | LR: 0.021611 | E: -42.247638 | E_var:     0.5825 | E_err:   0.011926
[2025-10-02 17:32:47] [Iter  763/2250] R3[474/1200] | LR: 0.021549 | E: -42.244060 | E_var:     0.5748 | E_err:   0.011846
[2025-10-02 17:32:52] [Iter  764/2250] R3[476/1200] | LR: 0.021487 | E: -42.265408 | E_var:     0.4125 | E_err:   0.010036
[2025-10-02 17:32:57] [Iter  765/2250] R3[478/1200] | LR: 0.021425 | E: -42.253354 | E_var:     0.5624 | E_err:   0.011718
[2025-10-02 17:33:02] [Iter  766/2250] R3[480/1200] | LR: 0.021363 | E: -42.258202 | E_var:     0.5464 | E_err:   0.011549
[2025-10-02 17:33:07] [Iter  767/2250] R3[482/1200] | LR: 0.021300 | E: -42.274309 | E_var:     0.4673 | E_err:   0.010682
[2025-10-02 17:33:13] [Iter  768/2250] R3[484/1200] | LR: 0.021238 | E: -42.237325 | E_var:     0.4560 | E_err:   0.010551
[2025-10-02 17:33:18] [Iter  769/2250] R3[486/1200] | LR: 0.021176 | E: -42.250734 | E_var:     0.4826 | E_err:   0.010855
[2025-10-02 17:33:23] [Iter  770/2250] R3[488/1200] | LR: 0.021113 | E: -42.247247 | E_var:     0.4121 | E_err:   0.010031
[2025-10-02 17:33:28] [Iter  771/2250] R3[490/1200] | LR: 0.021050 | E: -42.252934 | E_var:     0.4531 | E_err:   0.010518
[2025-10-02 17:33:33] [Iter  772/2250] R3[492/1200] | LR: 0.020987 | E: -42.264192 | E_var:     0.5693 | E_err:   0.011789
[2025-10-02 17:33:38] [Iter  773/2250] R3[494/1200] | LR: 0.020924 | E: -42.256334 | E_var:     0.5620 | E_err:   0.011713
[2025-10-02 17:33:44] [Iter  774/2250] R3[496/1200] | LR: 0.020861 | E: -42.256033 | E_var:     0.4347 | E_err:   0.010302
[2025-10-02 17:33:49] [Iter  775/2250] R3[498/1200] | LR: 0.020798 | E: -42.241073 | E_var:     0.4798 | E_err:   0.010823
[2025-10-02 17:33:54] [Iter  776/2250] R3[500/1200] | LR: 0.020735 | E: -42.261705 | E_var:     0.4022 | E_err:   0.009909
[2025-10-02 17:33:59] [Iter  777/2250] R3[502/1200] | LR: 0.020672 | E: -42.270879 | E_var:     0.5725 | E_err:   0.011823
[2025-10-02 17:34:04] [Iter  778/2250] R3[504/1200] | LR: 0.020609 | E: -42.246185 | E_var:     0.4097 | E_err:   0.010002
[2025-10-02 17:34:09] [Iter  779/2250] R3[506/1200] | LR: 0.020545 | E: -42.248302 | E_var:     0.4588 | E_err:   0.010583
[2025-10-02 17:34:14] [Iter  780/2250] R3[508/1200] | LR: 0.020482 | E: -42.235310 | E_var:     0.4627 | E_err:   0.010628
[2025-10-02 17:34:20] [Iter  781/2250] R3[510/1200] | LR: 0.020418 | E: -42.241780 | E_var:     0.5065 | E_err:   0.011120
[2025-10-02 17:34:25] [Iter  782/2250] R3[512/1200] | LR: 0.020354 | E: -42.245480 | E_var:     0.5761 | E_err:   0.011859
[2025-10-02 17:34:30] [Iter  783/2250] R3[514/1200] | LR: 0.020291 | E: -42.255643 | E_var:     0.4501 | E_err:   0.010483
[2025-10-02 17:34:35] [Iter  784/2250] R3[516/1200] | LR: 0.020227 | E: -42.245008 | E_var:     0.5361 | E_err:   0.011440
[2025-10-02 17:34:40] [Iter  785/2250] R3[518/1200] | LR: 0.020163 | E: -42.275980 | E_var:     0.5540 | E_err:   0.011630
[2025-10-02 17:34:45] [Iter  786/2250] R3[520/1200] | LR: 0.020099 | E: -42.270702 | E_var:     0.6167 | E_err:   0.012270
[2025-10-02 17:34:50] [Iter  787/2250] R3[522/1200] | LR: 0.020035 | E: -42.246540 | E_var:     0.4909 | E_err:   0.010948
[2025-10-02 17:34:56] [Iter  788/2250] R3[524/1200] | LR: 0.019971 | E: -42.245185 | E_var:     0.4609 | E_err:   0.010608
[2025-10-02 17:35:01] [Iter  789/2250] R3[526/1200] | LR: 0.019907 | E: -42.273772 | E_var:     0.5047 | E_err:   0.011101
[2025-10-02 17:35:06] [Iter  790/2250] R3[528/1200] | LR: 0.019842 | E: -42.242390 | E_var:     0.4508 | E_err:   0.010491
[2025-10-02 17:35:11] [Iter  791/2250] R3[530/1200] | LR: 0.019778 | E: -42.254996 | E_var:     0.4743 | E_err:   0.010760
[2025-10-02 17:35:16] [Iter  792/2250] R3[532/1200] | LR: 0.019714 | E: -42.264156 | E_var:     0.4324 | E_err:   0.010274
[2025-10-02 17:35:21] [Iter  793/2250] R3[534/1200] | LR: 0.019649 | E: -42.257605 | E_var:     0.4898 | E_err:   0.010935
[2025-10-02 17:35:26] [Iter  794/2250] R3[536/1200] | LR: 0.019585 | E: -42.272954 | E_var:     0.5081 | E_err:   0.011138
[2025-10-02 17:35:32] [Iter  795/2250] R3[538/1200] | LR: 0.019520 | E: -42.253431 | E_var:     0.5479 | E_err:   0.011566
[2025-10-02 17:35:37] [Iter  796/2250] R3[540/1200] | LR: 0.019455 | E: -42.263201 | E_var:     0.5042 | E_err:   0.011094
[2025-10-02 17:35:42] [Iter  797/2250] R3[542/1200] | LR: 0.019391 | E: -42.242266 | E_var:     0.5063 | E_err:   0.011118
[2025-10-02 17:35:47] [Iter  798/2250] R3[544/1200] | LR: 0.019326 | E: -42.248396 | E_var:     0.6645 | E_err:   0.012737
[2025-10-02 17:35:52] [Iter  799/2250] R3[546/1200] | LR: 0.019261 | E: -42.252823 | E_var:     0.4819 | E_err:   0.010847
[2025-10-02 17:35:57] [Iter  800/2250] R3[548/1200] | LR: 0.019196 | E: -42.252881 | E_var:     0.3992 | E_err:   0.009872
[2025-10-02 17:35:57] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-02 17:36:02] [Iter  801/2250] R3[550/1200] | LR: 0.019132 | E: -42.262804 | E_var:     0.3761 | E_err:   0.009583
[2025-10-02 17:36:08] [Iter  802/2250] R3[552/1200] | LR: 0.019067 | E: -42.254542 | E_var:     0.4523 | E_err:   0.010508
[2025-10-02 17:36:13] [Iter  803/2250] R3[554/1200] | LR: 0.019002 | E: -42.262583 | E_var:     0.4517 | E_err:   0.010502
[2025-10-02 17:36:18] [Iter  804/2250] R3[556/1200] | LR: 0.018937 | E: -42.256208 | E_var:     0.4785 | E_err:   0.010809
[2025-10-02 17:36:23] [Iter  805/2250] R3[558/1200] | LR: 0.018872 | E: -42.249108 | E_var:     0.5867 | E_err:   0.011968
[2025-10-02 17:36:28] [Iter  806/2250] R3[560/1200] | LR: 0.018807 | E: -42.263735 | E_var:     0.5456 | E_err:   0.011541
[2025-10-02 17:36:33] [Iter  807/2250] R3[562/1200] | LR: 0.018741 | E: -42.268458 | E_var:     0.5155 | E_err:   0.011218
[2025-10-02 17:36:38] [Iter  808/2250] R3[564/1200] | LR: 0.018676 | E: -42.261543 | E_var:     0.3966 | E_err:   0.009840
[2025-10-02 17:36:44] [Iter  809/2250] R3[566/1200] | LR: 0.018611 | E: -42.244801 | E_var:     0.4820 | E_err:   0.010848
[2025-10-02 17:36:49] [Iter  810/2250] R3[568/1200] | LR: 0.018546 | E: -42.264948 | E_var:     0.4521 | E_err:   0.010506
[2025-10-02 17:36:54] [Iter  811/2250] R3[570/1200] | LR: 0.018481 | E: -42.253567 | E_var:     0.4791 | E_err:   0.010816
[2025-10-02 17:36:59] [Iter  812/2250] R3[572/1200] | LR: 0.018415 | E: -42.259620 | E_var:     1.0693 | E_err:   0.016158
[2025-10-02 17:37:04] [Iter  813/2250] R3[574/1200] | LR: 0.018350 | E: -42.270047 | E_var:     0.5498 | E_err:   0.011586
[2025-10-02 17:37:09] [Iter  814/2250] R3[576/1200] | LR: 0.018285 | E: -42.256180 | E_var:     0.4456 | E_err:   0.010430
[2025-10-02 17:37:14] [Iter  815/2250] R3[578/1200] | LR: 0.018220 | E: -42.266320 | E_var:     0.5609 | E_err:   0.011702
[2025-10-02 17:37:20] [Iter  816/2250] R3[580/1200] | LR: 0.018154 | E: -42.246846 | E_var:     0.5472 | E_err:   0.011558
[2025-10-02 17:37:25] [Iter  817/2250] R3[582/1200] | LR: 0.018089 | E: -42.255894 | E_var:     0.4396 | E_err:   0.010359
[2025-10-02 17:37:30] [Iter  818/2250] R3[584/1200] | LR: 0.018023 | E: -42.251504 | E_var:     0.4709 | E_err:   0.010722
[2025-10-02 17:37:35] [Iter  819/2250] R3[586/1200] | LR: 0.017958 | E: -42.256733 | E_var:     0.4552 | E_err:   0.010542
[2025-10-02 17:37:40] [Iter  820/2250] R3[588/1200] | LR: 0.017893 | E: -42.267520 | E_var:     0.5903 | E_err:   0.012005
[2025-10-02 17:37:45] [Iter  821/2250] R3[590/1200] | LR: 0.017827 | E: -42.255776 | E_var:     0.4318 | E_err:   0.010267
[2025-10-02 17:37:51] [Iter  822/2250] R3[592/1200] | LR: 0.017762 | E: -42.238601 | E_var:     0.5197 | E_err:   0.011264
[2025-10-02 17:37:56] [Iter  823/2250] R3[594/1200] | LR: 0.017696 | E: -42.264250 | E_var:     0.5614 | E_err:   0.011707
[2025-10-02 17:38:01] [Iter  824/2250] R3[596/1200] | LR: 0.017631 | E: -42.247069 | E_var:     0.5494 | E_err:   0.011582
[2025-10-02 17:38:06] [Iter  825/2250] R3[598/1200] | LR: 0.017565 | E: -42.275240 | E_var:     0.5146 | E_err:   0.011208
[2025-10-02 17:38:11] [Iter  826/2250] R3[600/1200] | LR: 0.017500 | E: -42.248942 | E_var:     0.5886 | E_err:   0.011987
[2025-10-02 17:38:16] [Iter  827/2250] R3[602/1200] | LR: 0.017435 | E: -42.250687 | E_var:     0.5868 | E_err:   0.011969
[2025-10-02 17:38:21] [Iter  828/2250] R3[604/1200] | LR: 0.017369 | E: -42.270925 | E_var:     0.4921 | E_err:   0.010961
[2025-10-02 17:38:27] [Iter  829/2250] R3[606/1200] | LR: 0.017304 | E: -42.239224 | E_var:     0.5756 | E_err:   0.011855
[2025-10-02 17:38:32] [Iter  830/2250] R3[608/1200] | LR: 0.017238 | E: -42.252107 | E_var:     0.4593 | E_err:   0.010590
[2025-10-02 17:38:37] [Iter  831/2250] R3[610/1200] | LR: 0.017173 | E: -42.256429 | E_var:     0.4940 | E_err:   0.010983
[2025-10-02 17:38:42] [Iter  832/2250] R3[612/1200] | LR: 0.017107 | E: -42.260069 | E_var:     0.6345 | E_err:   0.012446
[2025-10-02 17:38:47] [Iter  833/2250] R3[614/1200] | LR: 0.017042 | E: -42.277708 | E_var:     0.4331 | E_err:   0.010283
[2025-10-02 17:38:52] [Iter  834/2250] R3[616/1200] | LR: 0.016977 | E: -42.257739 | E_var:     0.6224 | E_err:   0.012326
[2025-10-02 17:38:57] [Iter  835/2250] R3[618/1200] | LR: 0.016911 | E: -42.255563 | E_var:     0.5527 | E_err:   0.011617
[2025-10-02 17:39:03] [Iter  836/2250] R3[620/1200] | LR: 0.016846 | E: -42.263650 | E_var:     0.4363 | E_err:   0.010320
[2025-10-02 17:39:08] [Iter  837/2250] R3[622/1200] | LR: 0.016780 | E: -42.261634 | E_var:     0.6292 | E_err:   0.012394
[2025-10-02 17:39:13] [Iter  838/2250] R3[624/1200] | LR: 0.016715 | E: -42.251408 | E_var:     0.4068 | E_err:   0.009966
[2025-10-02 17:39:18] [Iter  839/2250] R3[626/1200] | LR: 0.016650 | E: -42.264274 | E_var:     0.4680 | E_err:   0.010689
[2025-10-02 17:39:23] [Iter  840/2250] R3[628/1200] | LR: 0.016585 | E: -42.261411 | E_var:     0.4170 | E_err:   0.010091
[2025-10-02 17:39:28] [Iter  841/2250] R3[630/1200] | LR: 0.016519 | E: -42.240257 | E_var:     0.4716 | E_err:   0.010730
[2025-10-02 17:39:33] [Iter  842/2250] R3[632/1200] | LR: 0.016454 | E: -42.253862 | E_var:     0.4620 | E_err:   0.010620
[2025-10-02 17:39:39] [Iter  843/2250] R3[634/1200] | LR: 0.016389 | E: -42.270627 | E_var:     0.4155 | E_err:   0.010072
[2025-10-02 17:39:44] [Iter  844/2250] R3[636/1200] | LR: 0.016324 | E: -42.243929 | E_var:     0.4385 | E_err:   0.010347
[2025-10-02 17:39:49] [Iter  845/2250] R3[638/1200] | LR: 0.016259 | E: -42.297898 | E_var:     0.4974 | E_err:   0.011020
[2025-10-02 17:39:54] [Iter  846/2250] R3[640/1200] | LR: 0.016193 | E: -42.262207 | E_var:     0.4644 | E_err:   0.010648
[2025-10-02 17:39:59] [Iter  847/2250] R3[642/1200] | LR: 0.016128 | E: -42.261169 | E_var:     0.4757 | E_err:   0.010776
[2025-10-02 17:40:04] [Iter  848/2250] R3[644/1200] | LR: 0.016063 | E: -42.266006 | E_var:     0.4770 | E_err:   0.010791
[2025-10-02 17:40:09] [Iter  849/2250] R3[646/1200] | LR: 0.015998 | E: -42.251698 | E_var:     0.4394 | E_err:   0.010358
[2025-10-02 17:40:15] [Iter  850/2250] R3[648/1200] | LR: 0.015933 | E: -42.253949 | E_var:     0.5187 | E_err:   0.011253
[2025-10-02 17:40:20] [Iter  851/2250] R3[650/1200] | LR: 0.015868 | E: -42.269322 | E_var:     0.5857 | E_err:   0.011958
[2025-10-02 17:40:25] [Iter  852/2250] R3[652/1200] | LR: 0.015804 | E: -42.256989 | E_var:     0.5045 | E_err:   0.011098
[2025-10-02 17:40:30] [Iter  853/2250] R3[654/1200] | LR: 0.015739 | E: -42.239733 | E_var:     0.5284 | E_err:   0.011358
[2025-10-02 17:40:35] [Iter  854/2250] R3[656/1200] | LR: 0.015674 | E: -42.260119 | E_var:     0.4322 | E_err:   0.010272
[2025-10-02 17:40:40] [Iter  855/2250] R3[658/1200] | LR: 0.015609 | E: -42.263097 | E_var:     0.4270 | E_err:   0.010210
[2025-10-02 17:40:45] [Iter  856/2250] R3[660/1200] | LR: 0.015545 | E: -42.270849 | E_var:     0.4107 | E_err:   0.010013
[2025-10-02 17:40:51] [Iter  857/2250] R3[662/1200] | LR: 0.015480 | E: -42.248634 | E_var:     0.4150 | E_err:   0.010066
[2025-10-02 17:40:56] [Iter  858/2250] R3[664/1200] | LR: 0.015415 | E: -42.260355 | E_var:     0.4556 | E_err:   0.010547
[2025-10-02 17:41:01] [Iter  859/2250] R3[666/1200] | LR: 0.015351 | E: -42.269291 | E_var:     0.6384 | E_err:   0.012484
[2025-10-02 17:41:06] [Iter  860/2250] R3[668/1200] | LR: 0.015286 | E: -42.264022 | E_var:     0.5071 | E_err:   0.011126
[2025-10-02 17:41:11] [Iter  861/2250] R3[670/1200] | LR: 0.015222 | E: -42.252999 | E_var:     0.4921 | E_err:   0.010961
[2025-10-02 17:41:16] [Iter  862/2250] R3[672/1200] | LR: 0.015158 | E: -42.261483 | E_var:     0.4297 | E_err:   0.010243
[2025-10-02 17:41:21] [Iter  863/2250] R3[674/1200] | LR: 0.015093 | E: -42.252622 | E_var:     0.4735 | E_err:   0.010752
[2025-10-02 17:41:27] [Iter  864/2250] R3[676/1200] | LR: 0.015029 | E: -42.256848 | E_var:     0.3765 | E_err:   0.009588
[2025-10-02 17:41:32] [Iter  865/2250] R3[678/1200] | LR: 0.014965 | E: -42.270912 | E_var:     1.3612 | E_err:   0.018230
[2025-10-02 17:41:37] [Iter  866/2250] R3[680/1200] | LR: 0.014901 | E: -42.281245 | E_var:     0.7519 | E_err:   0.013549
[2025-10-02 17:41:42] [Iter  867/2250] R3[682/1200] | LR: 0.014837 | E: -42.259559 | E_var:     0.6769 | E_err:   0.012856
[2025-10-02 17:41:47] [Iter  868/2250] R3[684/1200] | LR: 0.014773 | E: -42.272112 | E_var:     0.6846 | E_err:   0.012928
[2025-10-02 17:41:52] [Iter  869/2250] R3[686/1200] | LR: 0.014709 | E: -42.265351 | E_var:     0.3717 | E_err:   0.009527
[2025-10-02 17:41:58] [Iter  870/2250] R3[688/1200] | LR: 0.014646 | E: -42.269201 | E_var:     0.5693 | E_err:   0.011790
[2025-10-02 17:42:03] [Iter  871/2250] R3[690/1200] | LR: 0.014582 | E: -42.253702 | E_var:     0.5304 | E_err:   0.011379
[2025-10-02 17:42:08] [Iter  872/2250] R3[692/1200] | LR: 0.014518 | E: -42.270795 | E_var:     0.4673 | E_err:   0.010681
[2025-10-02 17:42:13] [Iter  873/2250] R3[694/1200] | LR: 0.014455 | E: -42.261832 | E_var:     0.5312 | E_err:   0.011388
[2025-10-02 17:42:18] [Iter  874/2250] R3[696/1200] | LR: 0.014391 | E: -42.281053 | E_var:     0.5709 | E_err:   0.011806
[2025-10-02 17:42:23] [Iter  875/2250] R3[698/1200] | LR: 0.014328 | E: -42.270741 | E_var:     0.6292 | E_err:   0.012394
[2025-10-02 17:42:28] [Iter  876/2250] R3[700/1200] | LR: 0.014265 | E: -42.277752 | E_var:     0.4476 | E_err:   0.010454
[2025-10-02 17:42:34] [Iter  877/2250] R3[702/1200] | LR: 0.014202 | E: -42.259416 | E_var:     0.4599 | E_err:   0.010596
[2025-10-02 17:42:39] [Iter  878/2250] R3[704/1200] | LR: 0.014139 | E: -42.236657 | E_var:     0.4774 | E_err:   0.010796
[2025-10-02 17:42:44] [Iter  879/2250] R3[706/1200] | LR: 0.014076 | E: -42.260787 | E_var:     1.6704 | E_err:   0.020195
[2025-10-02 17:42:49] [Iter  880/2250] R3[708/1200] | LR: 0.014013 | E: -42.253650 | E_var:     1.5679 | E_err:   0.019565
[2025-10-02 17:42:54] [Iter  881/2250] R3[710/1200] | LR: 0.013950 | E: -42.234771 | E_var:     2.1526 | E_err:   0.022925
[2025-10-02 17:42:59] [Iter  882/2250] R3[712/1200] | LR: 0.013887 | E: -42.267698 | E_var:     0.4772 | E_err:   0.010794
[2025-10-02 17:43:04] [Iter  883/2250] R3[714/1200] | LR: 0.013824 | E: -42.276070 | E_var:     0.4031 | E_err:   0.009921
[2025-10-02 17:43:10] [Iter  884/2250] R3[716/1200] | LR: 0.013762 | E: -42.280707 | E_var:     0.4504 | E_err:   0.010486
[2025-10-02 17:43:15] [Iter  885/2250] R3[718/1200] | LR: 0.013700 | E: -42.280789 | E_var:     0.5544 | E_err:   0.011634
[2025-10-02 17:43:20] [Iter  886/2250] R3[720/1200] | LR: 0.013637 | E: -42.268370 | E_var:     0.4305 | E_err:   0.010252
[2025-10-02 17:43:25] [Iter  887/2250] R3[722/1200] | LR: 0.013575 | E: -42.265585 | E_var:     0.4626 | E_err:   0.010627
[2025-10-02 17:43:30] [Iter  888/2250] R3[724/1200] | LR: 0.013513 | E: -42.263867 | E_var:     0.4932 | E_err:   0.010973
[2025-10-02 17:43:35] [Iter  889/2250] R3[726/1200] | LR: 0.013451 | E: -42.252620 | E_var:     0.8724 | E_err:   0.014594
[2025-10-02 17:43:40] [Iter  890/2250] R3[728/1200] | LR: 0.013389 | E: -42.255776 | E_var:     0.5889 | E_err:   0.011991
[2025-10-02 17:43:46] [Iter  891/2250] R3[730/1200] | LR: 0.013327 | E: -42.246575 | E_var:     0.7487 | E_err:   0.013520
[2025-10-02 17:43:51] [Iter  892/2250] R3[732/1200] | LR: 0.013266 | E: -42.281811 | E_var:     0.4751 | E_err:   0.010769
[2025-10-02 17:43:56] [Iter  893/2250] R3[734/1200] | LR: 0.013204 | E: -42.245607 | E_var:     0.5974 | E_err:   0.012077
[2025-10-02 17:44:01] [Iter  894/2250] R3[736/1200] | LR: 0.013143 | E: -42.281748 | E_var:     0.4689 | E_err:   0.010699
[2025-10-02 17:44:06] [Iter  895/2250] R3[738/1200] | LR: 0.013082 | E: -42.263585 | E_var:     0.4441 | E_err:   0.010413
[2025-10-02 17:44:11] [Iter  896/2250] R3[740/1200] | LR: 0.013020 | E: -42.260474 | E_var:     0.6475 | E_err:   0.012573
[2025-10-02 17:44:16] [Iter  897/2250] R3[742/1200] | LR: 0.012959 | E: -42.246653 | E_var:     0.4777 | E_err:   0.010800
[2025-10-02 17:44:22] [Iter  898/2250] R3[744/1200] | LR: 0.012898 | E: -42.266400 | E_var:     0.5300 | E_err:   0.011375
[2025-10-02 17:44:27] [Iter  899/2250] R3[746/1200] | LR: 0.012838 | E: -42.270422 | E_var:     0.5516 | E_err:   0.011604
[2025-10-02 17:44:32] [Iter  900/2250] R3[748/1200] | LR: 0.012777 | E: -42.282436 | E_var:     0.7520 | E_err:   0.013549
[2025-10-02 17:44:37] [Iter  901/2250] R3[750/1200] | LR: 0.012716 | E: -42.265511 | E_var:     0.4293 | E_err:   0.010238
[2025-10-02 17:44:42] [Iter  902/2250] R3[752/1200] | LR: 0.012656 | E: -42.278762 | E_var:     0.4820 | E_err:   0.010847
[2025-10-02 17:44:47] [Iter  903/2250] R3[754/1200] | LR: 0.012596 | E: -42.251836 | E_var:     0.3858 | E_err:   0.009705
[2025-10-02 17:44:52] [Iter  904/2250] R3[756/1200] | LR: 0.012536 | E: -42.277078 | E_var:     0.6449 | E_err:   0.012548
[2025-10-02 17:44:58] [Iter  905/2250] R3[758/1200] | LR: 0.012476 | E: -42.278702 | E_var:     0.4408 | E_err:   0.010374
[2025-10-02 17:45:03] [Iter  906/2250] R3[760/1200] | LR: 0.012416 | E: -42.238914 | E_var:     0.5207 | E_err:   0.011275
[2025-10-02 17:45:08] [Iter  907/2250] R3[762/1200] | LR: 0.012356 | E: -42.280420 | E_var:     0.4406 | E_err:   0.010372
[2025-10-02 17:45:13] [Iter  908/2250] R3[764/1200] | LR: 0.012296 | E: -42.266979 | E_var:     0.5965 | E_err:   0.012068
[2025-10-02 17:45:18] [Iter  909/2250] R3[766/1200] | LR: 0.012237 | E: -42.263993 | E_var:     0.6792 | E_err:   0.012877
[2025-10-02 17:45:23] [Iter  910/2250] R3[768/1200] | LR: 0.012178 | E: -42.258499 | E_var:     0.4107 | E_err:   0.010014
[2025-10-02 17:45:28] [Iter  911/2250] R3[770/1200] | LR: 0.012119 | E: -42.285923 | E_var:     0.4331 | E_err:   0.010283
[2025-10-02 17:45:34] [Iter  912/2250] R3[772/1200] | LR: 0.012060 | E: -42.282349 | E_var:     0.4242 | E_err:   0.010176
[2025-10-02 17:45:39] [Iter  913/2250] R3[774/1200] | LR: 0.012001 | E: -42.272102 | E_var:     0.4198 | E_err:   0.010124
[2025-10-02 17:45:44] [Iter  914/2250] R3[776/1200] | LR: 0.011942 | E: -42.264269 | E_var:     0.5092 | E_err:   0.011150
[2025-10-02 17:45:49] [Iter  915/2250] R3[778/1200] | LR: 0.011884 | E: -42.260248 | E_var:     0.4352 | E_err:   0.010308
[2025-10-02 17:45:54] [Iter  916/2250] R3[780/1200] | LR: 0.011825 | E: -42.263459 | E_var:     0.4191 | E_err:   0.010115
[2025-10-02 17:45:59] [Iter  917/2250] R3[782/1200] | LR: 0.011767 | E: -42.275282 | E_var:     0.6290 | E_err:   0.012392
[2025-10-02 17:46:05] [Iter  918/2250] R3[784/1200] | LR: 0.011709 | E: -42.262761 | E_var:     0.4273 | E_err:   0.010214
[2025-10-02 17:46:10] [Iter  919/2250] R3[786/1200] | LR: 0.011651 | E: -42.256566 | E_var:     0.5063 | E_err:   0.011118
[2025-10-02 17:46:15] [Iter  920/2250] R3[788/1200] | LR: 0.011593 | E: -42.254517 | E_var:     0.6342 | E_err:   0.012443
[2025-10-02 17:46:20] [Iter  921/2250] R3[790/1200] | LR: 0.011536 | E: -42.269183 | E_var:     0.3794 | E_err:   0.009624
[2025-10-02 17:46:25] [Iter  922/2250] R3[792/1200] | LR: 0.011478 | E: -42.286021 | E_var:     0.3967 | E_err:   0.009842
[2025-10-02 17:46:30] [Iter  923/2250] R3[794/1200] | LR: 0.011421 | E: -42.263901 | E_var:     0.5234 | E_err:   0.011305
[2025-10-02 17:46:35] [Iter  924/2250] R3[796/1200] | LR: 0.011364 | E: -42.267922 | E_var:     0.4763 | E_err:   0.010784
[2025-10-02 17:46:41] [Iter  925/2250] R3[798/1200] | LR: 0.011307 | E: -42.254575 | E_var:     1.2798 | E_err:   0.017677
[2025-10-02 17:46:46] [Iter  926/2250] R3[800/1200] | LR: 0.011250 | E: -42.283979 | E_var:     0.5510 | E_err:   0.011598
[2025-10-02 17:46:51] [Iter  927/2250] R3[802/1200] | LR: 0.011193 | E: -42.236649 | E_var:     0.8273 | E_err:   0.014212
[2025-10-02 17:46:56] [Iter  928/2250] R3[804/1200] | LR: 0.011137 | E: -42.287340 | E_var:     0.5539 | E_err:   0.011629
[2025-10-02 17:47:01] [Iter  929/2250] R3[806/1200] | LR: 0.011081 | E: -42.275502 | E_var:     0.5247 | E_err:   0.011319
[2025-10-02 17:47:06] [Iter  930/2250] R3[808/1200] | LR: 0.011025 | E: -42.270169 | E_var:     0.4738 | E_err:   0.010755
[2025-10-02 17:47:11] [Iter  931/2250] R3[810/1200] | LR: 0.010969 | E: -42.265780 | E_var:     0.5126 | E_err:   0.011187
[2025-10-02 17:47:17] [Iter  932/2250] R3[812/1200] | LR: 0.010913 | E: -42.253131 | E_var:     0.5069 | E_err:   0.011125
[2025-10-02 17:47:22] [Iter  933/2250] R3[814/1200] | LR: 0.010858 | E: -42.268993 | E_var:     0.4349 | E_err:   0.010304
[2025-10-02 17:47:27] [Iter  934/2250] R3[816/1200] | LR: 0.010802 | E: -42.269652 | E_var:     0.6502 | E_err:   0.012599
[2025-10-02 17:47:32] [Iter  935/2250] R3[818/1200] | LR: 0.010747 | E: -42.280308 | E_var:     0.4199 | E_err:   0.010125
[2025-10-02 17:47:37] [Iter  936/2250] R3[820/1200] | LR: 0.010692 | E: -42.261610 | E_var:     0.4168 | E_err:   0.010088
[2025-10-02 17:47:42] [Iter  937/2250] R3[822/1200] | LR: 0.010637 | E: -42.257522 | E_var:     0.7905 | E_err:   0.013892
[2025-10-02 17:47:47] [Iter  938/2250] R3[824/1200] | LR: 0.010583 | E: -42.268300 | E_var:     0.5739 | E_err:   0.011837
[2025-10-02 17:47:53] [Iter  939/2250] R3[826/1200] | LR: 0.010528 | E: -42.260579 | E_var:     0.4174 | E_err:   0.010095
[2025-10-02 17:47:58] [Iter  940/2250] R3[828/1200] | LR: 0.010474 | E: -42.260754 | E_var:     0.4413 | E_err:   0.010380
[2025-10-02 17:48:03] [Iter  941/2250] R3[830/1200] | LR: 0.010420 | E: -42.267481 | E_var:     0.3848 | E_err:   0.009693
[2025-10-02 17:48:08] [Iter  942/2250] R3[832/1200] | LR: 0.010366 | E: -42.267056 | E_var:     0.4563 | E_err:   0.010555
[2025-10-02 17:48:13] [Iter  943/2250] R3[834/1200] | LR: 0.010312 | E: -42.268140 | E_var:     0.4420 | E_err:   0.010388
[2025-10-02 17:48:18] [Iter  944/2250] R3[836/1200] | LR: 0.010259 | E: -42.277008 | E_var:     0.4180 | E_err:   0.010102
[2025-10-02 17:48:23] [Iter  945/2250] R3[838/1200] | LR: 0.010206 | E: -42.277471 | E_var:     0.8288 | E_err:   0.014225
[2025-10-02 17:48:29] [Iter  946/2250] R3[840/1200] | LR: 0.010153 | E: -42.248859 | E_var:     0.4897 | E_err:   0.010934
[2025-10-02 17:48:34] [Iter  947/2250] R3[842/1200] | LR: 0.010100 | E: -42.274340 | E_var:     0.3516 | E_err:   0.009265
[2025-10-02 17:48:39] [Iter  948/2250] R3[844/1200] | LR: 0.010047 | E: -42.259767 | E_var:     0.5786 | E_err:   0.011886
[2025-10-02 17:48:44] [Iter  949/2250] R3[846/1200] | LR: 0.009995 | E: -42.254975 | E_var:     0.4421 | E_err:   0.010389
[2025-10-02 17:48:49] [Iter  950/2250] R3[848/1200] | LR: 0.009943 | E: -42.265826 | E_var:     0.4309 | E_err:   0.010257
[2025-10-02 17:48:54] [Iter  951/2250] R3[850/1200] | LR: 0.009890 | E: -42.258857 | E_var:     0.4049 | E_err:   0.009943
[2025-10-02 17:49:00] [Iter  952/2250] R3[852/1200] | LR: 0.009839 | E: -42.265246 | E_var:     0.4669 | E_err:   0.010677
[2025-10-02 17:49:05] [Iter  953/2250] R3[854/1200] | LR: 0.009787 | E: -42.270952 | E_var:     0.3859 | E_err:   0.009706
[2025-10-02 17:49:10] [Iter  954/2250] R3[856/1200] | LR: 0.009736 | E: -42.267857 | E_var:     0.4266 | E_err:   0.010205
[2025-10-02 17:49:15] [Iter  955/2250] R3[858/1200] | LR: 0.009684 | E: -42.268538 | E_var:     0.4507 | E_err:   0.010490
[2025-10-02 17:49:20] [Iter  956/2250] R3[860/1200] | LR: 0.009633 | E: -42.264222 | E_var:     0.5608 | E_err:   0.011702
[2025-10-02 17:49:25] [Iter  957/2250] R3[862/1200] | LR: 0.009583 | E: -42.280128 | E_var:     0.6302 | E_err:   0.012404
[2025-10-02 17:49:30] [Iter  958/2250] R3[864/1200] | LR: 0.009532 | E: -42.279182 | E_var:     0.4945 | E_err:   0.010988
[2025-10-02 17:49:36] [Iter  959/2250] R3[866/1200] | LR: 0.009482 | E: -42.277541 | E_var:     0.4585 | E_err:   0.010580
[2025-10-02 17:49:41] [Iter  960/2250] R3[868/1200] | LR: 0.009432 | E: -42.281417 | E_var:     0.6764 | E_err:   0.012850
[2025-10-02 17:49:46] [Iter  961/2250] R3[870/1200] | LR: 0.009382 | E: -42.278638 | E_var:     0.4076 | E_err:   0.009975
[2025-10-02 17:49:51] [Iter  962/2250] R3[872/1200] | LR: 0.009332 | E: -42.266522 | E_var:     0.5595 | E_err:   0.011687
[2025-10-02 17:49:56] [Iter  963/2250] R3[874/1200] | LR: 0.009283 | E: -42.281790 | E_var:     0.5325 | E_err:   0.011402
[2025-10-02 17:50:01] [Iter  964/2250] R3[876/1200] | LR: 0.009234 | E: -42.260768 | E_var:     0.4446 | E_err:   0.010418
[2025-10-02 17:50:06] [Iter  965/2250] R3[878/1200] | LR: 0.009185 | E: -42.286346 | E_var:     0.4989 | E_err:   0.011037
[2025-10-02 17:50:12] [Iter  966/2250] R3[880/1200] | LR: 0.009136 | E: -42.258174 | E_var:     0.4425 | E_err:   0.010394
[2025-10-02 17:50:17] [Iter  967/2250] R3[882/1200] | LR: 0.009087 | E: -42.268161 | E_var:     0.3477 | E_err:   0.009214
[2025-10-02 17:50:22] [Iter  968/2250] R3[884/1200] | LR: 0.009039 | E: -42.275800 | E_var:     0.5050 | E_err:   0.011104
[2025-10-02 17:50:27] [Iter  969/2250] R3[886/1200] | LR: 0.008991 | E: -42.272274 | E_var:     0.4797 | E_err:   0.010822
[2025-10-02 17:50:32] [Iter  970/2250] R3[888/1200] | LR: 0.008943 | E: -42.262411 | E_var:     0.4586 | E_err:   0.010582
[2025-10-02 17:50:37] [Iter  971/2250] R3[890/1200] | LR: 0.008896 | E: -42.284459 | E_var:     0.4854 | E_err:   0.010886
[2025-10-02 17:50:42] [Iter  972/2250] R3[892/1200] | LR: 0.008848 | E: -42.266603 | E_var:     0.4259 | E_err:   0.010196
[2025-10-02 17:50:48] [Iter  973/2250] R3[894/1200] | LR: 0.008801 | E: -42.275873 | E_var:     0.4817 | E_err:   0.010845
[2025-10-02 17:50:53] [Iter  974/2250] R3[896/1200] | LR: 0.008754 | E: -42.281399 | E_var:     0.3830 | E_err:   0.009670
[2025-10-02 17:50:58] [Iter  975/2250] R3[898/1200] | LR: 0.008708 | E: -42.271353 | E_var:     0.4373 | E_err:   0.010333
[2025-10-02 17:51:03] [Iter  976/2250] R3[900/1200] | LR: 0.008661 | E: -42.262285 | E_var:     0.4512 | E_err:   0.010496
[2025-10-02 17:51:08] [Iter  977/2250] R3[902/1200] | LR: 0.008615 | E: -42.270538 | E_var:     0.4372 | E_err:   0.010332
[2025-10-02 17:51:13] [Iter  978/2250] R3[904/1200] | LR: 0.008569 | E: -42.259803 | E_var:     0.4176 | E_err:   0.010098
[2025-10-02 17:51:19] [Iter  979/2250] R3[906/1200] | LR: 0.008523 | E: -42.255954 | E_var:     0.4301 | E_err:   0.010248
[2025-10-02 17:51:24] [Iter  980/2250] R3[908/1200] | LR: 0.008478 | E: -42.271475 | E_var:     0.4199 | E_err:   0.010125
[2025-10-02 17:51:29] [Iter  981/2250] R3[910/1200] | LR: 0.008433 | E: -42.265972 | E_var:     0.5104 | E_err:   0.011163
[2025-10-02 17:51:34] [Iter  982/2250] R3[912/1200] | LR: 0.008388 | E: -42.289159 | E_var:     0.5825 | E_err:   0.011925
[2025-10-02 17:51:39] [Iter  983/2250] R3[914/1200] | LR: 0.008343 | E: -42.274943 | E_var:     0.4995 | E_err:   0.011043
[2025-10-02 17:51:44] [Iter  984/2250] R3[916/1200] | LR: 0.008299 | E: -42.289000 | E_var:     0.4119 | E_err:   0.010029
[2025-10-02 17:51:49] [Iter  985/2250] R3[918/1200] | LR: 0.008255 | E: -42.261072 | E_var:     0.5504 | E_err:   0.011591
[2025-10-02 17:51:55] [Iter  986/2250] R3[920/1200] | LR: 0.008211 | E: -42.246182 | E_var:     0.6559 | E_err:   0.012654
[2025-10-02 17:52:00] [Iter  987/2250] R3[922/1200] | LR: 0.008167 | E: -42.272702 | E_var:     0.4653 | E_err:   0.010658
[2025-10-02 17:52:05] [Iter  988/2250] R3[924/1200] | LR: 0.008124 | E: -42.278250 | E_var:     0.5124 | E_err:   0.011185
[2025-10-02 17:52:10] [Iter  989/2250] R3[926/1200] | LR: 0.008080 | E: -42.267030 | E_var:     0.5417 | E_err:   0.011500
[2025-10-02 17:52:15] [Iter  990/2250] R3[928/1200] | LR: 0.008038 | E: -42.274369 | E_var:     0.4496 | E_err:   0.010477
[2025-10-02 17:52:20] [Iter  991/2250] R3[930/1200] | LR: 0.007995 | E: -42.267632 | E_var:     0.4113 | E_err:   0.010021
[2025-10-02 17:52:25] [Iter  992/2250] R3[932/1200] | LR: 0.007953 | E: -42.281859 | E_var:     0.4560 | E_err:   0.010551
[2025-10-02 17:52:31] [Iter  993/2250] R3[934/1200] | LR: 0.007910 | E: -42.280753 | E_var:     0.5014 | E_err:   0.011064
[2025-10-02 17:52:36] [Iter  994/2250] R3[936/1200] | LR: 0.007869 | E: -42.281190 | E_var:     0.3964 | E_err:   0.009837
[2025-10-02 17:52:41] [Iter  995/2250] R3[938/1200] | LR: 0.007827 | E: -42.251532 | E_var:     0.5020 | E_err:   0.011070
[2025-10-02 17:52:46] [Iter  996/2250] R3[940/1200] | LR: 0.007786 | E: -42.251734 | E_var:     0.4306 | E_err:   0.010254
[2025-10-02 17:52:51] [Iter  997/2250] R3[942/1200] | LR: 0.007745 | E: -42.246998 | E_var:     0.4979 | E_err:   0.011025
[2025-10-02 17:52:56] [Iter  998/2250] R3[944/1200] | LR: 0.007704 | E: -42.276224 | E_var:     0.4387 | E_err:   0.010349
[2025-10-02 17:53:01] [Iter  999/2250] R3[946/1200] | LR: 0.007663 | E: -42.267755 | E_var:     0.4127 | E_err:   0.010038
[2025-10-02 17:53:07] [Iter 1000/2250] R3[948/1200] | LR: 0.007623 | E: -42.270544 | E_var:     0.5191 | E_err:   0.011257
[2025-10-02 17:53:07] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-02 17:53:12] [Iter 1001/2250] R3[950/1200] | LR: 0.007583 | E: -42.267061 | E_var:     0.5020 | E_err:   0.011070
[2025-10-02 17:53:17] [Iter 1002/2250] R3[952/1200] | LR: 0.007543 | E: -42.285725 | E_var:     0.5935 | E_err:   0.012037
[2025-10-02 17:53:22] [Iter 1003/2250] R3[954/1200] | LR: 0.007504 | E: -42.286058 | E_var:     0.4385 | E_err:   0.010347
[2025-10-02 17:53:27] [Iter 1004/2250] R3[956/1200] | LR: 0.007465 | E: -42.280805 | E_var:     0.4338 | E_err:   0.010291
[2025-10-02 17:53:32] [Iter 1005/2250] R3[958/1200] | LR: 0.007426 | E: -42.260238 | E_var:     0.5233 | E_err:   0.011303
[2025-10-02 17:53:37] [Iter 1006/2250] R3[960/1200] | LR: 0.007387 | E: -42.262386 | E_var:     0.3921 | E_err:   0.009784
[2025-10-02 17:53:43] [Iter 1007/2250] R3[962/1200] | LR: 0.007349 | E: -42.286437 | E_var:     0.3572 | E_err:   0.009338
[2025-10-02 17:53:48] [Iter 1008/2250] R3[964/1200] | LR: 0.007311 | E: -42.260717 | E_var:     0.4274 | E_err:   0.010215
[2025-10-02 17:53:53] [Iter 1009/2250] R3[966/1200] | LR: 0.007273 | E: -42.255580 | E_var:     0.4710 | E_err:   0.010723
[2025-10-02 17:53:58] [Iter 1010/2250] R3[968/1200] | LR: 0.007236 | E: -42.278319 | E_var:     0.4669 | E_err:   0.010676
[2025-10-02 17:54:03] [Iter 1011/2250] R3[970/1200] | LR: 0.007198 | E: -42.263199 | E_var:     0.5261 | E_err:   0.011333
[2025-10-02 17:54:08] [Iter 1012/2250] R3[972/1200] | LR: 0.007161 | E: -42.261572 | E_var:     0.4440 | E_err:   0.010411
[2025-10-02 17:54:14] [Iter 1013/2250] R3[974/1200] | LR: 0.007125 | E: -42.278409 | E_var:     0.3842 | E_err:   0.009685
[2025-10-02 17:54:19] [Iter 1014/2250] R3[976/1200] | LR: 0.007088 | E: -42.238119 | E_var:     0.4986 | E_err:   0.011033
[2025-10-02 17:54:24] [Iter 1015/2250] R3[978/1200] | LR: 0.007052 | E: -42.246217 | E_var:     0.5344 | E_err:   0.011423
[2025-10-02 17:54:29] [Iter 1016/2250] R3[980/1200] | LR: 0.007017 | E: -42.265236 | E_var:     0.4500 | E_err:   0.010481
[2025-10-02 17:54:34] [Iter 1017/2250] R3[982/1200] | LR: 0.006981 | E: -42.255443 | E_var:     0.9756 | E_err:   0.015433
[2025-10-02 17:54:39] [Iter 1018/2250] R3[984/1200] | LR: 0.006946 | E: -42.278270 | E_var:     0.6472 | E_err:   0.012570
[2025-10-02 17:54:44] [Iter 1019/2250] R3[986/1200] | LR: 0.006911 | E: -42.277144 | E_var:     0.4077 | E_err:   0.009977
[2025-10-02 17:54:50] [Iter 1020/2250] R3[988/1200] | LR: 0.006876 | E: -42.284606 | E_var:     0.4356 | E_err:   0.010313
[2025-10-02 17:54:55] [Iter 1021/2250] R3[990/1200] | LR: 0.006842 | E: -42.276085 | E_var:     0.3969 | E_err:   0.009843
[2025-10-02 17:55:00] [Iter 1022/2250] R3[992/1200] | LR: 0.006808 | E: -42.273064 | E_var:     0.4547 | E_err:   0.010537
[2025-10-02 17:55:05] [Iter 1023/2250] R3[994/1200] | LR: 0.006774 | E: -42.257116 | E_var:     0.4297 | E_err:   0.010242
[2025-10-02 17:55:10] [Iter 1024/2250] R3[996/1200] | LR: 0.006741 | E: -42.267441 | E_var:     1.0364 | E_err:   0.015907
[2025-10-02 17:55:15] [Iter 1025/2250] R3[998/1200] | LR: 0.006708 | E: -42.269455 | E_var:     0.4812 | E_err:   0.010838
[2025-10-02 17:55:20] [Iter 1026/2250] R3[1000/1200] | LR: 0.006675 | E: -42.262065 | E_var:     0.4194 | E_err:   0.010118
[2025-10-02 17:55:26] [Iter 1027/2250] R3[1002/1200] | LR: 0.006642 | E: -42.255127 | E_var:     0.4276 | E_err:   0.010217
[2025-10-02 17:55:31] [Iter 1028/2250] R3[1004/1200] | LR: 0.006610 | E: -42.278985 | E_var:     0.4800 | E_err:   0.010825
[2025-10-02 17:55:36] [Iter 1029/2250] R3[1006/1200] | LR: 0.006578 | E: -42.276085 | E_var:     0.4458 | E_err:   0.010432
[2025-10-02 17:55:41] [Iter 1030/2250] R3[1008/1200] | LR: 0.006546 | E: -42.268844 | E_var:     0.4080 | E_err:   0.009981
[2025-10-02 17:55:46] [Iter 1031/2250] R3[1010/1200] | LR: 0.006515 | E: -42.264691 | E_var:     0.5006 | E_err:   0.011055
[2025-10-02 17:55:51] [Iter 1032/2250] R3[1012/1200] | LR: 0.006484 | E: -42.261076 | E_var:     0.4433 | E_err:   0.010403
[2025-10-02 17:55:56] [Iter 1033/2250] R3[1014/1200] | LR: 0.006453 | E: -42.284472 | E_var:     1.3193 | E_err:   0.017947
[2025-10-02 17:56:02] [Iter 1034/2250] R3[1016/1200] | LR: 0.006422 | E: -42.273977 | E_var:     0.6551 | E_err:   0.012647
[2025-10-02 17:56:07] [Iter 1035/2250] R3[1018/1200] | LR: 0.006392 | E: -42.282221 | E_var:     0.4552 | E_err:   0.010542
[2025-10-02 17:56:12] [Iter 1036/2250] R3[1020/1200] | LR: 0.006362 | E: -42.268965 | E_var:     0.4012 | E_err:   0.009897
[2025-10-02 17:56:17] [Iter 1037/2250] R3[1022/1200] | LR: 0.006333 | E: -42.269338 | E_var:     0.4399 | E_err:   0.010363
[2025-10-02 17:56:22] [Iter 1038/2250] R3[1024/1200] | LR: 0.006304 | E: -42.276065 | E_var:     0.5013 | E_err:   0.011063
[2025-10-02 17:56:27] [Iter 1039/2250] R3[1026/1200] | LR: 0.006275 | E: -42.279010 | E_var:     0.6785 | E_err:   0.012870
[2025-10-02 17:56:32] [Iter 1040/2250] R3[1028/1200] | LR: 0.006246 | E: -42.267298 | E_var:     0.5590 | E_err:   0.011682
[2025-10-02 17:56:38] [Iter 1041/2250] R3[1030/1200] | LR: 0.006218 | E: -42.247017 | E_var:     0.4053 | E_err:   0.009947
[2025-10-02 17:56:43] [Iter 1042/2250] R3[1032/1200] | LR: 0.006190 | E: -42.263121 | E_var:     0.4311 | E_err:   0.010259
[2025-10-02 17:56:48] [Iter 1043/2250] R3[1034/1200] | LR: 0.006162 | E: -42.257593 | E_var:     0.4657 | E_err:   0.010663
[2025-10-02 17:56:53] [Iter 1044/2250] R3[1036/1200] | LR: 0.006135 | E: -42.277037 | E_var:     0.4165 | E_err:   0.010084
[2025-10-02 17:56:58] [Iter 1045/2250] R3[1038/1200] | LR: 0.006107 | E: -42.269638 | E_var:     0.4654 | E_err:   0.010659
[2025-10-02 17:57:03] [Iter 1046/2250] R3[1040/1200] | LR: 0.006081 | E: -42.272976 | E_var:     0.3937 | E_err:   0.009804
[2025-10-02 17:57:08] [Iter 1047/2250] R3[1042/1200] | LR: 0.006054 | E: -42.236242 | E_var:     0.4782 | E_err:   0.010805
[2025-10-02 17:57:14] [Iter 1048/2250] R3[1044/1200] | LR: 0.006028 | E: -42.285050 | E_var:     0.4447 | E_err:   0.010420
[2025-10-02 17:57:19] [Iter 1049/2250] R3[1046/1200] | LR: 0.006002 | E: -42.270183 | E_var:     0.5693 | E_err:   0.011790
[2025-10-02 17:57:24] [Iter 1050/2250] R3[1048/1200] | LR: 0.005977 | E: -42.291559 | E_var:     0.5589 | E_err:   0.011682
[2025-10-02 17:57:29] [Iter 1051/2250] R3[1050/1200] | LR: 0.005952 | E: -42.274590 | E_var:     0.6172 | E_err:   0.012276
[2025-10-02 17:57:34] [Iter 1052/2250] R3[1052/1200] | LR: 0.005927 | E: -42.265433 | E_var:     0.4748 | E_err:   0.010767
[2025-10-02 17:57:39] [Iter 1053/2250] R3[1054/1200] | LR: 0.005902 | E: -42.265420 | E_var:     0.4654 | E_err:   0.010659
[2025-10-02 17:57:44] [Iter 1054/2250] R3[1056/1200] | LR: 0.005878 | E: -42.269091 | E_var:     0.5819 | E_err:   0.011919
[2025-10-02 17:57:50] [Iter 1055/2250] R3[1058/1200] | LR: 0.005854 | E: -42.251886 | E_var:     0.4282 | E_err:   0.010224
[2025-10-02 17:57:55] [Iter 1056/2250] R3[1060/1200] | LR: 0.005830 | E: -42.266482 | E_var:     0.4165 | E_err:   0.010084
[2025-10-02 17:58:00] [Iter 1057/2250] R3[1062/1200] | LR: 0.005807 | E: -42.262506 | E_var:     0.5017 | E_err:   0.011067
[2025-10-02 17:58:05] [Iter 1058/2250] R3[1064/1200] | LR: 0.005784 | E: -42.287927 | E_var:     0.4141 | E_err:   0.010054
[2025-10-02 17:58:10] [Iter 1059/2250] R3[1066/1200] | LR: 0.005761 | E: -42.283931 | E_var:     0.3911 | E_err:   0.009772
[2025-10-02 17:58:15] [Iter 1060/2250] R3[1068/1200] | LR: 0.005739 | E: -42.266837 | E_var:     0.7129 | E_err:   0.013192
[2025-10-02 17:58:21] [Iter 1061/2250] R3[1070/1200] | LR: 0.005717 | E: -42.280551 | E_var:     0.3834 | E_err:   0.009675
[2025-10-02 17:58:26] [Iter 1062/2250] R3[1072/1200] | LR: 0.005695 | E: -42.274279 | E_var:     0.4926 | E_err:   0.010966
[2025-10-02 17:58:31] [Iter 1063/2250] R3[1074/1200] | LR: 0.005674 | E: -42.259633 | E_var:     0.4394 | E_err:   0.010357
[2025-10-02 17:58:36] [Iter 1064/2250] R3[1076/1200] | LR: 0.005653 | E: -42.291540 | E_var:     0.4454 | E_err:   0.010428
[2025-10-02 17:58:41] [Iter 1065/2250] R3[1078/1200] | LR: 0.005632 | E: -42.273480 | E_var:     0.4499 | E_err:   0.010480
[2025-10-02 17:58:46] [Iter 1066/2250] R3[1080/1200] | LR: 0.005612 | E: -42.274473 | E_var:     0.4576 | E_err:   0.010570
[2025-10-02 17:58:51] [Iter 1067/2250] R3[1082/1200] | LR: 0.005592 | E: -42.260615 | E_var:     0.5416 | E_err:   0.011499
[2025-10-02 17:58:57] [Iter 1068/2250] R3[1084/1200] | LR: 0.005572 | E: -42.236094 | E_var:     0.4185 | E_err:   0.010108
[2025-10-02 17:59:02] [Iter 1069/2250] R3[1086/1200] | LR: 0.005553 | E: -42.278411 | E_var:     0.5900 | E_err:   0.012002
[2025-10-02 17:59:07] [Iter 1070/2250] R3[1088/1200] | LR: 0.005534 | E: -42.295895 | E_var:     0.5429 | E_err:   0.011513
[2025-10-02 17:59:12] [Iter 1071/2250] R3[1090/1200] | LR: 0.005515 | E: -42.279210 | E_var:     0.4518 | E_err:   0.010503
[2025-10-02 17:59:17] [Iter 1072/2250] R3[1092/1200] | LR: 0.005496 | E: -42.296172 | E_var:     0.4202 | E_err:   0.010128
[2025-10-02 17:59:22] [Iter 1073/2250] R3[1094/1200] | LR: 0.005478 | E: -42.285408 | E_var:     0.3897 | E_err:   0.009754
[2025-10-02 17:59:27] [Iter 1074/2250] R3[1096/1200] | LR: 0.005460 | E: -42.276624 | E_var:     0.5162 | E_err:   0.011226
[2025-10-02 17:59:33] [Iter 1075/2250] R3[1098/1200] | LR: 0.005443 | E: -42.248677 | E_var:     0.6429 | E_err:   0.012529
[2025-10-02 17:59:38] [Iter 1076/2250] R3[1100/1200] | LR: 0.005426 | E: -42.278109 | E_var:     0.4012 | E_err:   0.009896
[2025-10-02 17:59:43] [Iter 1077/2250] R3[1102/1200] | LR: 0.005409 | E: -42.286040 | E_var:     0.3829 | E_err:   0.009668
[2025-10-02 17:59:48] [Iter 1078/2250] R3[1104/1200] | LR: 0.005393 | E: -42.270216 | E_var:     0.4667 | E_err:   0.010674
[2025-10-02 17:59:53] [Iter 1079/2250] R3[1106/1200] | LR: 0.005377 | E: -42.261726 | E_var:     0.4847 | E_err:   0.010878
[2025-10-02 17:59:58] [Iter 1080/2250] R3[1108/1200] | LR: 0.005361 | E: -42.265216 | E_var:     0.4047 | E_err:   0.009940
[2025-10-02 18:00:03] [Iter 1081/2250] R3[1110/1200] | LR: 0.005345 | E: -42.263342 | E_var:     0.5119 | E_err:   0.011179
[2025-10-02 18:00:09] [Iter 1082/2250] R3[1112/1200] | LR: 0.005330 | E: -42.294275 | E_var:     0.4237 | E_err:   0.010171
[2025-10-02 18:00:14] [Iter 1083/2250] R3[1114/1200] | LR: 0.005315 | E: -42.285210 | E_var:     0.5318 | E_err:   0.011394
[2025-10-02 18:00:19] [Iter 1084/2250] R3[1116/1200] | LR: 0.005301 | E: -42.276995 | E_var:     0.4206 | E_err:   0.010134
[2025-10-02 18:00:24] [Iter 1085/2250] R3[1118/1200] | LR: 0.005287 | E: -42.268588 | E_var:     0.6083 | E_err:   0.012187
[2025-10-02 18:00:29] [Iter 1086/2250] R3[1120/1200] | LR: 0.005273 | E: -42.292109 | E_var:     0.7037 | E_err:   0.013107
[2025-10-02 18:00:34] [Iter 1087/2250] R3[1122/1200] | LR: 0.005260 | E: -42.259810 | E_var:     0.4557 | E_err:   0.010547
[2025-10-02 18:00:39] [Iter 1088/2250] R3[1124/1200] | LR: 0.005247 | E: -42.265444 | E_var:     0.8002 | E_err:   0.013977
[2025-10-02 18:00:45] [Iter 1089/2250] R3[1126/1200] | LR: 0.005234 | E: -42.263353 | E_var:     0.4718 | E_err:   0.010733
[2025-10-02 18:00:50] [Iter 1090/2250] R3[1128/1200] | LR: 0.005221 | E: -42.266454 | E_var:     0.5023 | E_err:   0.011074
[2025-10-02 18:00:55] [Iter 1091/2250] R3[1130/1200] | LR: 0.005209 | E: -42.270867 | E_var:     0.4900 | E_err:   0.010938
[2025-10-02 18:01:00] [Iter 1092/2250] R3[1132/1200] | LR: 0.005198 | E: -42.254322 | E_var:     0.4035 | E_err:   0.009925
[2025-10-02 18:01:05] [Iter 1093/2250] R3[1134/1200] | LR: 0.005186 | E: -42.274660 | E_var:     0.3995 | E_err:   0.009876
[2025-10-02 18:01:10] [Iter 1094/2250] R3[1136/1200] | LR: 0.005175 | E: -42.255179 | E_var:     0.5191 | E_err:   0.011258
[2025-10-02 18:01:15] [Iter 1095/2250] R3[1138/1200] | LR: 0.005164 | E: -42.282357 | E_var:     0.4337 | E_err:   0.010290
[2025-10-02 18:01:21] [Iter 1096/2250] R3[1140/1200] | LR: 0.005154 | E: -42.268558 | E_var:     0.3865 | E_err:   0.009714
[2025-10-02 18:01:26] [Iter 1097/2250] R3[1142/1200] | LR: 0.005144 | E: -42.260137 | E_var:     0.4088 | E_err:   0.009991
[2025-10-02 18:01:31] [Iter 1098/2250] R3[1144/1200] | LR: 0.005134 | E: -42.292910 | E_var:     0.3674 | E_err:   0.009471
[2025-10-02 18:01:36] [Iter 1099/2250] R3[1146/1200] | LR: 0.005125 | E: -42.284059 | E_var:     0.4147 | E_err:   0.010062
[2025-10-02 18:01:41] [Iter 1100/2250] R3[1148/1200] | LR: 0.005116 | E: -42.253860 | E_var:     0.4661 | E_err:   0.010667
[2025-10-02 18:01:46] [Iter 1101/2250] R3[1150/1200] | LR: 0.005107 | E: -42.267745 | E_var:     0.4654 | E_err:   0.010659
[2025-10-02 18:01:51] [Iter 1102/2250] R3[1152/1200] | LR: 0.005099 | E: -42.264779 | E_var:     0.4338 | E_err:   0.010291
[2025-10-02 18:01:57] [Iter 1103/2250] R3[1154/1200] | LR: 0.005091 | E: -42.283546 | E_var:     0.4679 | E_err:   0.010687
[2025-10-02 18:02:02] [Iter 1104/2250] R3[1156/1200] | LR: 0.005083 | E: -42.267368 | E_var:     0.4324 | E_err:   0.010275
[2025-10-02 18:02:07] [Iter 1105/2250] R3[1158/1200] | LR: 0.005075 | E: -42.282048 | E_var:     0.4781 | E_err:   0.010803
[2025-10-02 18:02:12] [Iter 1106/2250] R3[1160/1200] | LR: 0.005068 | E: -42.276225 | E_var:     0.3849 | E_err:   0.009694
[2025-10-02 18:02:17] [Iter 1107/2250] R3[1162/1200] | LR: 0.005062 | E: -42.283069 | E_var:     0.3899 | E_err:   0.009756
[2025-10-02 18:02:22] [Iter 1108/2250] R3[1164/1200] | LR: 0.005055 | E: -42.287914 | E_var:     0.3785 | E_err:   0.009613
[2025-10-02 18:02:27] [Iter 1109/2250] R3[1166/1200] | LR: 0.005049 | E: -42.282826 | E_var:     0.3632 | E_err:   0.009417
[2025-10-02 18:02:33] [Iter 1110/2250] R3[1168/1200] | LR: 0.005044 | E: -42.274129 | E_var:     0.5507 | E_err:   0.011595
[2025-10-02 18:02:38] [Iter 1111/2250] R3[1170/1200] | LR: 0.005039 | E: -42.277800 | E_var:     0.4031 | E_err:   0.009921
[2025-10-02 18:02:43] [Iter 1112/2250] R3[1172/1200] | LR: 0.005034 | E: -42.266392 | E_var:     0.5059 | E_err:   0.011113
[2025-10-02 18:02:48] [Iter 1113/2250] R3[1174/1200] | LR: 0.005029 | E: -42.285013 | E_var:     0.4015 | E_err:   0.009901
[2025-10-02 18:02:53] [Iter 1114/2250] R3[1176/1200] | LR: 0.005025 | E: -42.281650 | E_var:     0.6681 | E_err:   0.012772
[2025-10-02 18:02:58] [Iter 1115/2250] R3[1178/1200] | LR: 0.005021 | E: -42.286523 | E_var:     0.6561 | E_err:   0.012656
[2025-10-02 18:03:04] [Iter 1116/2250] R3[1180/1200] | LR: 0.005017 | E: -42.275857 | E_var:     0.3894 | E_err:   0.009750
[2025-10-02 18:03:09] [Iter 1117/2250] R3[1182/1200] | LR: 0.005014 | E: -42.287753 | E_var:     0.3968 | E_err:   0.009842
[2025-10-02 18:03:14] [Iter 1118/2250] R3[1184/1200] | LR: 0.005011 | E: -42.298297 | E_var:     0.3732 | E_err:   0.009545
[2025-10-02 18:03:19] [Iter 1119/2250] R3[1186/1200] | LR: 0.005008 | E: -42.272607 | E_var:     0.4323 | E_err:   0.010274
[2025-10-02 18:03:24] [Iter 1120/2250] R3[1188/1200] | LR: 0.005006 | E: -42.269351 | E_var:     0.4526 | E_err:   0.010512
[2025-10-02 18:03:29] [Iter 1121/2250] R3[1190/1200] | LR: 0.005004 | E: -42.277654 | E_var:     0.4380 | E_err:   0.010341
[2025-10-02 18:03:34] [Iter 1122/2250] R3[1192/1200] | LR: 0.005003 | E: -42.303093 | E_var:     0.4286 | E_err:   0.010229
[2025-10-02 18:03:39] [Iter 1123/2250] R3[1194/1200] | LR: 0.005002 | E: -42.282916 | E_var:     0.4025 | E_err:   0.009913
[2025-10-02 18:03:45] [Iter 1124/2250] R3[1196/1200] | LR: 0.005001 | E: -42.269593 | E_var:     0.5229 | E_err:   0.011299
[2025-10-02 18:03:50] [Iter 1125/2250] R3[1198/1200] | LR: 0.005000 | E: -42.288083 | E_var:     0.5315 | E_err:   0.011391
[2025-10-02 18:03:50] 🔄 RESTART #4 | Period: 2400
[2025-10-02 18:03:55] [Iter 1126/2250] R4[0/2400]   | LR: 0.030000 | E: -42.282272 | E_var:     0.6634 | E_err:   0.012726
[2025-10-02 18:04:00] [Iter 1127/2250] R4[2/2400]   | LR: 0.030000 | E: -42.290813 | E_var:     0.4151 | E_err:   0.010067
[2025-10-02 18:04:05] [Iter 1128/2250] R4[4/2400]   | LR: 0.030000 | E: -42.297024 | E_var:     0.3734 | E_err:   0.009548
[2025-10-02 18:04:10] [Iter 1129/2250] R4[6/2400]   | LR: 0.030000 | E: -42.265591 | E_var:     0.5943 | E_err:   0.012045
[2025-10-02 18:04:16] [Iter 1130/2250] R4[8/2400]   | LR: 0.029999 | E: -42.283687 | E_var:     0.4451 | E_err:   0.010424
[2025-10-02 18:04:21] [Iter 1131/2250] R4[10/2400]  | LR: 0.029999 | E: -42.271957 | E_var:     0.6121 | E_err:   0.012225
[2025-10-02 18:04:26] [Iter 1132/2250] R4[12/2400]  | LR: 0.029998 | E: -42.291881 | E_var:     0.3438 | E_err:   0.009162
[2025-10-02 18:04:31] [Iter 1133/2250] R4[14/2400]  | LR: 0.029998 | E: -42.270925 | E_var:     0.4025 | E_err:   0.009913
[2025-10-02 18:04:36] [Iter 1134/2250] R4[16/2400]  | LR: 0.029997 | E: -42.285460 | E_var:     0.5891 | E_err:   0.011992
[2025-10-02 18:04:41] [Iter 1135/2250] R4[18/2400]  | LR: 0.029997 | E: -42.270455 | E_var:     0.3689 | E_err:   0.009490
[2025-10-02 18:04:46] [Iter 1136/2250] R4[20/2400]  | LR: 0.029996 | E: -42.286572 | E_var:     0.4598 | E_err:   0.010595
[2025-10-02 18:04:52] [Iter 1137/2250] R4[22/2400]  | LR: 0.029995 | E: -42.291199 | E_var:     0.4042 | E_err:   0.009934
[2025-10-02 18:04:57] [Iter 1138/2250] R4[24/2400]  | LR: 0.029994 | E: -42.283642 | E_var:     0.4756 | E_err:   0.010776
[2025-10-02 18:05:02] [Iter 1139/2250] R4[26/2400]  | LR: 0.029993 | E: -42.282474 | E_var:     0.4180 | E_err:   0.010102
[2025-10-02 18:05:07] [Iter 1140/2250] R4[28/2400]  | LR: 0.029992 | E: -42.270895 | E_var:     0.5268 | E_err:   0.011341
[2025-10-02 18:05:12] [Iter 1141/2250] R4[30/2400]  | LR: 0.029990 | E: -42.284726 | E_var:     0.4295 | E_err:   0.010240
[2025-10-02 18:05:17] [Iter 1142/2250] R4[32/2400]  | LR: 0.029989 | E: -42.277017 | E_var:     0.5370 | E_err:   0.011450
[2025-10-02 18:05:22] [Iter 1143/2250] R4[34/2400]  | LR: 0.029988 | E: -42.272119 | E_var:     0.4054 | E_err:   0.009949
[2025-10-02 18:05:28] [Iter 1144/2250] R4[36/2400]  | LR: 0.029986 | E: -42.285614 | E_var:     0.4611 | E_err:   0.010610
[2025-10-02 18:05:33] [Iter 1145/2250] R4[38/2400]  | LR: 0.029985 | E: -42.283819 | E_var:     0.4297 | E_err:   0.010242
[2025-10-02 18:05:38] [Iter 1146/2250] R4[40/2400]  | LR: 0.029983 | E: -42.282473 | E_var:     0.4853 | E_err:   0.010885
[2025-10-02 18:05:43] [Iter 1147/2250] R4[42/2400]  | LR: 0.029981 | E: -42.276932 | E_var:     0.4254 | E_err:   0.010190
[2025-10-02 18:05:48] [Iter 1148/2250] R4[44/2400]  | LR: 0.029979 | E: -42.275860 | E_var:     0.4536 | E_err:   0.010523
[2025-10-02 18:05:53] [Iter 1149/2250] R4[46/2400]  | LR: 0.029977 | E: -42.277136 | E_var:     0.5706 | E_err:   0.011803
[2025-10-02 18:05:58] [Iter 1150/2250] R4[48/2400]  | LR: 0.029975 | E: -42.294096 | E_var:     0.5024 | E_err:   0.011075
[2025-10-02 18:06:04] [Iter 1151/2250] R4[50/2400]  | LR: 0.029973 | E: -42.264487 | E_var:     1.5872 | E_err:   0.019685
[2025-10-02 18:06:09] [Iter 1152/2250] R4[52/2400]  | LR: 0.029971 | E: -42.279597 | E_var:     0.4679 | E_err:   0.010689
[2025-10-02 18:06:14] [Iter 1153/2250] R4[54/2400]  | LR: 0.029969 | E: -42.280045 | E_var:     0.4265 | E_err:   0.010205
[2025-10-02 18:06:19] [Iter 1154/2250] R4[56/2400]  | LR: 0.029966 | E: -42.272004 | E_var:     0.5321 | E_err:   0.011398
[2025-10-02 18:06:24] [Iter 1155/2250] R4[58/2400]  | LR: 0.029964 | E: -42.272597 | E_var:     0.4644 | E_err:   0.010648
[2025-10-02 18:06:29] [Iter 1156/2250] R4[60/2400]  | LR: 0.029961 | E: -42.276471 | E_var:     0.4364 | E_err:   0.010322
[2025-10-02 18:06:34] [Iter 1157/2250] R4[62/2400]  | LR: 0.029959 | E: -42.292507 | E_var:     0.4871 | E_err:   0.010905
[2025-10-02 18:06:40] [Iter 1158/2250] R4[64/2400]  | LR: 0.029956 | E: -42.287691 | E_var:     0.4551 | E_err:   0.010541
[2025-10-02 18:06:45] [Iter 1159/2250] R4[66/2400]  | LR: 0.029953 | E: -42.293140 | E_var:     0.5417 | E_err:   0.011500
[2025-10-02 18:06:50] [Iter 1160/2250] R4[68/2400]  | LR: 0.029951 | E: -42.282603 | E_var:     0.3568 | E_err:   0.009333
[2025-10-02 18:06:55] [Iter 1161/2250] R4[70/2400]  | LR: 0.029948 | E: -42.269076 | E_var:     0.5832 | E_err:   0.011932
[2025-10-02 18:07:00] [Iter 1162/2250] R4[72/2400]  | LR: 0.029945 | E: -42.281078 | E_var:     0.5030 | E_err:   0.011082
[2025-10-02 18:07:05] [Iter 1163/2250] R4[74/2400]  | LR: 0.029941 | E: -42.271226 | E_var:     0.4570 | E_err:   0.010563
[2025-10-02 18:07:10] [Iter 1164/2250] R4[76/2400]  | LR: 0.029938 | E: -42.290955 | E_var:     0.4556 | E_err:   0.010547
[2025-10-02 18:07:16] [Iter 1165/2250] R4[78/2400]  | LR: 0.029935 | E: -42.285972 | E_var:     0.5410 | E_err:   0.011493
[2025-10-02 18:07:21] [Iter 1166/2250] R4[80/2400]  | LR: 0.029932 | E: -42.278764 | E_var:     0.6113 | E_err:   0.012217
[2025-10-02 18:07:26] [Iter 1167/2250] R4[82/2400]  | LR: 0.029928 | E: -42.281793 | E_var:     0.3880 | E_err:   0.009733
[2025-10-02 18:07:31] [Iter 1168/2250] R4[84/2400]  | LR: 0.029925 | E: -42.276539 | E_var:     0.3563 | E_err:   0.009327
[2025-10-02 18:07:36] [Iter 1169/2250] R4[86/2400]  | LR: 0.029921 | E: -42.287687 | E_var:     0.4382 | E_err:   0.010343
[2025-10-02 18:07:41] [Iter 1170/2250] R4[88/2400]  | LR: 0.029917 | E: -42.279300 | E_var:     0.4764 | E_err:   0.010785
[2025-10-02 18:07:46] [Iter 1171/2250] R4[90/2400]  | LR: 0.029913 | E: -42.280136 | E_var:     0.4395 | E_err:   0.010359
[2025-10-02 18:07:52] [Iter 1172/2250] R4[92/2400]  | LR: 0.029909 | E: -42.280401 | E_var:     0.4670 | E_err:   0.010678
[2025-10-02 18:07:57] [Iter 1173/2250] R4[94/2400]  | LR: 0.029905 | E: -42.278929 | E_var:     0.4967 | E_err:   0.011012
[2025-10-02 18:08:02] [Iter 1174/2250] R4[96/2400]  | LR: 0.029901 | E: -42.277324 | E_var:     0.5052 | E_err:   0.011105
[2025-10-02 18:08:07] [Iter 1175/2250] R4[98/2400]  | LR: 0.029897 | E: -42.273304 | E_var:     0.3754 | E_err:   0.009573
[2025-10-02 18:08:12] [Iter 1176/2250] R4[100/2400] | LR: 0.029893 | E: -42.276265 | E_var:     0.3919 | E_err:   0.009781
[2025-10-02 18:08:17] [Iter 1177/2250] R4[102/2400] | LR: 0.029889 | E: -42.284546 | E_var:     0.4548 | E_err:   0.010538
[2025-10-02 18:08:22] [Iter 1178/2250] R4[104/2400] | LR: 0.029884 | E: -42.243096 | E_var:     1.0746 | E_err:   0.016197
[2025-10-02 18:08:28] [Iter 1179/2250] R4[106/2400] | LR: 0.029880 | E: -42.298977 | E_var:     0.4670 | E_err:   0.010678
[2025-10-02 18:08:33] [Iter 1180/2250] R4[108/2400] | LR: 0.029875 | E: -42.282492 | E_var:     0.4419 | E_err:   0.010386
[2025-10-02 18:08:38] [Iter 1181/2250] R4[110/2400] | LR: 0.029871 | E: -42.269175 | E_var:     0.3807 | E_err:   0.009641
[2025-10-02 18:08:43] [Iter 1182/2250] R4[112/2400] | LR: 0.029866 | E: -42.282912 | E_var:     0.4834 | E_err:   0.010864
[2025-10-02 18:08:48] [Iter 1183/2250] R4[114/2400] | LR: 0.029861 | E: -42.280060 | E_var:     0.4971 | E_err:   0.011016
[2025-10-02 18:08:53] [Iter 1184/2250] R4[116/2400] | LR: 0.029856 | E: -42.286665 | E_var:     0.3857 | E_err:   0.009703
[2025-10-02 18:08:59] [Iter 1185/2250] R4[118/2400] | LR: 0.029851 | E: -42.274309 | E_var:     0.5354 | E_err:   0.011433
[2025-10-02 18:09:04] [Iter 1186/2250] R4[120/2400] | LR: 0.029846 | E: -42.272482 | E_var:     0.3976 | E_err:   0.009852
[2025-10-02 18:09:09] [Iter 1187/2250] R4[122/2400] | LR: 0.029841 | E: -42.272693 | E_var:     0.4477 | E_err:   0.010455
[2025-10-02 18:09:14] [Iter 1188/2250] R4[124/2400] | LR: 0.029836 | E: -42.298733 | E_var:     0.4079 | E_err:   0.009979
[2025-10-02 18:09:19] [Iter 1189/2250] R4[126/2400] | LR: 0.029830 | E: -42.275937 | E_var:     0.4499 | E_err:   0.010480
[2025-10-02 18:09:24] [Iter 1190/2250] R4[128/2400] | LR: 0.029825 | E: -42.275537 | E_var:     0.5330 | E_err:   0.011407
[2025-10-02 18:09:29] [Iter 1191/2250] R4[130/2400] | LR: 0.029819 | E: -42.303179 | E_var:     0.4106 | E_err:   0.010012
[2025-10-02 18:09:35] [Iter 1192/2250] R4[132/2400] | LR: 0.029814 | E: -42.284448 | E_var:     0.4514 | E_err:   0.010498
[2025-10-02 18:09:40] [Iter 1193/2250] R4[134/2400] | LR: 0.029808 | E: -42.270935 | E_var:     0.5174 | E_err:   0.011239
[2025-10-02 18:09:45] [Iter 1194/2250] R4[136/2400] | LR: 0.029802 | E: -42.279512 | E_var:     0.4989 | E_err:   0.011037
[2025-10-02 18:09:50] [Iter 1195/2250] R4[138/2400] | LR: 0.029797 | E: -42.268695 | E_var:     0.6960 | E_err:   0.013035
[2025-10-02 18:09:55] [Iter 1196/2250] R4[140/2400] | LR: 0.029791 | E: -42.283897 | E_var:     0.4519 | E_err:   0.010503
[2025-10-02 18:10:00] [Iter 1197/2250] R4[142/2400] | LR: 0.029785 | E: -42.267206 | E_var:     0.3970 | E_err:   0.009845
[2025-10-02 18:10:05] [Iter 1198/2250] R4[144/2400] | LR: 0.029779 | E: -42.289193 | E_var:     0.4385 | E_err:   0.010347
[2025-10-02 18:10:11] [Iter 1199/2250] R4[146/2400] | LR: 0.029772 | E: -42.282810 | E_var:     0.5311 | E_err:   0.011387
[2025-10-02 18:10:16] [Iter 1200/2250] R4[148/2400] | LR: 0.029766 | E: -42.268020 | E_var:     0.5807 | E_err:   0.011907
[2025-10-02 18:10:16] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-02 18:10:21] [Iter 1201/2250] R4[150/2400] | LR: 0.029760 | E: -42.289817 | E_var:     0.6708 | E_err:   0.012798
[2025-10-02 18:10:26] [Iter 1202/2250] R4[152/2400] | LR: 0.029753 | E: -42.293958 | E_var:     0.6263 | E_err:   0.012365
[2025-10-02 18:10:31] [Iter 1203/2250] R4[154/2400] | LR: 0.029747 | E: -42.286192 | E_var:     0.5068 | E_err:   0.011123
[2025-10-02 18:10:36] [Iter 1204/2250] R4[156/2400] | LR: 0.029740 | E: -42.278365 | E_var:     0.6446 | E_err:   0.012545
[2025-10-02 18:10:41] [Iter 1205/2250] R4[158/2400] | LR: 0.029734 | E: -42.272336 | E_var:     0.5179 | E_err:   0.011244
[2025-10-02 18:10:47] [Iter 1206/2250] R4[160/2400] | LR: 0.029727 | E: -42.285787 | E_var:     0.4327 | E_err:   0.010278
[2025-10-02 18:10:52] [Iter 1207/2250] R4[162/2400] | LR: 0.029720 | E: -42.286951 | E_var:     0.5755 | E_err:   0.011854
[2025-10-02 18:10:57] [Iter 1208/2250] R4[164/2400] | LR: 0.029713 | E: -42.270944 | E_var:     0.3804 | E_err:   0.009637
[2025-10-02 18:11:02] [Iter 1209/2250] R4[166/2400] | LR: 0.029706 | E: -42.274732 | E_var:     0.5172 | E_err:   0.011236
[2025-10-02 18:11:07] [Iter 1210/2250] R4[168/2400] | LR: 0.029699 | E: -42.297659 | E_var:     0.9576 | E_err:   0.015290
[2025-10-02 18:11:12] [Iter 1211/2250] R4[170/2400] | LR: 0.029692 | E: -42.285808 | E_var:     0.5704 | E_err:   0.011801
[2025-10-02 18:11:18] [Iter 1212/2250] R4[172/2400] | LR: 0.029685 | E: -42.285007 | E_var:     0.4314 | E_err:   0.010262
[2025-10-02 18:11:23] [Iter 1213/2250] R4[174/2400] | LR: 0.029677 | E: -42.278469 | E_var:     0.3629 | E_err:   0.009413
[2025-10-02 18:11:28] [Iter 1214/2250] R4[176/2400] | LR: 0.029670 | E: -42.288463 | E_var:     0.6023 | E_err:   0.012126
[2025-10-02 18:11:33] [Iter 1215/2250] R4[178/2400] | LR: 0.029662 | E: -42.290862 | E_var:     0.4287 | E_err:   0.010231
[2025-10-02 18:11:38] [Iter 1216/2250] R4[180/2400] | LR: 0.029655 | E: -42.280258 | E_var:     0.4532 | E_err:   0.010519
[2025-10-02 18:11:43] [Iter 1217/2250] R4[182/2400] | LR: 0.029647 | E: -42.273347 | E_var:     0.4428 | E_err:   0.010398
[2025-10-02 18:11:48] [Iter 1218/2250] R4[184/2400] | LR: 0.029639 | E: -42.272908 | E_var:     0.4796 | E_err:   0.010821
[2025-10-02 18:11:54] [Iter 1219/2250] R4[186/2400] | LR: 0.029631 | E: -42.284928 | E_var:     0.3696 | E_err:   0.009500
[2025-10-02 18:11:59] [Iter 1220/2250] R4[188/2400] | LR: 0.029623 | E: -42.296301 | E_var:     0.4161 | E_err:   0.010078
[2025-10-02 18:12:04] [Iter 1221/2250] R4[190/2400] | LR: 0.029615 | E: -42.286960 | E_var:     0.3810 | E_err:   0.009645
[2025-10-02 18:12:09] [Iter 1222/2250] R4[192/2400] | LR: 0.029607 | E: -42.278753 | E_var:     0.3680 | E_err:   0.009479
[2025-10-02 18:12:14] [Iter 1223/2250] R4[194/2400] | LR: 0.029599 | E: -42.290778 | E_var:     0.3798 | E_err:   0.009629
[2025-10-02 18:12:19] [Iter 1224/2250] R4[196/2400] | LR: 0.029591 | E: -42.292646 | E_var:     0.4688 | E_err:   0.010698
[2025-10-02 18:12:24] [Iter 1225/2250] R4[198/2400] | LR: 0.029583 | E: -42.285211 | E_var:     0.5292 | E_err:   0.011367
[2025-10-02 18:12:30] [Iter 1226/2250] R4[200/2400] | LR: 0.029574 | E: -42.280255 | E_var:     0.3816 | E_err:   0.009652
[2025-10-02 18:12:35] [Iter 1227/2250] R4[202/2400] | LR: 0.029566 | E: -42.283513 | E_var:     0.6026 | E_err:   0.012130
[2025-10-02 18:12:40] [Iter 1228/2250] R4[204/2400] | LR: 0.029557 | E: -42.295838 | E_var:     0.5155 | E_err:   0.011218
[2025-10-02 18:12:45] [Iter 1229/2250] R4[206/2400] | LR: 0.029548 | E: -42.273560 | E_var:     0.4900 | E_err:   0.010937
[2025-10-02 18:12:50] [Iter 1230/2250] R4[208/2400] | LR: 0.029540 | E: -42.270351 | E_var:     0.4351 | E_err:   0.010306
[2025-10-02 18:12:55] [Iter 1231/2250] R4[210/2400] | LR: 0.029531 | E: -42.298442 | E_var:     0.3714 | E_err:   0.009523
[2025-10-02 18:13:00] [Iter 1232/2250] R4[212/2400] | LR: 0.029522 | E: -42.288333 | E_var:     0.4511 | E_err:   0.010494
[2025-10-02 18:13:06] [Iter 1233/2250] R4[214/2400] | LR: 0.029513 | E: -42.277165 | E_var:     0.4231 | E_err:   0.010163
[2025-10-02 18:13:11] [Iter 1234/2250] R4[216/2400] | LR: 0.029504 | E: -42.279032 | E_var:     0.4799 | E_err:   0.010824
[2025-10-02 18:13:16] [Iter 1235/2250] R4[218/2400] | LR: 0.029494 | E: -42.296088 | E_var:     0.3762 | E_err:   0.009584
[2025-10-02 18:13:21] [Iter 1236/2250] R4[220/2400] | LR: 0.029485 | E: -42.281842 | E_var:     0.4458 | E_err:   0.010433
[2025-10-02 18:13:26] [Iter 1237/2250] R4[222/2400] | LR: 0.029476 | E: -42.298010 | E_var:     0.4067 | E_err:   0.009964
[2025-10-02 18:13:31] [Iter 1238/2250] R4[224/2400] | LR: 0.029466 | E: -42.284847 | E_var:     0.4064 | E_err:   0.009961
[2025-10-02 18:13:36] [Iter 1239/2250] R4[226/2400] | LR: 0.029457 | E: -42.309029 | E_var:     0.4186 | E_err:   0.010110
[2025-10-02 18:13:42] [Iter 1240/2250] R4[228/2400] | LR: 0.029447 | E: -42.284968 | E_var:     0.4371 | E_err:   0.010330
[2025-10-02 18:13:47] [Iter 1241/2250] R4[230/2400] | LR: 0.029438 | E: -42.291443 | E_var:     0.3623 | E_err:   0.009406
[2025-10-02 18:13:52] [Iter 1242/2250] R4[232/2400] | LR: 0.029428 | E: -42.285310 | E_var:     0.4258 | E_err:   0.010196
[2025-10-02 18:13:57] [Iter 1243/2250] R4[234/2400] | LR: 0.029418 | E: -42.274685 | E_var:     0.4320 | E_err:   0.010270
[2025-10-02 18:14:02] [Iter 1244/2250] R4[236/2400] | LR: 0.029408 | E: -42.285620 | E_var:     0.4887 | E_err:   0.010923
[2025-10-02 18:14:08] [Iter 1245/2250] R4[238/2400] | LR: 0.029398 | E: -42.272108 | E_var:     0.4487 | E_err:   0.010466
[2025-10-02 18:14:13] [Iter 1246/2250] R4[240/2400] | LR: 0.029388 | E: -42.268167 | E_var:     0.4582 | E_err:   0.010577
[2025-10-02 18:14:18] [Iter 1247/2250] R4[242/2400] | LR: 0.029378 | E: -42.281367 | E_var:     0.4388 | E_err:   0.010350
[2025-10-02 18:14:23] [Iter 1248/2250] R4[244/2400] | LR: 0.029368 | E: -42.283216 | E_var:     0.5432 | E_err:   0.011516
[2025-10-02 18:14:28] [Iter 1249/2250] R4[246/2400] | LR: 0.029358 | E: -42.274626 | E_var:     0.4086 | E_err:   0.009988
[2025-10-02 18:14:33] [Iter 1250/2250] R4[248/2400] | LR: 0.029347 | E: -42.304687 | E_var:     0.4366 | E_err:   0.010324
[2025-10-02 18:14:39] [Iter 1251/2250] R4[250/2400] | LR: 0.029337 | E: -42.292385 | E_var:     0.9787 | E_err:   0.015457
[2025-10-02 18:14:44] [Iter 1252/2250] R4[252/2400] | LR: 0.029326 | E: -42.289915 | E_var:     0.3235 | E_err:   0.008887
[2025-10-02 18:14:49] [Iter 1253/2250] R4[254/2400] | LR: 0.029315 | E: -42.288462 | E_var:     0.3563 | E_err:   0.009326
[2025-10-02 18:14:54] [Iter 1254/2250] R4[256/2400] | LR: 0.029305 | E: -42.280438 | E_var:     0.4166 | E_err:   0.010085
[2025-10-02 18:14:59] [Iter 1255/2250] R4[258/2400] | LR: 0.029294 | E: -42.274202 | E_var:     0.5496 | E_err:   0.011584
[2025-10-02 18:15:04] [Iter 1256/2250] R4[260/2400] | LR: 0.029283 | E: -42.274634 | E_var:     0.4288 | E_err:   0.010232
[2025-10-02 18:15:09] [Iter 1257/2250] R4[262/2400] | LR: 0.029272 | E: -42.287001 | E_var:     0.5047 | E_err:   0.011100
[2025-10-02 18:15:15] [Iter 1258/2250] R4[264/2400] | LR: 0.029261 | E: -42.292005 | E_var:     0.3497 | E_err:   0.009240
[2025-10-02 18:15:20] [Iter 1259/2250] R4[266/2400] | LR: 0.029250 | E: -42.287917 | E_var:     0.4543 | E_err:   0.010532
[2025-10-02 18:15:25] [Iter 1260/2250] R4[268/2400] | LR: 0.029239 | E: -42.288662 | E_var:     0.3788 | E_err:   0.009616
[2025-10-02 18:15:30] [Iter 1261/2250] R4[270/2400] | LR: 0.029227 | E: -42.284565 | E_var:     0.3493 | E_err:   0.009234
[2025-10-02 18:15:35] [Iter 1262/2250] R4[272/2400] | LR: 0.029216 | E: -42.273931 | E_var:     0.4128 | E_err:   0.010039
[2025-10-02 18:15:40] [Iter 1263/2250] R4[274/2400] | LR: 0.029205 | E: -42.295083 | E_var:     0.4497 | E_err:   0.010478
[2025-10-02 18:15:46] [Iter 1264/2250] R4[276/2400] | LR: 0.029193 | E: -42.274800 | E_var:     0.6766 | E_err:   0.012852
[2025-10-02 18:15:51] [Iter 1265/2250] R4[278/2400] | LR: 0.029181 | E: -42.273367 | E_var:     0.4105 | E_err:   0.010011
[2025-10-02 18:15:56] [Iter 1266/2250] R4[280/2400] | LR: 0.029170 | E: -42.286137 | E_var:     0.4038 | E_err:   0.009929
[2025-10-02 18:16:01] [Iter 1267/2250] R4[282/2400] | LR: 0.029158 | E: -42.279993 | E_var:     0.4020 | E_err:   0.009907
[2025-10-02 18:16:06] [Iter 1268/2250] R4[284/2400] | LR: 0.029146 | E: -42.295806 | E_var:     0.3938 | E_err:   0.009805
[2025-10-02 18:16:11] [Iter 1269/2250] R4[286/2400] | LR: 0.029134 | E: -42.301108 | E_var:     0.4552 | E_err:   0.010543
[2025-10-02 18:16:16] [Iter 1270/2250] R4[288/2400] | LR: 0.029122 | E: -42.273838 | E_var:     0.4487 | E_err:   0.010466
[2025-10-02 18:16:22] [Iter 1271/2250] R4[290/2400] | LR: 0.029110 | E: -42.295597 | E_var:     0.3685 | E_err:   0.009486
[2025-10-02 18:16:27] [Iter 1272/2250] R4[292/2400] | LR: 0.029098 | E: -42.287446 | E_var:     0.9650 | E_err:   0.015349
[2025-10-02 18:16:32] [Iter 1273/2250] R4[294/2400] | LR: 0.029086 | E: -42.290250 | E_var:     0.4003 | E_err:   0.009886
[2025-10-02 18:16:37] [Iter 1274/2250] R4[296/2400] | LR: 0.029073 | E: -42.279295 | E_var:     0.3837 | E_err:   0.009678
[2025-10-02 18:16:42] [Iter 1275/2250] R4[298/2400] | LR: 0.029061 | E: -42.281174 | E_var:     0.5356 | E_err:   0.011435
[2025-10-02 18:16:47] [Iter 1276/2250] R4[300/2400] | LR: 0.029048 | E: -42.284091 | E_var:     0.3924 | E_err:   0.009788
[2025-10-02 18:16:52] [Iter 1277/2250] R4[302/2400] | LR: 0.029036 | E: -42.283228 | E_var:     0.4674 | E_err:   0.010683
[2025-10-02 18:16:58] [Iter 1278/2250] R4[304/2400] | LR: 0.029023 | E: -42.295881 | E_var:     0.5189 | E_err:   0.011255
[2025-10-02 18:17:03] [Iter 1279/2250] R4[306/2400] | LR: 0.029011 | E: -42.287966 | E_var:     0.4737 | E_err:   0.010755
[2025-10-02 18:17:08] [Iter 1280/2250] R4[308/2400] | LR: 0.028998 | E: -42.306373 | E_var:     0.3514 | E_err:   0.009263
[2025-10-02 18:17:13] [Iter 1281/2250] R4[310/2400] | LR: 0.028985 | E: -42.293302 | E_var:     0.3897 | E_err:   0.009754
[2025-10-02 18:17:18] [Iter 1282/2250] R4[312/2400] | LR: 0.028972 | E: -42.290340 | E_var:     0.3947 | E_err:   0.009816
[2025-10-02 18:17:23] [Iter 1283/2250] R4[314/2400] | LR: 0.028959 | E: -42.280905 | E_var:     0.3840 | E_err:   0.009682
[2025-10-02 18:17:29] [Iter 1284/2250] R4[316/2400] | LR: 0.028946 | E: -42.300596 | E_var:     0.4336 | E_err:   0.010289
[2025-10-02 18:17:34] [Iter 1285/2250] R4[318/2400] | LR: 0.028933 | E: -42.286525 | E_var:     0.3955 | E_err:   0.009827
[2025-10-02 18:17:39] [Iter 1286/2250] R4[320/2400] | LR: 0.028919 | E: -42.268419 | E_var:     0.3850 | E_err:   0.009695
[2025-10-02 18:17:44] [Iter 1287/2250] R4[322/2400] | LR: 0.028906 | E: -42.278505 | E_var:     0.4002 | E_err:   0.009884
[2025-10-02 18:17:49] [Iter 1288/2250] R4[324/2400] | LR: 0.028893 | E: -42.273555 | E_var:     0.3759 | E_err:   0.009579
[2025-10-02 18:17:54] [Iter 1289/2250] R4[326/2400] | LR: 0.028879 | E: -42.296373 | E_var:     0.3779 | E_err:   0.009605
[2025-10-02 18:17:59] [Iter 1290/2250] R4[328/2400] | LR: 0.028865 | E: -42.301598 | E_var:     0.5666 | E_err:   0.011761
[2025-10-02 18:18:05] [Iter 1291/2250] R4[330/2400] | LR: 0.028852 | E: -42.277703 | E_var:     0.4436 | E_err:   0.010407
[2025-10-02 18:18:10] [Iter 1292/2250] R4[332/2400] | LR: 0.028838 | E: -42.289512 | E_var:     0.4183 | E_err:   0.010106
[2025-10-02 18:18:15] [Iter 1293/2250] R4[334/2400] | LR: 0.028824 | E: -42.305584 | E_var:     1.6705 | E_err:   0.020195
[2025-10-02 18:18:20] [Iter 1294/2250] R4[336/2400] | LR: 0.028810 | E: -42.253866 | E_var:     0.7695 | E_err:   0.013707
[2025-10-02 18:18:25] [Iter 1295/2250] R4[338/2400] | LR: 0.028796 | E: -42.296258 | E_var:     0.4115 | E_err:   0.010023
[2025-10-02 18:18:30] [Iter 1296/2250] R4[340/2400] | LR: 0.028782 | E: -42.279874 | E_var:     0.4182 | E_err:   0.010105
[2025-10-02 18:18:36] [Iter 1297/2250] R4[342/2400] | LR: 0.028768 | E: -42.287181 | E_var:     0.3856 | E_err:   0.009702
[2025-10-02 18:18:41] [Iter 1298/2250] R4[344/2400] | LR: 0.028754 | E: -42.296364 | E_var:     0.3341 | E_err:   0.009032
[2025-10-02 18:18:46] [Iter 1299/2250] R4[346/2400] | LR: 0.028740 | E: -42.286665 | E_var:     0.4336 | E_err:   0.010289
[2025-10-02 18:18:51] [Iter 1300/2250] R4[348/2400] | LR: 0.028725 | E: -42.299788 | E_var:     0.4922 | E_err:   0.010962
[2025-10-02 18:18:56] [Iter 1301/2250] R4[350/2400] | LR: 0.028711 | E: -42.283958 | E_var:     0.3890 | E_err:   0.009745
[2025-10-02 18:19:02] [Iter 1302/2250] R4[352/2400] | LR: 0.028696 | E: -42.286715 | E_var:     0.4610 | E_err:   0.010609
[2025-10-02 18:19:07] [Iter 1303/2250] R4[354/2400] | LR: 0.028682 | E: -42.299810 | E_var:     0.3767 | E_err:   0.009591
[2025-10-02 18:19:12] [Iter 1304/2250] R4[356/2400] | LR: 0.028667 | E: -42.306848 | E_var:     0.3829 | E_err:   0.009669
[2025-10-02 18:19:17] [Iter 1305/2250] R4[358/2400] | LR: 0.028652 | E: -42.289299 | E_var:     0.3988 | E_err:   0.009867
[2025-10-02 18:19:22] [Iter 1306/2250] R4[360/2400] | LR: 0.028638 | E: -42.292195 | E_var:     0.3731 | E_err:   0.009544
[2025-10-02 18:19:27] [Iter 1307/2250] R4[362/2400] | LR: 0.028623 | E: -42.286012 | E_var:     0.4313 | E_err:   0.010261
[2025-10-02 18:19:32] [Iter 1308/2250] R4[364/2400] | LR: 0.028608 | E: -42.279749 | E_var:     0.4264 | E_err:   0.010203
[2025-10-02 18:19:38] [Iter 1309/2250] R4[366/2400] | LR: 0.028593 | E: -42.295167 | E_var:     0.5704 | E_err:   0.011800
[2025-10-02 18:19:43] [Iter 1310/2250] R4[368/2400] | LR: 0.028578 | E: -42.299779 | E_var:     0.4229 | E_err:   0.010161
[2025-10-02 18:19:48] [Iter 1311/2250] R4[370/2400] | LR: 0.028562 | E: -42.263753 | E_var:     0.4864 | E_err:   0.010897
[2025-10-02 18:19:53] [Iter 1312/2250] R4[372/2400] | LR: 0.028547 | E: -42.300804 | E_var:     0.3557 | E_err:   0.009319
[2025-10-02 18:19:58] [Iter 1313/2250] R4[374/2400] | LR: 0.028532 | E: -42.293710 | E_var:     0.3930 | E_err:   0.009796
[2025-10-02 18:20:03] [Iter 1314/2250] R4[376/2400] | LR: 0.028516 | E: -42.286439 | E_var:     0.3634 | E_err:   0.009419
[2025-10-02 18:20:09] [Iter 1315/2250] R4[378/2400] | LR: 0.028501 | E: -42.306671 | E_var:     0.4639 | E_err:   0.010642
[2025-10-02 18:20:14] [Iter 1316/2250] R4[380/2400] | LR: 0.028485 | E: -42.292609 | E_var:     0.4439 | E_err:   0.010410
[2025-10-02 18:20:19] [Iter 1317/2250] R4[382/2400] | LR: 0.028470 | E: -42.284902 | E_var:     0.4679 | E_err:   0.010688
[2025-10-02 18:20:24] [Iter 1318/2250] R4[384/2400] | LR: 0.028454 | E: -42.291017 | E_var:     0.3955 | E_err:   0.009826
[2025-10-02 18:20:29] [Iter 1319/2250] R4[386/2400] | LR: 0.028438 | E: -42.276887 | E_var:     0.4355 | E_err:   0.010311
[2025-10-02 18:20:34] [Iter 1320/2250] R4[388/2400] | LR: 0.028422 | E: -42.287997 | E_var:     0.4140 | E_err:   0.010053
[2025-10-02 18:20:40] [Iter 1321/2250] R4[390/2400] | LR: 0.028406 | E: -42.272760 | E_var:     0.3816 | E_err:   0.009652
[2025-10-02 18:20:45] [Iter 1322/2250] R4[392/2400] | LR: 0.028390 | E: -42.306489 | E_var:     0.4332 | E_err:   0.010284
[2025-10-02 18:20:50] [Iter 1323/2250] R4[394/2400] | LR: 0.028374 | E: -42.294223 | E_var:     0.5143 | E_err:   0.011205
[2025-10-02 18:20:55] [Iter 1324/2250] R4[396/2400] | LR: 0.028358 | E: -42.281285 | E_var:     0.4272 | E_err:   0.010213
[2025-10-02 18:21:00] [Iter 1325/2250] R4[398/2400] | LR: 0.028342 | E: -42.293681 | E_var:     0.3462 | E_err:   0.009194
[2025-10-02 18:21:05] [Iter 1326/2250] R4[400/2400] | LR: 0.028325 | E: -42.282865 | E_var:     0.3648 | E_err:   0.009437
[2025-10-02 18:21:10] [Iter 1327/2250] R4[402/2400] | LR: 0.028309 | E: -42.277767 | E_var:     0.5316 | E_err:   0.011392
[2025-10-02 18:21:16] [Iter 1328/2250] R4[404/2400] | LR: 0.028292 | E: -42.278100 | E_var:     0.3483 | E_err:   0.009222
[2025-10-02 18:21:21] [Iter 1329/2250] R4[406/2400] | LR: 0.028276 | E: -42.291520 | E_var:     0.4305 | E_err:   0.010252
[2025-10-02 18:21:26] [Iter 1330/2250] R4[408/2400] | LR: 0.028259 | E: -42.298710 | E_var:     0.4214 | E_err:   0.010144
[2025-10-02 18:21:31] [Iter 1331/2250] R4[410/2400] | LR: 0.028243 | E: -42.302871 | E_var:     0.4538 | E_err:   0.010526
[2025-10-02 18:21:36] [Iter 1332/2250] R4[412/2400] | LR: 0.028226 | E: -42.286367 | E_var:     0.4405 | E_err:   0.010370
[2025-10-02 18:21:41] [Iter 1333/2250] R4[414/2400] | LR: 0.028209 | E: -42.289515 | E_var:     0.3829 | E_err:   0.009669
[2025-10-02 18:21:47] [Iter 1334/2250] R4[416/2400] | LR: 0.028192 | E: -42.284188 | E_var:     0.4495 | E_err:   0.010476
[2025-10-02 18:21:52] [Iter 1335/2250] R4[418/2400] | LR: 0.028175 | E: -42.291112 | E_var:     0.4448 | E_err:   0.010421
[2025-10-02 18:21:57] [Iter 1336/2250] R4[420/2400] | LR: 0.028158 | E: -42.273317 | E_var:     0.4456 | E_err:   0.010430
[2025-10-02 18:22:02] [Iter 1337/2250] R4[422/2400] | LR: 0.028141 | E: -42.269808 | E_var:     0.4098 | E_err:   0.010002
[2025-10-02 18:22:07] [Iter 1338/2250] R4[424/2400] | LR: 0.028124 | E: -42.307631 | E_var:     0.4138 | E_err:   0.010051
[2025-10-02 18:22:12] [Iter 1339/2250] R4[426/2400] | LR: 0.028106 | E: -42.295068 | E_var:     0.4066 | E_err:   0.009963
[2025-10-02 18:22:18] [Iter 1340/2250] R4[428/2400] | LR: 0.028089 | E: -42.301570 | E_var:     0.3978 | E_err:   0.009855
[2025-10-02 18:22:23] [Iter 1341/2250] R4[430/2400] | LR: 0.028072 | E: -42.276368 | E_var:     0.4485 | E_err:   0.010465
[2025-10-02 18:22:28] [Iter 1342/2250] R4[432/2400] | LR: 0.028054 | E: -42.294203 | E_var:     0.4053 | E_err:   0.009947
[2025-10-02 18:22:33] [Iter 1343/2250] R4[434/2400] | LR: 0.028037 | E: -42.283852 | E_var:     0.3626 | E_err:   0.009409
[2025-10-02 18:22:38] [Iter 1344/2250] R4[436/2400] | LR: 0.028019 | E: -42.292305 | E_var:     0.3891 | E_err:   0.009747
[2025-10-02 18:22:43] [Iter 1345/2250] R4[438/2400] | LR: 0.028001 | E: -42.310288 | E_var:     0.4757 | E_err:   0.010777
[2025-10-02 18:22:49] [Iter 1346/2250] R4[440/2400] | LR: 0.027983 | E: -42.278561 | E_var:     0.4303 | E_err:   0.010249
[2025-10-02 18:22:54] [Iter 1347/2250] R4[442/2400] | LR: 0.027966 | E: -42.295006 | E_var:     0.3920 | E_err:   0.009783
[2025-10-02 18:22:59] [Iter 1348/2250] R4[444/2400] | LR: 0.027948 | E: -42.289783 | E_var:     0.4139 | E_err:   0.010053
[2025-10-02 18:23:04] [Iter 1349/2250] R4[446/2400] | LR: 0.027930 | E: -42.290355 | E_var:     0.4510 | E_err:   0.010493
[2025-10-02 18:23:09] [Iter 1350/2250] R4[448/2400] | LR: 0.027912 | E: -42.287084 | E_var:     0.3460 | E_err:   0.009191
[2025-10-02 18:23:14] [Iter 1351/2250] R4[450/2400] | LR: 0.027893 | E: -42.290090 | E_var:     0.3516 | E_err:   0.009265
[2025-10-02 18:23:19] [Iter 1352/2250] R4[452/2400] | LR: 0.027875 | E: -42.280685 | E_var:     0.4035 | E_err:   0.009925
[2025-10-02 18:23:25] [Iter 1353/2250] R4[454/2400] | LR: 0.027857 | E: -42.266098 | E_var:     0.5926 | E_err:   0.012028
[2025-10-02 18:23:30] [Iter 1354/2250] R4[456/2400] | LR: 0.027839 | E: -42.293796 | E_var:     0.4807 | E_err:   0.010833
[2025-10-02 18:23:35] [Iter 1355/2250] R4[458/2400] | LR: 0.027820 | E: -42.298219 | E_var:     0.4306 | E_err:   0.010254
[2025-10-02 18:23:40] [Iter 1356/2250] R4[460/2400] | LR: 0.027802 | E: -42.284323 | E_var:     0.3299 | E_err:   0.008975
[2025-10-02 18:23:45] [Iter 1357/2250] R4[462/2400] | LR: 0.027783 | E: -42.266056 | E_var:     0.5694 | E_err:   0.011790
[2025-10-02 18:23:50] [Iter 1358/2250] R4[464/2400] | LR: 0.027764 | E: -42.286853 | E_var:     0.5214 | E_err:   0.011282
[2025-10-02 18:23:56] [Iter 1359/2250] R4[466/2400] | LR: 0.027746 | E: -42.287457 | E_var:     0.4393 | E_err:   0.010357
[2025-10-02 18:24:01] [Iter 1360/2250] R4[468/2400] | LR: 0.027727 | E: -42.278502 | E_var:     0.3562 | E_err:   0.009326
[2025-10-02 18:24:06] [Iter 1361/2250] R4[470/2400] | LR: 0.027708 | E: -42.279912 | E_var:     0.3813 | E_err:   0.009648
[2025-10-02 18:24:11] [Iter 1362/2250] R4[472/2400] | LR: 0.027689 | E: -42.295553 | E_var:     0.3410 | E_err:   0.009125
[2025-10-02 18:24:16] [Iter 1363/2250] R4[474/2400] | LR: 0.027670 | E: -42.284526 | E_var:     0.5314 | E_err:   0.011391
[2025-10-02 18:24:21] [Iter 1364/2250] R4[476/2400] | LR: 0.027651 | E: -42.302585 | E_var:     0.3830 | E_err:   0.009670
[2025-10-02 18:24:27] [Iter 1365/2250] R4[478/2400] | LR: 0.027632 | E: -42.284425 | E_var:     0.3808 | E_err:   0.009642
[2025-10-02 18:24:32] [Iter 1366/2250] R4[480/2400] | LR: 0.027613 | E: -42.301336 | E_var:     0.4582 | E_err:   0.010577
[2025-10-02 18:24:37] [Iter 1367/2250] R4[482/2400] | LR: 0.027593 | E: -42.296795 | E_var:     0.4831 | E_err:   0.010860
[2025-10-02 18:24:42] [Iter 1368/2250] R4[484/2400] | LR: 0.027574 | E: -42.296931 | E_var:     0.4574 | E_err:   0.010568
[2025-10-02 18:24:47] [Iter 1369/2250] R4[486/2400] | LR: 0.027555 | E: -42.282094 | E_var:     0.3356 | E_err:   0.009052
[2025-10-02 18:24:52] [Iter 1370/2250] R4[488/2400] | LR: 0.027535 | E: -42.273970 | E_var:     0.4009 | E_err:   0.009893
[2025-10-02 18:24:57] [Iter 1371/2250] R4[490/2400] | LR: 0.027516 | E: -42.287778 | E_var:     0.3461 | E_err:   0.009193
[2025-10-02 18:25:03] [Iter 1372/2250] R4[492/2400] | LR: 0.027496 | E: -42.311461 | E_var:     0.3904 | E_err:   0.009763
[2025-10-02 18:25:08] [Iter 1373/2250] R4[494/2400] | LR: 0.027476 | E: -42.295217 | E_var:     0.3949 | E_err:   0.009819
[2025-10-02 18:25:13] [Iter 1374/2250] R4[496/2400] | LR: 0.027457 | E: -42.295259 | E_var:     0.4468 | E_err:   0.010444
[2025-10-02 18:25:18] [Iter 1375/2250] R4[498/2400] | LR: 0.027437 | E: -42.293183 | E_var:     0.4626 | E_err:   0.010627
[2025-10-02 18:25:23] [Iter 1376/2250] R4[500/2400] | LR: 0.027417 | E: -42.297655 | E_var:     0.4129 | E_err:   0.010040
[2025-10-02 18:25:28] [Iter 1377/2250] R4[502/2400] | LR: 0.027397 | E: -42.291263 | E_var:     0.4048 | E_err:   0.009941
[2025-10-02 18:25:34] [Iter 1378/2250] R4[504/2400] | LR: 0.027377 | E: -42.289810 | E_var:     0.3393 | E_err:   0.009101
[2025-10-02 18:25:39] [Iter 1379/2250] R4[506/2400] | LR: 0.027357 | E: -42.287283 | E_var:     0.3464 | E_err:   0.009196
[2025-10-02 18:25:44] [Iter 1380/2250] R4[508/2400] | LR: 0.027337 | E: -42.290110 | E_var:     0.5030 | E_err:   0.011082
[2025-10-02 18:25:49] [Iter 1381/2250] R4[510/2400] | LR: 0.027316 | E: -42.287759 | E_var:     0.4942 | E_err:   0.010985
[2025-10-02 18:25:54] [Iter 1382/2250] R4[512/2400] | LR: 0.027296 | E: -42.295011 | E_var:     0.4433 | E_err:   0.010403
[2025-10-02 18:25:59] [Iter 1383/2250] R4[514/2400] | LR: 0.027276 | E: -42.289850 | E_var:     0.4339 | E_err:   0.010293
[2025-10-02 18:26:05] [Iter 1384/2250] R4[516/2400] | LR: 0.027255 | E: -42.278008 | E_var:     2.5720 | E_err:   0.025059
[2025-10-02 18:26:10] [Iter 1385/2250] R4[518/2400] | LR: 0.027235 | E: -42.297240 | E_var:     0.5269 | E_err:   0.011342
[2025-10-02 18:26:15] [Iter 1386/2250] R4[520/2400] | LR: 0.027214 | E: -42.280611 | E_var:     0.4312 | E_err:   0.010260
[2025-10-02 18:26:20] [Iter 1387/2250] R4[522/2400] | LR: 0.027194 | E: -42.307378 | E_var:     0.4457 | E_err:   0.010431
[2025-10-02 18:26:25] [Iter 1388/2250] R4[524/2400] | LR: 0.027173 | E: -42.289082 | E_var:     0.4267 | E_err:   0.010207
[2025-10-02 18:26:30] [Iter 1389/2250] R4[526/2400] | LR: 0.027152 | E: -42.289028 | E_var:     0.4536 | E_err:   0.010524
[2025-10-02 18:26:36] [Iter 1390/2250] R4[528/2400] | LR: 0.027131 | E: -42.296632 | E_var:     0.3682 | E_err:   0.009481
[2025-10-02 18:26:41] [Iter 1391/2250] R4[530/2400] | LR: 0.027111 | E: -42.296629 | E_var:     0.4287 | E_err:   0.010230
[2025-10-02 18:26:46] [Iter 1392/2250] R4[532/2400] | LR: 0.027090 | E: -42.289069 | E_var:     0.3922 | E_err:   0.009785
[2025-10-02 18:26:51] [Iter 1393/2250] R4[534/2400] | LR: 0.027069 | E: -42.278095 | E_var:     0.5071 | E_err:   0.011126
[2025-10-02 18:26:56] [Iter 1394/2250] R4[536/2400] | LR: 0.027047 | E: -42.294383 | E_var:     0.4469 | E_err:   0.010445
[2025-10-02 18:27:01] [Iter 1395/2250] R4[538/2400] | LR: 0.027026 | E: -42.294633 | E_var:     0.3386 | E_err:   0.009092
[2025-10-02 18:27:07] [Iter 1396/2250] R4[540/2400] | LR: 0.027005 | E: -42.301266 | E_var:     0.4268 | E_err:   0.010208
[2025-10-02 18:27:12] [Iter 1397/2250] R4[542/2400] | LR: 0.026984 | E: -42.282779 | E_var:     0.3880 | E_err:   0.009733
[2025-10-02 18:27:17] [Iter 1398/2250] R4[544/2400] | LR: 0.026962 | E: -42.276805 | E_var:     0.5024 | E_err:   0.011075
[2025-10-02 18:27:22] [Iter 1399/2250] R4[546/2400] | LR: 0.026941 | E: -42.292533 | E_var:     0.4828 | E_err:   0.010857
[2025-10-02 18:27:27] [Iter 1400/2250] R4[548/2400] | LR: 0.026920 | E: -42.304421 | E_var:     0.7377 | E_err:   0.013420
[2025-10-02 18:27:27] ✓ Checkpoint saved: checkpoint_iter_001400.pkl
[2025-10-02 18:27:32] [Iter 1401/2250] R4[550/2400] | LR: 0.026898 | E: -42.305153 | E_var:     0.4030 | E_err:   0.009920
[2025-10-02 18:27:38] [Iter 1402/2250] R4[552/2400] | LR: 0.026876 | E: -42.278175 | E_var:     0.4467 | E_err:   0.010443
[2025-10-02 18:27:43] [Iter 1403/2250] R4[554/2400] | LR: 0.026855 | E: -42.298568 | E_var:     0.4236 | E_err:   0.010169
[2025-10-02 18:27:48] [Iter 1404/2250] R4[556/2400] | LR: 0.026833 | E: -42.294117 | E_var:     0.6209 | E_err:   0.012312
[2025-10-02 18:27:53] [Iter 1405/2250] R4[558/2400] | LR: 0.026811 | E: -42.297683 | E_var:     0.3998 | E_err:   0.009880
[2025-10-02 18:27:58] [Iter 1406/2250] R4[560/2400] | LR: 0.026789 | E: -42.289130 | E_var:     0.4064 | E_err:   0.009960
[2025-10-02 18:28:03] [Iter 1407/2250] R4[562/2400] | LR: 0.026767 | E: -42.300597 | E_var:     0.4219 | E_err:   0.010149
[2025-10-02 18:28:09] [Iter 1408/2250] R4[564/2400] | LR: 0.026745 | E: -42.296152 | E_var:     0.4006 | E_err:   0.009889
[2025-10-02 18:28:14] [Iter 1409/2250] R4[566/2400] | LR: 0.026723 | E: -42.295055 | E_var:     0.3926 | E_err:   0.009790
[2025-10-02 18:28:19] [Iter 1410/2250] R4[568/2400] | LR: 0.026701 | E: -42.292648 | E_var:     0.5083 | E_err:   0.011140
[2025-10-02 18:28:24] [Iter 1411/2250] R4[570/2400] | LR: 0.026679 | E: -42.282413 | E_var:     0.3591 | E_err:   0.009363
[2025-10-02 18:28:29] [Iter 1412/2250] R4[572/2400] | LR: 0.026657 | E: -42.283868 | E_var:     0.3878 | E_err:   0.009731
[2025-10-02 18:28:34] [Iter 1413/2250] R4[574/2400] | LR: 0.026634 | E: -42.289673 | E_var:     0.5241 | E_err:   0.011312
[2025-10-02 18:28:40] [Iter 1414/2250] R4[576/2400] | LR: 0.026612 | E: -42.297825 | E_var:     0.3573 | E_err:   0.009340
[2025-10-02 18:28:45] [Iter 1415/2250] R4[578/2400] | LR: 0.026590 | E: -42.309451 | E_var:     0.4942 | E_err:   0.010985
[2025-10-02 18:28:50] [Iter 1416/2250] R4[580/2400] | LR: 0.026567 | E: -42.295651 | E_var:     0.3151 | E_err:   0.008771
[2025-10-02 18:28:55] [Iter 1417/2250] R4[582/2400] | LR: 0.026545 | E: -42.287263 | E_var:     0.5739 | E_err:   0.011837
[2025-10-02 18:29:00] [Iter 1418/2250] R4[584/2400] | LR: 0.026522 | E: -42.277874 | E_var:     1.7923 | E_err:   0.020918
[2025-10-02 18:29:05] [Iter 1419/2250] R4[586/2400] | LR: 0.026499 | E: -42.303208 | E_var:     0.3864 | E_err:   0.009713
[2025-10-02 18:29:11] [Iter 1420/2250] R4[588/2400] | LR: 0.026477 | E: -42.293661 | E_var:     0.3962 | E_err:   0.009835
[2025-10-02 18:29:16] [Iter 1421/2250] R4[590/2400] | LR: 0.026454 | E: -42.287825 | E_var:     0.4419 | E_err:   0.010387
[2025-10-02 18:29:21] [Iter 1422/2250] R4[592/2400] | LR: 0.026431 | E: -42.309861 | E_var:     0.4668 | E_err:   0.010675
[2025-10-02 18:29:26] [Iter 1423/2250] R4[594/2400] | LR: 0.026408 | E: -42.295098 | E_var:     0.4216 | E_err:   0.010146
[2025-10-02 18:29:31] [Iter 1424/2250] R4[596/2400] | LR: 0.026385 | E: -42.274891 | E_var:     0.3746 | E_err:   0.009563
[2025-10-02 18:29:36] [Iter 1425/2250] R4[598/2400] | LR: 0.026362 | E: -42.302546 | E_var:     0.4215 | E_err:   0.010144
[2025-10-02 18:29:42] [Iter 1426/2250] R4[600/2400] | LR: 0.026339 | E: -42.289408 | E_var:     0.3759 | E_err:   0.009580
[2025-10-02 18:29:47] [Iter 1427/2250] R4[602/2400] | LR: 0.026316 | E: -42.286330 | E_var:     0.3201 | E_err:   0.008840
[2025-10-02 18:29:52] [Iter 1428/2250] R4[604/2400] | LR: 0.026292 | E: -42.287935 | E_var:     0.3534 | E_err:   0.009289
[2025-10-02 18:29:57] [Iter 1429/2250] R4[606/2400] | LR: 0.026269 | E: -42.285168 | E_var:     0.3976 | E_err:   0.009852
[2025-10-02 18:30:02] [Iter 1430/2250] R4[608/2400] | LR: 0.026246 | E: -42.276855 | E_var:     0.4550 | E_err:   0.010540
[2025-10-02 18:30:07] [Iter 1431/2250] R4[610/2400] | LR: 0.026222 | E: -42.287888 | E_var:     0.3869 | E_err:   0.009719
[2025-10-02 18:30:13] [Iter 1432/2250] R4[612/2400] | LR: 0.026199 | E: -42.303403 | E_var:     0.4252 | E_err:   0.010189
[2025-10-02 18:30:18] [Iter 1433/2250] R4[614/2400] | LR: 0.026175 | E: -42.291225 | E_var:     0.4396 | E_err:   0.010360
[2025-10-02 18:30:23] [Iter 1434/2250] R4[616/2400] | LR: 0.026152 | E: -42.284765 | E_var:     0.4609 | E_err:   0.010608
[2025-10-02 18:30:28] [Iter 1435/2250] R4[618/2400] | LR: 0.026128 | E: -42.294873 | E_var:     0.4507 | E_err:   0.010490
[2025-10-02 18:30:33] [Iter 1436/2250] R4[620/2400] | LR: 0.026104 | E: -42.285278 | E_var:     0.3359 | E_err:   0.009055
[2025-10-02 18:30:38] [Iter 1437/2250] R4[622/2400] | LR: 0.026081 | E: -42.271846 | E_var:     0.4507 | E_err:   0.010490
[2025-10-02 18:30:43] [Iter 1438/2250] R4[624/2400] | LR: 0.026057 | E: -42.298544 | E_var:     0.4645 | E_err:   0.010649
[2025-10-02 18:30:49] [Iter 1439/2250] R4[626/2400] | LR: 0.026033 | E: -42.276001 | E_var:     0.3810 | E_err:   0.009645
[2025-10-02 18:30:54] [Iter 1440/2250] R4[628/2400] | LR: 0.026009 | E: -42.286218 | E_var:     0.4053 | E_err:   0.009948
[2025-10-02 18:30:59] [Iter 1441/2250] R4[630/2400] | LR: 0.025985 | E: -42.291181 | E_var:     0.3640 | E_err:   0.009427
[2025-10-02 18:31:04] [Iter 1442/2250] R4[632/2400] | LR: 0.025961 | E: -42.283274 | E_var:     0.3941 | E_err:   0.009809
[2025-10-02 18:31:09] [Iter 1443/2250] R4[634/2400] | LR: 0.025937 | E: -42.282168 | E_var:     0.5892 | E_err:   0.011993
[2025-10-02 18:31:14] [Iter 1444/2250] R4[636/2400] | LR: 0.025913 | E: -42.303372 | E_var:     0.5263 | E_err:   0.011335
[2025-10-02 18:31:20] [Iter 1445/2250] R4[638/2400] | LR: 0.025888 | E: -42.280507 | E_var:     0.3724 | E_err:   0.009536
[2025-10-02 18:31:25] [Iter 1446/2250] R4[640/2400] | LR: 0.025864 | E: -42.285915 | E_var:     0.3954 | E_err:   0.009825
[2025-10-02 18:31:30] [Iter 1447/2250] R4[642/2400] | LR: 0.025840 | E: -42.300971 | E_var:     0.4217 | E_err:   0.010147
[2025-10-02 18:31:35] [Iter 1448/2250] R4[644/2400] | LR: 0.025815 | E: -42.300594 | E_var:     0.4506 | E_err:   0.010488
[2025-10-02 18:31:40] [Iter 1449/2250] R4[646/2400] | LR: 0.025791 | E: -42.280413 | E_var:     0.3708 | E_err:   0.009515
[2025-10-02 18:31:45] [Iter 1450/2250] R4[648/2400] | LR: 0.025766 | E: -42.299976 | E_var:     0.3803 | E_err:   0.009636
[2025-10-02 18:31:51] [Iter 1451/2250] R4[650/2400] | LR: 0.025742 | E: -42.315625 | E_var:     0.3738 | E_err:   0.009553
[2025-10-02 18:31:56] [Iter 1452/2250] R4[652/2400] | LR: 0.025717 | E: -42.281818 | E_var:     0.4028 | E_err:   0.009917
[2025-10-02 18:32:01] [Iter 1453/2250] R4[654/2400] | LR: 0.025693 | E: -42.281261 | E_var:     0.4309 | E_err:   0.010257
[2025-10-02 18:32:06] [Iter 1454/2250] R4[656/2400] | LR: 0.025668 | E: -42.281213 | E_var:     0.3878 | E_err:   0.009730
[2025-10-02 18:32:11] [Iter 1455/2250] R4[658/2400] | LR: 0.025643 | E: -42.307230 | E_var:     0.4286 | E_err:   0.010229
[2025-10-02 18:32:16] [Iter 1456/2250] R4[660/2400] | LR: 0.025618 | E: -42.298587 | E_var:     0.4451 | E_err:   0.010425
[2025-10-02 18:32:22] [Iter 1457/2250] R4[662/2400] | LR: 0.025593 | E: -42.286345 | E_var:     0.3330 | E_err:   0.009017
[2025-10-02 18:32:27] [Iter 1458/2250] R4[664/2400] | LR: 0.025568 | E: -42.300615 | E_var:     0.4825 | E_err:   0.010853
[2025-10-02 18:32:32] [Iter 1459/2250] R4[666/2400] | LR: 0.025543 | E: -42.309504 | E_var:     0.3953 | E_err:   0.009824
[2025-10-02 18:32:37] [Iter 1460/2250] R4[668/2400] | LR: 0.025518 | E: -42.291079 | E_var:     0.4476 | E_err:   0.010454
[2025-10-02 18:32:42] [Iter 1461/2250] R4[670/2400] | LR: 0.025493 | E: -42.285040 | E_var:     0.4331 | E_err:   0.010283
[2025-10-02 18:32:47] [Iter 1462/2250] R4[672/2400] | LR: 0.025468 | E: -42.305771 | E_var:     0.3136 | E_err:   0.008750
[2025-10-02 18:32:52] [Iter 1463/2250] R4[674/2400] | LR: 0.025443 | E: -42.289723 | E_var:     0.3453 | E_err:   0.009182
[2025-10-02 18:32:58] [Iter 1464/2250] R4[676/2400] | LR: 0.025417 | E: -42.303787 | E_var:     0.3830 | E_err:   0.009670
[2025-10-02 18:33:03] [Iter 1465/2250] R4[678/2400] | LR: 0.025392 | E: -42.304245 | E_var:     0.3648 | E_err:   0.009438
[2025-10-02 18:33:08] [Iter 1466/2250] R4[680/2400] | LR: 0.025367 | E: -42.301353 | E_var:     0.4292 | E_err:   0.010237
[2025-10-02 18:33:13] [Iter 1467/2250] R4[682/2400] | LR: 0.025341 | E: -42.294963 | E_var:     0.4218 | E_err:   0.010147
[2025-10-02 18:33:18] [Iter 1468/2250] R4[684/2400] | LR: 0.025316 | E: -42.309986 | E_var:     0.4970 | E_err:   0.011016
[2025-10-02 18:33:23] [Iter 1469/2250] R4[686/2400] | LR: 0.025290 | E: -42.285834 | E_var:     0.3953 | E_err:   0.009824
[2025-10-02 18:33:29] [Iter 1470/2250] R4[688/2400] | LR: 0.025264 | E: -42.295753 | E_var:     0.4234 | E_err:   0.010167
[2025-10-02 18:33:34] [Iter 1471/2250] R4[690/2400] | LR: 0.025239 | E: -42.297094 | E_var:     0.3973 | E_err:   0.009849
[2025-10-02 18:33:39] [Iter 1472/2250] R4[692/2400] | LR: 0.025213 | E: -42.292408 | E_var:     0.4789 | E_err:   0.010813
[2025-10-02 18:33:44] [Iter 1473/2250] R4[694/2400] | LR: 0.025187 | E: -42.298255 | E_var:     0.5330 | E_err:   0.011407
[2025-10-02 18:33:49] [Iter 1474/2250] R4[696/2400] | LR: 0.025161 | E: -42.286389 | E_var:     0.3996 | E_err:   0.009877
[2025-10-02 18:33:54] [Iter 1475/2250] R4[698/2400] | LR: 0.025135 | E: -42.294826 | E_var:     0.4144 | E_err:   0.010059
[2025-10-02 18:34:00] [Iter 1476/2250] R4[700/2400] | LR: 0.025110 | E: -42.285959 | E_var:     0.4353 | E_err:   0.010309
[2025-10-02 18:34:05] [Iter 1477/2250] R4[702/2400] | LR: 0.025084 | E: -42.285415 | E_var:     0.3751 | E_err:   0.009570
[2025-10-02 18:34:10] [Iter 1478/2250] R4[704/2400] | LR: 0.025057 | E: -42.296475 | E_var:     0.5133 | E_err:   0.011195
[2025-10-02 18:34:15] [Iter 1479/2250] R4[706/2400] | LR: 0.025031 | E: -42.291144 | E_var:     0.3401 | E_err:   0.009112
[2025-10-02 18:34:20] [Iter 1480/2250] R4[708/2400] | LR: 0.025005 | E: -42.303279 | E_var:     0.4467 | E_err:   0.010443
[2025-10-02 18:34:25] [Iter 1481/2250] R4[710/2400] | LR: 0.024979 | E: -42.303327 | E_var:     0.3330 | E_err:   0.009017
[2025-10-02 18:34:31] [Iter 1482/2250] R4[712/2400] | LR: 0.024953 | E: -42.295333 | E_var:     0.4063 | E_err:   0.009960
[2025-10-02 18:34:36] [Iter 1483/2250] R4[714/2400] | LR: 0.024927 | E: -42.292660 | E_var:     0.3886 | E_err:   0.009741
[2025-10-02 18:34:41] [Iter 1484/2250] R4[716/2400] | LR: 0.024900 | E: -42.298005 | E_var:     0.5956 | E_err:   0.012058
[2025-10-02 18:34:46] [Iter 1485/2250] R4[718/2400] | LR: 0.024874 | E: -42.290576 | E_var:     0.4080 | E_err:   0.009980
[2025-10-02 18:34:51] [Iter 1486/2250] R4[720/2400] | LR: 0.024847 | E: -42.308746 | E_var:     0.3964 | E_err:   0.009838
[2025-10-02 18:34:56] [Iter 1487/2250] R4[722/2400] | LR: 0.024821 | E: -42.297588 | E_var:     0.3585 | E_err:   0.009356
[2025-10-02 18:35:02] [Iter 1488/2250] R4[724/2400] | LR: 0.024794 | E: -42.292668 | E_var:     0.3634 | E_err:   0.009419
[2025-10-02 18:35:07] [Iter 1489/2250] R4[726/2400] | LR: 0.024768 | E: -42.299991 | E_var:     0.3470 | E_err:   0.009204
[2025-10-02 18:35:12] [Iter 1490/2250] R4[728/2400] | LR: 0.024741 | E: -42.304516 | E_var:     0.2973 | E_err:   0.008519
[2025-10-02 18:35:17] [Iter 1491/2250] R4[730/2400] | LR: 0.024714 | E: -42.287995 | E_var:     0.5014 | E_err:   0.011064
[2025-10-02 18:35:22] [Iter 1492/2250] R4[732/2400] | LR: 0.024688 | E: -42.291327 | E_var:     0.4417 | E_err:   0.010385
[2025-10-02 18:35:27] [Iter 1493/2250] R4[734/2400] | LR: 0.024661 | E: -42.276836 | E_var:     0.9349 | E_err:   0.015108
[2025-10-02 18:35:33] [Iter 1494/2250] R4[736/2400] | LR: 0.024634 | E: -42.300311 | E_var:     0.5504 | E_err:   0.011592
[2025-10-02 18:35:38] [Iter 1495/2250] R4[738/2400] | LR: 0.024607 | E: -42.296034 | E_var:     0.3565 | E_err:   0.009329
[2025-10-02 18:35:43] [Iter 1496/2250] R4[740/2400] | LR: 0.024580 | E: -42.291254 | E_var:     0.3796 | E_err:   0.009627
[2025-10-02 18:35:48] [Iter 1497/2250] R4[742/2400] | LR: 0.024553 | E: -42.307744 | E_var:     0.3842 | E_err:   0.009685
[2025-10-02 18:35:53] [Iter 1498/2250] R4[744/2400] | LR: 0.024526 | E: -42.301742 | E_var:     0.3656 | E_err:   0.009448
[2025-10-02 18:35:58] [Iter 1499/2250] R4[746/2400] | LR: 0.024499 | E: -42.287562 | E_var:     0.4071 | E_err:   0.009970
[2025-10-02 18:36:04] [Iter 1500/2250] R4[748/2400] | LR: 0.024472 | E: -42.286016 | E_var:     0.3732 | E_err:   0.009546
[2025-10-02 18:36:09] [Iter 1501/2250] R4[750/2400] | LR: 0.024445 | E: -42.282103 | E_var:     0.4017 | E_err:   0.009903
[2025-10-02 18:36:14] [Iter 1502/2250] R4[752/2400] | LR: 0.024417 | E: -42.285216 | E_var:     0.4104 | E_err:   0.010009
[2025-10-02 18:36:19] [Iter 1503/2250] R4[754/2400] | LR: 0.024390 | E: -42.287709 | E_var:     0.4384 | E_err:   0.010345
[2025-10-02 18:36:24] [Iter 1504/2250] R4[756/2400] | LR: 0.024363 | E: -42.285133 | E_var:     0.5809 | E_err:   0.011909
[2025-10-02 18:36:29] [Iter 1505/2250] R4[758/2400] | LR: 0.024335 | E: -42.290554 | E_var:     0.4765 | E_err:   0.010786
[2025-10-02 18:36:34] [Iter 1506/2250] R4[760/2400] | LR: 0.024308 | E: -42.300438 | E_var:     0.5076 | E_err:   0.011132
[2025-10-02 18:36:40] [Iter 1507/2250] R4[762/2400] | LR: 0.024281 | E: -42.297654 | E_var:     0.4018 | E_err:   0.009904
[2025-10-02 18:36:45] [Iter 1508/2250] R4[764/2400] | LR: 0.024253 | E: -42.291401 | E_var:     0.5174 | E_err:   0.011239
[2025-10-02 18:36:50] [Iter 1509/2250] R4[766/2400] | LR: 0.024225 | E: -42.302484 | E_var:     0.4885 | E_err:   0.010921
[2025-10-02 18:36:55] [Iter 1510/2250] R4[768/2400] | LR: 0.024198 | E: -42.296718 | E_var:     0.4520 | E_err:   0.010505
[2025-10-02 18:37:00] [Iter 1511/2250] R4[770/2400] | LR: 0.024170 | E: -42.288025 | E_var:     0.3960 | E_err:   0.009832
[2025-10-02 18:37:05] [Iter 1512/2250] R4[772/2400] | LR: 0.024142 | E: -42.297561 | E_var:     0.4638 | E_err:   0.010641
[2025-10-02 18:37:11] [Iter 1513/2250] R4[774/2400] | LR: 0.024115 | E: -42.312268 | E_var:     0.3789 | E_err:   0.009617
[2025-10-02 18:37:16] [Iter 1514/2250] R4[776/2400] | LR: 0.024087 | E: -42.311558 | E_var:     0.3650 | E_err:   0.009441
[2025-10-02 18:37:21] [Iter 1515/2250] R4[778/2400] | LR: 0.024059 | E: -42.283747 | E_var:     0.4866 | E_err:   0.010900
[2025-10-02 18:37:26] [Iter 1516/2250] R4[780/2400] | LR: 0.024031 | E: -42.297415 | E_var:     0.3635 | E_err:   0.009421
[2025-10-02 18:37:31] [Iter 1517/2250] R4[782/2400] | LR: 0.024003 | E: -42.291007 | E_var:     0.4420 | E_err:   0.010388
[2025-10-02 18:37:36] [Iter 1518/2250] R4[784/2400] | LR: 0.023975 | E: -42.280139 | E_var:     0.4836 | E_err:   0.010866
[2025-10-02 18:37:42] [Iter 1519/2250] R4[786/2400] | LR: 0.023947 | E: -42.297416 | E_var:     0.3627 | E_err:   0.009411
[2025-10-02 18:37:47] [Iter 1520/2250] R4[788/2400] | LR: 0.023919 | E: -42.267638 | E_var:     0.4133 | E_err:   0.010046
[2025-10-02 18:37:52] [Iter 1521/2250] R4[790/2400] | LR: 0.023891 | E: -42.286166 | E_var:     0.3911 | E_err:   0.009772
[2025-10-02 18:37:57] [Iter 1522/2250] R4[792/2400] | LR: 0.023863 | E: -42.288594 | E_var:     0.8038 | E_err:   0.014009
[2025-10-02 18:38:02] [Iter 1523/2250] R4[794/2400] | LR: 0.023835 | E: -42.294588 | E_var:     0.4471 | E_err:   0.010448
[2025-10-02 18:38:07] [Iter 1524/2250] R4[796/2400] | LR: 0.023807 | E: -42.284620 | E_var:     0.4530 | E_err:   0.010517
[2025-10-02 18:38:13] [Iter 1525/2250] R4[798/2400] | LR: 0.023778 | E: -42.302602 | E_var:     0.3641 | E_err:   0.009428
[2025-10-02 18:38:18] [Iter 1526/2250] R4[800/2400] | LR: 0.023750 | E: -42.280418 | E_var:     0.4008 | E_err:   0.009892
[2025-10-02 18:38:23] [Iter 1527/2250] R4[802/2400] | LR: 0.023722 | E: -42.290003 | E_var:     0.4116 | E_err:   0.010024
[2025-10-02 18:38:28] [Iter 1528/2250] R4[804/2400] | LR: 0.023693 | E: -42.288650 | E_var:     0.4086 | E_err:   0.009988
[2025-10-02 18:38:33] [Iter 1529/2250] R4[806/2400] | LR: 0.023665 | E: -42.285000 | E_var:     0.2843 | E_err:   0.008331
[2025-10-02 18:38:38] [Iter 1530/2250] R4[808/2400] | LR: 0.023636 | E: -42.307841 | E_var:     0.4644 | E_err:   0.010648
[2025-10-02 18:38:43] [Iter 1531/2250] R4[810/2400] | LR: 0.023608 | E: -42.291305 | E_var:     0.4581 | E_err:   0.010576
[2025-10-02 18:38:49] [Iter 1532/2250] R4[812/2400] | LR: 0.023579 | E: -42.291859 | E_var:     0.4549 | E_err:   0.010539
[2025-10-02 18:38:54] [Iter 1533/2250] R4[814/2400] | LR: 0.023551 | E: -42.275787 | E_var:     0.3918 | E_err:   0.009780
[2025-10-02 18:38:59] [Iter 1534/2250] R4[816/2400] | LR: 0.023522 | E: -42.310502 | E_var:     0.4081 | E_err:   0.009982
[2025-10-02 18:39:04] [Iter 1535/2250] R4[818/2400] | LR: 0.023493 | E: -42.287570 | E_var:     0.3655 | E_err:   0.009446
[2025-10-02 18:39:09] [Iter 1536/2250] R4[820/2400] | LR: 0.023464 | E: -42.289278 | E_var:     0.6847 | E_err:   0.012930
[2025-10-02 18:39:14] [Iter 1537/2250] R4[822/2400] | LR: 0.023436 | E: -42.305304 | E_var:     0.3624 | E_err:   0.009406
[2025-10-02 18:39:20] [Iter 1538/2250] R4[824/2400] | LR: 0.023407 | E: -42.267155 | E_var:     1.8548 | E_err:   0.021280
[2025-10-02 18:39:25] [Iter 1539/2250] R4[826/2400] | LR: 0.023378 | E: -42.292553 | E_var:     1.9065 | E_err:   0.021575
[2025-10-02 18:39:30] [Iter 1540/2250] R4[828/2400] | LR: 0.023349 | E: -42.335765 | E_var:     0.4239 | E_err:   0.010173
[2025-10-02 18:39:35] [Iter 1541/2250] R4[830/2400] | LR: 0.023320 | E: -42.292199 | E_var:     0.3462 | E_err:   0.009193
[2025-10-02 18:39:40] [Iter 1542/2250] R4[832/2400] | LR: 0.023291 | E: -42.292566 | E_var:     0.4702 | E_err:   0.010714
[2025-10-02 18:39:45] [Iter 1543/2250] R4[834/2400] | LR: 0.023262 | E: -42.284822 | E_var:     0.3891 | E_err:   0.009747
[2025-10-02 18:39:51] [Iter 1544/2250] R4[836/2400] | LR: 0.023233 | E: -42.309196 | E_var:     0.3745 | E_err:   0.009562
[2025-10-02 18:39:56] [Iter 1545/2250] R4[838/2400] | LR: 0.023204 | E: -42.300902 | E_var:     0.4369 | E_err:   0.010328
[2025-10-02 18:40:01] [Iter 1546/2250] R4[840/2400] | LR: 0.023175 | E: -42.289760 | E_var:     0.3676 | E_err:   0.009474
[2025-10-02 18:40:06] [Iter 1547/2250] R4[842/2400] | LR: 0.023146 | E: -42.310087 | E_var:     0.4061 | E_err:   0.009957
[2025-10-02 18:40:11] [Iter 1548/2250] R4[844/2400] | LR: 0.023116 | E: -42.289412 | E_var:     0.4214 | E_err:   0.010143
[2025-10-02 18:40:16] [Iter 1549/2250] R4[846/2400] | LR: 0.023087 | E: -42.312279 | E_var:     0.3985 | E_err:   0.009864
[2025-10-02 18:40:22] [Iter 1550/2250] R4[848/2400] | LR: 0.023058 | E: -42.310565 | E_var:     0.3424 | E_err:   0.009142
[2025-10-02 18:40:27] [Iter 1551/2250] R4[850/2400] | LR: 0.023029 | E: -42.281610 | E_var:     0.5094 | E_err:   0.011152
[2025-10-02 18:40:32] [Iter 1552/2250] R4[852/2400] | LR: 0.022999 | E: -42.303051 | E_var:     0.3607 | E_err:   0.009384
[2025-10-02 18:40:37] [Iter 1553/2250] R4[854/2400] | LR: 0.022970 | E: -42.300542 | E_var:     0.4525 | E_err:   0.010510
[2025-10-02 18:40:42] [Iter 1554/2250] R4[856/2400] | LR: 0.022940 | E: -42.309319 | E_var:     0.4296 | E_err:   0.010241
[2025-10-02 18:40:47] [Iter 1555/2250] R4[858/2400] | LR: 0.022911 | E: -42.309572 | E_var:     0.4013 | E_err:   0.009898
[2025-10-02 18:40:53] [Iter 1556/2250] R4[860/2400] | LR: 0.022881 | E: -42.300052 | E_var:     0.5080 | E_err:   0.011137
[2025-10-02 18:40:58] [Iter 1557/2250] R4[862/2400] | LR: 0.022852 | E: -42.302873 | E_var:     0.5210 | E_err:   0.011279
[2025-10-02 18:41:03] [Iter 1558/2250] R4[864/2400] | LR: 0.022822 | E: -42.295891 | E_var:     0.5263 | E_err:   0.011335
[2025-10-02 18:41:08] [Iter 1559/2250] R4[866/2400] | LR: 0.022793 | E: -42.318474 | E_var:     0.4257 | E_err:   0.010194
[2025-10-02 18:41:13] [Iter 1560/2250] R4[868/2400] | LR: 0.022763 | E: -42.304319 | E_var:     0.6301 | E_err:   0.012403
[2025-10-02 18:41:18] [Iter 1561/2250] R4[870/2400] | LR: 0.022733 | E: -42.332174 | E_var:     0.4780 | E_err:   0.010803
[2025-10-02 18:41:24] [Iter 1562/2250] R4[872/2400] | LR: 0.022704 | E: -42.313620 | E_var:     0.5053 | E_err:   0.011107
[2025-10-02 18:41:29] [Iter 1563/2250] R4[874/2400] | LR: 0.022674 | E: -42.310151 | E_var:     0.3440 | E_err:   0.009164
[2025-10-02 18:41:34] [Iter 1564/2250] R4[876/2400] | LR: 0.022644 | E: -42.302349 | E_var:     0.4238 | E_err:   0.010172
[2025-10-02 18:41:39] [Iter 1565/2250] R4[878/2400] | LR: 0.022614 | E: -42.297929 | E_var:     0.4519 | E_err:   0.010504
[2025-10-02 18:41:44] [Iter 1566/2250] R4[880/2400] | LR: 0.022584 | E: -42.299269 | E_var:     0.3759 | E_err:   0.009580
[2025-10-02 18:41:49] [Iter 1567/2250] R4[882/2400] | LR: 0.022554 | E: -42.309914 | E_var:     0.3275 | E_err:   0.008942
[2025-10-02 18:41:54] [Iter 1568/2250] R4[884/2400] | LR: 0.022524 | E: -42.294773 | E_var:     0.4132 | E_err:   0.010044
[2025-10-02 18:42:00] [Iter 1569/2250] R4[886/2400] | LR: 0.022494 | E: -42.294858 | E_var:     0.5417 | E_err:   0.011500
[2025-10-02 18:42:05] [Iter 1570/2250] R4[888/2400] | LR: 0.022464 | E: -42.297503 | E_var:     0.4178 | E_err:   0.010099
[2025-10-02 18:42:10] [Iter 1571/2250] R4[890/2400] | LR: 0.022434 | E: -42.300457 | E_var:     0.4051 | E_err:   0.009945
[2025-10-02 18:42:15] [Iter 1572/2250] R4[892/2400] | LR: 0.022404 | E: -42.292535 | E_var:     0.4059 | E_err:   0.009955
[2025-10-02 18:42:20] [Iter 1573/2250] R4[894/2400] | LR: 0.022374 | E: -42.308623 | E_var:     0.3426 | E_err:   0.009146
[2025-10-02 18:42:25] [Iter 1574/2250] R4[896/2400] | LR: 0.022344 | E: -42.304745 | E_var:     0.4524 | E_err:   0.010509
[2025-10-02 18:42:31] [Iter 1575/2250] R4[898/2400] | LR: 0.022314 | E: -42.295077 | E_var:     0.4741 | E_err:   0.010759
[2025-10-02 18:42:36] [Iter 1576/2250] R4[900/2400] | LR: 0.022284 | E: -42.296149 | E_var:     0.4017 | E_err:   0.009903
[2025-10-02 18:42:41] [Iter 1577/2250] R4[902/2400] | LR: 0.022253 | E: -42.307351 | E_var:     0.3739 | E_err:   0.009554
[2025-10-02 18:42:46] [Iter 1578/2250] R4[904/2400] | LR: 0.022223 | E: -42.272305 | E_var:     0.4031 | E_err:   0.009921
[2025-10-02 18:42:51] [Iter 1579/2250] R4[906/2400] | LR: 0.022193 | E: -42.292122 | E_var:     0.3241 | E_err:   0.008895
[2025-10-02 18:42:56] [Iter 1580/2250] R4[908/2400] | LR: 0.022162 | E: -42.288943 | E_var:     0.3422 | E_err:   0.009140
[2025-10-02 18:43:02] [Iter 1581/2250] R4[910/2400] | LR: 0.022132 | E: -42.299892 | E_var:     0.4429 | E_err:   0.010399
[2025-10-02 18:43:07] [Iter 1582/2250] R4[912/2400] | LR: 0.022102 | E: -42.303228 | E_var:     0.5085 | E_err:   0.011142
[2025-10-02 18:43:12] [Iter 1583/2250] R4[914/2400] | LR: 0.022071 | E: -42.298427 | E_var:     0.3786 | E_err:   0.009615
[2025-10-02 18:43:17] [Iter 1584/2250] R4[916/2400] | LR: 0.022041 | E: -42.277279 | E_var:     0.3866 | E_err:   0.009716
[2025-10-02 18:43:22] [Iter 1585/2250] R4[918/2400] | LR: 0.022010 | E: -42.314643 | E_var:     0.7072 | E_err:   0.013140
[2025-10-02 18:43:27] [Iter 1586/2250] R4[920/2400] | LR: 0.021980 | E: -42.285024 | E_var:     0.4080 | E_err:   0.009980
[2025-10-02 18:43:33] [Iter 1587/2250] R4[922/2400] | LR: 0.021949 | E: -42.291779 | E_var:     0.3756 | E_err:   0.009576
[2025-10-02 18:43:38] [Iter 1588/2250] R4[924/2400] | LR: 0.021918 | E: -42.313859 | E_var:     0.3479 | E_err:   0.009216
[2025-10-02 18:43:43] [Iter 1589/2250] R4[926/2400] | LR: 0.021888 | E: -42.307044 | E_var:     0.5835 | E_err:   0.011935
[2025-10-02 18:43:48] [Iter 1590/2250] R4[928/2400] | LR: 0.021857 | E: -42.304944 | E_var:     0.4143 | E_err:   0.010058
[2025-10-02 18:43:53] [Iter 1591/2250] R4[930/2400] | LR: 0.021826 | E: -42.304190 | E_var:     0.4050 | E_err:   0.009944
[2025-10-02 18:43:58] [Iter 1592/2250] R4[932/2400] | LR: 0.021796 | E: -42.297083 | E_var:     0.3903 | E_err:   0.009762
[2025-10-02 18:44:03] [Iter 1593/2250] R4[934/2400] | LR: 0.021765 | E: -42.294004 | E_var:     0.4418 | E_err:   0.010386
[2025-10-02 18:44:09] [Iter 1594/2250] R4[936/2400] | LR: 0.021734 | E: -42.293350 | E_var:     0.4004 | E_err:   0.009887
[2025-10-02 18:44:14] [Iter 1595/2250] R4[938/2400] | LR: 0.021703 | E: -42.298140 | E_var:     0.3743 | E_err:   0.009559
[2025-10-02 18:44:19] [Iter 1596/2250] R4[940/2400] | LR: 0.021673 | E: -42.296953 | E_var:     0.4047 | E_err:   0.009940
[2025-10-02 18:44:24] [Iter 1597/2250] R4[942/2400] | LR: 0.021642 | E: -42.297731 | E_var:     0.3929 | E_err:   0.009795
[2025-10-02 18:44:29] [Iter 1598/2250] R4[944/2400] | LR: 0.021611 | E: -42.306179 | E_var:     0.3534 | E_err:   0.009289
[2025-10-02 18:44:34] [Iter 1599/2250] R4[946/2400] | LR: 0.021580 | E: -42.288872 | E_var:     0.3711 | E_err:   0.009519
[2025-10-02 18:44:40] [Iter 1600/2250] R4[948/2400] | LR: 0.021549 | E: -42.302207 | E_var:     0.3340 | E_err:   0.009030
[2025-10-02 18:44:40] ✓ Checkpoint saved: checkpoint_iter_001600.pkl
[2025-10-02 18:44:45] [Iter 1601/2250] R4[950/2400] | LR: 0.021518 | E: -42.166359 | E_var:    12.0079 | E_err:   0.054144
[2025-10-02 18:44:50] [Iter 1602/2250] R4[952/2400] | LR: 0.021487 | E: -42.156502 | E_var:    11.7112 | E_err:   0.053471
[2025-10-02 18:44:55] [Iter 1603/2250] R4[954/2400] | LR: 0.021456 | E: -42.208892 | E_var:     6.4021 | E_err:   0.039535
[2025-10-02 18:45:00] [Iter 1604/2250] R4[956/2400] | LR: 0.021425 | E: -42.287104 | E_var:     0.4630 | E_err:   0.010632
[2025-10-02 18:45:05] [Iter 1605/2250] R4[958/2400] | LR: 0.021394 | E: -42.289636 | E_var:     0.3768 | E_err:   0.009592
[2025-10-02 18:45:11] [Iter 1606/2250] R4[960/2400] | LR: 0.021363 | E: -42.302356 | E_var:     0.3268 | E_err:   0.008932
[2025-10-02 18:45:16] [Iter 1607/2250] R4[962/2400] | LR: 0.021332 | E: -42.276958 | E_var:     0.3381 | E_err:   0.009085
[2025-10-02 18:45:21] [Iter 1608/2250] R4[964/2400] | LR: 0.021300 | E: -42.296679 | E_var:     0.3899 | E_err:   0.009757
[2025-10-02 18:45:26] [Iter 1609/2250] R4[966/2400] | LR: 0.021269 | E: -42.301138 | E_var:     0.4426 | E_err:   0.010395
[2025-10-02 18:45:31] [Iter 1610/2250] R4[968/2400] | LR: 0.021238 | E: -42.292195 | E_var:     0.4499 | E_err:   0.010480
[2025-10-02 18:45:36] [Iter 1611/2250] R4[970/2400] | LR: 0.021207 | E: -42.318260 | E_var:     0.3796 | E_err:   0.009627
[2025-10-02 18:45:42] [Iter 1612/2250] R4[972/2400] | LR: 0.021176 | E: -42.302154 | E_var:     0.3196 | E_err:   0.008834
[2025-10-02 18:45:47] [Iter 1613/2250] R4[974/2400] | LR: 0.021144 | E: -42.311755 | E_var:     0.4394 | E_err:   0.010358
[2025-10-02 18:45:52] [Iter 1614/2250] R4[976/2400] | LR: 0.021113 | E: -42.302367 | E_var:     0.3425 | E_err:   0.009144
[2025-10-02 18:45:57] [Iter 1615/2250] R4[978/2400] | LR: 0.021082 | E: -42.277587 | E_var:     0.3561 | E_err:   0.009324
[2025-10-02 18:46:02] [Iter 1616/2250] R4[980/2400] | LR: 0.021050 | E: -42.310797 | E_var:     0.3517 | E_err:   0.009267
[2025-10-02 18:46:07] [Iter 1617/2250] R4[982/2400] | LR: 0.021019 | E: -42.304553 | E_var:     0.4016 | E_err:   0.009902
[2025-10-02 18:46:13] [Iter 1618/2250] R4[984/2400] | LR: 0.020987 | E: -42.279373 | E_var:     0.4708 | E_err:   0.010721
[2025-10-02 18:46:18] [Iter 1619/2250] R4[986/2400] | LR: 0.020956 | E: -42.287584 | E_var:     0.3945 | E_err:   0.009815
[2025-10-02 18:46:23] [Iter 1620/2250] R4[988/2400] | LR: 0.020924 | E: -42.305108 | E_var:     0.5069 | E_err:   0.011124
[2025-10-02 18:46:28] [Iter 1621/2250] R4[990/2400] | LR: 0.020893 | E: -42.275126 | E_var:     0.4688 | E_err:   0.010698
[2025-10-02 18:46:33] [Iter 1622/2250] R4[992/2400] | LR: 0.020861 | E: -42.310665 | E_var:     0.4026 | E_err:   0.009915
[2025-10-02 18:46:38] [Iter 1623/2250] R4[994/2400] | LR: 0.020830 | E: -42.287427 | E_var:     0.3985 | E_err:   0.009864
[2025-10-02 18:46:44] [Iter 1624/2250] R4[996/2400] | LR: 0.020798 | E: -42.290531 | E_var:     0.3627 | E_err:   0.009410
[2025-10-02 18:46:49] [Iter 1625/2250] R4[998/2400] | LR: 0.020767 | E: -42.306536 | E_var:     0.3949 | E_err:   0.009819
[2025-10-02 18:46:54] [Iter 1626/2250] R4[1000/2400] | LR: 0.020735 | E: -42.283465 | E_var:     0.3782 | E_err:   0.009609
[2025-10-02 18:46:59] [Iter 1627/2250] R4[1002/2400] | LR: 0.020704 | E: -42.297582 | E_var:     0.3735 | E_err:   0.009549
[2025-10-02 18:47:04] [Iter 1628/2250] R4[1004/2400] | LR: 0.020672 | E: -42.308956 | E_var:     0.3831 | E_err:   0.009671
[2025-10-02 18:47:09] [Iter 1629/2250] R4[1006/2400] | LR: 0.020640 | E: -42.307466 | E_var:     0.3668 | E_err:   0.009463
[2025-10-02 18:47:15] [Iter 1630/2250] R4[1008/2400] | LR: 0.020609 | E: -42.306662 | E_var:     0.5294 | E_err:   0.011369
[2025-10-02 18:47:20] [Iter 1631/2250] R4[1010/2400] | LR: 0.020577 | E: -42.318447 | E_var:     0.4547 | E_err:   0.010536
[2025-10-02 18:47:25] [Iter 1632/2250] R4[1012/2400] | LR: 0.020545 | E: -42.298496 | E_var:     0.3798 | E_err:   0.009629
[2025-10-02 18:47:30] [Iter 1633/2250] R4[1014/2400] | LR: 0.020513 | E: -42.309056 | E_var:     0.3865 | E_err:   0.009714
[2025-10-02 18:47:35] [Iter 1634/2250] R4[1016/2400] | LR: 0.020482 | E: -42.306551 | E_var:     0.3763 | E_err:   0.009585
[2025-10-02 18:47:40] [Iter 1635/2250] R4[1018/2400] | LR: 0.020450 | E: -42.306801 | E_var:     0.4102 | E_err:   0.010008
[2025-10-02 18:47:46] [Iter 1636/2250] R4[1020/2400] | LR: 0.020418 | E: -42.298109 | E_var:     0.4445 | E_err:   0.010417
[2025-10-02 18:47:51] [Iter 1637/2250] R4[1022/2400] | LR: 0.020386 | E: -42.271874 | E_var:     0.5633 | E_err:   0.011728
[2025-10-02 18:47:56] [Iter 1638/2250] R4[1024/2400] | LR: 0.020354 | E: -42.314814 | E_var:     0.3983 | E_err:   0.009861
[2025-10-02 18:48:01] [Iter 1639/2250] R4[1026/2400] | LR: 0.020323 | E: -42.302034 | E_var:     0.3689 | E_err:   0.009490
[2025-10-02 18:48:06] [Iter 1640/2250] R4[1028/2400] | LR: 0.020291 | E: -42.303543 | E_var:     0.4746 | E_err:   0.010764
[2025-10-02 18:48:11] [Iter 1641/2250] R4[1030/2400] | LR: 0.020259 | E: -42.298103 | E_var:     0.3937 | E_err:   0.009804
[2025-10-02 18:48:17] [Iter 1642/2250] R4[1032/2400] | LR: 0.020227 | E: -42.301772 | E_var:     0.4004 | E_err:   0.009887
[2025-10-02 18:48:22] [Iter 1643/2250] R4[1034/2400] | LR: 0.020195 | E: -42.293774 | E_var:     0.4195 | E_err:   0.010120
[2025-10-02 18:48:27] [Iter 1644/2250] R4[1036/2400] | LR: 0.020163 | E: -42.302059 | E_var:     0.3254 | E_err:   0.008913
[2025-10-02 18:48:32] [Iter 1645/2250] R4[1038/2400] | LR: 0.020131 | E: -42.288804 | E_var:     0.4798 | E_err:   0.010823
[2025-10-02 18:48:37] [Iter 1646/2250] R4[1040/2400] | LR: 0.020099 | E: -42.284601 | E_var:     0.3701 | E_err:   0.009505
[2025-10-02 18:48:42] [Iter 1647/2250] R4[1042/2400] | LR: 0.020067 | E: -42.296475 | E_var:     0.4253 | E_err:   0.010189
[2025-10-02 18:48:48] [Iter 1648/2250] R4[1044/2400] | LR: 0.020035 | E: -42.296845 | E_var:     0.3781 | E_err:   0.009608
[2025-10-02 18:48:53] [Iter 1649/2250] R4[1046/2400] | LR: 0.020003 | E: -42.292457 | E_var:     0.3789 | E_err:   0.009618
[2025-10-02 18:48:58] [Iter 1650/2250] R4[1048/2400] | LR: 0.019971 | E: -42.306169 | E_var:     0.8438 | E_err:   0.014353
[2025-10-02 18:49:03] [Iter 1651/2250] R4[1050/2400] | LR: 0.019939 | E: -42.311627 | E_var:     0.3800 | E_err:   0.009632
[2025-10-02 18:49:08] [Iter 1652/2250] R4[1052/2400] | LR: 0.019907 | E: -42.314534 | E_var:     0.3625 | E_err:   0.009408
[2025-10-02 18:49:13] [Iter 1653/2250] R4[1054/2400] | LR: 0.019874 | E: -42.329878 | E_var:     0.4977 | E_err:   0.011023
[2025-10-02 18:49:18] [Iter 1654/2250] R4[1056/2400] | LR: 0.019842 | E: -42.306483 | E_var:     0.3772 | E_err:   0.009596
[2025-10-02 18:49:24] [Iter 1655/2250] R4[1058/2400] | LR: 0.019810 | E: -42.298713 | E_var:     0.3028 | E_err:   0.008598
[2025-10-02 18:49:29] [Iter 1656/2250] R4[1060/2400] | LR: 0.019778 | E: -42.290535 | E_var:     0.4229 | E_err:   0.010161
[2025-10-02 18:49:34] [Iter 1657/2250] R4[1062/2400] | LR: 0.019746 | E: -42.300943 | E_var:     0.4447 | E_err:   0.010419
[2025-10-02 18:49:39] [Iter 1658/2250] R4[1064/2400] | LR: 0.019714 | E: -42.300743 | E_var:     0.4757 | E_err:   0.010777
[2025-10-02 18:49:44] [Iter 1659/2250] R4[1066/2400] | LR: 0.019681 | E: -42.293261 | E_var:     0.3347 | E_err:   0.009039
[2025-10-02 18:49:49] [Iter 1660/2250] R4[1068/2400] | LR: 0.019649 | E: -42.300013 | E_var:     0.4232 | E_err:   0.010165
[2025-10-02 18:49:55] [Iter 1661/2250] R4[1070/2400] | LR: 0.019617 | E: -42.287378 | E_var:     0.4368 | E_err:   0.010327
[2025-10-02 18:50:00] [Iter 1662/2250] R4[1072/2400] | LR: 0.019585 | E: -42.300906 | E_var:     0.4329 | E_err:   0.010281
[2025-10-02 18:50:05] [Iter 1663/2250] R4[1074/2400] | LR: 0.019552 | E: -42.293168 | E_var:     0.3659 | E_err:   0.009451
[2025-10-02 18:50:10] [Iter 1664/2250] R4[1076/2400] | LR: 0.019520 | E: -42.296304 | E_var:     0.5490 | E_err:   0.011577
[2025-10-02 18:50:15] [Iter 1665/2250] R4[1078/2400] | LR: 0.019488 | E: -42.301251 | E_var:     0.3207 | E_err:   0.008849
[2025-10-02 18:50:20] [Iter 1666/2250] R4[1080/2400] | LR: 0.019455 | E: -42.286297 | E_var:     0.4806 | E_err:   0.010832
[2025-10-02 18:50:26] [Iter 1667/2250] R4[1082/2400] | LR: 0.019423 | E: -42.283941 | E_var:     0.3072 | E_err:   0.008660
[2025-10-02 18:50:31] [Iter 1668/2250] R4[1084/2400] | LR: 0.019391 | E: -42.296815 | E_var:     0.3697 | E_err:   0.009501
[2025-10-02 18:50:36] [Iter 1669/2250] R4[1086/2400] | LR: 0.019358 | E: -42.312263 | E_var:     0.3421 | E_err:   0.009140
[2025-10-02 18:50:41] [Iter 1670/2250] R4[1088/2400] | LR: 0.019326 | E: -42.313217 | E_var:     0.3907 | E_err:   0.009766
[2025-10-02 18:50:46] [Iter 1671/2250] R4[1090/2400] | LR: 0.019294 | E: -42.305799 | E_var:     0.3894 | E_err:   0.009751
[2025-10-02 18:50:51] [Iter 1672/2250] R4[1092/2400] | LR: 0.019261 | E: -42.292438 | E_var:     0.3186 | E_err:   0.008820
[2025-10-02 18:50:57] [Iter 1673/2250] R4[1094/2400] | LR: 0.019229 | E: -42.322478 | E_var:     0.3358 | E_err:   0.009055
[2025-10-02 18:51:02] [Iter 1674/2250] R4[1096/2400] | LR: 0.019196 | E: -42.305696 | E_var:     0.3106 | E_err:   0.008708
[2025-10-02 18:51:07] [Iter 1675/2250] R4[1098/2400] | LR: 0.019164 | E: -42.295790 | E_var:     0.3868 | E_err:   0.009718
[2025-10-02 18:51:12] [Iter 1676/2250] R4[1100/2400] | LR: 0.019132 | E: -42.312508 | E_var:     0.3290 | E_err:   0.008962
[2025-10-02 18:51:17] [Iter 1677/2250] R4[1102/2400] | LR: 0.019099 | E: -42.263714 | E_var:     2.5920 | E_err:   0.025156
[2025-10-02 18:51:22] [Iter 1678/2250] R4[1104/2400] | LR: 0.019067 | E: -42.289643 | E_var:     1.5548 | E_err:   0.019483
[2025-10-02 18:51:28] [Iter 1679/2250] R4[1106/2400] | LR: 0.019034 | E: -42.286197 | E_var:     1.7288 | E_err:   0.020545
[2025-10-02 18:51:33] [Iter 1680/2250] R4[1108/2400] | LR: 0.019002 | E: -42.281722 | E_var:     1.6098 | E_err:   0.019825
[2025-10-02 18:51:38] [Iter 1681/2250] R4[1110/2400] | LR: 0.018969 | E: -42.309608 | E_var:     0.5062 | E_err:   0.011117
[2025-10-02 18:51:43] [Iter 1682/2250] R4[1112/2400] | LR: 0.018937 | E: -42.303221 | E_var:     0.4098 | E_err:   0.010003
[2025-10-02 18:51:48] [Iter 1683/2250] R4[1114/2400] | LR: 0.018904 | E: -42.288929 | E_var:     0.4013 | E_err:   0.009898
[2025-10-02 18:51:53] [Iter 1684/2250] R4[1116/2400] | LR: 0.018872 | E: -42.304247 | E_var:     0.5215 | E_err:   0.011284
[2025-10-02 18:51:59] [Iter 1685/2250] R4[1118/2400] | LR: 0.018839 | E: -42.305221 | E_var:     0.4564 | E_err:   0.010556
[2025-10-02 18:52:04] [Iter 1686/2250] R4[1120/2400] | LR: 0.018807 | E: -42.312774 | E_var:     0.3641 | E_err:   0.009429
[2025-10-02 18:52:09] [Iter 1687/2250] R4[1122/2400] | LR: 0.018774 | E: -42.298491 | E_var:     0.3978 | E_err:   0.009854
[2025-10-02 18:52:14] [Iter 1688/2250] R4[1124/2400] | LR: 0.018741 | E: -42.295475 | E_var:     0.3663 | E_err:   0.009456
[2025-10-02 18:52:19] [Iter 1689/2250] R4[1126/2400] | LR: 0.018709 | E: -42.292014 | E_var:     0.3631 | E_err:   0.009415
[2025-10-02 18:52:24] [Iter 1690/2250] R4[1128/2400] | LR: 0.018676 | E: -42.309559 | E_var:     0.4470 | E_err:   0.010447
[2025-10-02 18:52:29] [Iter 1691/2250] R4[1130/2400] | LR: 0.018644 | E: -42.308071 | E_var:     0.3623 | E_err:   0.009404
[2025-10-02 18:52:35] [Iter 1692/2250] R4[1132/2400] | LR: 0.018611 | E: -42.280250 | E_var:     0.3958 | E_err:   0.009830
[2025-10-02 18:52:40] [Iter 1693/2250] R4[1134/2400] | LR: 0.018579 | E: -42.308497 | E_var:     0.5030 | E_err:   0.011082
[2025-10-02 18:52:45] [Iter 1694/2250] R4[1136/2400] | LR: 0.018546 | E: -42.296672 | E_var:     0.4566 | E_err:   0.010558
[2025-10-02 18:52:50] [Iter 1695/2250] R4[1138/2400] | LR: 0.018513 | E: -42.298467 | E_var:     0.3345 | E_err:   0.009037
[2025-10-02 18:52:55] [Iter 1696/2250] R4[1140/2400] | LR: 0.018481 | E: -42.290300 | E_var:     0.4231 | E_err:   0.010163
[2025-10-02 18:53:00] [Iter 1697/2250] R4[1142/2400] | LR: 0.018448 | E: -42.287931 | E_var:     0.4968 | E_err:   0.011013
[2025-10-02 18:53:06] [Iter 1698/2250] R4[1144/2400] | LR: 0.018415 | E: -42.299105 | E_var:     0.4216 | E_err:   0.010145
[2025-10-02 18:53:11] [Iter 1699/2250] R4[1146/2400] | LR: 0.018383 | E: -42.318382 | E_var:     0.3616 | E_err:   0.009396
[2025-10-02 18:53:16] [Iter 1700/2250] R4[1148/2400] | LR: 0.018350 | E: -42.317678 | E_var:     0.3358 | E_err:   0.009054
[2025-10-02 18:53:21] [Iter 1701/2250] R4[1150/2400] | LR: 0.018318 | E: -42.302972 | E_var:     0.3152 | E_err:   0.008772
[2025-10-02 18:53:26] [Iter 1702/2250] R4[1152/2400] | LR: 0.018285 | E: -42.311972 | E_var:     0.4274 | E_err:   0.010215
[2025-10-02 18:53:31] [Iter 1703/2250] R4[1154/2400] | LR: 0.018252 | E: -42.302528 | E_var:     0.5177 | E_err:   0.011243
[2025-10-02 18:53:37] [Iter 1704/2250] R4[1156/2400] | LR: 0.018220 | E: -42.293315 | E_var:     0.6475 | E_err:   0.012574
[2025-10-02 18:53:42] [Iter 1705/2250] R4[1158/2400] | LR: 0.018187 | E: -42.312773 | E_var:     0.4403 | E_err:   0.010368
[2025-10-02 18:53:47] [Iter 1706/2250] R4[1160/2400] | LR: 0.018154 | E: -42.320985 | E_var:     0.3268 | E_err:   0.008932
[2025-10-02 18:53:52] [Iter 1707/2250] R4[1162/2400] | LR: 0.018122 | E: -42.307374 | E_var:     0.4310 | E_err:   0.010258
[2025-10-02 18:53:57] [Iter 1708/2250] R4[1164/2400] | LR: 0.018089 | E: -42.305387 | E_var:     0.3655 | E_err:   0.009446
[2025-10-02 18:54:02] [Iter 1709/2250] R4[1166/2400] | LR: 0.018056 | E: -42.303037 | E_var:     0.3814 | E_err:   0.009649
[2025-10-02 18:54:08] [Iter 1710/2250] R4[1168/2400] | LR: 0.018023 | E: -42.306770 | E_var:     0.4035 | E_err:   0.009926
[2025-10-02 18:54:13] [Iter 1711/2250] R4[1170/2400] | LR: 0.017991 | E: -42.313791 | E_var:     0.6359 | E_err:   0.012460
[2025-10-02 18:54:18] [Iter 1712/2250] R4[1172/2400] | LR: 0.017958 | E: -42.301723 | E_var:     0.4194 | E_err:   0.010119
[2025-10-02 18:54:23] [Iter 1713/2250] R4[1174/2400] | LR: 0.017925 | E: -42.301158 | E_var:     0.3725 | E_err:   0.009536
[2025-10-02 18:54:28] [Iter 1714/2250] R4[1176/2400] | LR: 0.017893 | E: -42.296778 | E_var:     0.4285 | E_err:   0.010228
[2025-10-02 18:54:33] [Iter 1715/2250] R4[1178/2400] | LR: 0.017860 | E: -42.301454 | E_var:     0.4128 | E_err:   0.010039
[2025-10-02 18:54:39] [Iter 1716/2250] R4[1180/2400] | LR: 0.017827 | E: -42.309033 | E_var:     0.3647 | E_err:   0.009436
[2025-10-02 18:54:44] [Iter 1717/2250] R4[1182/2400] | LR: 0.017794 | E: -42.295643 | E_var:     0.4307 | E_err:   0.010254
[2025-10-02 18:54:49] [Iter 1718/2250] R4[1184/2400] | LR: 0.017762 | E: -42.314324 | E_var:     0.4261 | E_err:   0.010199
[2025-10-02 18:54:54] [Iter 1719/2250] R4[1186/2400] | LR: 0.017729 | E: -42.311776 | E_var:     0.4178 | E_err:   0.010100
[2025-10-02 18:54:59] [Iter 1720/2250] R4[1188/2400] | LR: 0.017696 | E: -42.297481 | E_var:     0.6953 | E_err:   0.013029
[2025-10-02 18:55:04] [Iter 1721/2250] R4[1190/2400] | LR: 0.017664 | E: -42.301455 | E_var:     0.3354 | E_err:   0.009049
[2025-10-02 18:55:10] [Iter 1722/2250] R4[1192/2400] | LR: 0.017631 | E: -42.314063 | E_var:     0.3279 | E_err:   0.008947
[2025-10-02 18:55:15] [Iter 1723/2250] R4[1194/2400] | LR: 0.017598 | E: -42.315393 | E_var:     0.4934 | E_err:   0.010976
[2025-10-02 18:55:20] [Iter 1724/2250] R4[1196/2400] | LR: 0.017565 | E: -42.296734 | E_var:     0.3749 | E_err:   0.009567
[2025-10-02 18:55:25] [Iter 1725/2250] R4[1198/2400] | LR: 0.017533 | E: -42.308257 | E_var:     0.3690 | E_err:   0.009491
[2025-10-02 18:55:30] [Iter 1726/2250] R4[1200/2400] | LR: 0.017500 | E: -42.280315 | E_var:     0.4738 | E_err:   0.010755
[2025-10-02 18:55:35] [Iter 1727/2250] R4[1202/2400] | LR: 0.017467 | E: -42.306274 | E_var:     0.3740 | E_err:   0.009555
[2025-10-02 18:55:41] [Iter 1728/2250] R4[1204/2400] | LR: 0.017435 | E: -42.303972 | E_var:     0.3623 | E_err:   0.009406
[2025-10-02 18:55:46] [Iter 1729/2250] R4[1206/2400] | LR: 0.017402 | E: -42.321163 | E_var:     0.3787 | E_err:   0.009615
[2025-10-02 18:55:51] [Iter 1730/2250] R4[1208/2400] | LR: 0.017369 | E: -42.301667 | E_var:     0.4318 | E_err:   0.010268
[2025-10-02 18:55:56] [Iter 1731/2250] R4[1210/2400] | LR: 0.017336 | E: -42.300463 | E_var:     0.4624 | E_err:   0.010625
[2025-10-02 18:56:01] [Iter 1732/2250] R4[1212/2400] | LR: 0.017304 | E: -42.294649 | E_var:     0.4063 | E_err:   0.009960
[2025-10-02 18:56:06] [Iter 1733/2250] R4[1214/2400] | LR: 0.017271 | E: -42.279746 | E_var:     0.3794 | E_err:   0.009624
[2025-10-02 18:56:11] [Iter 1734/2250] R4[1216/2400] | LR: 0.017238 | E: -42.284520 | E_var:     0.6441 | E_err:   0.012540
[2025-10-02 18:56:17] [Iter 1735/2250] R4[1218/2400] | LR: 0.017206 | E: -42.325346 | E_var:     0.4338 | E_err:   0.010291
[2025-10-02 18:56:22] [Iter 1736/2250] R4[1220/2400] | LR: 0.017173 | E: -42.317186 | E_var:     0.3450 | E_err:   0.009177
[2025-10-02 18:56:27] [Iter 1737/2250] R4[1222/2400] | LR: 0.017140 | E: -42.292230 | E_var:     0.3775 | E_err:   0.009600
[2025-10-02 18:56:32] [Iter 1738/2250] R4[1224/2400] | LR: 0.017107 | E: -42.303313 | E_var:     0.3520 | E_err:   0.009270
[2025-10-02 18:56:37] [Iter 1739/2250] R4[1226/2400] | LR: 0.017075 | E: -42.299055 | E_var:     0.8933 | E_err:   0.014768
[2025-10-02 18:56:42] [Iter 1740/2250] R4[1228/2400] | LR: 0.017042 | E: -42.289294 | E_var:     0.3597 | E_err:   0.009371
[2025-10-02 18:56:48] [Iter 1741/2250] R4[1230/2400] | LR: 0.017009 | E: -42.301675 | E_var:     0.3710 | E_err:   0.009517
[2025-10-02 18:56:53] [Iter 1742/2250] R4[1232/2400] | LR: 0.016977 | E: -42.303823 | E_var:     0.4948 | E_err:   0.010990
[2025-10-02 18:56:58] [Iter 1743/2250] R4[1234/2400] | LR: 0.016944 | E: -42.313603 | E_var:     0.3231 | E_err:   0.008882
[2025-10-02 18:57:03] [Iter 1744/2250] R4[1236/2400] | LR: 0.016911 | E: -42.291975 | E_var:     0.5276 | E_err:   0.011349
[2025-10-02 18:57:08] [Iter 1745/2250] R4[1238/2400] | LR: 0.016878 | E: -42.320521 | E_var:     0.4478 | E_err:   0.010456
[2025-10-02 18:57:13] [Iter 1746/2250] R4[1240/2400] | LR: 0.016846 | E: -42.305656 | E_var:     0.4580 | E_err:   0.010574
[2025-10-02 18:57:19] [Iter 1747/2250] R4[1242/2400] | LR: 0.016813 | E: -42.316496 | E_var:     0.4362 | E_err:   0.010319
[2025-10-02 18:57:24] [Iter 1748/2250] R4[1244/2400] | LR: 0.016780 | E: -42.309384 | E_var:     0.3746 | E_err:   0.009563
[2025-10-02 18:57:29] [Iter 1749/2250] R4[1246/2400] | LR: 0.016748 | E: -42.295505 | E_var:     0.3071 | E_err:   0.008658
[2025-10-02 18:57:34] [Iter 1750/2250] R4[1248/2400] | LR: 0.016715 | E: -42.291206 | E_var:     0.4189 | E_err:   0.010112
[2025-10-02 18:57:39] [Iter 1751/2250] R4[1250/2400] | LR: 0.016682 | E: -42.308824 | E_var:     0.4351 | E_err:   0.010306
[2025-10-02 18:57:44] [Iter 1752/2250] R4[1252/2400] | LR: 0.016650 | E: -42.315705 | E_var:     0.4153 | E_err:   0.010070
[2025-10-02 18:57:50] [Iter 1753/2250] R4[1254/2400] | LR: 0.016617 | E: -42.304627 | E_var:     0.3707 | E_err:   0.009514
[2025-10-02 18:57:55] [Iter 1754/2250] R4[1256/2400] | LR: 0.016585 | E: -42.312635 | E_var:     0.3854 | E_err:   0.009700
[2025-10-02 18:58:00] [Iter 1755/2250] R4[1258/2400] | LR: 0.016552 | E: -42.322744 | E_var:     0.4206 | E_err:   0.010134
[2025-10-02 18:58:05] [Iter 1756/2250] R4[1260/2400] | LR: 0.016519 | E: -42.292865 | E_var:     0.3469 | E_err:   0.009202
[2025-10-02 18:58:10] [Iter 1757/2250] R4[1262/2400] | LR: 0.016487 | E: -42.308832 | E_var:     0.3585 | E_err:   0.009355
[2025-10-02 18:58:15] [Iter 1758/2250] R4[1264/2400] | LR: 0.016454 | E: -42.315664 | E_var:     0.3636 | E_err:   0.009422
[2025-10-02 18:58:21] [Iter 1759/2250] R4[1266/2400] | LR: 0.016421 | E: -42.331296 | E_var:     0.6149 | E_err:   0.012253
[2025-10-02 18:58:26] [Iter 1760/2250] R4[1268/2400] | LR: 0.016389 | E: -42.309249 | E_var:     0.3605 | E_err:   0.009381
[2025-10-02 18:58:31] [Iter 1761/2250] R4[1270/2400] | LR: 0.016356 | E: -42.298952 | E_var:     0.3753 | E_err:   0.009573
[2025-10-02 18:58:36] [Iter 1762/2250] R4[1272/2400] | LR: 0.016324 | E: -42.311657 | E_var:     0.4918 | E_err:   0.010958
[2025-10-02 18:58:41] [Iter 1763/2250] R4[1274/2400] | LR: 0.016291 | E: -42.287330 | E_var:     0.3833 | E_err:   0.009673
[2025-10-02 18:58:46] [Iter 1764/2250] R4[1276/2400] | LR: 0.016259 | E: -42.299615 | E_var:     0.3403 | E_err:   0.009115
[2025-10-02 18:58:52] [Iter 1765/2250] R4[1278/2400] | LR: 0.016226 | E: -42.332388 | E_var:     0.3507 | E_err:   0.009253
[2025-10-02 18:58:57] [Iter 1766/2250] R4[1280/2400] | LR: 0.016193 | E: -42.315015 | E_var:     0.6756 | E_err:   0.012843
[2025-10-02 18:59:02] [Iter 1767/2250] R4[1282/2400] | LR: 0.016161 | E: -42.292670 | E_var:     0.4027 | E_err:   0.009916
[2025-10-02 18:59:07] [Iter 1768/2250] R4[1284/2400] | LR: 0.016128 | E: -42.313556 | E_var:     0.3350 | E_err:   0.009044
[2025-10-02 18:59:12] [Iter 1769/2250] R4[1286/2400] | LR: 0.016096 | E: -42.309476 | E_var:     0.3327 | E_err:   0.009012
[2025-10-02 18:59:17] [Iter 1770/2250] R4[1288/2400] | LR: 0.016063 | E: -42.303166 | E_var:     0.4330 | E_err:   0.010282
[2025-10-02 18:59:22] [Iter 1771/2250] R4[1290/2400] | LR: 0.016031 | E: -42.299580 | E_var:     0.3633 | E_err:   0.009418
[2025-10-02 18:59:28] [Iter 1772/2250] R4[1292/2400] | LR: 0.015998 | E: -42.310479 | E_var:     0.3180 | E_err:   0.008811
[2025-10-02 18:59:33] [Iter 1773/2250] R4[1294/2400] | LR: 0.015966 | E: -42.310879 | E_var:     0.3828 | E_err:   0.009667
[2025-10-02 18:59:38] [Iter 1774/2250] R4[1296/2400] | LR: 0.015933 | E: -42.311431 | E_var:     0.3787 | E_err:   0.009616
[2025-10-02 18:59:43] [Iter 1775/2250] R4[1298/2400] | LR: 0.015901 | E: -42.299940 | E_var:     0.3705 | E_err:   0.009510
[2025-10-02 18:59:48] [Iter 1776/2250] R4[1300/2400] | LR: 0.015868 | E: -42.301627 | E_var:     0.4335 | E_err:   0.010288
[2025-10-02 18:59:53] [Iter 1777/2250] R4[1302/2400] | LR: 0.015836 | E: -42.289480 | E_var:     0.3810 | E_err:   0.009644
[2025-10-02 18:59:59] [Iter 1778/2250] R4[1304/2400] | LR: 0.015804 | E: -42.320151 | E_var:     0.3740 | E_err:   0.009555
[2025-10-02 19:00:04] [Iter 1779/2250] R4[1306/2400] | LR: 0.015771 | E: -42.292731 | E_var:     0.3781 | E_err:   0.009608
[2025-10-02 19:00:09] [Iter 1780/2250] R4[1308/2400] | LR: 0.015739 | E: -42.302234 | E_var:     0.3890 | E_err:   0.009746
[2025-10-02 19:00:14] [Iter 1781/2250] R4[1310/2400] | LR: 0.015706 | E: -42.306773 | E_var:     0.4416 | E_err:   0.010384
[2025-10-02 19:00:19] [Iter 1782/2250] R4[1312/2400] | LR: 0.015674 | E: -42.303806 | E_var:     0.3776 | E_err:   0.009602
[2025-10-02 19:00:24] [Iter 1783/2250] R4[1314/2400] | LR: 0.015642 | E: -42.298154 | E_var:     0.5188 | E_err:   0.011254
[2025-10-02 19:00:30] [Iter 1784/2250] R4[1316/2400] | LR: 0.015609 | E: -42.281930 | E_var:     0.4166 | E_err:   0.010086
[2025-10-02 19:00:35] [Iter 1785/2250] R4[1318/2400] | LR: 0.015577 | E: -42.316889 | E_var:     0.3052 | E_err:   0.008632
[2025-10-02 19:00:40] [Iter 1786/2250] R4[1320/2400] | LR: 0.015545 | E: -42.292692 | E_var:     0.5405 | E_err:   0.011488
[2025-10-02 19:00:45] [Iter 1787/2250] R4[1322/2400] | LR: 0.015512 | E: -42.306942 | E_var:     0.4936 | E_err:   0.010978
[2025-10-02 19:00:50] [Iter 1788/2250] R4[1324/2400] | LR: 0.015480 | E: -42.293449 | E_var:     0.4142 | E_err:   0.010056
[2025-10-02 19:00:55] [Iter 1789/2250] R4[1326/2400] | LR: 0.015448 | E: -42.311630 | E_var:     0.3464 | E_err:   0.009196
[2025-10-02 19:01:00] [Iter 1790/2250] R4[1328/2400] | LR: 0.015415 | E: -42.298630 | E_var:     0.3785 | E_err:   0.009613
[2025-10-02 19:01:06] [Iter 1791/2250] R4[1330/2400] | LR: 0.015383 | E: -42.304843 | E_var:     0.4337 | E_err:   0.010290
[2025-10-02 19:01:11] [Iter 1792/2250] R4[1332/2400] | LR: 0.015351 | E: -42.304781 | E_var:     0.3775 | E_err:   0.009600
[2025-10-02 19:01:16] [Iter 1793/2250] R4[1334/2400] | LR: 0.015319 | E: -42.287902 | E_var:     0.3662 | E_err:   0.009455
[2025-10-02 19:01:21] [Iter 1794/2250] R4[1336/2400] | LR: 0.015286 | E: -42.310799 | E_var:     0.4678 | E_err:   0.010687
[2025-10-02 19:01:26] [Iter 1795/2250] R4[1338/2400] | LR: 0.015254 | E: -42.312434 | E_var:     0.3414 | E_err:   0.009129
[2025-10-02 19:01:31] [Iter 1796/2250] R4[1340/2400] | LR: 0.015222 | E: -42.282947 | E_var:     0.7774 | E_err:   0.013776
[2025-10-02 19:01:37] [Iter 1797/2250] R4[1342/2400] | LR: 0.015190 | E: -42.301655 | E_var:     0.4973 | E_err:   0.011019
[2025-10-02 19:01:42] [Iter 1798/2250] R4[1344/2400] | LR: 0.015158 | E: -42.301153 | E_var:     0.4127 | E_err:   0.010038
[2025-10-02 19:01:47] [Iter 1799/2250] R4[1346/2400] | LR: 0.015126 | E: -42.318114 | E_var:     0.4550 | E_err:   0.010539
[2025-10-02 19:01:52] [Iter 1800/2250] R4[1348/2400] | LR: 0.015093 | E: -42.304597 | E_var:     0.4744 | E_err:   0.010762
[2025-10-02 19:01:52] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-02 19:01:57] [Iter 1801/2250] R4[1350/2400] | LR: 0.015061 | E: -42.300116 | E_var:     0.3555 | E_err:   0.009317
[2025-10-02 19:02:02] [Iter 1802/2250] R4[1352/2400] | LR: 0.015029 | E: -42.314601 | E_var:     0.4257 | E_err:   0.010195
[2025-10-02 19:02:08] [Iter 1803/2250] R4[1354/2400] | LR: 0.014997 | E: -42.314150 | E_var:     0.4183 | E_err:   0.010106
[2025-10-02 19:02:13] [Iter 1804/2250] R4[1356/2400] | LR: 0.014965 | E: -42.311153 | E_var:     0.3523 | E_err:   0.009274
[2025-10-02 19:02:18] [Iter 1805/2250] R4[1358/2400] | LR: 0.014933 | E: -42.313811 | E_var:     0.4453 | E_err:   0.010426
[2025-10-02 19:02:23] [Iter 1806/2250] R4[1360/2400] | LR: 0.014901 | E: -42.321757 | E_var:     0.8729 | E_err:   0.014598
[2025-10-02 19:02:28] [Iter 1807/2250] R4[1362/2400] | LR: 0.014869 | E: -42.292266 | E_var:     0.4020 | E_err:   0.009907
[2025-10-02 19:02:33] [Iter 1808/2250] R4[1364/2400] | LR: 0.014837 | E: -42.309294 | E_var:     0.3520 | E_err:   0.009270
[2025-10-02 19:02:38] [Iter 1809/2250] R4[1366/2400] | LR: 0.014805 | E: -42.299430 | E_var:     0.4436 | E_err:   0.010407
[2025-10-02 19:02:44] [Iter 1810/2250] R4[1368/2400] | LR: 0.014773 | E: -42.312074 | E_var:     0.4435 | E_err:   0.010405
[2025-10-02 19:02:49] [Iter 1811/2250] R4[1370/2400] | LR: 0.014741 | E: -42.290289 | E_var:     0.7844 | E_err:   0.013838
[2025-10-02 19:02:54] [Iter 1812/2250] R4[1372/2400] | LR: 0.014709 | E: -42.307052 | E_var:     0.3888 | E_err:   0.009743
[2025-10-02 19:02:59] [Iter 1813/2250] R4[1374/2400] | LR: 0.014677 | E: -42.294429 | E_var:     0.3842 | E_err:   0.009685
[2025-10-02 19:03:04] [Iter 1814/2250] R4[1376/2400] | LR: 0.014646 | E: -42.302172 | E_var:     0.3597 | E_err:   0.009371
[2025-10-02 19:03:09] [Iter 1815/2250] R4[1378/2400] | LR: 0.014614 | E: -42.321305 | E_var:     0.5659 | E_err:   0.011754
[2025-10-02 19:03:15] [Iter 1816/2250] R4[1380/2400] | LR: 0.014582 | E: -42.314381 | E_var:     0.4875 | E_err:   0.010909
[2025-10-02 19:03:20] [Iter 1817/2250] R4[1382/2400] | LR: 0.014550 | E: -42.312377 | E_var:     0.3806 | E_err:   0.009640
[2025-10-02 19:03:25] [Iter 1818/2250] R4[1384/2400] | LR: 0.014518 | E: -42.306401 | E_var:     0.3777 | E_err:   0.009602
[2025-10-02 19:03:30] [Iter 1819/2250] R4[1386/2400] | LR: 0.014487 | E: -42.314342 | E_var:     0.4432 | E_err:   0.010402
[2025-10-02 19:03:35] [Iter 1820/2250] R4[1388/2400] | LR: 0.014455 | E: -42.288219 | E_var:     0.4134 | E_err:   0.010046
[2025-10-02 19:03:40] [Iter 1821/2250] R4[1390/2400] | LR: 0.014423 | E: -42.294419 | E_var:     0.4091 | E_err:   0.009993
[2025-10-02 19:03:46] [Iter 1822/2250] R4[1392/2400] | LR: 0.014391 | E: -42.312993 | E_var:     0.4780 | E_err:   0.010803
[2025-10-02 19:03:51] [Iter 1823/2250] R4[1394/2400] | LR: 0.014360 | E: -42.294409 | E_var:     0.4445 | E_err:   0.010417
[2025-10-02 19:03:56] [Iter 1824/2250] R4[1396/2400] | LR: 0.014328 | E: -42.323979 | E_var:     0.3830 | E_err:   0.009670
[2025-10-02 19:04:01] [Iter 1825/2250] R4[1398/2400] | LR: 0.014296 | E: -42.304895 | E_var:     0.3322 | E_err:   0.009006
[2025-10-02 19:04:06] [Iter 1826/2250] R4[1400/2400] | LR: 0.014265 | E: -42.300438 | E_var:     0.2967 | E_err:   0.008511
[2025-10-02 19:04:11] [Iter 1827/2250] R4[1402/2400] | LR: 0.014233 | E: -42.308315 | E_var:     0.3660 | E_err:   0.009453
[2025-10-02 19:04:16] [Iter 1828/2250] R4[1404/2400] | LR: 0.014202 | E: -42.294987 | E_var:     0.3351 | E_err:   0.009045
[2025-10-02 19:04:22] [Iter 1829/2250] R4[1406/2400] | LR: 0.014170 | E: -42.304033 | E_var:     0.3665 | E_err:   0.009459
[2025-10-02 19:04:27] [Iter 1830/2250] R4[1408/2400] | LR: 0.014139 | E: -42.314020 | E_var:     0.4906 | E_err:   0.010944
[2025-10-02 19:04:32] [Iter 1831/2250] R4[1410/2400] | LR: 0.014107 | E: -42.307994 | E_var:     0.4446 | E_err:   0.010419
[2025-10-02 19:04:37] [Iter 1832/2250] R4[1412/2400] | LR: 0.014076 | E: -42.300736 | E_var:     0.3791 | E_err:   0.009620
[2025-10-02 19:04:42] [Iter 1833/2250] R4[1414/2400] | LR: 0.014044 | E: -42.293639 | E_var:     0.3766 | E_err:   0.009589
[2025-10-02 19:04:47] [Iter 1834/2250] R4[1416/2400] | LR: 0.014013 | E: -42.299244 | E_var:     0.4123 | E_err:   0.010033
[2025-10-02 19:04:53] [Iter 1835/2250] R4[1418/2400] | LR: 0.013981 | E: -42.310385 | E_var:     0.3241 | E_err:   0.008896
[2025-10-02 19:04:58] [Iter 1836/2250] R4[1420/2400] | LR: 0.013950 | E: -42.311946 | E_var:     0.4071 | E_err:   0.009969
[2025-10-02 19:05:03] [Iter 1837/2250] R4[1422/2400] | LR: 0.013918 | E: -42.307367 | E_var:     0.3375 | E_err:   0.009077
[2025-10-02 19:05:08] [Iter 1838/2250] R4[1424/2400] | LR: 0.013887 | E: -42.288474 | E_var:     0.4347 | E_err:   0.010302
[2025-10-02 19:05:13] [Iter 1839/2250] R4[1426/2400] | LR: 0.013856 | E: -42.324093 | E_var:     0.4976 | E_err:   0.011022
[2025-10-02 19:05:18] [Iter 1840/2250] R4[1428/2400] | LR: 0.013824 | E: -42.307905 | E_var:     0.3513 | E_err:   0.009261
[2025-10-02 19:05:23] [Iter 1841/2250] R4[1430/2400] | LR: 0.013793 | E: -42.320317 | E_var:     0.3260 | E_err:   0.008921
[2025-10-02 19:05:29] [Iter 1842/2250] R4[1432/2400] | LR: 0.013762 | E: -42.309112 | E_var:     0.5054 | E_err:   0.011108
[2025-10-02 19:05:34] [Iter 1843/2250] R4[1434/2400] | LR: 0.013731 | E: -42.294809 | E_var:     0.5029 | E_err:   0.011080
[2025-10-02 19:05:39] [Iter 1844/2250] R4[1436/2400] | LR: 0.013700 | E: -42.289697 | E_var:     0.3909 | E_err:   0.009769
[2025-10-02 19:05:44] [Iter 1845/2250] R4[1438/2400] | LR: 0.013668 | E: -42.297543 | E_var:     0.3780 | E_err:   0.009606
[2025-10-02 19:05:49] [Iter 1846/2250] R4[1440/2400] | LR: 0.013637 | E: -42.309468 | E_var:     0.3273 | E_err:   0.008939
[2025-10-02 19:05:54] [Iter 1847/2250] R4[1442/2400] | LR: 0.013606 | E: -42.299154 | E_var:     0.3677 | E_err:   0.009475
[2025-10-02 19:06:00] [Iter 1848/2250] R4[1444/2400] | LR: 0.013575 | E: -42.302707 | E_var:     0.3721 | E_err:   0.009532
[2025-10-02 19:06:05] [Iter 1849/2250] R4[1446/2400] | LR: 0.013544 | E: -42.303859 | E_var:     0.3328 | E_err:   0.009014
[2025-10-02 19:06:10] [Iter 1850/2250] R4[1448/2400] | LR: 0.013513 | E: -42.306897 | E_var:     0.4142 | E_err:   0.010055
[2025-10-02 19:06:15] [Iter 1851/2250] R4[1450/2400] | LR: 0.013482 | E: -42.307356 | E_var:     0.3847 | E_err:   0.009691
[2025-10-02 19:06:20] [Iter 1852/2250] R4[1452/2400] | LR: 0.013451 | E: -42.290149 | E_var:     0.4719 | E_err:   0.010734
[2025-10-02 19:06:25] [Iter 1853/2250] R4[1454/2400] | LR: 0.013420 | E: -42.300829 | E_var:     0.5966 | E_err:   0.012069
[2025-10-02 19:06:31] [Iter 1854/2250] R4[1456/2400] | LR: 0.013389 | E: -42.310685 | E_var:     0.4051 | E_err:   0.009945
[2025-10-02 19:06:36] [Iter 1855/2250] R4[1458/2400] | LR: 0.013358 | E: -42.293327 | E_var:     0.3143 | E_err:   0.008759
[2025-10-02 19:06:41] [Iter 1856/2250] R4[1460/2400] | LR: 0.013327 | E: -42.292385 | E_var:     0.5846 | E_err:   0.011947
[2025-10-02 19:06:46] [Iter 1857/2250] R4[1462/2400] | LR: 0.013297 | E: -42.293789 | E_var:     0.4702 | E_err:   0.010714
[2025-10-02 19:06:51] [Iter 1858/2250] R4[1464/2400] | LR: 0.013266 | E: -42.288084 | E_var:     0.3957 | E_err:   0.009829
[2025-10-02 19:06:56] [Iter 1859/2250] R4[1466/2400] | LR: 0.013235 | E: -42.307499 | E_var:     0.3472 | E_err:   0.009206
[2025-10-02 19:07:02] [Iter 1860/2250] R4[1468/2400] | LR: 0.013204 | E: -42.301816 | E_var:     0.3849 | E_err:   0.009694
[2025-10-02 19:07:07] [Iter 1861/2250] R4[1470/2400] | LR: 0.013174 | E: -42.301241 | E_var:     0.3768 | E_err:   0.009591
[2025-10-02 19:07:12] [Iter 1862/2250] R4[1472/2400] | LR: 0.013143 | E: -42.296196 | E_var:     0.3220 | E_err:   0.008867
[2025-10-02 19:07:17] [Iter 1863/2250] R4[1474/2400] | LR: 0.013112 | E: -42.299607 | E_var:     0.4023 | E_err:   0.009910
[2025-10-02 19:07:22] [Iter 1864/2250] R4[1476/2400] | LR: 0.013082 | E: -42.319831 | E_var:     0.5703 | E_err:   0.011800
[2025-10-02 19:07:28] [Iter 1865/2250] R4[1478/2400] | LR: 0.013051 | E: -42.304190 | E_var:     0.4203 | E_err:   0.010130
[2025-10-02 19:07:33] [Iter 1866/2250] R4[1480/2400] | LR: 0.013020 | E: -42.295676 | E_var:     0.5006 | E_err:   0.011055
[2025-10-02 19:07:38] [Iter 1867/2250] R4[1482/2400] | LR: 0.012990 | E: -42.312102 | E_var:     0.3729 | E_err:   0.009542
[2025-10-02 19:07:43] [Iter 1868/2250] R4[1484/2400] | LR: 0.012959 | E: -42.307930 | E_var:     0.3692 | E_err:   0.009495
[2025-10-02 19:07:48] [Iter 1869/2250] R4[1486/2400] | LR: 0.012929 | E: -42.302112 | E_var:     0.3430 | E_err:   0.009151
[2025-10-02 19:07:53] [Iter 1870/2250] R4[1488/2400] | LR: 0.012898 | E: -42.308190 | E_var:     0.4653 | E_err:   0.010658
[2025-10-02 19:07:58] [Iter 1871/2250] R4[1490/2400] | LR: 0.012868 | E: -42.295274 | E_var:     0.4764 | E_err:   0.010785
[2025-10-02 19:08:04] [Iter 1872/2250] R4[1492/2400] | LR: 0.012838 | E: -42.305555 | E_var:     0.4717 | E_err:   0.010732
[2025-10-02 19:08:09] [Iter 1873/2250] R4[1494/2400] | LR: 0.012807 | E: -42.303774 | E_var:     0.4304 | E_err:   0.010251
[2025-10-02 19:08:14] [Iter 1874/2250] R4[1496/2400] | LR: 0.012777 | E: -42.315267 | E_var:     0.3903 | E_err:   0.009762
[2025-10-02 19:08:19] [Iter 1875/2250] R4[1498/2400] | LR: 0.012747 | E: -42.314067 | E_var:     0.4500 | E_err:   0.010482
[2025-10-02 19:08:24] [Iter 1876/2250] R4[1500/2400] | LR: 0.012716 | E: -42.304607 | E_var:     0.3223 | E_err:   0.008871
[2025-10-02 19:08:29] [Iter 1877/2250] R4[1502/2400] | LR: 0.012686 | E: -42.306811 | E_var:     0.3311 | E_err:   0.008991
[2025-10-02 19:08:34] [Iter 1878/2250] R4[1504/2400] | LR: 0.012656 | E: -42.297764 | E_var:     0.4467 | E_err:   0.010443
[2025-10-02 19:08:40] [Iter 1879/2250] R4[1506/2400] | LR: 0.012626 | E: -42.312564 | E_var:     0.3562 | E_err:   0.009326
[2025-10-02 19:08:45] [Iter 1880/2250] R4[1508/2400] | LR: 0.012596 | E: -42.309231 | E_var:     0.3971 | E_err:   0.009846
[2025-10-02 19:08:50] [Iter 1881/2250] R4[1510/2400] | LR: 0.012566 | E: -42.320843 | E_var:     0.5877 | E_err:   0.011978
[2025-10-02 19:08:55] [Iter 1882/2250] R4[1512/2400] | LR: 0.012536 | E: -42.301186 | E_var:     0.3816 | E_err:   0.009652
[2025-10-02 19:09:00] [Iter 1883/2250] R4[1514/2400] | LR: 0.012506 | E: -42.300291 | E_var:     0.4443 | E_err:   0.010415
[2025-10-02 19:09:05] [Iter 1884/2250] R4[1516/2400] | LR: 0.012476 | E: -42.298135 | E_var:     0.3406 | E_err:   0.009119
[2025-10-02 19:09:11] [Iter 1885/2250] R4[1518/2400] | LR: 0.012446 | E: -42.307521 | E_var:     0.5344 | E_err:   0.011422
[2025-10-02 19:09:16] [Iter 1886/2250] R4[1520/2400] | LR: 0.012416 | E: -42.303402 | E_var:     0.4390 | E_err:   0.010353
[2025-10-02 19:09:21] [Iter 1887/2250] R4[1522/2400] | LR: 0.012386 | E: -42.289388 | E_var:     1.0776 | E_err:   0.016220
[2025-10-02 19:09:26] [Iter 1888/2250] R4[1524/2400] | LR: 0.012356 | E: -42.320209 | E_var:     0.3303 | E_err:   0.008980
[2025-10-02 19:09:31] [Iter 1889/2250] R4[1526/2400] | LR: 0.012326 | E: -42.318616 | E_var:     0.4427 | E_err:   0.010396
[2025-10-02 19:09:36] [Iter 1890/2250] R4[1528/2400] | LR: 0.012296 | E: -42.300618 | E_var:     0.3630 | E_err:   0.009414
[2025-10-02 19:09:41] [Iter 1891/2250] R4[1530/2400] | LR: 0.012267 | E: -42.314310 | E_var:     0.3682 | E_err:   0.009481
[2025-10-02 19:09:47] [Iter 1892/2250] R4[1532/2400] | LR: 0.012237 | E: -42.303958 | E_var:     0.3947 | E_err:   0.009816
[2025-10-02 19:09:52] [Iter 1893/2250] R4[1534/2400] | LR: 0.012207 | E: -42.321163 | E_var:     0.3520 | E_err:   0.009270
[2025-10-02 19:09:57] [Iter 1894/2250] R4[1536/2400] | LR: 0.012178 | E: -42.313077 | E_var:     0.3813 | E_err:   0.009649
[2025-10-02 19:10:02] [Iter 1895/2250] R4[1538/2400] | LR: 0.012148 | E: -42.313040 | E_var:     0.3667 | E_err:   0.009462
[2025-10-02 19:10:07] [Iter 1896/2250] R4[1540/2400] | LR: 0.012119 | E: -42.314704 | E_var:     0.3485 | E_err:   0.009225
[2025-10-02 19:10:12] [Iter 1897/2250] R4[1542/2400] | LR: 0.012089 | E: -42.330693 | E_var:     0.6095 | E_err:   0.012198
[2025-10-02 19:10:17] [Iter 1898/2250] R4[1544/2400] | LR: 0.012060 | E: -42.302473 | E_var:     0.3667 | E_err:   0.009461
[2025-10-02 19:10:23] [Iter 1899/2250] R4[1546/2400] | LR: 0.012030 | E: -42.305400 | E_var:     0.5067 | E_err:   0.011123
[2025-10-02 19:10:28] [Iter 1900/2250] R4[1548/2400] | LR: 0.012001 | E: -42.288764 | E_var:     1.8084 | E_err:   0.021012
[2025-10-02 19:10:33] [Iter 1901/2250] R4[1550/2400] | LR: 0.011971 | E: -42.299967 | E_var:     0.4754 | E_err:   0.010774
[2025-10-02 19:10:38] [Iter 1902/2250] R4[1552/2400] | LR: 0.011942 | E: -42.292378 | E_var:     0.3895 | E_err:   0.009751
[2025-10-02 19:10:43] [Iter 1903/2250] R4[1554/2400] | LR: 0.011913 | E: -42.315538 | E_var:     0.3752 | E_err:   0.009571
[2025-10-02 19:10:48] [Iter 1904/2250] R4[1556/2400] | LR: 0.011884 | E: -42.294792 | E_var:     0.3131 | E_err:   0.008743
[2025-10-02 19:10:53] [Iter 1905/2250] R4[1558/2400] | LR: 0.011854 | E: -42.302873 | E_var:     0.4122 | E_err:   0.010032
[2025-10-02 19:10:59] [Iter 1906/2250] R4[1560/2400] | LR: 0.011825 | E: -42.309407 | E_var:     0.3142 | E_err:   0.008758
[2025-10-02 19:11:04] [Iter 1907/2250] R4[1562/2400] | LR: 0.011796 | E: -42.326793 | E_var:     0.5289 | E_err:   0.011363
[2025-10-02 19:11:09] [Iter 1908/2250] R4[1564/2400] | LR: 0.011767 | E: -42.329712 | E_var:     0.2893 | E_err:   0.008405
[2025-10-02 19:11:14] [Iter 1909/2250] R4[1566/2400] | LR: 0.011738 | E: -42.308178 | E_var:     0.4697 | E_err:   0.010708
[2025-10-02 19:11:19] [Iter 1910/2250] R4[1568/2400] | LR: 0.011709 | E: -42.305636 | E_var:     0.3808 | E_err:   0.009642
[2025-10-02 19:11:24] [Iter 1911/2250] R4[1570/2400] | LR: 0.011680 | E: -42.302579 | E_var:     0.3182 | E_err:   0.008814
[2025-10-02 19:11:29] [Iter 1912/2250] R4[1572/2400] | LR: 0.011651 | E: -42.303884 | E_var:     0.2991 | E_err:   0.008545
[2025-10-02 19:11:35] [Iter 1913/2250] R4[1574/2400] | LR: 0.011622 | E: -42.299205 | E_var:     0.3256 | E_err:   0.008916
[2025-10-02 19:11:40] [Iter 1914/2250] R4[1576/2400] | LR: 0.011593 | E: -42.308836 | E_var:     0.3353 | E_err:   0.009048
[2025-10-02 19:11:45] [Iter 1915/2250] R4[1578/2400] | LR: 0.011564 | E: -42.299484 | E_var:     0.2879 | E_err:   0.008384
[2025-10-02 19:11:50] [Iter 1916/2250] R4[1580/2400] | LR: 0.011536 | E: -42.314122 | E_var:     0.4180 | E_err:   0.010102
[2025-10-02 19:11:55] [Iter 1917/2250] R4[1582/2400] | LR: 0.011507 | E: -42.314909 | E_var:     0.3945 | E_err:   0.009814
[2025-10-02 19:12:00] [Iter 1918/2250] R4[1584/2400] | LR: 0.011478 | E: -42.307021 | E_var:     0.4039 | E_err:   0.009930
[2025-10-02 19:12:05] [Iter 1919/2250] R4[1586/2400] | LR: 0.011449 | E: -42.315471 | E_var:     0.3707 | E_err:   0.009514
[2025-10-02 19:12:11] [Iter 1920/2250] R4[1588/2400] | LR: 0.011421 | E: -42.319947 | E_var:     0.3578 | E_err:   0.009346
[2025-10-02 19:12:16] [Iter 1921/2250] R4[1590/2400] | LR: 0.011392 | E: -42.302605 | E_var:     0.3414 | E_err:   0.009130
[2025-10-02 19:12:21] [Iter 1922/2250] R4[1592/2400] | LR: 0.011364 | E: -42.310268 | E_var:     0.3964 | E_err:   0.009838
[2025-10-02 19:12:26] [Iter 1923/2250] R4[1594/2400] | LR: 0.011335 | E: -42.330326 | E_var:     0.3714 | E_err:   0.009522
[2025-10-02 19:12:31] [Iter 1924/2250] R4[1596/2400] | LR: 0.011307 | E: -42.291923 | E_var:     0.4458 | E_err:   0.010432
[2025-10-02 19:12:36] [Iter 1925/2250] R4[1598/2400] | LR: 0.011278 | E: -42.298339 | E_var:     0.4453 | E_err:   0.010427
[2025-10-02 19:12:42] [Iter 1926/2250] R4[1600/2400] | LR: 0.011250 | E: -42.306494 | E_var:     0.3808 | E_err:   0.009641
[2025-10-02 19:12:47] [Iter 1927/2250] R4[1602/2400] | LR: 0.011222 | E: -42.293940 | E_var:     0.3625 | E_err:   0.009407
[2025-10-02 19:12:52] [Iter 1928/2250] R4[1604/2400] | LR: 0.011193 | E: -42.322228 | E_var:     0.3749 | E_err:   0.009567
[2025-10-02 19:12:57] [Iter 1929/2250] R4[1606/2400] | LR: 0.011165 | E: -42.321668 | E_var:     0.5665 | E_err:   0.011760
[2025-10-02 19:13:02] [Iter 1930/2250] R4[1608/2400] | LR: 0.011137 | E: -42.300822 | E_var:     0.4264 | E_err:   0.010203
[2025-10-02 19:13:07] [Iter 1931/2250] R4[1610/2400] | LR: 0.011109 | E: -42.313473 | E_var:     0.3960 | E_err:   0.009832
[2025-10-02 19:13:12] [Iter 1932/2250] R4[1612/2400] | LR: 0.011081 | E: -42.307548 | E_var:     0.3398 | E_err:   0.009108
[2025-10-02 19:13:18] [Iter 1933/2250] R4[1614/2400] | LR: 0.011053 | E: -42.306452 | E_var:     0.3669 | E_err:   0.009464
[2025-10-02 19:13:23] [Iter 1934/2250] R4[1616/2400] | LR: 0.011025 | E: -42.298544 | E_var:     0.4209 | E_err:   0.010137
[2025-10-02 19:13:28] [Iter 1935/2250] R4[1618/2400] | LR: 0.010997 | E: -42.314886 | E_var:     0.3937 | E_err:   0.009804
[2025-10-02 19:13:33] [Iter 1936/2250] R4[1620/2400] | LR: 0.010969 | E: -42.294464 | E_var:     0.3464 | E_err:   0.009196
[2025-10-02 19:13:38] [Iter 1937/2250] R4[1622/2400] | LR: 0.010941 | E: -42.293024 | E_var:     0.3929 | E_err:   0.009794
[2025-10-02 19:13:43] [Iter 1938/2250] R4[1624/2400] | LR: 0.010913 | E: -42.306464 | E_var:     0.3408 | E_err:   0.009121
[2025-10-02 19:13:48] [Iter 1939/2250] R4[1626/2400] | LR: 0.010885 | E: -42.318247 | E_var:     0.3640 | E_err:   0.009427
[2025-10-02 19:13:54] [Iter 1940/2250] R4[1628/2400] | LR: 0.010858 | E: -42.321142 | E_var:     0.3338 | E_err:   0.009028
[2025-10-02 19:13:59] [Iter 1941/2250] R4[1630/2400] | LR: 0.010830 | E: -42.292957 | E_var:     0.3701 | E_err:   0.009506
[2025-10-02 19:14:04] [Iter 1942/2250] R4[1632/2400] | LR: 0.010802 | E: -42.310061 | E_var:     0.3666 | E_err:   0.009461
[2025-10-02 19:14:09] [Iter 1943/2250] R4[1634/2400] | LR: 0.010775 | E: -42.311026 | E_var:     0.3061 | E_err:   0.008645
[2025-10-02 19:14:14] [Iter 1944/2250] R4[1636/2400] | LR: 0.010747 | E: -42.311815 | E_var:     0.4143 | E_err:   0.010057
[2025-10-02 19:14:19] [Iter 1945/2250] R4[1638/2400] | LR: 0.010719 | E: -42.317509 | E_var:     0.4681 | E_err:   0.010690
[2025-10-02 19:14:24] [Iter 1946/2250] R4[1640/2400] | LR: 0.010692 | E: -42.299248 | E_var:     0.4423 | E_err:   0.010392
[2025-10-02 19:14:30] [Iter 1947/2250] R4[1642/2400] | LR: 0.010665 | E: -42.313659 | E_var:     0.3332 | E_err:   0.009019
[2025-10-02 19:14:35] [Iter 1948/2250] R4[1644/2400] | LR: 0.010637 | E: -42.312331 | E_var:     0.3797 | E_err:   0.009629
[2025-10-02 19:14:40] [Iter 1949/2250] R4[1646/2400] | LR: 0.010610 | E: -42.315601 | E_var:     0.4452 | E_err:   0.010425
[2025-10-02 19:14:45] [Iter 1950/2250] R4[1648/2400] | LR: 0.010583 | E: -42.309242 | E_var:     0.3434 | E_err:   0.009156
[2025-10-02 19:14:50] [Iter 1951/2250] R4[1650/2400] | LR: 0.010555 | E: -42.296466 | E_var:     0.2994 | E_err:   0.008550
[2025-10-02 19:14:55] [Iter 1952/2250] R4[1652/2400] | LR: 0.010528 | E: -42.314403 | E_var:     0.3518 | E_err:   0.009268
[2025-10-02 19:15:00] [Iter 1953/2250] R4[1654/2400] | LR: 0.010501 | E: -42.301212 | E_var:     0.5892 | E_err:   0.011994
[2025-10-02 19:15:06] [Iter 1954/2250] R4[1656/2400] | LR: 0.010474 | E: -42.285209 | E_var:     0.3357 | E_err:   0.009053
[2025-10-02 19:15:11] [Iter 1955/2250] R4[1658/2400] | LR: 0.010447 | E: -42.325021 | E_var:     0.8230 | E_err:   0.014175
[2025-10-02 19:15:16] [Iter 1956/2250] R4[1660/2400] | LR: 0.010420 | E: -42.302575 | E_var:     0.3074 | E_err:   0.008663
[2025-10-02 19:15:21] [Iter 1957/2250] R4[1662/2400] | LR: 0.010393 | E: -42.299062 | E_var:     0.3411 | E_err:   0.009126
[2025-10-02 19:15:26] [Iter 1958/2250] R4[1664/2400] | LR: 0.010366 | E: -42.323433 | E_var:     0.3456 | E_err:   0.009185
[2025-10-02 19:15:31] [Iter 1959/2250] R4[1666/2400] | LR: 0.010339 | E: -42.309616 | E_var:     0.3463 | E_err:   0.009195
[2025-10-02 19:15:36] [Iter 1960/2250] R4[1668/2400] | LR: 0.010312 | E: -42.302020 | E_var:     0.4296 | E_err:   0.010241
[2025-10-02 19:15:42] [Iter 1961/2250] R4[1670/2400] | LR: 0.010286 | E: -42.309957 | E_var:     0.3096 | E_err:   0.008694
[2025-10-02 19:15:47] [Iter 1962/2250] R4[1672/2400] | LR: 0.010259 | E: -42.317083 | E_var:     0.3491 | E_err:   0.009233
[2025-10-02 19:15:52] [Iter 1963/2250] R4[1674/2400] | LR: 0.010232 | E: -42.318655 | E_var:     0.3504 | E_err:   0.009249
[2025-10-02 19:15:57] [Iter 1964/2250] R4[1676/2400] | LR: 0.010206 | E: -42.295807 | E_var:     0.3528 | E_err:   0.009281
[2025-10-02 19:16:02] [Iter 1965/2250] R4[1678/2400] | LR: 0.010179 | E: -42.314974 | E_var:     0.3766 | E_err:   0.009589
[2025-10-02 19:16:07] [Iter 1966/2250] R4[1680/2400] | LR: 0.010153 | E: -42.314081 | E_var:     0.3476 | E_err:   0.009213
[2025-10-02 19:16:12] [Iter 1967/2250] R4[1682/2400] | LR: 0.010126 | E: -42.304513 | E_var:     0.6644 | E_err:   0.012736
[2025-10-02 19:16:18] [Iter 1968/2250] R4[1684/2400] | LR: 0.010100 | E: -42.319957 | E_var:     0.3382 | E_err:   0.009087
[2025-10-02 19:16:23] [Iter 1969/2250] R4[1686/2400] | LR: 0.010073 | E: -42.284974 | E_var:     2.7404 | E_err:   0.025866
[2025-10-02 19:16:28] [Iter 1970/2250] R4[1688/2400] | LR: 0.010047 | E: -42.276523 | E_var:     3.1909 | E_err:   0.027911
[2025-10-02 19:16:33] [Iter 1971/2250] R4[1690/2400] | LR: 0.010021 | E: -42.317043 | E_var:     0.3638 | E_err:   0.009424
[2025-10-02 19:16:38] [Iter 1972/2250] R4[1692/2400] | LR: 0.009995 | E: -42.306306 | E_var:     0.3367 | E_err:   0.009067
[2025-10-02 19:16:43] [Iter 1973/2250] R4[1694/2400] | LR: 0.009969 | E: -42.312724 | E_var:     0.4665 | E_err:   0.010673
[2025-10-02 19:16:48] [Iter 1974/2250] R4[1696/2400] | LR: 0.009943 | E: -42.315989 | E_var:     0.3593 | E_err:   0.009365
[2025-10-02 19:16:54] [Iter 1975/2250] R4[1698/2400] | LR: 0.009916 | E: -42.308027 | E_var:     0.2905 | E_err:   0.008422
[2025-10-02 19:16:59] [Iter 1976/2250] R4[1700/2400] | LR: 0.009890 | E: -42.290657 | E_var:     0.7049 | E_err:   0.013119
[2025-10-02 19:17:04] [Iter 1977/2250] R4[1702/2400] | LR: 0.009865 | E: -42.308184 | E_var:     0.3357 | E_err:   0.009054
[2025-10-02 19:17:09] [Iter 1978/2250] R4[1704/2400] | LR: 0.009839 | E: -42.297089 | E_var:     0.3521 | E_err:   0.009272
[2025-10-02 19:17:14] [Iter 1979/2250] R4[1706/2400] | LR: 0.009813 | E: -42.301576 | E_var:     0.3554 | E_err:   0.009314
[2025-10-02 19:17:19] [Iter 1980/2250] R4[1708/2400] | LR: 0.009787 | E: -42.305750 | E_var:     0.3528 | E_err:   0.009281
[2025-10-02 19:17:24] [Iter 1981/2250] R4[1710/2400] | LR: 0.009761 | E: -42.305403 | E_var:     0.3337 | E_err:   0.009026
[2025-10-02 19:17:30] [Iter 1982/2250] R4[1712/2400] | LR: 0.009736 | E: -42.327034 | E_var:     0.3249 | E_err:   0.008906
[2025-10-02 19:17:35] [Iter 1983/2250] R4[1714/2400] | LR: 0.009710 | E: -42.294934 | E_var:     0.3304 | E_err:   0.008981
[2025-10-02 19:17:40] [Iter 1984/2250] R4[1716/2400] | LR: 0.009684 | E: -42.320808 | E_var:     0.4894 | E_err:   0.010931
[2025-10-02 19:17:45] [Iter 1985/2250] R4[1718/2400] | LR: 0.009659 | E: -42.302780 | E_var:     0.4286 | E_err:   0.010229
[2025-10-02 19:17:50] [Iter 1986/2250] R4[1720/2400] | LR: 0.009633 | E: -42.313806 | E_var:     0.5137 | E_err:   0.011199
[2025-10-02 19:17:55] [Iter 1987/2250] R4[1722/2400] | LR: 0.009608 | E: -42.332812 | E_var:     0.6483 | E_err:   0.012581
[2025-10-02 19:18:00] [Iter 1988/2250] R4[1724/2400] | LR: 0.009583 | E: -42.297052 | E_var:     0.3523 | E_err:   0.009275
[2025-10-02 19:18:06] [Iter 1989/2250] R4[1726/2400] | LR: 0.009557 | E: -42.306231 | E_var:     0.3483 | E_err:   0.009221
[2025-10-02 19:18:11] [Iter 1990/2250] R4[1728/2400] | LR: 0.009532 | E: -42.322859 | E_var:     0.3437 | E_err:   0.009160
[2025-10-02 19:18:16] [Iter 1991/2250] R4[1730/2400] | LR: 0.009507 | E: -42.306036 | E_var:     0.4013 | E_err:   0.009898
[2025-10-02 19:18:21] [Iter 1992/2250] R4[1732/2400] | LR: 0.009482 | E: -42.318576 | E_var:     0.3922 | E_err:   0.009786
[2025-10-02 19:18:26] [Iter 1993/2250] R4[1734/2400] | LR: 0.009457 | E: -42.304238 | E_var:     0.3302 | E_err:   0.008978
[2025-10-02 19:18:31] [Iter 1994/2250] R4[1736/2400] | LR: 0.009432 | E: -42.309352 | E_var:     0.4349 | E_err:   0.010304
[2025-10-02 19:18:36] [Iter 1995/2250] R4[1738/2400] | LR: 0.009407 | E: -42.299276 | E_var:     0.3777 | E_err:   0.009602
[2025-10-02 19:18:42] [Iter 1996/2250] R4[1740/2400] | LR: 0.009382 | E: -42.298137 | E_var:     0.4052 | E_err:   0.009947
[2025-10-02 19:18:47] [Iter 1997/2250] R4[1742/2400] | LR: 0.009357 | E: -42.290976 | E_var:     1.4680 | E_err:   0.018931
[2025-10-02 19:18:52] [Iter 1998/2250] R4[1744/2400] | LR: 0.009332 | E: -42.308067 | E_var:     0.3346 | E_err:   0.009039
[2025-10-02 19:18:57] [Iter 1999/2250] R4[1746/2400] | LR: 0.009307 | E: -42.299784 | E_var:     0.4468 | E_err:   0.010444
[2025-10-02 19:19:02] [Iter 2000/2250] R4[1748/2400] | LR: 0.009283 | E: -42.309647 | E_var:     0.2705 | E_err:   0.008126
[2025-10-02 19:19:02] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-10-02 19:19:07] [Iter 2001/2250] R4[1750/2400] | LR: 0.009258 | E: -42.285972 | E_var:     0.3930 | E_err:   0.009796
[2025-10-02 19:19:13] [Iter 2002/2250] R4[1752/2400] | LR: 0.009234 | E: -42.301603 | E_var:     0.4335 | E_err:   0.010288
[2025-10-02 19:19:18] [Iter 2003/2250] R4[1754/2400] | LR: 0.009209 | E: -42.309725 | E_var:     0.4206 | E_err:   0.010133
[2025-10-02 19:19:23] [Iter 2004/2250] R4[1756/2400] | LR: 0.009185 | E: -42.305054 | E_var:     0.4265 | E_err:   0.010204
[2025-10-02 19:19:28] [Iter 2005/2250] R4[1758/2400] | LR: 0.009160 | E: -42.319193 | E_var:     0.4777 | E_err:   0.010799
[2025-10-02 19:19:33] [Iter 2006/2250] R4[1760/2400] | LR: 0.009136 | E: -42.323748 | E_var:     0.4534 | E_err:   0.010521
[2025-10-02 19:19:38] [Iter 2007/2250] R4[1762/2400] | LR: 0.009112 | E: -42.302094 | E_var:     0.3679 | E_err:   0.009477
[2025-10-02 19:19:43] [Iter 2008/2250] R4[1764/2400] | LR: 0.009087 | E: -42.321976 | E_var:     0.2985 | E_err:   0.008537
[2025-10-02 19:19:49] [Iter 2009/2250] R4[1766/2400] | LR: 0.009063 | E: -42.334456 | E_var:     0.3529 | E_err:   0.009282
[2025-10-02 19:19:54] [Iter 2010/2250] R4[1768/2400] | LR: 0.009039 | E: -42.296378 | E_var:     0.4364 | E_err:   0.010322
[2025-10-02 19:19:59] [Iter 2011/2250] R4[1770/2400] | LR: 0.009015 | E: -42.313053 | E_var:     0.3338 | E_err:   0.009027
[2025-10-02 19:20:04] [Iter 2012/2250] R4[1772/2400] | LR: 0.008991 | E: -42.312804 | E_var:     0.3417 | E_err:   0.009134
[2025-10-02 19:20:09] [Iter 2013/2250] R4[1774/2400] | LR: 0.008967 | E: -42.288094 | E_var:     0.4092 | E_err:   0.009995
[2025-10-02 19:20:14] [Iter 2014/2250] R4[1776/2400] | LR: 0.008943 | E: -42.306315 | E_var:     0.3660 | E_err:   0.009453
[2025-10-02 19:20:19] [Iter 2015/2250] R4[1778/2400] | LR: 0.008919 | E: -42.298647 | E_var:     0.4117 | E_err:   0.010026
[2025-10-02 19:20:25] [Iter 2016/2250] R4[1780/2400] | LR: 0.008896 | E: -42.307566 | E_var:     0.3823 | E_err:   0.009661
[2025-10-02 19:20:30] [Iter 2017/2250] R4[1782/2400] | LR: 0.008872 | E: -42.305530 | E_var:     0.3305 | E_err:   0.008982
[2025-10-02 19:20:35] [Iter 2018/2250] R4[1784/2400] | LR: 0.008848 | E: -42.307464 | E_var:     0.3556 | E_err:   0.009318
[2025-10-02 19:20:40] [Iter 2019/2250] R4[1786/2400] | LR: 0.008825 | E: -42.312835 | E_var:     0.3879 | E_err:   0.009731
[2025-10-02 19:20:45] [Iter 2020/2250] R4[1788/2400] | LR: 0.008801 | E: -42.305037 | E_var:     0.3626 | E_err:   0.009409
[2025-10-02 19:20:50] [Iter 2021/2250] R4[1790/2400] | LR: 0.008778 | E: -42.321544 | E_var:     0.3607 | E_err:   0.009384
[2025-10-02 19:20:55] [Iter 2022/2250] R4[1792/2400] | LR: 0.008754 | E: -42.322878 | E_var:     0.4045 | E_err:   0.009937
[2025-10-02 19:21:01] [Iter 2023/2250] R4[1794/2400] | LR: 0.008731 | E: -42.319041 | E_var:     0.5968 | E_err:   0.012071
[2025-10-02 19:21:06] [Iter 2024/2250] R4[1796/2400] | LR: 0.008708 | E: -42.305491 | E_var:     0.4108 | E_err:   0.010015
[2025-10-02 19:21:11] [Iter 2025/2250] R4[1798/2400] | LR: 0.008684 | E: -42.305746 | E_var:     0.6687 | E_err:   0.012777
[2025-10-02 19:21:16] [Iter 2026/2250] R4[1800/2400] | LR: 0.008661 | E: -42.310159 | E_var:     0.3222 | E_err:   0.008868
[2025-10-02 19:21:21] [Iter 2027/2250] R4[1802/2400] | LR: 0.008638 | E: -42.303871 | E_var:     0.3712 | E_err:   0.009519
[2025-10-02 19:21:26] [Iter 2028/2250] R4[1804/2400] | LR: 0.008615 | E: -42.300638 | E_var:     0.3844 | E_err:   0.009688
[2025-10-02 19:21:31] [Iter 2029/2250] R4[1806/2400] | LR: 0.008592 | E: -42.322516 | E_var:     0.3650 | E_err:   0.009440
[2025-10-02 19:21:37] [Iter 2030/2250] R4[1808/2400] | LR: 0.008569 | E: -42.299145 | E_var:     0.3369 | E_err:   0.009069
[2025-10-02 19:21:42] [Iter 2031/2250] R4[1810/2400] | LR: 0.008546 | E: -42.295675 | E_var:     0.3299 | E_err:   0.008975
[2025-10-02 19:21:47] [Iter 2032/2250] R4[1812/2400] | LR: 0.008523 | E: -42.325349 | E_var:     0.3085 | E_err:   0.008678
[2025-10-02 19:21:52] [Iter 2033/2250] R4[1814/2400] | LR: 0.008501 | E: -42.313059 | E_var:     0.3110 | E_err:   0.008714
[2025-10-02 19:21:57] [Iter 2034/2250] R4[1816/2400] | LR: 0.008478 | E: -42.336837 | E_var:     0.4483 | E_err:   0.010462
[2025-10-02 19:22:02] [Iter 2035/2250] R4[1818/2400] | LR: 0.008455 | E: -42.319172 | E_var:     0.4339 | E_err:   0.010292
[2025-10-02 19:22:08] [Iter 2036/2250] R4[1820/2400] | LR: 0.008433 | E: -42.314131 | E_var:     0.3868 | E_err:   0.009718
[2025-10-02 19:22:13] [Iter 2037/2250] R4[1822/2400] | LR: 0.008410 | E: -42.309604 | E_var:     0.3103 | E_err:   0.008704
[2025-10-02 19:22:18] [Iter 2038/2250] R4[1824/2400] | LR: 0.008388 | E: -42.314204 | E_var:     0.3017 | E_err:   0.008583
[2025-10-02 19:22:23] [Iter 2039/2250] R4[1826/2400] | LR: 0.008366 | E: -42.309682 | E_var:     0.3588 | E_err:   0.009359
[2025-10-02 19:22:28] [Iter 2040/2250] R4[1828/2400] | LR: 0.008343 | E: -42.328316 | E_var:     0.2804 | E_err:   0.008273
[2025-10-02 19:22:33] [Iter 2041/2250] R4[1830/2400] | LR: 0.008321 | E: -42.301314 | E_var:     0.4674 | E_err:   0.010682
[2025-10-02 19:22:38] [Iter 2042/2250] R4[1832/2400] | LR: 0.008299 | E: -42.289033 | E_var:     0.6342 | E_err:   0.012443
[2025-10-02 19:22:44] [Iter 2043/2250] R4[1834/2400] | LR: 0.008277 | E: -42.307568 | E_var:     0.4027 | E_err:   0.009916
[2025-10-02 19:22:49] [Iter 2044/2250] R4[1836/2400] | LR: 0.008255 | E: -42.302552 | E_var:     0.4143 | E_err:   0.010057
[2025-10-02 19:22:54] [Iter 2045/2250] R4[1838/2400] | LR: 0.008233 | E: -42.308405 | E_var:     0.3291 | E_err:   0.008964
[2025-10-02 19:22:59] [Iter 2046/2250] R4[1840/2400] | LR: 0.008211 | E: -42.323274 | E_var:     0.3431 | E_err:   0.009152
[2025-10-02 19:23:04] [Iter 2047/2250] R4[1842/2400] | LR: 0.008189 | E: -42.315553 | E_var:     0.3519 | E_err:   0.009269
[2025-10-02 19:23:09] [Iter 2048/2250] R4[1844/2400] | LR: 0.008167 | E: -42.309139 | E_var:     0.3429 | E_err:   0.009149
[2025-10-02 19:23:14] [Iter 2049/2250] R4[1846/2400] | LR: 0.008145 | E: -42.317294 | E_var:     0.3389 | E_err:   0.009096
[2025-10-02 19:23:20] [Iter 2050/2250] R4[1848/2400] | LR: 0.008124 | E: -42.301885 | E_var:     0.5067 | E_err:   0.011122
[2025-10-02 19:23:25] [Iter 2051/2250] R4[1850/2400] | LR: 0.008102 | E: -42.321114 | E_var:     0.4186 | E_err:   0.010110
[2025-10-02 19:23:30] [Iter 2052/2250] R4[1852/2400] | LR: 0.008080 | E: -42.309132 | E_var:     0.6252 | E_err:   0.012355
[2025-10-02 19:23:35] [Iter 2053/2250] R4[1854/2400] | LR: 0.008059 | E: -42.309723 | E_var:     0.5833 | E_err:   0.011934
[2025-10-02 19:23:40] [Iter 2054/2250] R4[1856/2400] | LR: 0.008038 | E: -42.311720 | E_var:     0.2782 | E_err:   0.008241
[2025-10-02 19:23:45] [Iter 2055/2250] R4[1858/2400] | LR: 0.008016 | E: -42.317000 | E_var:     0.3105 | E_err:   0.008707
[2025-10-02 19:23:50] [Iter 2056/2250] R4[1860/2400] | LR: 0.007995 | E: -42.307547 | E_var:     0.3349 | E_err:   0.009042
[2025-10-02 19:23:56] [Iter 2057/2250] R4[1862/2400] | LR: 0.007974 | E: -42.312329 | E_var:     0.3022 | E_err:   0.008590
[2025-10-02 19:24:01] [Iter 2058/2250] R4[1864/2400] | LR: 0.007953 | E: -42.294626 | E_var:     0.3577 | E_err:   0.009346
[2025-10-02 19:24:06] [Iter 2059/2250] R4[1866/2400] | LR: 0.007931 | E: -42.314059 | E_var:     0.3487 | E_err:   0.009226
[2025-10-02 19:24:11] [Iter 2060/2250] R4[1868/2400] | LR: 0.007910 | E: -42.309499 | E_var:     0.3519 | E_err:   0.009269
[2025-10-02 19:24:16] [Iter 2061/2250] R4[1870/2400] | LR: 0.007889 | E: -42.303881 | E_var:     0.4327 | E_err:   0.010278
[2025-10-02 19:24:21] [Iter 2062/2250] R4[1872/2400] | LR: 0.007869 | E: -42.315072 | E_var:     0.3544 | E_err:   0.009302
[2025-10-02 19:24:26] [Iter 2063/2250] R4[1874/2400] | LR: 0.007848 | E: -42.311800 | E_var:     0.5208 | E_err:   0.011276
[2025-10-02 19:24:32] [Iter 2064/2250] R4[1876/2400] | LR: 0.007827 | E: -42.310136 | E_var:     0.4999 | E_err:   0.011047
[2025-10-02 19:24:37] [Iter 2065/2250] R4[1878/2400] | LR: 0.007806 | E: -42.302439 | E_var:     0.3702 | E_err:   0.009506
[2025-10-02 19:24:42] [Iter 2066/2250] R4[1880/2400] | LR: 0.007786 | E: -42.303598 | E_var:     0.3763 | E_err:   0.009584
[2025-10-02 19:24:47] [Iter 2067/2250] R4[1882/2400] | LR: 0.007765 | E: -42.329625 | E_var:     0.3463 | E_err:   0.009195
[2025-10-02 19:24:52] [Iter 2068/2250] R4[1884/2400] | LR: 0.007745 | E: -42.320282 | E_var:     0.4108 | E_err:   0.010015
[2025-10-02 19:24:57] [Iter 2069/2250] R4[1886/2400] | LR: 0.007724 | E: -42.311891 | E_var:     0.3568 | E_err:   0.009333
[2025-10-02 19:25:02] [Iter 2070/2250] R4[1888/2400] | LR: 0.007704 | E: -42.302808 | E_var:     0.2948 | E_err:   0.008484
[2025-10-02 19:25:08] [Iter 2071/2250] R4[1890/2400] | LR: 0.007684 | E: -42.308482 | E_var:     0.3559 | E_err:   0.009321
[2025-10-02 19:25:13] [Iter 2072/2250] R4[1892/2400] | LR: 0.007663 | E: -42.319964 | E_var:     0.3873 | E_err:   0.009724
[2025-10-02 19:25:18] [Iter 2073/2250] R4[1894/2400] | LR: 0.007643 | E: -42.319147 | E_var:     0.3745 | E_err:   0.009562
[2025-10-02 19:25:23] [Iter 2074/2250] R4[1896/2400] | LR: 0.007623 | E: -42.307185 | E_var:     0.4629 | E_err:   0.010631
[2025-10-02 19:25:28] [Iter 2075/2250] R4[1898/2400] | LR: 0.007603 | E: -42.293364 | E_var:     0.4028 | E_err:   0.009916
[2025-10-02 19:25:33] [Iter 2076/2250] R4[1900/2400] | LR: 0.007583 | E: -42.299049 | E_var:     0.5785 | E_err:   0.011884
[2025-10-02 19:25:38] [Iter 2077/2250] R4[1902/2400] | LR: 0.007563 | E: -42.314431 | E_var:     0.3985 | E_err:   0.009863
[2025-10-02 19:25:44] [Iter 2078/2250] R4[1904/2400] | LR: 0.007543 | E: -42.316483 | E_var:     0.3870 | E_err:   0.009720
[2025-10-02 19:25:49] [Iter 2079/2250] R4[1906/2400] | LR: 0.007524 | E: -42.303182 | E_var:     0.3377 | E_err:   0.009080
[2025-10-02 19:25:54] [Iter 2080/2250] R4[1908/2400] | LR: 0.007504 | E: -42.302258 | E_var:     0.3712 | E_err:   0.009519
[2025-10-02 19:25:59] [Iter 2081/2250] R4[1910/2400] | LR: 0.007484 | E: -42.293829 | E_var:     0.3234 | E_err:   0.008885
[2025-10-02 19:26:04] [Iter 2082/2250] R4[1912/2400] | LR: 0.007465 | E: -42.284066 | E_var:     0.4204 | E_err:   0.010131
[2025-10-02 19:26:09] [Iter 2083/2250] R4[1914/2400] | LR: 0.007445 | E: -42.325621 | E_var:     0.3192 | E_err:   0.008828
[2025-10-02 19:26:15] [Iter 2084/2250] R4[1916/2400] | LR: 0.007426 | E: -42.312956 | E_var:     0.3657 | E_err:   0.009449
[2025-10-02 19:26:20] [Iter 2085/2250] R4[1918/2400] | LR: 0.007407 | E: -42.290737 | E_var:     0.3755 | E_err:   0.009575
[2025-10-02 19:26:25] [Iter 2086/2250] R4[1920/2400] | LR: 0.007387 | E: -42.287890 | E_var:     0.4588 | E_err:   0.010583
[2025-10-02 19:26:30] [Iter 2087/2250] R4[1922/2400] | LR: 0.007368 | E: -42.312478 | E_var:     0.3816 | E_err:   0.009653
[2025-10-02 19:26:35] [Iter 2088/2250] R4[1924/2400] | LR: 0.007349 | E: -42.338272 | E_var:     0.5439 | E_err:   0.011523
[2025-10-02 19:26:40] [Iter 2089/2250] R4[1926/2400] | LR: 0.007330 | E: -42.318013 | E_var:     0.6317 | E_err:   0.012419
[2025-10-02 19:26:45] [Iter 2090/2250] R4[1928/2400] | LR: 0.007311 | E: -42.309757 | E_var:     0.3215 | E_err:   0.008860
[2025-10-02 19:26:51] [Iter 2091/2250] R4[1930/2400] | LR: 0.007292 | E: -42.312432 | E_var:     0.3668 | E_err:   0.009463
[2025-10-02 19:26:56] [Iter 2092/2250] R4[1932/2400] | LR: 0.007273 | E: -42.315304 | E_var:     0.3276 | E_err:   0.008944
[2025-10-02 19:27:01] [Iter 2093/2250] R4[1934/2400] | LR: 0.007254 | E: -42.304818 | E_var:     0.4255 | E_err:   0.010192
[2025-10-02 19:27:06] [Iter 2094/2250] R4[1936/2400] | LR: 0.007236 | E: -42.302280 | E_var:     0.4246 | E_err:   0.010182
[2025-10-02 19:27:11] [Iter 2095/2250] R4[1938/2400] | LR: 0.007217 | E: -42.317838 | E_var:     0.3605 | E_err:   0.009381
[2025-10-02 19:27:16] [Iter 2096/2250] R4[1940/2400] | LR: 0.007198 | E: -42.313442 | E_var:     0.4649 | E_err:   0.010654
[2025-10-02 19:27:21] [Iter 2097/2250] R4[1942/2400] | LR: 0.007180 | E: -42.321802 | E_var:     0.3395 | E_err:   0.009104
[2025-10-02 19:27:27] [Iter 2098/2250] R4[1944/2400] | LR: 0.007161 | E: -42.310614 | E_var:     0.3568 | E_err:   0.009333
[2025-10-02 19:27:32] [Iter 2099/2250] R4[1946/2400] | LR: 0.007143 | E: -42.309934 | E_var:     0.4015 | E_err:   0.009901
[2025-10-02 19:27:37] [Iter 2100/2250] R4[1948/2400] | LR: 0.007125 | E: -42.313918 | E_var:     0.3076 | E_err:   0.008665
[2025-10-02 19:27:42] [Iter 2101/2250] R4[1950/2400] | LR: 0.007107 | E: -42.311434 | E_var:     0.7203 | E_err:   0.013261
[2025-10-02 19:27:47] [Iter 2102/2250] R4[1952/2400] | LR: 0.007088 | E: -42.303302 | E_var:     0.3775 | E_err:   0.009600
[2025-10-02 19:27:52] [Iter 2103/2250] R4[1954/2400] | LR: 0.007070 | E: -42.313544 | E_var:     0.3902 | E_err:   0.009760
[2025-10-02 19:27:57] [Iter 2104/2250] R4[1956/2400] | LR: 0.007052 | E: -42.326208 | E_var:     0.4424 | E_err:   0.010392
[2025-10-02 19:28:03] [Iter 2105/2250] R4[1958/2400] | LR: 0.007034 | E: -42.316316 | E_var:     0.2732 | E_err:   0.008166
[2025-10-02 19:28:08] [Iter 2106/2250] R4[1960/2400] | LR: 0.007017 | E: -42.335023 | E_var:     0.5138 | E_err:   0.011200
[2025-10-02 19:28:13] [Iter 2107/2250] R4[1962/2400] | LR: 0.006999 | E: -42.297405 | E_var:     0.4982 | E_err:   0.011028
[2025-10-02 19:28:18] [Iter 2108/2250] R4[1964/2400] | LR: 0.006981 | E: -42.298189 | E_var:     0.3279 | E_err:   0.008947
[2025-10-02 19:28:23] [Iter 2109/2250] R4[1966/2400] | LR: 0.006963 | E: -42.313448 | E_var:     0.3847 | E_err:   0.009692
[2025-10-02 19:28:28] [Iter 2110/2250] R4[1968/2400] | LR: 0.006946 | E: -42.308809 | E_var:     0.3413 | E_err:   0.009128
[2025-10-02 19:28:33] [Iter 2111/2250] R4[1970/2400] | LR: 0.006928 | E: -42.315372 | E_var:     0.8181 | E_err:   0.014133
[2025-10-02 19:28:39] [Iter 2112/2250] R4[1972/2400] | LR: 0.006911 | E: -42.318882 | E_var:     0.3489 | E_err:   0.009229
[2025-10-02 19:28:44] [Iter 2113/2250] R4[1974/2400] | LR: 0.006894 | E: -42.312255 | E_var:     0.3028 | E_err:   0.008598
[2025-10-02 19:28:49] [Iter 2114/2250] R4[1976/2400] | LR: 0.006876 | E: -42.302984 | E_var:     0.3851 | E_err:   0.009697
[2025-10-02 19:28:54] [Iter 2115/2250] R4[1978/2400] | LR: 0.006859 | E: -42.304912 | E_var:     0.3992 | E_err:   0.009872
[2025-10-02 19:28:59] [Iter 2116/2250] R4[1980/2400] | LR: 0.006842 | E: -42.310997 | E_var:     0.3209 | E_err:   0.008852
[2025-10-02 19:29:04] [Iter 2117/2250] R4[1982/2400] | LR: 0.006825 | E: -42.299661 | E_var:     0.3492 | E_err:   0.009233
[2025-10-02 19:29:09] [Iter 2118/2250] R4[1984/2400] | LR: 0.006808 | E: -42.309457 | E_var:     0.2897 | E_err:   0.008410
[2025-10-02 19:29:15] [Iter 2119/2250] R4[1986/2400] | LR: 0.006791 | E: -42.308469 | E_var:     0.3764 | E_err:   0.009587
[2025-10-02 19:29:20] [Iter 2120/2250] R4[1988/2400] | LR: 0.006774 | E: -42.321992 | E_var:     0.4033 | E_err:   0.009923
[2025-10-02 19:29:25] [Iter 2121/2250] R4[1990/2400] | LR: 0.006757 | E: -42.307056 | E_var:     0.3130 | E_err:   0.008742
[2025-10-02 19:29:30] [Iter 2122/2250] R4[1992/2400] | LR: 0.006741 | E: -42.315370 | E_var:     0.4368 | E_err:   0.010327
[2025-10-02 19:29:35] [Iter 2123/2250] R4[1994/2400] | LR: 0.006724 | E: -42.307629 | E_var:     0.4314 | E_err:   0.010263
[2025-10-02 19:29:40] [Iter 2124/2250] R4[1996/2400] | LR: 0.006708 | E: -42.315251 | E_var:     0.4413 | E_err:   0.010380
[2025-10-02 19:29:46] [Iter 2125/2250] R4[1998/2400] | LR: 0.006691 | E: -42.312238 | E_var:     0.4616 | E_err:   0.010616
[2025-10-02 19:29:51] [Iter 2126/2250] R4[2000/2400] | LR: 0.006675 | E: -42.316072 | E_var:     0.6142 | E_err:   0.012246
[2025-10-02 19:29:56] [Iter 2127/2250] R4[2002/2400] | LR: 0.006658 | E: -42.303951 | E_var:     0.5979 | E_err:   0.012082
[2025-10-02 19:30:01] [Iter 2128/2250] R4[2004/2400] | LR: 0.006642 | E: -42.311442 | E_var:     0.3324 | E_err:   0.009009
[2025-10-02 19:30:06] [Iter 2129/2250] R4[2006/2400] | LR: 0.006626 | E: -42.310414 | E_var:     0.3504 | E_err:   0.009249
[2025-10-02 19:30:11] [Iter 2130/2250] R4[2008/2400] | LR: 0.006610 | E: -42.296973 | E_var:     0.4116 | E_err:   0.010024
[2025-10-02 19:30:16] [Iter 2131/2250] R4[2010/2400] | LR: 0.006594 | E: -42.303602 | E_var:     0.3334 | E_err:   0.009022
[2025-10-02 19:30:22] [Iter 2132/2250] R4[2012/2400] | LR: 0.006578 | E: -42.309777 | E_var:     0.4128 | E_err:   0.010039
[2025-10-02 19:30:27] [Iter 2133/2250] R4[2014/2400] | LR: 0.006562 | E: -42.307171 | E_var:     0.3356 | E_err:   0.009052
[2025-10-02 19:30:32] [Iter 2134/2250] R4[2016/2400] | LR: 0.006546 | E: -42.308393 | E_var:     0.3481 | E_err:   0.009218
[2025-10-02 19:30:37] [Iter 2135/2250] R4[2018/2400] | LR: 0.006530 | E: -42.302956 | E_var:     0.4039 | E_err:   0.009930
[2025-10-02 19:30:42] [Iter 2136/2250] R4[2020/2400] | LR: 0.006515 | E: -42.314433 | E_var:     0.3282 | E_err:   0.008952
[2025-10-02 19:30:47] [Iter 2137/2250] R4[2022/2400] | LR: 0.006499 | E: -42.307190 | E_var:     0.4181 | E_err:   0.010103
[2025-10-02 19:30:52] [Iter 2138/2250] R4[2024/2400] | LR: 0.006484 | E: -42.323708 | E_var:     0.4416 | E_err:   0.010384
[2025-10-02 19:30:58] [Iter 2139/2250] R4[2026/2400] | LR: 0.006468 | E: -42.309412 | E_var:     0.3488 | E_err:   0.009228
[2025-10-02 19:31:03] [Iter 2140/2250] R4[2028/2400] | LR: 0.006453 | E: -42.305810 | E_var:     0.3999 | E_err:   0.009881
[2025-10-02 19:31:08] [Iter 2141/2250] R4[2030/2400] | LR: 0.006438 | E: -42.299864 | E_var:     0.5325 | E_err:   0.011402
[2025-10-02 19:31:13] [Iter 2142/2250] R4[2032/2400] | LR: 0.006422 | E: -42.316885 | E_var:     0.3112 | E_err:   0.008716
[2025-10-02 19:31:18] [Iter 2143/2250] R4[2034/2400] | LR: 0.006407 | E: -42.308093 | E_var:     0.3401 | E_err:   0.009112
[2025-10-02 19:31:23] [Iter 2144/2250] R4[2036/2400] | LR: 0.006392 | E: -42.317953 | E_var:     0.3990 | E_err:   0.009869
[2025-10-02 19:31:28] [Iter 2145/2250] R4[2038/2400] | LR: 0.006377 | E: -42.324328 | E_var:     0.5626 | E_err:   0.011720
[2025-10-02 19:31:34] [Iter 2146/2250] R4[2040/2400] | LR: 0.006362 | E: -42.315440 | E_var:     0.3526 | E_err:   0.009278
[2025-10-02 19:31:39] [Iter 2147/2250] R4[2042/2400] | LR: 0.006348 | E: -42.298744 | E_var:     0.4219 | E_err:   0.010148
[2025-10-02 19:31:44] [Iter 2148/2250] R4[2044/2400] | LR: 0.006333 | E: -42.309894 | E_var:     0.3784 | E_err:   0.009612
[2025-10-02 19:31:49] [Iter 2149/2250] R4[2046/2400] | LR: 0.006318 | E: -42.307772 | E_var:     0.2967 | E_err:   0.008511
[2025-10-02 19:31:54] [Iter 2150/2250] R4[2048/2400] | LR: 0.006304 | E: -42.309850 | E_var:     1.0817 | E_err:   0.016251
[2025-10-02 19:31:59] [Iter 2151/2250] R4[2050/2400] | LR: 0.006289 | E: -42.311259 | E_var:     0.3180 | E_err:   0.008811
[2025-10-02 19:32:04] [Iter 2152/2250] R4[2052/2400] | LR: 0.006275 | E: -42.325678 | E_var:     0.3487 | E_err:   0.009227
[2025-10-02 19:32:10] [Iter 2153/2250] R4[2054/2400] | LR: 0.006260 | E: -42.315371 | E_var:     0.3365 | E_err:   0.009064
[2025-10-02 19:32:15] [Iter 2154/2250] R4[2056/2400] | LR: 0.006246 | E: -42.310381 | E_var:     0.3937 | E_err:   0.009804
[2025-10-02 19:32:20] [Iter 2155/2250] R4[2058/2400] | LR: 0.006232 | E: -42.298044 | E_var:     0.5250 | E_err:   0.011321
[2025-10-02 19:32:25] [Iter 2156/2250] R4[2060/2400] | LR: 0.006218 | E: -42.325603 | E_var:     0.3579 | E_err:   0.009348
[2025-10-02 19:32:30] [Iter 2157/2250] R4[2062/2400] | LR: 0.006204 | E: -42.307254 | E_var:     0.3252 | E_err:   0.008911
[2025-10-02 19:32:35] [Iter 2158/2250] R4[2064/2400] | LR: 0.006190 | E: -42.310650 | E_var:     0.3611 | E_err:   0.009389
[2025-10-02 19:32:40] [Iter 2159/2250] R4[2066/2400] | LR: 0.006176 | E: -42.301623 | E_var:     0.5616 | E_err:   0.011709
[2025-10-02 19:32:46] [Iter 2160/2250] R4[2068/2400] | LR: 0.006162 | E: -42.314950 | E_var:     0.3952 | E_err:   0.009823
[2025-10-02 19:32:51] [Iter 2161/2250] R4[2070/2400] | LR: 0.006148 | E: -42.313857 | E_var:     0.4699 | E_err:   0.010711
[2025-10-02 19:32:56] [Iter 2162/2250] R4[2072/2400] | LR: 0.006135 | E: -42.312293 | E_var:     0.4009 | E_err:   0.009893
[2025-10-02 19:33:01] [Iter 2163/2250] R4[2074/2400] | LR: 0.006121 | E: -42.309571 | E_var:     0.3009 | E_err:   0.008572
[2025-10-02 19:33:06] [Iter 2164/2250] R4[2076/2400] | LR: 0.006107 | E: -42.308816 | E_var:     0.2905 | E_err:   0.008421
[2025-10-02 19:33:11] [Iter 2165/2250] R4[2078/2400] | LR: 0.006094 | E: -42.317903 | E_var:     0.4219 | E_err:   0.010149
[2025-10-02 19:33:17] [Iter 2166/2250] R4[2080/2400] | LR: 0.006081 | E: -42.316778 | E_var:     0.3860 | E_err:   0.009708
[2025-10-02 19:33:22] [Iter 2167/2250] R4[2082/2400] | LR: 0.006067 | E: -42.305495 | E_var:     0.3188 | E_err:   0.008822
[2025-10-02 19:33:27] [Iter 2168/2250] R4[2084/2400] | LR: 0.006054 | E: -42.296672 | E_var:     0.3168 | E_err:   0.008795
[2025-10-02 19:33:32] [Iter 2169/2250] R4[2086/2400] | LR: 0.006041 | E: -42.315187 | E_var:     0.5099 | E_err:   0.011157
[2025-10-02 19:33:37] [Iter 2170/2250] R4[2088/2400] | LR: 0.006028 | E: -42.301442 | E_var:     0.3206 | E_err:   0.008847
[2025-10-02 19:33:42] [Iter 2171/2250] R4[2090/2400] | LR: 0.006015 | E: -42.311072 | E_var:     0.3519 | E_err:   0.009270
[2025-10-02 19:33:47] [Iter 2172/2250] R4[2092/2400] | LR: 0.006002 | E: -42.324571 | E_var:     0.3595 | E_err:   0.009369
[2025-10-02 19:33:53] [Iter 2173/2250] R4[2094/2400] | LR: 0.005989 | E: -42.324259 | E_var:     0.3270 | E_err:   0.008935
[2025-10-02 19:33:58] [Iter 2174/2250] R4[2096/2400] | LR: 0.005977 | E: -42.325154 | E_var:     0.3272 | E_err:   0.008938
[2025-10-02 19:34:03] [Iter 2175/2250] R4[2098/2400] | LR: 0.005964 | E: -42.316363 | E_var:     0.3522 | E_err:   0.009273
[2025-10-02 19:34:08] [Iter 2176/2250] R4[2100/2400] | LR: 0.005952 | E: -42.314563 | E_var:     0.3451 | E_err:   0.009179
[2025-10-02 19:34:13] [Iter 2177/2250] R4[2102/2400] | LR: 0.005939 | E: -42.314678 | E_var:     0.3769 | E_err:   0.009592
[2025-10-02 19:34:18] [Iter 2178/2250] R4[2104/2400] | LR: 0.005927 | E: -42.324006 | E_var:     0.3837 | E_err:   0.009679
[2025-10-02 19:34:23] [Iter 2179/2250] R4[2106/2400] | LR: 0.005914 | E: -42.305761 | E_var:     0.3484 | E_err:   0.009222
[2025-10-02 19:34:29] [Iter 2180/2250] R4[2108/2400] | LR: 0.005902 | E: -42.321889 | E_var:     0.3526 | E_err:   0.009278
[2025-10-02 19:34:34] [Iter 2181/2250] R4[2110/2400] | LR: 0.005890 | E: -42.305802 | E_var:     0.3973 | E_err:   0.009848
[2025-10-02 19:34:39] [Iter 2182/2250] R4[2112/2400] | LR: 0.005878 | E: -42.313915 | E_var:     0.3862 | E_err:   0.009711
[2025-10-02 19:34:44] [Iter 2183/2250] R4[2114/2400] | LR: 0.005866 | E: -42.315405 | E_var:     0.3650 | E_err:   0.009440
[2025-10-02 19:34:49] [Iter 2184/2250] R4[2116/2400] | LR: 0.005854 | E: -42.316708 | E_var:     0.4759 | E_err:   0.010779
[2025-10-02 19:34:54] [Iter 2185/2250] R4[2118/2400] | LR: 0.005842 | E: -42.298626 | E_var:     0.3977 | E_err:   0.009854
[2025-10-02 19:34:59] [Iter 2186/2250] R4[2120/2400] | LR: 0.005830 | E: -42.315159 | E_var:     0.4020 | E_err:   0.009907
[2025-10-02 19:35:05] [Iter 2187/2250] R4[2122/2400] | LR: 0.005819 | E: -42.315325 | E_var:     0.3498 | E_err:   0.009242
[2025-10-02 19:35:10] [Iter 2188/2250] R4[2124/2400] | LR: 0.005807 | E: -42.325302 | E_var:     0.4459 | E_err:   0.010434
[2025-10-02 19:35:15] [Iter 2189/2250] R4[2126/2400] | LR: 0.005795 | E: -42.318106 | E_var:     0.4243 | E_err:   0.010178
[2025-10-02 19:35:20] [Iter 2190/2250] R4[2128/2400] | LR: 0.005784 | E: -42.313259 | E_var:     0.3356 | E_err:   0.009052
[2025-10-02 19:35:25] [Iter 2191/2250] R4[2130/2400] | LR: 0.005773 | E: -42.303677 | E_var:     0.3841 | E_err:   0.009684
[2025-10-02 19:35:30] [Iter 2192/2250] R4[2132/2400] | LR: 0.005761 | E: -42.305017 | E_var:     0.3550 | E_err:   0.009310
[2025-10-02 19:35:35] [Iter 2193/2250] R4[2134/2400] | LR: 0.005750 | E: -42.303283 | E_var:     0.5006 | E_err:   0.011055
[2025-10-02 19:35:41] [Iter 2194/2250] R4[2136/2400] | LR: 0.005739 | E: -42.317864 | E_var:     0.4469 | E_err:   0.010446
[2025-10-02 19:35:46] [Iter 2195/2250] R4[2138/2400] | LR: 0.005728 | E: -42.305458 | E_var:     0.3091 | E_err:   0.008687
[2025-10-02 19:35:51] [Iter 2196/2250] R4[2140/2400] | LR: 0.005717 | E: -42.314059 | E_var:     0.4518 | E_err:   0.010502
[2025-10-02 19:35:56] [Iter 2197/2250] R4[2142/2400] | LR: 0.005706 | E: -42.318146 | E_var:     0.3578 | E_err:   0.009347
[2025-10-02 19:36:01] [Iter 2198/2250] R4[2144/2400] | LR: 0.005695 | E: -42.328262 | E_var:     0.3907 | E_err:   0.009766
[2025-10-02 19:36:06] [Iter 2199/2250] R4[2146/2400] | LR: 0.005685 | E: -42.314339 | E_var:     0.4056 | E_err:   0.009951
[2025-10-02 19:36:11] [Iter 2200/2250] R4[2148/2400] | LR: 0.005674 | E: -42.294199 | E_var:     0.3225 | E_err:   0.008874
[2025-10-02 19:36:12] ✓ Checkpoint saved: checkpoint_iter_002200.pkl
[2025-10-02 19:36:17] [Iter 2201/2250] R4[2150/2400] | LR: 0.005663 | E: -42.318694 | E_var:     0.3581 | E_err:   0.009350
[2025-10-02 19:36:22] [Iter 2202/2250] R4[2152/2400] | LR: 0.005653 | E: -42.314204 | E_var:     0.4359 | E_err:   0.010316
[2025-10-02 19:36:27] [Iter 2203/2250] R4[2154/2400] | LR: 0.005642 | E: -42.328322 | E_var:     0.3570 | E_err:   0.009336
[2025-10-02 19:36:32] [Iter 2204/2250] R4[2156/2400] | LR: 0.005632 | E: -42.303293 | E_var:     0.3612 | E_err:   0.009391
[2025-10-02 19:36:37] [Iter 2205/2250] R4[2158/2400] | LR: 0.005622 | E: -42.309722 | E_var:     0.4355 | E_err:   0.010312
[2025-10-02 19:36:42] [Iter 2206/2250] R4[2160/2400] | LR: 0.005612 | E: -42.313616 | E_var:     0.3525 | E_err:   0.009276
[2025-10-02 19:36:48] [Iter 2207/2250] R4[2162/2400] | LR: 0.005602 | E: -42.314261 | E_var:     0.5828 | E_err:   0.011928
[2025-10-02 19:36:53] [Iter 2208/2250] R4[2164/2400] | LR: 0.005592 | E: -42.307190 | E_var:     0.3597 | E_err:   0.009371
[2025-10-02 19:36:58] [Iter 2209/2250] R4[2166/2400] | LR: 0.005582 | E: -42.313714 | E_var:     0.2942 | E_err:   0.008475
[2025-10-02 19:37:03] [Iter 2210/2250] R4[2168/2400] | LR: 0.005572 | E: -42.314961 | E_var:     0.4445 | E_err:   0.010417
[2025-10-02 19:37:08] [Iter 2211/2250] R4[2170/2400] | LR: 0.005562 | E: -42.312267 | E_var:     0.4232 | E_err:   0.010165
[2025-10-02 19:37:13] [Iter 2212/2250] R4[2172/2400] | LR: 0.005553 | E: -42.314470 | E_var:     0.3667 | E_err:   0.009462
[2025-10-02 19:37:18] [Iter 2213/2250] R4[2174/2400] | LR: 0.005543 | E: -42.311821 | E_var:     0.4010 | E_err:   0.009894
[2025-10-02 19:37:24] [Iter 2214/2250] R4[2176/2400] | LR: 0.005534 | E: -42.325071 | E_var:     0.3575 | E_err:   0.009342
[2025-10-02 19:37:29] [Iter 2215/2250] R4[2178/2400] | LR: 0.005524 | E: -42.309616 | E_var:     0.3335 | E_err:   0.009024
[2025-10-02 19:37:34] [Iter 2216/2250] R4[2180/2400] | LR: 0.005515 | E: -42.318454 | E_var:     0.4406 | E_err:   0.010372
[2025-10-02 19:37:39] [Iter 2217/2250] R4[2182/2400] | LR: 0.005506 | E: -42.286675 | E_var:     0.3450 | E_err:   0.009178
[2025-10-02 19:37:44] [Iter 2218/2250] R4[2184/2400] | LR: 0.005496 | E: -42.301145 | E_var:     0.3814 | E_err:   0.009650
[2025-10-02 19:37:49] [Iter 2219/2250] R4[2186/2400] | LR: 0.005487 | E: -42.315896 | E_var:     0.3192 | E_err:   0.008827
[2025-10-02 19:37:54] [Iter 2220/2250] R4[2188/2400] | LR: 0.005478 | E: -42.311153 | E_var:     0.4716 | E_err:   0.010730
[2025-10-02 19:38:00] [Iter 2221/2250] R4[2190/2400] | LR: 0.005469 | E: -42.318439 | E_var:     0.4096 | E_err:   0.009999
[2025-10-02 19:38:05] [Iter 2222/2250] R4[2192/2400] | LR: 0.005460 | E: -42.316790 | E_var:     0.5631 | E_err:   0.011725
[2025-10-02 19:38:10] [Iter 2223/2250] R4[2194/2400] | LR: 0.005452 | E: -42.306186 | E_var:     0.3820 | E_err:   0.009657
[2025-10-02 19:38:15] [Iter 2224/2250] R4[2196/2400] | LR: 0.005443 | E: -42.312198 | E_var:     0.3428 | E_err:   0.009148
[2025-10-02 19:38:20] [Iter 2225/2250] R4[2198/2400] | LR: 0.005434 | E: -42.308798 | E_var:     0.4688 | E_err:   0.010699
[2025-10-02 19:38:25] [Iter 2226/2250] R4[2200/2400] | LR: 0.005426 | E: -42.324679 | E_var:     0.3709 | E_err:   0.009516
[2025-10-02 19:38:31] [Iter 2227/2250] R4[2202/2400] | LR: 0.005417 | E: -42.316127 | E_var:     0.3569 | E_err:   0.009334
[2025-10-02 19:38:36] [Iter 2228/2250] R4[2204/2400] | LR: 0.005409 | E: -42.307900 | E_var:     0.2811 | E_err:   0.008285
[2025-10-02 19:38:41] [Iter 2229/2250] R4[2206/2400] | LR: 0.005401 | E: -42.315701 | E_var:     0.3372 | E_err:   0.009074
[2025-10-02 19:38:46] [Iter 2230/2250] R4[2208/2400] | LR: 0.005393 | E: -42.318743 | E_var:     0.3393 | E_err:   0.009101
[2025-10-02 19:38:51] [Iter 2231/2250] R4[2210/2400] | LR: 0.005385 | E: -42.315920 | E_var:     0.4050 | E_err:   0.009944
[2025-10-02 19:38:56] [Iter 2232/2250] R4[2212/2400] | LR: 0.005377 | E: -42.310143 | E_var:     0.3325 | E_err:   0.009010
[2025-10-02 19:39:01] [Iter 2233/2250] R4[2214/2400] | LR: 0.005369 | E: -42.310869 | E_var:     0.3273 | E_err:   0.008940
[2025-10-02 19:39:07] [Iter 2234/2250] R4[2216/2400] | LR: 0.005361 | E: -42.308226 | E_var:     0.3792 | E_err:   0.009621
[2025-10-02 19:39:12] [Iter 2235/2250] R4[2218/2400] | LR: 0.005353 | E: -42.298911 | E_var:     1.5536 | E_err:   0.019475
[2025-10-02 19:39:17] [Iter 2236/2250] R4[2220/2400] | LR: 0.005345 | E: -42.293080 | E_var:     1.7424 | E_err:   0.020625
[2025-10-02 19:39:22] [Iter 2237/2250] R4[2222/2400] | LR: 0.005338 | E: -42.334057 | E_var:     0.3667 | E_err:   0.009462
[2025-10-02 19:39:27] [Iter 2238/2250] R4[2224/2400] | LR: 0.005330 | E: -42.315640 | E_var:     0.3413 | E_err:   0.009128
[2025-10-02 19:39:32] [Iter 2239/2250] R4[2226/2400] | LR: 0.005323 | E: -42.314725 | E_var:     0.3796 | E_err:   0.009627
[2025-10-02 19:39:37] [Iter 2240/2250] R4[2228/2400] | LR: 0.005315 | E: -42.319925 | E_var:     0.3160 | E_err:   0.008784
[2025-10-02 19:39:43] [Iter 2241/2250] R4[2230/2400] | LR: 0.005308 | E: -42.316999 | E_var:     0.3746 | E_err:   0.009563
[2025-10-02 19:39:48] [Iter 2242/2250] R4[2232/2400] | LR: 0.005301 | E: -42.298408 | E_var:     0.3353 | E_err:   0.009048
[2025-10-02 19:39:53] [Iter 2243/2250] R4[2234/2400] | LR: 0.005294 | E: -42.314302 | E_var:     0.3989 | E_err:   0.009869
[2025-10-02 19:39:58] [Iter 2244/2250] R4[2236/2400] | LR: 0.005287 | E: -42.328901 | E_var:     0.2939 | E_err:   0.008471
[2025-10-02 19:40:03] [Iter 2245/2250] R4[2238/2400] | LR: 0.005280 | E: -42.310625 | E_var:     0.2867 | E_err:   0.008367
[2025-10-02 19:40:08] [Iter 2246/2250] R4[2240/2400] | LR: 0.005273 | E: -42.314170 | E_var:     0.3685 | E_err:   0.009485
[2025-10-02 19:40:13] [Iter 2247/2250] R4[2242/2400] | LR: 0.005266 | E: -42.311902 | E_var:     0.3881 | E_err:   0.009734
[2025-10-02 19:40:19] [Iter 2248/2250] R4[2244/2400] | LR: 0.005260 | E: -42.315327 | E_var:     0.3835 | E_err:   0.009676
[2025-10-02 19:40:24] [Iter 2249/2250] R4[2246/2400] | LR: 0.005253 | E: -42.320931 | E_var:     0.2982 | E_err:   0.008533
[2025-10-02 19:40:29] [Iter 2250/2250] R4[2248/2400] | LR: 0.005247 | E: -42.320216 | E_var:     0.3614 | E_err:   0.009394
[2025-10-02 19:40:29] ================================================================================
[2025-10-02 19:40:29] ✅ Training completed successfully
[2025-10-02 19:40:29] Total restarts: 4
[2025-10-02 19:40:31] Final Energy: -42.32021632 ± 0.00939361
[2025-10-02 19:40:31] Final Variance: 0.361431
[2025-10-02 19:40:31] ================================================================================
[2025-10-02 19:40:31] ============================================================
[2025-10-02 19:40:31] Training completed | Runtime: 11620.1s
[2025-10-02 19:40:32] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-02 19:40:32] ============================================================
