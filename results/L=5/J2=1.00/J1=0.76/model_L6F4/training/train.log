[2025-10-02 21:59:32] ==================================================
[2025-10-02 21:59:32] GCNN for Shastry-Sutherland Model
[2025-10-02 21:59:32] ==================================================
[2025-10-02 21:59:32] System parameters:
[2025-10-02 21:59:32]   - System size: L=5, N=100
[2025-10-02 21:59:32]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-02 21:59:32] --------------------------------------------------
[2025-10-02 21:59:32] Model parameters:
[2025-10-02 21:59:32]   - Number of layers = 6
[2025-10-02 21:59:32]   - Number of features = 4
[2025-10-02 21:59:32]   - Total parameters = 32444
[2025-10-02 21:59:32] --------------------------------------------------
[2025-10-02 21:59:32] Training parameters:
[2025-10-02 21:59:32]   - Total iterations: 2250
[2025-10-02 21:59:32]   - Annealing cycles: 4
[2025-10-02 21:59:32]   - Initial period: 150
[2025-10-02 21:59:32]   - Period multiplier: 2.0
[2025-10-02 21:59:32]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-02 21:59:32]   - Samples: 4096
[2025-10-02 21:59:32]   - Discarded samples: 0
[2025-10-02 21:59:32]   - Chunk size: 4096
[2025-10-02 21:59:32]   - Diagonal shift: 0.15
[2025-10-02 21:59:32]   - Gradient clipping: 1.0
[2025-10-02 21:59:32]   - Checkpoint enabled: interval=200
[2025-10-02 21:59:32]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.76/model_L6F4/training/checkpoints
[2025-10-02 21:59:32] --------------------------------------------------
[2025-10-02 21:59:32] Device status:
[2025-10-02 21:59:32]   - Devices model: NVIDIA H200 NVL
[2025-10-02 21:59:32]   - Number of devices: 1
[2025-10-02 21:59:32]   - Sharding: True
[2025-10-02 22:00:10] [Iter    1/2250] R0[0/150]    | LR: 0.030000 | E:  50.490487 | E_var:     0.0865 | E_err:   0.004595
[2025-10-02 22:00:18] [Iter    2/2250] R0[2/150]    | LR: 0.029989 | E:  50.480304 | E_var:     0.1209 | E_err:   0.005434
[2025-10-02 22:00:26] [Iter    3/2250] R0[4/150]    | LR: 0.029956 | E:  50.478004 | E_var:     0.1790 | E_err:   0.006611
[2025-10-02 22:00:34] [Iter    4/2250] R0[6/150]    | LR: 0.029901 | E:  50.456188 | E_var:     0.2882 | E_err:   0.008387
[2025-10-02 22:00:42] [Iter    5/2250] R0[8/150]    | LR: 0.029825 | E:  50.430122 | E_var:     0.5032 | E_err:   0.011084
[2025-10-02 22:00:50] [Iter    6/2250] R0[10/150]   | LR: 0.029727 | E:  50.370080 | E_var:     1.0058 | E_err:   0.015670
[2025-10-02 22:00:57] [Iter    7/2250] R0[12/150]   | LR: 0.029607 | E:  50.211761 | E_var:     2.5296 | E_err:   0.024851
[2025-10-02 22:01:05] [Iter    8/2250] R0[14/150]   | LR: 0.029466 | E:  49.742494 | E_var:     7.9127 | E_err:   0.043952
[2025-10-02 22:01:13] [Iter    9/2250] R0[16/150]   | LR: 0.029305 | E:  47.706022 | E_var:    31.8473 | E_err:   0.088177
[2025-10-02 22:01:21] [Iter   10/2250] R0[18/150]   | LR: 0.029122 | E:  40.704981 | E_var:   109.3767 | E_err:   0.163411
[2025-10-02 22:01:29] [Iter   11/2250] R0[20/150]   | LR: 0.028919 | E:  30.485763 | E_var:    84.8847 | E_err:   0.143958
[2025-10-02 22:01:37] [Iter   12/2250] R0[22/150]   | LR: 0.028696 | E:  23.680368 | E_var:    67.3779 | E_err:   0.128256
[2025-10-02 22:01:44] [Iter   13/2250] R0[24/150]   | LR: 0.028454 | E:  18.060317 | E_var:    64.3631 | E_err:   0.125354
[2025-10-02 22:01:52] [Iter   14/2250] R0[26/150]   | LR: 0.028192 | E:  13.242320 | E_var:    65.8832 | E_err:   0.126826
[2025-10-02 22:02:00] [Iter   15/2250] R0[28/150]   | LR: 0.027912 | E:   8.283273 | E_var:    65.0426 | E_err:   0.126014
[2025-10-02 22:02:08] [Iter   16/2250] R0[30/150]   | LR: 0.027613 | E:   3.903914 | E_var:    61.1860 | E_err:   0.122221
[2025-10-02 22:02:16] [Iter   17/2250] R0[32/150]   | LR: 0.027296 | E:   0.427100 | E_var:    54.8801 | E_err:   0.115752
[2025-10-02 22:02:24] [Iter   18/2250] R0[34/150]   | LR: 0.026962 | E:  -2.330646 | E_var:    46.5230 | E_err:   0.106575
[2025-10-02 22:02:31] [Iter   19/2250] R0[36/150]   | LR: 0.026612 | E:  -4.564782 | E_var:    42.4823 | E_err:   0.101841
[2025-10-02 22:02:39] [Iter   20/2250] R0[38/150]   | LR: 0.026246 | E:  -6.425447 | E_var:    35.7031 | E_err:   0.093363
[2025-10-02 22:02:47] [Iter   21/2250] R0[40/150]   | LR: 0.025864 | E:  -7.838702 | E_var:    36.2569 | E_err:   0.094084
[2025-10-02 22:02:55] [Iter   22/2250] R0[42/150]   | LR: 0.025468 | E:  -9.106960 | E_var:    33.1292 | E_err:   0.089934
[2025-10-02 22:03:03] [Iter   23/2250] R0[44/150]   | LR: 0.025057 | E: -10.351639 | E_var:    34.9323 | E_err:   0.092349
[2025-10-02 22:03:11] [Iter   24/2250] R0[46/150]   | LR: 0.024634 | E: -11.354649 | E_var:    34.0030 | E_err:   0.091113
[2025-10-02 22:03:18] [Iter   25/2250] R0[48/150]   | LR: 0.024198 | E: -12.192403 | E_var:    32.8111 | E_err:   0.089501
[2025-10-02 22:03:26] [Iter   26/2250] R0[50/150]   | LR: 0.023750 | E: -13.150317 | E_var:    41.0490 | E_err:   0.100109
[2025-10-02 22:03:34] [Iter   27/2250] R0[52/150]   | LR: 0.023291 | E: -13.860356 | E_var:    31.5526 | E_err:   0.087768
[2025-10-02 22:03:42] [Iter   28/2250] R0[54/150]   | LR: 0.022822 | E: -14.562987 | E_var:    32.9438 | E_err:   0.089682
[2025-10-02 22:03:50] [Iter   29/2250] R0[56/150]   | LR: 0.022344 | E: -15.326142 | E_var:    29.7960 | E_err:   0.085290
[2025-10-02 22:03:58] [Iter   30/2250] R0[58/150]   | LR: 0.021857 | E: -16.017461 | E_var:    30.7084 | E_err:   0.086586
[2025-10-02 22:04:05] [Iter   31/2250] R0[60/150]   | LR: 0.021363 | E: -16.559923 | E_var:    31.1607 | E_err:   0.087222
[2025-10-02 22:04:13] [Iter   32/2250] R0[62/150]   | LR: 0.020861 | E: -17.281637 | E_var:    29.0590 | E_err:   0.084229
[2025-10-02 22:04:21] [Iter   33/2250] R0[64/150]   | LR: 0.020354 | E: -17.764713 | E_var:    28.6412 | E_err:   0.083621
[2025-10-02 22:04:29] [Iter   34/2250] R0[66/150]   | LR: 0.019842 | E: -18.339316 | E_var:    27.6698 | E_err:   0.082191
[2025-10-02 22:04:37] [Iter   35/2250] R0[68/150]   | LR: 0.019326 | E: -18.977793 | E_var:    26.0685 | E_err:   0.079777
[2025-10-02 22:04:45] [Iter   36/2250] R0[70/150]   | LR: 0.018807 | E: -19.577373 | E_var:    25.7714 | E_err:   0.079321
[2025-10-02 22:04:52] [Iter   37/2250] R0[72/150]   | LR: 0.018285 | E: -20.057921 | E_var:    25.9295 | E_err:   0.079564
[2025-10-02 22:05:00] [Iter   38/2250] R0[74/150]   | LR: 0.017762 | E: -20.603138 | E_var:    24.9025 | E_err:   0.077973
[2025-10-02 22:05:08] [Iter   39/2250] R0[76/150]   | LR: 0.017238 | E: -20.922998 | E_var:    24.5992 | E_err:   0.077496
[2025-10-02 22:05:16] [Iter   40/2250] R0[78/150]   | LR: 0.016715 | E: -21.396292 | E_var:    23.9401 | E_err:   0.076451
[2025-10-02 22:05:24] [Iter   41/2250] R0[80/150]   | LR: 0.016193 | E: -21.950280 | E_var:    27.2482 | E_err:   0.081562
[2025-10-02 22:05:32] [Iter   42/2250] R0[82/150]   | LR: 0.015674 | E: -22.311743 | E_var:    25.4185 | E_err:   0.078776
[2025-10-02 22:05:39] [Iter   43/2250] R0[84/150]   | LR: 0.015158 | E: -22.790377 | E_var:    23.7222 | E_err:   0.076102
[2025-10-02 22:05:47] [Iter   44/2250] R0[86/150]   | LR: 0.014646 | E: -23.221120 | E_var:    23.6456 | E_err:   0.075979
[2025-10-02 22:05:55] [Iter   45/2250] R0[88/150]   | LR: 0.014139 | E: -23.645372 | E_var:    21.4213 | E_err:   0.072317
[2025-10-02 22:06:03] [Iter   46/2250] R0[90/150]   | LR: 0.013637 | E: -23.987103 | E_var:    21.9818 | E_err:   0.073257
[2025-10-02 22:06:11] [Iter   47/2250] R0[92/150]   | LR: 0.013143 | E: -24.382778 | E_var:    22.4605 | E_err:   0.074051
[2025-10-02 22:06:19] [Iter   48/2250] R0[94/150]   | LR: 0.012656 | E: -24.755247 | E_var:    23.9177 | E_err:   0.076415
[2025-10-02 22:06:26] [Iter   49/2250] R0[96/150]   | LR: 0.012178 | E: -25.085373 | E_var:    20.8506 | E_err:   0.071348
[2025-10-02 22:06:34] [Iter   50/2250] R0[98/150]   | LR: 0.011709 | E: -25.382780 | E_var:    22.3927 | E_err:   0.073939
[2025-10-02 22:06:42] [Iter   51/2250] R0[100/150]  | LR: 0.011250 | E: -25.705564 | E_var:    19.5906 | E_err:   0.069158
[2025-10-02 22:06:50] [Iter   52/2250] R0[102/150]  | LR: 0.010802 | E: -25.981818 | E_var:    20.6745 | E_err:   0.071046
[2025-10-02 22:06:58] [Iter   53/2250] R0[104/150]  | LR: 0.010366 | E: -26.385544 | E_var:    20.1724 | E_err:   0.070178
[2025-10-02 22:07:06] [Iter   54/2250] R0[106/150]  | LR: 0.009943 | E: -26.617717 | E_var:    20.3465 | E_err:   0.070480
[2025-10-02 22:07:13] [Iter   55/2250] R0[108/150]  | LR: 0.009532 | E: -26.948374 | E_var:    19.6064 | E_err:   0.069186
[2025-10-02 22:07:21] [Iter   56/2250] R0[110/150]  | LR: 0.009136 | E: -27.169210 | E_var:    17.8221 | E_err:   0.065963
[2025-10-02 22:07:29] [Iter   57/2250] R0[112/150]  | LR: 0.008754 | E: -27.403749 | E_var:    18.9087 | E_err:   0.067944
[2025-10-02 22:07:37] [Iter   58/2250] R0[114/150]  | LR: 0.008388 | E: -27.659208 | E_var:    18.5912 | E_err:   0.067371
[2025-10-02 22:07:45] [Iter   59/2250] R0[116/150]  | LR: 0.008038 | E: -27.888735 | E_var:    17.8777 | E_err:   0.066066
[2025-10-02 22:07:53] [Iter   60/2250] R0[118/150]  | LR: 0.007704 | E: -28.102004 | E_var:    17.9360 | E_err:   0.066173
[2025-10-02 22:08:00] [Iter   61/2250] R0[120/150]  | LR: 0.007387 | E: -28.361761 | E_var:    16.4475 | E_err:   0.063368
[2025-10-02 22:08:08] [Iter   62/2250] R0[122/150]  | LR: 0.007088 | E: -28.478007 | E_var:    16.9189 | E_err:   0.064270
[2025-10-02 22:08:16] [Iter   63/2250] R0[124/150]  | LR: 0.006808 | E: -28.732456 | E_var:    17.5003 | E_err:   0.065365
[2025-10-02 22:08:24] [Iter   64/2250] R0[126/150]  | LR: 0.006546 | E: -28.970894 | E_var:    16.7600 | E_err:   0.063967
[2025-10-02 22:08:32] [Iter   65/2250] R0[128/150]  | LR: 0.006304 | E: -29.139615 | E_var:    17.3259 | E_err:   0.065038
[2025-10-02 22:08:39] [Iter   66/2250] R0[130/150]  | LR: 0.006081 | E: -29.316282 | E_var:    16.0677 | E_err:   0.062632
[2025-10-02 22:08:47] [Iter   67/2250] R0[132/150]  | LR: 0.005878 | E: -29.443624 | E_var:    15.0555 | E_err:   0.060627
[2025-10-02 22:08:55] [Iter   68/2250] R0[134/150]  | LR: 0.005695 | E: -29.576478 | E_var:    16.3265 | E_err:   0.063134
[2025-10-02 22:09:03] [Iter   69/2250] R0[136/150]  | LR: 0.005534 | E: -29.765894 | E_var:    14.7472 | E_err:   0.060003
[2025-10-02 22:09:11] [Iter   70/2250] R0[138/150]  | LR: 0.005393 | E: -29.989989 | E_var:    15.5726 | E_err:   0.061660
[2025-10-02 22:09:19] [Iter   71/2250] R0[140/150]  | LR: 0.005273 | E: -30.159790 | E_var:    15.1620 | E_err:   0.060841
[2025-10-02 22:09:26] [Iter   72/2250] R0[142/150]  | LR: 0.005175 | E: -30.235931 | E_var:    15.9243 | E_err:   0.062352
[2025-10-02 22:09:34] [Iter   73/2250] R0[144/150]  | LR: 0.005099 | E: -30.518784 | E_var:    14.6725 | E_err:   0.059851
[2025-10-02 22:09:42] [Iter   74/2250] R0[146/150]  | LR: 0.005044 | E: -30.611088 | E_var:    13.7527 | E_err:   0.057945
[2025-10-02 22:09:50] [Iter   75/2250] R0[148/150]  | LR: 0.005011 | E: -30.761537 | E_var:    14.5701 | E_err:   0.059642
[2025-10-02 22:09:50] 🔄 RESTART #1 | Period: 300
[2025-10-02 22:09:58] [Iter   76/2250] R1[0/300]    | LR: 0.030000 | E: -30.907459 | E_var:    14.5823 | E_err:   0.059667
[2025-10-02 22:10:06] [Iter   77/2250] R1[2/300]    | LR: 0.029997 | E: -30.921534 | E_var:    13.7534 | E_err:   0.057946
[2025-10-02 22:10:13] [Iter   78/2250] R1[4/300]    | LR: 0.029989 | E: -31.209145 | E_var:    13.9974 | E_err:   0.058458
[2025-10-02 22:10:21] [Iter   79/2250] R1[6/300]    | LR: 0.029975 | E: -31.296477 | E_var:    12.9599 | E_err:   0.056250
[2025-10-02 22:10:29] [Iter   80/2250] R1[8/300]    | LR: 0.029956 | E: -31.505675 | E_var:    13.4938 | E_err:   0.057397
[2025-10-02 22:10:37] [Iter   81/2250] R1[10/300]   | LR: 0.029932 | E: -31.511169 | E_var:    13.1187 | E_err:   0.056593
[2025-10-02 22:10:45] [Iter   82/2250] R1[12/300]   | LR: 0.029901 | E: -31.739111 | E_var:    12.5834 | E_err:   0.055427
[2025-10-02 22:10:53] [Iter   83/2250] R1[14/300]   | LR: 0.029866 | E: -31.846508 | E_var:    13.0604 | E_err:   0.056468
[2025-10-02 22:11:00] [Iter   84/2250] R1[16/300]   | LR: 0.029825 | E: -31.870525 | E_var:    12.8699 | E_err:   0.056054
[2025-10-02 22:11:08] [Iter   85/2250] R1[18/300]   | LR: 0.029779 | E: -31.983046 | E_var:    12.3962 | E_err:   0.055013
[2025-10-02 22:11:16] [Iter   86/2250] R1[20/300]   | LR: 0.029727 | E: -32.088874 | E_var:    12.6826 | E_err:   0.055645
[2025-10-02 22:11:24] [Iter   87/2250] R1[22/300]   | LR: 0.029670 | E: -32.278261 | E_var:    11.5368 | E_err:   0.053072
[2025-10-02 22:11:32] [Iter   88/2250] R1[24/300]   | LR: 0.029607 | E: -32.367710 | E_var:    12.0996 | E_err:   0.054351
[2025-10-02 22:11:39] [Iter   89/2250] R1[26/300]   | LR: 0.029540 | E: -32.472582 | E_var:    11.2518 | E_err:   0.052412
[2025-10-02 22:11:47] [Iter   90/2250] R1[28/300]   | LR: 0.029466 | E: -32.577635 | E_var:    11.2305 | E_err:   0.052362
[2025-10-02 22:11:55] [Iter   91/2250] R1[30/300]   | LR: 0.029388 | E: -32.696946 | E_var:    11.5112 | E_err:   0.053013
[2025-10-02 22:12:03] [Iter   92/2250] R1[32/300]   | LR: 0.029305 | E: -32.728096 | E_var:    10.9318 | E_err:   0.051661
[2025-10-02 22:12:11] [Iter   93/2250] R1[34/300]   | LR: 0.029216 | E: -32.957933 | E_var:    11.6661 | E_err:   0.053368
[2025-10-02 22:12:19] [Iter   94/2250] R1[36/300]   | LR: 0.029122 | E: -32.944474 | E_var:    10.8301 | E_err:   0.051421
[2025-10-02 22:12:26] [Iter   95/2250] R1[38/300]   | LR: 0.029023 | E: -33.062551 | E_var:    10.6019 | E_err:   0.050876
[2025-10-02 22:12:34] [Iter   96/2250] R1[40/300]   | LR: 0.028919 | E: -33.140057 | E_var:     9.5957 | E_err:   0.048402
[2025-10-02 22:12:42] [Iter   97/2250] R1[42/300]   | LR: 0.028810 | E: -33.223907 | E_var:    10.5906 | E_err:   0.050849
[2025-10-02 22:12:50] [Iter   98/2250] R1[44/300]   | LR: 0.028696 | E: -33.283049 | E_var:     9.6065 | E_err:   0.048429
[2025-10-02 22:12:58] [Iter   99/2250] R1[46/300]   | LR: 0.028578 | E: -33.316435 | E_var:     9.7872 | E_err:   0.048882
[2025-10-02 22:13:06] [Iter  100/2250] R1[48/300]   | LR: 0.028454 | E: -33.490960 | E_var:     9.8277 | E_err:   0.048983
[2025-10-02 22:13:13] [Iter  101/2250] R1[50/300]   | LR: 0.028325 | E: -33.513999 | E_var:     9.1197 | E_err:   0.047186
[2025-10-02 22:13:21] [Iter  102/2250] R1[52/300]   | LR: 0.028192 | E: -33.613693 | E_var:     9.1686 | E_err:   0.047312
[2025-10-02 22:13:29] [Iter  103/2250] R1[54/300]   | LR: 0.028054 | E: -33.716194 | E_var:     9.0070 | E_err:   0.046893
[2025-10-02 22:13:37] [Iter  104/2250] R1[56/300]   | LR: 0.027912 | E: -33.739479 | E_var:     8.8754 | E_err:   0.046549
[2025-10-02 22:13:45] [Iter  105/2250] R1[58/300]   | LR: 0.027764 | E: -33.876490 | E_var:     8.6163 | E_err:   0.045865
[2025-10-02 22:13:52] [Iter  106/2250] R1[60/300]   | LR: 0.027613 | E: -33.910394 | E_var:     8.7711 | E_err:   0.046275
[2025-10-02 22:14:00] [Iter  107/2250] R1[62/300]   | LR: 0.027457 | E: -33.948229 | E_var:     8.7786 | E_err:   0.046295
[2025-10-02 22:14:08] [Iter  108/2250] R1[64/300]   | LR: 0.027296 | E: -34.015813 | E_var:     8.0929 | E_err:   0.044450
[2025-10-02 22:14:16] [Iter  109/2250] R1[66/300]   | LR: 0.027131 | E: -34.146965 | E_var:     8.2879 | E_err:   0.044982
[2025-10-02 22:14:24] [Iter  110/2250] R1[68/300]   | LR: 0.026962 | E: -34.177030 | E_var:     8.4329 | E_err:   0.045374
[2025-10-02 22:14:32] [Iter  111/2250] R1[70/300]   | LR: 0.026789 | E: -34.324268 | E_var:     7.8487 | E_err:   0.043774
[2025-10-02 22:14:39] [Iter  112/2250] R1[72/300]   | LR: 0.026612 | E: -34.333619 | E_var:     7.7067 | E_err:   0.043377
[2025-10-02 22:14:47] [Iter  113/2250] R1[74/300]   | LR: 0.026431 | E: -34.388311 | E_var:     7.7592 | E_err:   0.043524
[2025-10-02 22:14:55] [Iter  114/2250] R1[76/300]   | LR: 0.026246 | E: -34.459541 | E_var:     7.6908 | E_err:   0.043332
[2025-10-02 22:15:03] [Iter  115/2250] R1[78/300]   | LR: 0.026057 | E: -34.550801 | E_var:     8.0165 | E_err:   0.044240
[2025-10-02 22:15:11] [Iter  116/2250] R1[80/300]   | LR: 0.025864 | E: -34.607977 | E_var:     7.2592 | E_err:   0.042098
[2025-10-02 22:15:19] [Iter  117/2250] R1[82/300]   | LR: 0.025668 | E: -34.681959 | E_var:     7.0635 | E_err:   0.041527
[2025-10-02 22:15:26] [Iter  118/2250] R1[84/300]   | LR: 0.025468 | E: -34.732247 | E_var:     6.9605 | E_err:   0.041223
[2025-10-02 22:15:34] [Iter  119/2250] R1[86/300]   | LR: 0.025264 | E: -34.786169 | E_var:     7.0086 | E_err:   0.041365
[2025-10-02 22:15:42] [Iter  120/2250] R1[88/300]   | LR: 0.025057 | E: -34.843925 | E_var:     6.5602 | E_err:   0.040020
[2025-10-02 22:15:50] [Iter  121/2250] R1[90/300]   | LR: 0.024847 | E: -34.927026 | E_var:     6.7135 | E_err:   0.040485
[2025-10-02 22:15:58] [Iter  122/2250] R1[92/300]   | LR: 0.024634 | E: -34.984020 | E_var:     6.6996 | E_err:   0.040443
[2025-10-02 22:16:06] [Iter  123/2250] R1[94/300]   | LR: 0.024417 | E: -35.027046 | E_var:     6.7359 | E_err:   0.040552
[2025-10-02 22:16:13] [Iter  124/2250] R1[96/300]   | LR: 0.024198 | E: -35.099037 | E_var:     6.3544 | E_err:   0.039387
[2025-10-02 22:16:21] [Iter  125/2250] R1[98/300]   | LR: 0.023975 | E: -35.150428 | E_var:     6.3981 | E_err:   0.039523
[2025-10-02 22:16:29] [Iter  126/2250] R1[100/300]  | LR: 0.023750 | E: -35.250131 | E_var:     6.2552 | E_err:   0.039079
[2025-10-02 22:16:37] [Iter  127/2250] R1[102/300]  | LR: 0.023522 | E: -35.294792 | E_var:     5.9816 | E_err:   0.038215
[2025-10-02 22:16:45] [Iter  128/2250] R1[104/300]  | LR: 0.023291 | E: -35.372553 | E_var:     6.5785 | E_err:   0.040076
[2025-10-02 22:16:52] [Iter  129/2250] R1[106/300]  | LR: 0.023058 | E: -35.412187 | E_var:     5.7914 | E_err:   0.037602
[2025-10-02 22:17:00] [Iter  130/2250] R1[108/300]  | LR: 0.022822 | E: -35.453710 | E_var:     5.8088 | E_err:   0.037658
[2025-10-02 22:17:08] [Iter  131/2250] R1[110/300]  | LR: 0.022584 | E: -35.478716 | E_var:     5.6050 | E_err:   0.036992
[2025-10-02 22:17:16] [Iter  132/2250] R1[112/300]  | LR: 0.022344 | E: -35.518580 | E_var:     5.7783 | E_err:   0.037560
[2025-10-02 22:17:24] [Iter  133/2250] R1[114/300]  | LR: 0.022102 | E: -35.597522 | E_var:     5.5758 | E_err:   0.036896
[2025-10-02 22:17:32] [Iter  134/2250] R1[116/300]  | LR: 0.021857 | E: -35.639806 | E_var:     6.0097 | E_err:   0.038304
[2025-10-02 22:17:39] [Iter  135/2250] R1[118/300]  | LR: 0.021611 | E: -35.761594 | E_var:     5.7193 | E_err:   0.037367
[2025-10-02 22:17:47] [Iter  136/2250] R1[120/300]  | LR: 0.021363 | E: -35.723031 | E_var:     6.1250 | E_err:   0.038670
[2025-10-02 22:17:55] [Iter  137/2250] R1[122/300]  | LR: 0.021113 | E: -35.771456 | E_var:     5.4692 | E_err:   0.036541
[2025-10-02 22:18:03] [Iter  138/2250] R1[124/300]  | LR: 0.020861 | E: -35.857192 | E_var:     5.6756 | E_err:   0.037224
[2025-10-02 22:18:11] [Iter  139/2250] R1[126/300]  | LR: 0.020609 | E: -35.915117 | E_var:     5.4117 | E_err:   0.036348
[2025-10-02 22:18:19] [Iter  140/2250] R1[128/300]  | LR: 0.020354 | E: -35.910252 | E_var:     5.1568 | E_err:   0.035482
[2025-10-02 22:18:26] [Iter  141/2250] R1[130/300]  | LR: 0.020099 | E: -35.945892 | E_var:     5.1977 | E_err:   0.035622
[2025-10-02 22:18:34] [Iter  142/2250] R1[132/300]  | LR: 0.019842 | E: -36.005616 | E_var:     4.9842 | E_err:   0.034883
[2025-10-02 22:18:42] [Iter  143/2250] R1[134/300]  | LR: 0.019585 | E: -36.045271 | E_var:     4.8669 | E_err:   0.034470
[2025-10-02 22:18:50] [Iter  144/2250] R1[136/300]  | LR: 0.019326 | E: -36.076547 | E_var:     4.9283 | E_err:   0.034687
[2025-10-02 22:18:58] [Iter  145/2250] R1[138/300]  | LR: 0.019067 | E: -36.173704 | E_var:     5.0559 | E_err:   0.035133
[2025-10-02 22:19:05] [Iter  146/2250] R1[140/300]  | LR: 0.018807 | E: -36.171608 | E_var:     4.7962 | E_err:   0.034219
[2025-10-02 22:19:13] [Iter  147/2250] R1[142/300]  | LR: 0.018546 | E: -36.201193 | E_var:     4.6872 | E_err:   0.033828
[2025-10-02 22:19:21] [Iter  148/2250] R1[144/300]  | LR: 0.018285 | E: -36.265258 | E_var:     4.7731 | E_err:   0.034137
[2025-10-02 22:19:29] [Iter  149/2250] R1[146/300]  | LR: 0.018023 | E: -36.301339 | E_var:     4.7532 | E_err:   0.034065
[2025-10-02 22:19:37] [Iter  150/2250] R1[148/300]  | LR: 0.017762 | E: -36.310614 | E_var:     4.6679 | E_err:   0.033758
[2025-10-02 22:19:45] [Iter  151/2250] R1[150/300]  | LR: 0.017500 | E: -36.364254 | E_var:     4.6751 | E_err:   0.033784
[2025-10-02 22:19:52] [Iter  152/2250] R1[152/300]  | LR: 0.017238 | E: -36.405272 | E_var:     4.5331 | E_err:   0.033267
[2025-10-02 22:20:00] [Iter  153/2250] R1[154/300]  | LR: 0.016977 | E: -36.401826 | E_var:     4.5296 | E_err:   0.033254
[2025-10-02 22:20:08] [Iter  154/2250] R1[156/300]  | LR: 0.016715 | E: -36.473238 | E_var:     4.8181 | E_err:   0.034297
[2025-10-02 22:20:16] [Iter  155/2250] R1[158/300]  | LR: 0.016454 | E: -36.450846 | E_var:     5.0042 | E_err:   0.034953
[2025-10-02 22:20:24] [Iter  156/2250] R1[160/300]  | LR: 0.016193 | E: -36.485935 | E_var:     4.9538 | E_err:   0.034777
[2025-10-02 22:20:31] [Iter  157/2250] R1[162/300]  | LR: 0.015933 | E: -36.568220 | E_var:     4.5305 | E_err:   0.033258
[2025-10-02 22:20:39] [Iter  158/2250] R1[164/300]  | LR: 0.015674 | E: -36.584725 | E_var:     4.6963 | E_err:   0.033861
[2025-10-02 22:20:47] [Iter  159/2250] R1[166/300]  | LR: 0.015415 | E: -36.545992 | E_var:     4.4052 | E_err:   0.032795
[2025-10-02 22:20:55] [Iter  160/2250] R1[168/300]  | LR: 0.015158 | E: -36.628186 | E_var:     4.2525 | E_err:   0.032221
[2025-10-02 22:21:03] [Iter  161/2250] R1[170/300]  | LR: 0.014901 | E: -36.651487 | E_var:     4.4404 | E_err:   0.032925
[2025-10-02 22:21:11] [Iter  162/2250] R1[172/300]  | LR: 0.014646 | E: -36.695656 | E_var:     4.3996 | E_err:   0.032774
[2025-10-02 22:21:18] [Iter  163/2250] R1[174/300]  | LR: 0.014391 | E: -36.704949 | E_var:     4.3482 | E_err:   0.032582
[2025-10-02 22:21:26] [Iter  164/2250] R1[176/300]  | LR: 0.014139 | E: -36.742337 | E_var:     4.3050 | E_err:   0.032419
[2025-10-02 22:21:34] [Iter  165/2250] R1[178/300]  | LR: 0.013887 | E: -36.743430 | E_var:     4.5501 | E_err:   0.033330
[2025-10-02 22:21:42] [Iter  166/2250] R1[180/300]  | LR: 0.013637 | E: -36.817881 | E_var:     4.1632 | E_err:   0.031881
[2025-10-02 22:21:50] [Iter  167/2250] R1[182/300]  | LR: 0.013389 | E: -36.839928 | E_var:     4.1619 | E_err:   0.031876
[2025-10-02 22:21:58] [Iter  168/2250] R1[184/300]  | LR: 0.013143 | E: -36.818305 | E_var:     4.3883 | E_err:   0.032732
[2025-10-02 22:22:05] [Iter  169/2250] R1[186/300]  | LR: 0.012898 | E: -36.869356 | E_var:     4.4333 | E_err:   0.032899
[2025-10-02 22:22:13] [Iter  170/2250] R1[188/300]  | LR: 0.012656 | E: -36.908059 | E_var:     4.0717 | E_err:   0.031529
[2025-10-02 22:22:21] [Iter  171/2250] R1[190/300]  | LR: 0.012416 | E: -36.955892 | E_var:     4.5798 | E_err:   0.033438
[2025-10-02 22:22:29] [Iter  172/2250] R1[192/300]  | LR: 0.012178 | E: -36.914208 | E_var:     4.2320 | E_err:   0.032143
[2025-10-02 22:22:37] [Iter  173/2250] R1[194/300]  | LR: 0.011942 | E: -36.977342 | E_var:     3.8833 | E_err:   0.030791
[2025-10-02 22:22:44] [Iter  174/2250] R1[196/300]  | LR: 0.011709 | E: -37.026535 | E_var:     4.2382 | E_err:   0.032167
[2025-10-02 22:22:52] [Iter  175/2250] R1[198/300]  | LR: 0.011478 | E: -37.004439 | E_var:     3.9949 | E_err:   0.031230
[2025-10-02 22:23:00] [Iter  176/2250] R1[200/300]  | LR: 0.011250 | E: -37.069956 | E_var:     4.1075 | E_err:   0.031667
[2025-10-02 22:23:08] [Iter  177/2250] R1[202/300]  | LR: 0.011025 | E: -37.110417 | E_var:     4.2432 | E_err:   0.032186
[2025-10-02 22:23:16] [Iter  178/2250] R1[204/300]  | LR: 0.010802 | E: -37.110466 | E_var:     3.8991 | E_err:   0.030853
[2025-10-02 22:23:24] [Iter  179/2250] R1[206/300]  | LR: 0.010583 | E: -37.100904 | E_var:     4.0561 | E_err:   0.031468
[2025-10-02 22:23:31] [Iter  180/2250] R1[208/300]  | LR: 0.010366 | E: -37.150285 | E_var:     3.9919 | E_err:   0.031218
[2025-10-02 22:23:39] [Iter  181/2250] R1[210/300]  | LR: 0.010153 | E: -37.175737 | E_var:     4.1828 | E_err:   0.031956
[2025-10-02 22:23:47] [Iter  182/2250] R1[212/300]  | LR: 0.009943 | E: -37.144889 | E_var:     3.9290 | E_err:   0.030971
[2025-10-02 22:23:55] [Iter  183/2250] R1[214/300]  | LR: 0.009736 | E: -37.184885 | E_var:     4.2756 | E_err:   0.032308
[2025-10-02 22:24:03] [Iter  184/2250] R1[216/300]  | LR: 0.009532 | E: -37.228130 | E_var:     3.7947 | E_err:   0.030437
[2025-10-02 22:24:10] [Iter  185/2250] R1[218/300]  | LR: 0.009332 | E: -37.289979 | E_var:     3.8934 | E_err:   0.030831
[2025-10-02 22:24:18] [Iter  186/2250] R1[220/300]  | LR: 0.009136 | E: -37.292041 | E_var:     4.1447 | E_err:   0.031810
[2025-10-02 22:24:26] [Iter  187/2250] R1[222/300]  | LR: 0.008943 | E: -37.284270 | E_var:     3.8509 | E_err:   0.030662
[2025-10-02 22:24:34] [Iter  188/2250] R1[224/300]  | LR: 0.008754 | E: -37.311369 | E_var:     3.9268 | E_err:   0.030963
[2025-10-02 22:24:42] [Iter  189/2250] R1[226/300]  | LR: 0.008569 | E: -37.361000 | E_var:     3.7080 | E_err:   0.030088
[2025-10-02 22:24:50] [Iter  190/2250] R1[228/300]  | LR: 0.008388 | E: -37.384499 | E_var:     3.7464 | E_err:   0.030243
[2025-10-02 22:24:57] [Iter  191/2250] R1[230/300]  | LR: 0.008211 | E: -37.408700 | E_var:     3.6182 | E_err:   0.029721
[2025-10-02 22:25:05] [Iter  192/2250] R1[232/300]  | LR: 0.008038 | E: -37.427028 | E_var:     3.9050 | E_err:   0.030877
[2025-10-02 22:25:13] [Iter  193/2250] R1[234/300]  | LR: 0.007869 | E: -37.444925 | E_var:     4.0332 | E_err:   0.031379
[2025-10-02 22:25:21] [Iter  194/2250] R1[236/300]  | LR: 0.007704 | E: -37.456110 | E_var:     3.7838 | E_err:   0.030394
[2025-10-02 22:25:29] [Iter  195/2250] R1[238/300]  | LR: 0.007543 | E: -37.518657 | E_var:     4.0248 | E_err:   0.031347
[2025-10-02 22:25:37] [Iter  196/2250] R1[240/300]  | LR: 0.007387 | E: -37.528876 | E_var:     4.3646 | E_err:   0.032643
[2025-10-02 22:25:44] [Iter  197/2250] R1[242/300]  | LR: 0.007236 | E: -37.584983 | E_var:     4.0418 | E_err:   0.031413
[2025-10-02 22:25:52] [Iter  198/2250] R1[244/300]  | LR: 0.007088 | E: -37.508889 | E_var:     3.9656 | E_err:   0.031115
[2025-10-02 22:26:00] [Iter  199/2250] R1[246/300]  | LR: 0.006946 | E: -37.603345 | E_var:     3.9281 | E_err:   0.030968
[2025-10-02 22:26:08] [Iter  200/2250] R1[248/300]  | LR: 0.006808 | E: -37.592606 | E_var:     3.6226 | E_err:   0.029739
[2025-10-02 22:26:08] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-02 22:26:16] [Iter  201/2250] R1[250/300]  | LR: 0.006675 | E: -37.636052 | E_var:     3.7307 | E_err:   0.030180
[2025-10-02 22:26:23] [Iter  202/2250] R1[252/300]  | LR: 0.006546 | E: -37.658395 | E_var:     3.7918 | E_err:   0.030426
[2025-10-02 22:26:31] [Iter  203/2250] R1[254/300]  | LR: 0.006422 | E: -37.641590 | E_var:     5.5172 | E_err:   0.036701
[2025-10-02 22:26:39] [Iter  204/2250] R1[256/300]  | LR: 0.006304 | E: -37.678135 | E_var:     3.9592 | E_err:   0.031090
[2025-10-02 22:26:47] [Iter  205/2250] R1[258/300]  | LR: 0.006190 | E: -37.695796 | E_var:     3.8845 | E_err:   0.030795
[2025-10-02 22:26:55] [Iter  206/2250] R1[260/300]  | LR: 0.006081 | E: -37.730348 | E_var:     3.7743 | E_err:   0.030356
[2025-10-02 22:27:03] [Iter  207/2250] R1[262/300]  | LR: 0.005977 | E: -37.744372 | E_var:     3.9083 | E_err:   0.030890
[2025-10-02 22:27:10] [Iter  208/2250] R1[264/300]  | LR: 0.005878 | E: -37.761331 | E_var:     3.7974 | E_err:   0.030448
[2025-10-02 22:27:18] [Iter  209/2250] R1[266/300]  | LR: 0.005784 | E: -37.804111 | E_var:     3.9491 | E_err:   0.031051
[2025-10-02 22:27:26] [Iter  210/2250] R1[268/300]  | LR: 0.005695 | E: -37.856272 | E_var:     3.6290 | E_err:   0.029766
[2025-10-02 22:27:34] [Iter  211/2250] R1[270/300]  | LR: 0.005612 | E: -37.862122 | E_var:     3.3638 | E_err:   0.028657
[2025-10-02 22:27:42] [Iter  212/2250] R1[272/300]  | LR: 0.005534 | E: -37.845661 | E_var:     3.8660 | E_err:   0.030722
[2025-10-02 22:27:50] [Iter  213/2250] R1[274/300]  | LR: 0.005460 | E: -37.879471 | E_var:     3.7472 | E_err:   0.030246
[2025-10-02 22:27:57] [Iter  214/2250] R1[276/300]  | LR: 0.005393 | E: -37.949641 | E_var:     3.8455 | E_err:   0.030641
[2025-10-02 22:28:05] [Iter  215/2250] R1[278/300]  | LR: 0.005330 | E: -37.944954 | E_var:     3.7265 | E_err:   0.030163
[2025-10-02 22:28:13] [Iter  216/2250] R1[280/300]  | LR: 0.005273 | E: -38.004672 | E_var:     3.6068 | E_err:   0.029674
[2025-10-02 22:28:21] [Iter  217/2250] R1[282/300]  | LR: 0.005221 | E: -37.970719 | E_var:     3.7660 | E_err:   0.030322
[2025-10-02 22:28:29] [Iter  218/2250] R1[284/300]  | LR: 0.005175 | E: -38.027557 | E_var:     3.7991 | E_err:   0.030455
[2025-10-02 22:28:36] [Iter  219/2250] R1[286/300]  | LR: 0.005134 | E: -38.034004 | E_var:     3.7543 | E_err:   0.030275
[2025-10-02 22:28:44] [Iter  220/2250] R1[288/300]  | LR: 0.005099 | E: -38.059129 | E_var:     3.3837 | E_err:   0.028742
[2025-10-02 22:28:52] [Iter  221/2250] R1[290/300]  | LR: 0.005068 | E: -38.056835 | E_var:     3.8546 | E_err:   0.030677
[2025-10-02 22:29:00] [Iter  222/2250] R1[292/300]  | LR: 0.005044 | E: -38.111306 | E_var:     3.8970 | E_err:   0.030845
[2025-10-02 22:29:08] [Iter  223/2250] R1[294/300]  | LR: 0.005025 | E: -38.093207 | E_var:     3.6023 | E_err:   0.029656
[2025-10-02 22:29:16] [Iter  224/2250] R1[296/300]  | LR: 0.005011 | E: -38.143578 | E_var:     3.6278 | E_err:   0.029761
[2025-10-02 22:29:23] [Iter  225/2250] R1[298/300]  | LR: 0.005003 | E: -38.148520 | E_var:     3.6707 | E_err:   0.029936
[2025-10-02 22:29:23] 🔄 RESTART #2 | Period: 600
[2025-10-02 22:29:31] [Iter  226/2250] R2[0/600]    | LR: 0.030000 | E: -38.175798 | E_var:     3.7774 | E_err:   0.030368
[2025-10-02 22:29:39] [Iter  227/2250] R2[2/600]    | LR: 0.029999 | E: -38.182591 | E_var:     3.4820 | E_err:   0.029157
[2025-10-02 22:29:47] [Iter  228/2250] R2[4/600]    | LR: 0.029997 | E: -38.173954 | E_var:     3.6467 | E_err:   0.029838
[2025-10-02 22:29:55] [Iter  229/2250] R2[6/600]    | LR: 0.029994 | E: -38.223262 | E_var:     3.7208 | E_err:   0.030140
[2025-10-02 22:30:03] [Iter  230/2250] R2[8/600]    | LR: 0.029989 | E: -38.259302 | E_var:     3.9280 | E_err:   0.030968
[2025-10-02 22:30:10] [Iter  231/2250] R2[10/600]   | LR: 0.029983 | E: -38.269675 | E_var:     3.7401 | E_err:   0.030218
[2025-10-02 22:30:18] [Iter  232/2250] R2[12/600]   | LR: 0.029975 | E: -38.287787 | E_var:     3.6494 | E_err:   0.029849
[2025-10-02 22:30:26] [Iter  233/2250] R2[14/600]   | LR: 0.029966 | E: -38.322552 | E_var:     3.8901 | E_err:   0.030818
[2025-10-02 22:30:34] [Iter  234/2250] R2[16/600]   | LR: 0.029956 | E: -38.360728 | E_var:     3.8313 | E_err:   0.030584
[2025-10-02 22:30:42] [Iter  235/2250] R2[18/600]   | LR: 0.029945 | E: -38.349072 | E_var:     3.5936 | E_err:   0.029620
[2025-10-02 22:30:49] [Iter  236/2250] R2[20/600]   | LR: 0.029932 | E: -38.372528 | E_var:     3.6615 | E_err:   0.029898
[2025-10-02 22:30:57] [Iter  237/2250] R2[22/600]   | LR: 0.029917 | E: -38.374804 | E_var:     3.5572 | E_err:   0.029470
[2025-10-02 22:31:05] [Iter  238/2250] R2[24/600]   | LR: 0.029901 | E: -38.439829 | E_var:     3.6123 | E_err:   0.029697
[2025-10-02 22:31:13] [Iter  239/2250] R2[26/600]   | LR: 0.029884 | E: -38.489799 | E_var:     3.7653 | E_err:   0.030319
[2025-10-02 22:31:21] [Iter  240/2250] R2[28/600]   | LR: 0.029866 | E: -38.447750 | E_var:     3.5640 | E_err:   0.029498
[2025-10-02 22:31:29] [Iter  241/2250] R2[30/600]   | LR: 0.029846 | E: -38.507173 | E_var:     3.3312 | E_err:   0.028518
[2025-10-02 22:31:36] [Iter  242/2250] R2[32/600]   | LR: 0.029825 | E: -38.508970 | E_var:     3.5499 | E_err:   0.029439
[2025-10-02 22:31:44] [Iter  243/2250] R2[34/600]   | LR: 0.029802 | E: -38.503788 | E_var:     3.4161 | E_err:   0.028879
[2025-10-02 22:31:52] [Iter  244/2250] R2[36/600]   | LR: 0.029779 | E: -38.517189 | E_var:     3.5681 | E_err:   0.029515
[2025-10-02 22:32:00] [Iter  245/2250] R2[38/600]   | LR: 0.029753 | E: -38.534854 | E_var:     4.3505 | E_err:   0.032591
[2025-10-02 22:32:08] [Iter  246/2250] R2[40/600]   | LR: 0.029727 | E: -38.555132 | E_var:     3.5611 | E_err:   0.029486
[2025-10-02 22:32:15] [Iter  247/2250] R2[42/600]   | LR: 0.029699 | E: -38.573857 | E_var:     7.4133 | E_err:   0.042543
[2025-10-02 22:32:23] [Iter  248/2250] R2[44/600]   | LR: 0.029670 | E: -38.638719 | E_var:     3.4049 | E_err:   0.028832
[2025-10-02 22:32:31] [Iter  249/2250] R2[46/600]   | LR: 0.029639 | E: -38.588589 | E_var:     3.6284 | E_err:   0.029763
[2025-10-02 22:32:39] [Iter  250/2250] R2[48/600]   | LR: 0.029607 | E: -38.728954 | E_var:     4.0241 | E_err:   0.031344
[2025-10-02 22:32:47] [Iter  251/2250] R2[50/600]   | LR: 0.029574 | E: -38.687011 | E_var:     3.3508 | E_err:   0.028602
[2025-10-02 22:32:55] [Iter  252/2250] R2[52/600]   | LR: 0.029540 | E: -38.703808 | E_var:     3.4975 | E_err:   0.029221
[2025-10-02 22:33:02] [Iter  253/2250] R2[54/600]   | LR: 0.029504 | E: -38.679438 | E_var:     3.6986 | E_err:   0.030050
[2025-10-02 22:33:10] [Iter  254/2250] R2[56/600]   | LR: 0.029466 | E: -38.766437 | E_var:     3.4892 | E_err:   0.029187
[2025-10-02 22:33:18] [Iter  255/2250] R2[58/600]   | LR: 0.029428 | E: -38.771690 | E_var:     3.5327 | E_err:   0.029368
[2025-10-02 22:33:26] [Iter  256/2250] R2[60/600]   | LR: 0.029388 | E: -38.727512 | E_var:     3.5146 | E_err:   0.029293
[2025-10-02 22:33:34] [Iter  257/2250] R2[62/600]   | LR: 0.029347 | E: -38.840801 | E_var:     3.5582 | E_err:   0.029474
[2025-10-02 22:33:41] [Iter  258/2250] R2[64/600]   | LR: 0.029305 | E: -38.829969 | E_var:     3.5074 | E_err:   0.029263
[2025-10-02 22:33:49] [Iter  259/2250] R2[66/600]   | LR: 0.029261 | E: -38.804404 | E_var:     3.3242 | E_err:   0.028488
[2025-10-02 22:33:57] [Iter  260/2250] R2[68/600]   | LR: 0.029216 | E: -38.858449 | E_var:     3.6959 | E_err:   0.030039
[2025-10-02 22:34:05] [Iter  261/2250] R2[70/600]   | LR: 0.029170 | E: -38.856397 | E_var:     3.2937 | E_err:   0.028357
[2025-10-02 22:34:13] [Iter  262/2250] R2[72/600]   | LR: 0.029122 | E: -38.879810 | E_var:     3.6280 | E_err:   0.029762
[2025-10-02 22:34:21] [Iter  263/2250] R2[74/600]   | LR: 0.029073 | E: -38.887740 | E_var:     3.4199 | E_err:   0.028895
[2025-10-02 22:34:28] [Iter  264/2250] R2[76/600]   | LR: 0.029023 | E: -38.964396 | E_var:     3.7785 | E_err:   0.030373
[2025-10-02 22:34:36] [Iter  265/2250] R2[78/600]   | LR: 0.028972 | E: -38.963162 | E_var:     3.4313 | E_err:   0.028944
[2025-10-02 22:34:44] [Iter  266/2250] R2[80/600]   | LR: 0.028919 | E: -38.964834 | E_var:     3.5735 | E_err:   0.029537
[2025-10-02 22:34:52] [Iter  267/2250] R2[82/600]   | LR: 0.028865 | E: -39.018412 | E_var:     3.4524 | E_err:   0.029032
[2025-10-02 22:35:00] [Iter  268/2250] R2[84/600]   | LR: 0.028810 | E: -39.055326 | E_var:     3.2581 | E_err:   0.028203
[2025-10-02 22:35:07] [Iter  269/2250] R2[86/600]   | LR: 0.028754 | E: -39.021713 | E_var:     3.3369 | E_err:   0.028543
[2025-10-02 22:35:15] [Iter  270/2250] R2[88/600]   | LR: 0.028696 | E: -39.040978 | E_var:     3.3761 | E_err:   0.028709
[2025-10-02 22:35:23] [Iter  271/2250] R2[90/600]   | LR: 0.028638 | E: -39.038744 | E_var:     3.4832 | E_err:   0.029162
[2025-10-02 22:35:31] [Iter  272/2250] R2[92/600]   | LR: 0.028578 | E: -39.128061 | E_var:     3.7498 | E_err:   0.030257
[2025-10-02 22:35:39] [Iter  273/2250] R2[94/600]   | LR: 0.028516 | E: -39.115976 | E_var:     3.3904 | E_err:   0.028770
[2025-10-02 22:35:47] [Iter  274/2250] R2[96/600]   | LR: 0.028454 | E: -39.088154 | E_var:     3.1427 | E_err:   0.027699
[2025-10-02 22:35:54] [Iter  275/2250] R2[98/600]   | LR: 0.028390 | E: -39.158919 | E_var:     3.2930 | E_err:   0.028354
[2025-10-02 22:36:02] [Iter  276/2250] R2[100/600]  | LR: 0.028325 | E: -39.210205 | E_var:     3.5455 | E_err:   0.029421
[2025-10-02 22:36:10] [Iter  277/2250] R2[102/600]  | LR: 0.028259 | E: -39.174206 | E_var:     3.1392 | E_err:   0.027684
[2025-10-02 22:36:18] [Iter  278/2250] R2[104/600]  | LR: 0.028192 | E: -39.201309 | E_var:     3.1292 | E_err:   0.027640
[2025-10-02 22:36:26] [Iter  279/2250] R2[106/600]  | LR: 0.028124 | E: -39.211458 | E_var:     3.4892 | E_err:   0.029187
[2025-10-02 22:36:34] [Iter  280/2250] R2[108/600]  | LR: 0.028054 | E: -39.246176 | E_var:     3.2831 | E_err:   0.028311
[2025-10-02 22:36:41] [Iter  281/2250] R2[110/600]  | LR: 0.027983 | E: -39.224349 | E_var:     3.2858 | E_err:   0.028323
[2025-10-02 22:36:49] [Iter  282/2250] R2[112/600]  | LR: 0.027912 | E: -39.283437 | E_var:     3.7331 | E_err:   0.030189
[2025-10-02 22:36:57] [Iter  283/2250] R2[114/600]  | LR: 0.027839 | E: -39.322189 | E_var:     3.3340 | E_err:   0.028530
[2025-10-02 22:37:05] [Iter  284/2250] R2[116/600]  | LR: 0.027764 | E: -39.317516 | E_var:     3.3082 | E_err:   0.028419
[2025-10-02 22:37:13] [Iter  285/2250] R2[118/600]  | LR: 0.027689 | E: -39.357099 | E_var:     3.8475 | E_err:   0.030649
[2025-10-02 22:37:20] [Iter  286/2250] R2[120/600]  | LR: 0.027613 | E: -39.347651 | E_var:     3.3454 | E_err:   0.028579
[2025-10-02 22:37:28] [Iter  287/2250] R2[122/600]  | LR: 0.027535 | E: -39.344445 | E_var:     3.3282 | E_err:   0.028505
[2025-10-02 22:37:36] [Iter  288/2250] R2[124/600]  | LR: 0.027457 | E: -39.390319 | E_var:     3.2946 | E_err:   0.028361
[2025-10-02 22:37:44] [Iter  289/2250] R2[126/600]  | LR: 0.027377 | E: -39.378676 | E_var:     3.1482 | E_err:   0.027724
[2025-10-02 22:37:52] [Iter  290/2250] R2[128/600]  | LR: 0.027296 | E: -39.436172 | E_var:     3.3369 | E_err:   0.028543
[2025-10-02 22:38:00] [Iter  291/2250] R2[130/600]  | LR: 0.027214 | E: -39.498613 | E_var:     3.2301 | E_err:   0.028082
[2025-10-02 22:38:07] [Iter  292/2250] R2[132/600]  | LR: 0.027131 | E: -39.466624 | E_var:     3.6338 | E_err:   0.029785
[2025-10-02 22:38:15] [Iter  293/2250] R2[134/600]  | LR: 0.027047 | E: -39.522927 | E_var:     3.3281 | E_err:   0.028505
[2025-10-02 22:38:23] [Iter  294/2250] R2[136/600]  | LR: 0.026962 | E: -39.504042 | E_var:     2.9242 | E_err:   0.026719
[2025-10-02 22:38:31] [Iter  295/2250] R2[138/600]  | LR: 0.026876 | E: -39.534834 | E_var:     3.4232 | E_err:   0.028909
[2025-10-02 22:38:39] [Iter  296/2250] R2[140/600]  | LR: 0.026789 | E: -39.624447 | E_var:     3.5276 | E_err:   0.029347
[2025-10-02 22:38:46] [Iter  297/2250] R2[142/600]  | LR: 0.026701 | E: -39.609227 | E_var:     3.2535 | E_err:   0.028183
[2025-10-02 22:38:54] [Iter  298/2250] R2[144/600]  | LR: 0.026612 | E: -39.702571 | E_var:     3.2491 | E_err:   0.028165
[2025-10-02 22:39:02] [Iter  299/2250] R2[146/600]  | LR: 0.026522 | E: -39.670499 | E_var:     3.3877 | E_err:   0.028759
[2025-10-02 22:39:10] [Iter  300/2250] R2[148/600]  | LR: 0.026431 | E: -39.743006 | E_var:     3.3257 | E_err:   0.028495
[2025-10-02 22:39:18] [Iter  301/2250] R2[150/600]  | LR: 0.026339 | E: -39.769533 | E_var:     3.5486 | E_err:   0.029434
[2025-10-02 22:39:26] [Iter  302/2250] R2[152/600]  | LR: 0.026246 | E: -39.797159 | E_var:     3.8442 | E_err:   0.030635
[2025-10-02 22:39:33] [Iter  303/2250] R2[154/600]  | LR: 0.026152 | E: -39.844047 | E_var:     3.2454 | E_err:   0.028148
[2025-10-02 22:39:41] [Iter  304/2250] R2[156/600]  | LR: 0.026057 | E: -39.884847 | E_var:     3.3021 | E_err:   0.028393
[2025-10-02 22:39:49] [Iter  305/2250] R2[158/600]  | LR: 0.025961 | E: -39.936728 | E_var:     3.8391 | E_err:   0.030615
[2025-10-02 22:39:57] [Iter  306/2250] R2[160/600]  | LR: 0.025864 | E: -40.003735 | E_var:     3.7234 | E_err:   0.030150
[2025-10-02 22:40:05] [Iter  307/2250] R2[162/600]  | LR: 0.025766 | E: -40.073077 | E_var:     4.3617 | E_err:   0.032632
[2025-10-02 22:40:12] [Iter  308/2250] R2[164/600]  | LR: 0.025668 | E: -40.140761 | E_var:     3.9053 | E_err:   0.030878
[2025-10-02 22:40:20] [Iter  309/2250] R2[166/600]  | LR: 0.025568 | E: -40.201784 | E_var:     4.0270 | E_err:   0.031355
[2025-10-02 22:40:28] [Iter  310/2250] R2[168/600]  | LR: 0.025468 | E: -40.319038 | E_var:     3.5711 | E_err:   0.029527
[2025-10-02 22:40:36] [Iter  311/2250] R2[170/600]  | LR: 0.025367 | E: -40.325262 | E_var:     3.6732 | E_err:   0.029946
[2025-10-02 22:40:44] [Iter  312/2250] R2[172/600]  | LR: 0.025264 | E: -40.463670 | E_var:     3.9496 | E_err:   0.031052
[2025-10-02 22:40:52] [Iter  313/2250] R2[174/600]  | LR: 0.025161 | E: -40.564127 | E_var:     3.7901 | E_err:   0.030419
[2025-10-02 22:40:59] [Iter  314/2250] R2[176/600]  | LR: 0.025057 | E: -40.690471 | E_var:     4.0063 | E_err:   0.031275
[2025-10-02 22:41:07] [Iter  315/2250] R2[178/600]  | LR: 0.024953 | E: -40.811847 | E_var:     3.4647 | E_err:   0.029084
[2025-10-02 22:41:15] [Iter  316/2250] R2[180/600]  | LR: 0.024847 | E: -40.889542 | E_var:     3.4454 | E_err:   0.029003
[2025-10-02 22:41:23] [Iter  317/2250] R2[182/600]  | LR: 0.024741 | E: -40.933816 | E_var:     4.6870 | E_err:   0.033827
[2025-10-02 22:41:31] [Iter  318/2250] R2[184/600]  | LR: 0.024634 | E: -41.025528 | E_var:     3.6133 | E_err:   0.029701
[2025-10-02 22:41:38] [Iter  319/2250] R2[186/600]  | LR: 0.024526 | E: -41.115768 | E_var:     4.2807 | E_err:   0.032328
[2025-10-02 22:41:46] [Iter  320/2250] R2[188/600]  | LR: 0.024417 | E: -41.287029 | E_var:     2.6913 | E_err:   0.025633
[2025-10-02 22:41:54] [Iter  321/2250] R2[190/600]  | LR: 0.024308 | E: -41.382192 | E_var:     2.8187 | E_err:   0.026233
[2025-10-02 22:42:02] [Iter  322/2250] R2[192/600]  | LR: 0.024198 | E: -41.437408 | E_var:     2.6506 | E_err:   0.025439
[2025-10-02 22:42:10] [Iter  323/2250] R2[194/600]  | LR: 0.024087 | E: -41.498523 | E_var:     2.8859 | E_err:   0.026544
[2025-10-02 22:42:18] [Iter  324/2250] R2[196/600]  | LR: 0.023975 | E: -41.513354 | E_var:     2.7928 | E_err:   0.026112
[2025-10-02 22:42:25] [Iter  325/2250] R2[198/600]  | LR: 0.023863 | E: -41.603435 | E_var:     2.6274 | E_err:   0.025327
[2025-10-02 22:42:33] [Iter  326/2250] R2[200/600]  | LR: 0.023750 | E: -41.624324 | E_var:     3.0681 | E_err:   0.027369
[2025-10-02 22:42:41] [Iter  327/2250] R2[202/600]  | LR: 0.023636 | E: -41.702502 | E_var:     2.3414 | E_err:   0.023909
[2025-10-02 22:42:49] [Iter  328/2250] R2[204/600]  | LR: 0.023522 | E: -41.727276 | E_var:     2.0708 | E_err:   0.022485
[2025-10-02 22:42:57] [Iter  329/2250] R2[206/600]  | LR: 0.023407 | E: -41.774086 | E_var:     2.1530 | E_err:   0.022927
[2025-10-02 22:43:04] [Iter  330/2250] R2[208/600]  | LR: 0.023291 | E: -41.769801 | E_var:     1.6852 | E_err:   0.020284
[2025-10-02 22:43:12] [Iter  331/2250] R2[210/600]  | LR: 0.023175 | E: -41.781094 | E_var:     2.2070 | E_err:   0.023213
[2025-10-02 22:43:20] [Iter  332/2250] R2[212/600]  | LR: 0.023058 | E: -41.784336 | E_var:     1.4779 | E_err:   0.018995
[2025-10-02 22:43:28] [Iter  333/2250] R2[214/600]  | LR: 0.022940 | E: -41.796259 | E_var:     1.9327 | E_err:   0.021722
[2025-10-02 22:43:36] [Iter  334/2250] R2[216/600]  | LR: 0.022822 | E: -41.833942 | E_var:     1.5786 | E_err:   0.019632
[2025-10-02 22:43:44] [Iter  335/2250] R2[218/600]  | LR: 0.022704 | E: -41.840562 | E_var:     1.4302 | E_err:   0.018686
[2025-10-02 22:43:51] [Iter  336/2250] R2[220/600]  | LR: 0.022584 | E: -41.855400 | E_var:     1.5071 | E_err:   0.019182
[2025-10-02 22:43:59] [Iter  337/2250] R2[222/600]  | LR: 0.022464 | E: -41.855380 | E_var:     1.5842 | E_err:   0.019667
[2025-10-02 22:44:07] [Iter  338/2250] R2[224/600]  | LR: 0.022344 | E: -41.850197 | E_var:     1.7426 | E_err:   0.020626
[2025-10-02 22:44:15] [Iter  339/2250] R2[226/600]  | LR: 0.022223 | E: -41.864492 | E_var:     1.5404 | E_err:   0.019393
[2025-10-02 22:44:23] [Iter  340/2250] R2[228/600]  | LR: 0.022102 | E: -41.875439 | E_var:     1.3804 | E_err:   0.018358
[2025-10-02 22:44:31] [Iter  341/2250] R2[230/600]  | LR: 0.021980 | E: -41.885131 | E_var:     1.6041 | E_err:   0.019790
[2025-10-02 22:44:38] [Iter  342/2250] R2[232/600]  | LR: 0.021857 | E: -41.932020 | E_var:     1.1276 | E_err:   0.016592
[2025-10-02 22:44:46] [Iter  343/2250] R2[234/600]  | LR: 0.021734 | E: -41.919509 | E_var:     1.3482 | E_err:   0.018142
[2025-10-02 22:44:54] [Iter  344/2250] R2[236/600]  | LR: 0.021611 | E: -41.953632 | E_var:     1.5111 | E_err:   0.019207
[2025-10-02 22:45:02] [Iter  345/2250] R2[238/600]  | LR: 0.021487 | E: -41.924960 | E_var:     1.7208 | E_err:   0.020497
[2025-10-02 22:45:10] [Iter  346/2250] R2[240/600]  | LR: 0.021363 | E: -41.934929 | E_var:     1.1783 | E_err:   0.016961
[2025-10-02 22:45:17] [Iter  347/2250] R2[242/600]  | LR: 0.021238 | E: -41.934548 | E_var:     1.5126 | E_err:   0.019217
[2025-10-02 22:45:25] [Iter  348/2250] R2[244/600]  | LR: 0.021113 | E: -41.928830 | E_var:     1.2574 | E_err:   0.017521
[2025-10-02 22:45:33] [Iter  349/2250] R2[246/600]  | LR: 0.020987 | E: -41.971053 | E_var:     1.2501 | E_err:   0.017470
[2025-10-02 22:45:41] [Iter  350/2250] R2[248/600]  | LR: 0.020861 | E: -41.939489 | E_var:     1.4526 | E_err:   0.018832
[2025-10-02 22:45:49] [Iter  351/2250] R2[250/600]  | LR: 0.020735 | E: -41.972253 | E_var:     1.8008 | E_err:   0.020968
[2025-10-02 22:45:57] [Iter  352/2250] R2[252/600]  | LR: 0.020609 | E: -41.918043 | E_var:     1.3350 | E_err:   0.018053
[2025-10-02 22:46:04] [Iter  353/2250] R2[254/600]  | LR: 0.020482 | E: -41.989361 | E_var:     1.2971 | E_err:   0.017795
[2025-10-02 22:46:12] [Iter  354/2250] R2[256/600]  | LR: 0.020354 | E: -41.962072 | E_var:     1.6258 | E_err:   0.019923
[2025-10-02 22:46:20] [Iter  355/2250] R2[258/600]  | LR: 0.020227 | E: -41.989609 | E_var:     1.3169 | E_err:   0.017931
[2025-10-02 22:46:28] [Iter  356/2250] R2[260/600]  | LR: 0.020099 | E: -41.995326 | E_var:     1.6266 | E_err:   0.019928
[2025-10-02 22:46:36] [Iter  357/2250] R2[262/600]  | LR: 0.019971 | E: -41.994977 | E_var:     1.5219 | E_err:   0.019276
[2025-10-02 22:46:43] [Iter  358/2250] R2[264/600]  | LR: 0.019842 | E: -41.953944 | E_var:     1.4923 | E_err:   0.019088
[2025-10-02 22:46:51] [Iter  359/2250] R2[266/600]  | LR: 0.019714 | E: -41.983586 | E_var:     1.4327 | E_err:   0.018702
[2025-10-02 22:46:59] [Iter  360/2250] R2[268/600]  | LR: 0.019585 | E: -42.000706 | E_var:     2.0794 | E_err:   0.022531
[2025-10-02 22:47:07] [Iter  361/2250] R2[270/600]  | LR: 0.019455 | E: -41.998173 | E_var:     1.1950 | E_err:   0.017080
[2025-10-02 22:47:15] [Iter  362/2250] R2[272/600]  | LR: 0.019326 | E: -42.001232 | E_var:     1.3878 | E_err:   0.018407
[2025-10-02 22:47:23] [Iter  363/2250] R2[274/600]  | LR: 0.019196 | E: -41.984618 | E_var:     1.3841 | E_err:   0.018383
[2025-10-02 22:47:30] [Iter  364/2250] R2[276/600]  | LR: 0.019067 | E: -42.011129 | E_var:     1.0607 | E_err:   0.016092
[2025-10-02 22:47:38] [Iter  365/2250] R2[278/600]  | LR: 0.018937 | E: -41.992334 | E_var:     1.0839 | E_err:   0.016268
[2025-10-02 22:47:46] [Iter  366/2250] R2[280/600]  | LR: 0.018807 | E: -41.986802 | E_var:     1.2756 | E_err:   0.017647
[2025-10-02 22:47:54] [Iter  367/2250] R2[282/600]  | LR: 0.018676 | E: -42.001430 | E_var:     1.5424 | E_err:   0.019405
[2025-10-02 22:48:02] [Iter  368/2250] R2[284/600]  | LR: 0.018546 | E: -41.997856 | E_var:     1.8268 | E_err:   0.021118
[2025-10-02 22:48:09] [Iter  369/2250] R2[286/600]  | LR: 0.018415 | E: -42.017724 | E_var:     1.2362 | E_err:   0.017373
[2025-10-02 22:48:17] [Iter  370/2250] R2[288/600]  | LR: 0.018285 | E: -42.021084 | E_var:     1.6250 | E_err:   0.019918
[2025-10-02 22:48:25] [Iter  371/2250] R2[290/600]  | LR: 0.018154 | E: -42.036775 | E_var:     1.2826 | E_err:   0.017696
[2025-10-02 22:48:33] [Iter  372/2250] R2[292/600]  | LR: 0.018023 | E: -42.041227 | E_var:     1.2065 | E_err:   0.017163
[2025-10-02 22:48:41] [Iter  373/2250] R2[294/600]  | LR: 0.017893 | E: -42.048120 | E_var:     1.1598 | E_err:   0.016827
[2025-10-02 22:48:49] [Iter  374/2250] R2[296/600]  | LR: 0.017762 | E: -42.059386 | E_var:     1.9291 | E_err:   0.021702
[2025-10-02 22:48:56] [Iter  375/2250] R2[298/600]  | LR: 0.017631 | E: -42.066744 | E_var:     1.1432 | E_err:   0.016707
[2025-10-02 22:49:04] [Iter  376/2250] R2[300/600]  | LR: 0.017500 | E: -42.001356 | E_var:     1.2410 | E_err:   0.017406
[2025-10-02 22:49:12] [Iter  377/2250] R2[302/600]  | LR: 0.017369 | E: -42.019069 | E_var:     1.8512 | E_err:   0.021259
[2025-10-02 22:49:20] [Iter  378/2250] R2[304/600]  | LR: 0.017238 | E: -42.049767 | E_var:     1.2296 | E_err:   0.017326
[2025-10-02 22:49:28] [Iter  379/2250] R2[306/600]  | LR: 0.017107 | E: -42.050369 | E_var:     1.1383 | E_err:   0.016671
[2025-10-02 22:49:35] [Iter  380/2250] R2[308/600]  | LR: 0.016977 | E: -42.044972 | E_var:     1.0325 | E_err:   0.015877
[2025-10-02 22:49:43] [Iter  381/2250] R2[310/600]  | LR: 0.016846 | E: -42.071458 | E_var:     1.0688 | E_err:   0.016154
[2025-10-02 22:49:51] [Iter  382/2250] R2[312/600]  | LR: 0.016715 | E: -42.042807 | E_var:     1.3476 | E_err:   0.018139
[2025-10-02 22:49:59] [Iter  383/2250] R2[314/600]  | LR: 0.016585 | E: -42.053477 | E_var:     0.9465 | E_err:   0.015201
[2025-10-02 22:50:07] [Iter  384/2250] R2[316/600]  | LR: 0.016454 | E: -42.037683 | E_var:     0.9774 | E_err:   0.015448
[2025-10-02 22:50:15] [Iter  385/2250] R2[318/600]  | LR: 0.016324 | E: -42.071975 | E_var:     1.0201 | E_err:   0.015781
[2025-10-02 22:50:22] [Iter  386/2250] R2[320/600]  | LR: 0.016193 | E: -42.034756 | E_var:     1.2334 | E_err:   0.017353
[2025-10-02 22:50:30] [Iter  387/2250] R2[322/600]  | LR: 0.016063 | E: -42.037752 | E_var:     0.9594 | E_err:   0.015305
[2025-10-02 22:50:38] [Iter  388/2250] R2[324/600]  | LR: 0.015933 | E: -42.062703 | E_var:     1.2482 | E_err:   0.017456
[2025-10-02 22:50:46] [Iter  389/2250] R2[326/600]  | LR: 0.015804 | E: -42.065585 | E_var:     1.1065 | E_err:   0.016436
[2025-10-02 22:50:54] [Iter  390/2250] R2[328/600]  | LR: 0.015674 | E: -42.079573 | E_var:     0.8882 | E_err:   0.014726
[2025-10-02 22:51:01] [Iter  391/2250] R2[330/600]  | LR: 0.015545 | E: -42.068088 | E_var:     1.7094 | E_err:   0.020429
[2025-10-02 22:51:09] [Iter  392/2250] R2[332/600]  | LR: 0.015415 | E: -42.066137 | E_var:     1.1044 | E_err:   0.016420
[2025-10-02 22:51:17] [Iter  393/2250] R2[334/600]  | LR: 0.015286 | E: -42.109192 | E_var:     0.9365 | E_err:   0.015121
[2025-10-02 22:51:25] [Iter  394/2250] R2[336/600]  | LR: 0.015158 | E: -42.088786 | E_var:     1.0776 | E_err:   0.016220
[2025-10-02 22:51:33] [Iter  395/2250] R2[338/600]  | LR: 0.015029 | E: -42.072951 | E_var:     1.6838 | E_err:   0.020275
[2025-10-02 22:51:41] [Iter  396/2250] R2[340/600]  | LR: 0.014901 | E: -42.100238 | E_var:     1.1125 | E_err:   0.016481
[2025-10-02 22:51:48] [Iter  397/2250] R2[342/600]  | LR: 0.014773 | E: -42.094293 | E_var:     0.9079 | E_err:   0.014888
[2025-10-02 22:51:56] [Iter  398/2250] R2[344/600]  | LR: 0.014646 | E: -42.104296 | E_var:     0.9351 | E_err:   0.015109
[2025-10-02 22:52:04] [Iter  399/2250] R2[346/600]  | LR: 0.014518 | E: -42.066705 | E_var:     1.1102 | E_err:   0.016463
[2025-10-02 22:52:12] [Iter  400/2250] R2[348/600]  | LR: 0.014391 | E: -42.117159 | E_var:     1.0880 | E_err:   0.016298
[2025-10-02 22:52:12] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-02 22:52:20] [Iter  401/2250] R2[350/600]  | LR: 0.014265 | E: -42.060455 | E_var:     2.0752 | E_err:   0.022509
[2025-10-02 22:52:28] [Iter  402/2250] R2[352/600]  | LR: 0.014139 | E: -42.083359 | E_var:     1.4777 | E_err:   0.018994
[2025-10-02 22:52:35] [Iter  403/2250] R2[354/600]  | LR: 0.014013 | E: -42.095204 | E_var:     2.3404 | E_err:   0.023904
[2025-10-02 22:52:43] [Iter  404/2250] R2[356/600]  | LR: 0.013887 | E: -42.092028 | E_var:     0.8257 | E_err:   0.014198
[2025-10-02 22:52:51] [Iter  405/2250] R2[358/600]  | LR: 0.013762 | E: -42.096613 | E_var:     0.9787 | E_err:   0.015458
[2025-10-02 22:52:59] [Iter  406/2250] R2[360/600]  | LR: 0.013637 | E: -42.123877 | E_var:     1.0786 | E_err:   0.016227
[2025-10-02 22:53:07] [Iter  407/2250] R2[362/600]  | LR: 0.013513 | E: -42.096551 | E_var:     1.8782 | E_err:   0.021414
[2025-10-02 22:53:15] [Iter  408/2250] R2[364/600]  | LR: 0.013389 | E: -42.095946 | E_var:     0.9518 | E_err:   0.015244
[2025-10-02 22:53:22] [Iter  409/2250] R2[366/600]  | LR: 0.013266 | E: -42.085820 | E_var:     1.0633 | E_err:   0.016112
[2025-10-02 22:53:30] [Iter  410/2250] R2[368/600]  | LR: 0.013143 | E: -42.093778 | E_var:     0.9445 | E_err:   0.015185
[2025-10-02 22:53:38] [Iter  411/2250] R2[370/600]  | LR: 0.013020 | E: -42.115936 | E_var:     1.0996 | E_err:   0.016384
[2025-10-02 22:53:46] [Iter  412/2250] R2[372/600]  | LR: 0.012898 | E: -42.131805 | E_var:     0.8956 | E_err:   0.014787
[2025-10-02 22:53:54] [Iter  413/2250] R2[374/600]  | LR: 0.012777 | E: -42.148695 | E_var:     0.9404 | E_err:   0.015152
[2025-10-02 22:54:01] [Iter  414/2250] R2[376/600]  | LR: 0.012656 | E: -42.119943 | E_var:     0.8940 | E_err:   0.014774
[2025-10-02 22:54:09] [Iter  415/2250] R2[378/600]  | LR: 0.012536 | E: -42.099081 | E_var:     1.0862 | E_err:   0.016285
[2025-10-02 22:54:17] [Iter  416/2250] R2[380/600]  | LR: 0.012416 | E: -42.132580 | E_var:     1.6489 | E_err:   0.020064
[2025-10-02 22:54:25] [Iter  417/2250] R2[382/600]  | LR: 0.012296 | E: -42.114621 | E_var:     1.1415 | E_err:   0.016694
[2025-10-02 22:54:33] [Iter  418/2250] R2[384/600]  | LR: 0.012178 | E: -42.120924 | E_var:     0.8273 | E_err:   0.014212
[2025-10-02 22:54:41] [Iter  419/2250] R2[386/600]  | LR: 0.012060 | E: -42.150651 | E_var:     0.7499 | E_err:   0.013531
[2025-10-02 22:54:48] [Iter  420/2250] R2[388/600]  | LR: 0.011942 | E: -42.139378 | E_var:     0.8697 | E_err:   0.014571
[2025-10-02 22:54:56] [Iter  421/2250] R2[390/600]  | LR: 0.011825 | E: -42.125384 | E_var:     0.9518 | E_err:   0.015243
[2025-10-02 22:55:04] [Iter  422/2250] R2[392/600]  | LR: 0.011709 | E: -42.126828 | E_var:     0.8017 | E_err:   0.013990
[2025-10-02 22:55:12] [Iter  423/2250] R2[394/600]  | LR: 0.011593 | E: -42.114796 | E_var:     0.8496 | E_err:   0.014402
[2025-10-02 22:55:20] [Iter  424/2250] R2[396/600]  | LR: 0.011478 | E: -42.131997 | E_var:     0.8504 | E_err:   0.014409
[2025-10-02 22:55:27] [Iter  425/2250] R2[398/600]  | LR: 0.011364 | E: -42.119932 | E_var:     0.8854 | E_err:   0.014702
[2025-10-02 22:55:35] [Iter  426/2250] R2[400/600]  | LR: 0.011250 | E: -42.127734 | E_var:     0.8947 | E_err:   0.014780
[2025-10-02 22:55:43] [Iter  427/2250] R2[402/600]  | LR: 0.011137 | E: -42.141133 | E_var:     0.9879 | E_err:   0.015530
[2025-10-02 22:55:51] [Iter  428/2250] R2[404/600]  | LR: 0.011025 | E: -42.162554 | E_var:     0.8806 | E_err:   0.014662
[2025-10-02 22:55:59] [Iter  429/2250] R2[406/600]  | LR: 0.010913 | E: -42.139443 | E_var:     0.7576 | E_err:   0.013600
[2025-10-02 22:56:07] [Iter  430/2250] R2[408/600]  | LR: 0.010802 | E: -42.133363 | E_var:     0.7701 | E_err:   0.013712
[2025-10-02 22:56:14] [Iter  431/2250] R2[410/600]  | LR: 0.010692 | E: -42.124068 | E_var:     0.9902 | E_err:   0.015548
[2025-10-02 22:56:22] [Iter  432/2250] R2[412/600]  | LR: 0.010583 | E: -42.115204 | E_var:     0.9152 | E_err:   0.014948
[2025-10-02 22:56:30] [Iter  433/2250] R2[414/600]  | LR: 0.010474 | E: -42.123326 | E_var:     0.7469 | E_err:   0.013504
[2025-10-02 22:56:38] [Iter  434/2250] R2[416/600]  | LR: 0.010366 | E: -42.147576 | E_var:     0.7209 | E_err:   0.013266
[2025-10-02 22:56:46] [Iter  435/2250] R2[418/600]  | LR: 0.010259 | E: -42.141442 | E_var:     0.7724 | E_err:   0.013732
[2025-10-02 22:56:53] [Iter  436/2250] R2[420/600]  | LR: 0.010153 | E: -42.133524 | E_var:     0.8453 | E_err:   0.014366
[2025-10-02 22:57:01] [Iter  437/2250] R2[422/600]  | LR: 0.010047 | E: -42.130438 | E_var:     0.6418 | E_err:   0.012517
[2025-10-02 22:57:09] [Iter  438/2250] R2[424/600]  | LR: 0.009943 | E: -42.125705 | E_var:     1.4226 | E_err:   0.018637
[2025-10-02 22:57:17] [Iter  439/2250] R2[426/600]  | LR: 0.009839 | E: -42.167221 | E_var:     0.9065 | E_err:   0.014876
[2025-10-02 22:57:25] [Iter  440/2250] R2[428/600]  | LR: 0.009736 | E: -42.160059 | E_var:     0.7907 | E_err:   0.013894
[2025-10-02 22:57:33] [Iter  441/2250] R2[430/600]  | LR: 0.009633 | E: -42.155677 | E_var:     0.9157 | E_err:   0.014952
[2025-10-02 22:57:40] [Iter  442/2250] R2[432/600]  | LR: 0.009532 | E: -42.134409 | E_var:     0.8230 | E_err:   0.014175
[2025-10-02 22:57:48] [Iter  443/2250] R2[434/600]  | LR: 0.009432 | E: -42.148535 | E_var:     0.8225 | E_err:   0.014170
[2025-10-02 22:57:56] [Iter  444/2250] R2[436/600]  | LR: 0.009332 | E: -42.173637 | E_var:     0.6180 | E_err:   0.012284
[2025-10-02 22:58:04] [Iter  445/2250] R2[438/600]  | LR: 0.009234 | E: -42.150797 | E_var:     0.7654 | E_err:   0.013670
[2025-10-02 22:58:12] [Iter  446/2250] R2[440/600]  | LR: 0.009136 | E: -42.160993 | E_var:     0.7383 | E_err:   0.013426
[2025-10-02 22:58:19] [Iter  447/2250] R2[442/600]  | LR: 0.009039 | E: -42.164391 | E_var:     0.7858 | E_err:   0.013851
[2025-10-02 22:58:27] [Iter  448/2250] R2[444/600]  | LR: 0.008943 | E: -42.169654 | E_var:     0.7228 | E_err:   0.013284
[2025-10-02 22:58:35] [Iter  449/2250] R2[446/600]  | LR: 0.008848 | E: -42.179183 | E_var:     1.1800 | E_err:   0.016973
[2025-10-02 22:58:43] [Iter  450/2250] R2[448/600]  | LR: 0.008754 | E: -42.131303 | E_var:     0.7652 | E_err:   0.013668
[2025-10-02 22:58:51] [Iter  451/2250] R2[450/600]  | LR: 0.008661 | E: -42.158370 | E_var:     0.7094 | E_err:   0.013160
[2025-10-02 22:58:59] [Iter  452/2250] R2[452/600]  | LR: 0.008569 | E: -42.145489 | E_var:     0.8013 | E_err:   0.013987
[2025-10-02 22:59:06] [Iter  453/2250] R2[454/600]  | LR: 0.008478 | E: -42.157929 | E_var:     0.8136 | E_err:   0.014094
[2025-10-02 22:59:14] [Iter  454/2250] R2[456/600]  | LR: 0.008388 | E: -42.193639 | E_var:     0.7810 | E_err:   0.013809
[2025-10-02 22:59:22] [Iter  455/2250] R2[458/600]  | LR: 0.008299 | E: -42.171749 | E_var:     0.9297 | E_err:   0.015066
[2025-10-02 22:59:30] [Iter  456/2250] R2[460/600]  | LR: 0.008211 | E: -42.160646 | E_var:     0.7855 | E_err:   0.013848
[2025-10-02 22:59:38] [Iter  457/2250] R2[462/600]  | LR: 0.008124 | E: -42.161696 | E_var:     1.1502 | E_err:   0.016757
[2025-10-02 22:59:45] [Iter  458/2250] R2[464/600]  | LR: 0.008038 | E: -42.165509 | E_var:     0.7046 | E_err:   0.013116
[2025-10-02 22:59:53] [Iter  459/2250] R2[466/600]  | LR: 0.007953 | E: -42.156745 | E_var:     0.7038 | E_err:   0.013108
[2025-10-02 23:00:01] [Iter  460/2250] R2[468/600]  | LR: 0.007869 | E: -42.200224 | E_var:     0.8787 | E_err:   0.014647
[2025-10-02 23:00:09] [Iter  461/2250] R2[470/600]  | LR: 0.007786 | E: -42.159482 | E_var:     0.8151 | E_err:   0.014107
[2025-10-02 23:00:17] [Iter  462/2250] R2[472/600]  | LR: 0.007704 | E: -42.171602 | E_var:     0.7103 | E_err:   0.013169
[2025-10-02 23:00:25] [Iter  463/2250] R2[474/600]  | LR: 0.007623 | E: -42.173433 | E_var:     0.5983 | E_err:   0.012086
[2025-10-02 23:00:32] [Iter  464/2250] R2[476/600]  | LR: 0.007543 | E: -42.158220 | E_var:     0.6664 | E_err:   0.012755
[2025-10-02 23:00:40] [Iter  465/2250] R2[478/600]  | LR: 0.007465 | E: -42.184091 | E_var:     0.6648 | E_err:   0.012740
[2025-10-02 23:00:48] [Iter  466/2250] R2[480/600]  | LR: 0.007387 | E: -42.173348 | E_var:     1.0850 | E_err:   0.016275
[2025-10-02 23:00:56] [Iter  467/2250] R2[482/600]  | LR: 0.007311 | E: -42.161752 | E_var:     0.8179 | E_err:   0.014131
[2025-10-02 23:01:04] [Iter  468/2250] R2[484/600]  | LR: 0.007236 | E: -42.177005 | E_var:     0.5690 | E_err:   0.011786
[2025-10-02 23:01:11] [Iter  469/2250] R2[486/600]  | LR: 0.007161 | E: -42.191387 | E_var:     0.8414 | E_err:   0.014333
[2025-10-02 23:01:19] [Iter  470/2250] R2[488/600]  | LR: 0.007088 | E: -42.202143 | E_var:     0.9650 | E_err:   0.015349
[2025-10-02 23:01:27] [Iter  471/2250] R2[490/600]  | LR: 0.007017 | E: -42.200058 | E_var:     0.7143 | E_err:   0.013205
[2025-10-02 23:01:35] [Iter  472/2250] R2[492/600]  | LR: 0.006946 | E: -42.173717 | E_var:     0.8507 | E_err:   0.014411
[2025-10-02 23:01:43] [Iter  473/2250] R2[494/600]  | LR: 0.006876 | E: -42.161672 | E_var:     0.6329 | E_err:   0.012431
[2025-10-02 23:01:51] [Iter  474/2250] R2[496/600]  | LR: 0.006808 | E: -42.177628 | E_var:     0.6683 | E_err:   0.012774
[2025-10-02 23:01:58] [Iter  475/2250] R2[498/600]  | LR: 0.006741 | E: -42.197331 | E_var:     0.6428 | E_err:   0.012527
[2025-10-02 23:02:06] [Iter  476/2250] R2[500/600]  | LR: 0.006675 | E: -42.180259 | E_var:     0.5939 | E_err:   0.012042
[2025-10-02 23:02:14] [Iter  477/2250] R2[502/600]  | LR: 0.006610 | E: -42.205533 | E_var:     0.7796 | E_err:   0.013796
[2025-10-02 23:02:22] [Iter  478/2250] R2[504/600]  | LR: 0.006546 | E: -42.211661 | E_var:     0.7691 | E_err:   0.013703
[2025-10-02 23:02:30] [Iter  479/2250] R2[506/600]  | LR: 0.006484 | E: -42.168786 | E_var:     0.9373 | E_err:   0.015127
[2025-10-02 23:02:37] [Iter  480/2250] R2[508/600]  | LR: 0.006422 | E: -42.206125 | E_var:     1.0943 | E_err:   0.016345
[2025-10-02 23:02:45] [Iter  481/2250] R2[510/600]  | LR: 0.006362 | E: -42.197382 | E_var:     0.7030 | E_err:   0.013101
[2025-10-02 23:02:53] [Iter  482/2250] R2[512/600]  | LR: 0.006304 | E: -42.174933 | E_var:     0.5508 | E_err:   0.011597
[2025-10-02 23:03:01] [Iter  483/2250] R2[514/600]  | LR: 0.006246 | E: -42.173609 | E_var:     0.4873 | E_err:   0.010907
[2025-10-02 23:03:09] [Iter  484/2250] R2[516/600]  | LR: 0.006190 | E: -42.186121 | E_var:     0.7604 | E_err:   0.013625
[2025-10-02 23:03:17] [Iter  485/2250] R2[518/600]  | LR: 0.006135 | E: -42.176777 | E_var:     0.6128 | E_err:   0.012231
[2025-10-02 23:03:24] [Iter  486/2250] R2[520/600]  | LR: 0.006081 | E: -42.189809 | E_var:     0.6362 | E_err:   0.012463
[2025-10-02 23:03:32] [Iter  487/2250] R2[522/600]  | LR: 0.006028 | E: -42.192975 | E_var:     0.9521 | E_err:   0.015246
[2025-10-02 23:03:40] [Iter  488/2250] R2[524/600]  | LR: 0.005977 | E: -42.169697 | E_var:     0.6022 | E_err:   0.012125
[2025-10-02 23:03:48] [Iter  489/2250] R2[526/600]  | LR: 0.005927 | E: -42.216758 | E_var:     0.6084 | E_err:   0.012187
[2025-10-02 23:03:56] [Iter  490/2250] R2[528/600]  | LR: 0.005878 | E: -42.183473 | E_var:     0.7267 | E_err:   0.013320
[2025-10-02 23:04:03] [Iter  491/2250] R2[530/600]  | LR: 0.005830 | E: -42.217601 | E_var:     0.6615 | E_err:   0.012708
[2025-10-02 23:04:11] [Iter  492/2250] R2[532/600]  | LR: 0.005784 | E: -42.205839 | E_var:     1.1239 | E_err:   0.016565
[2025-10-02 23:04:19] [Iter  493/2250] R2[534/600]  | LR: 0.005739 | E: -42.221375 | E_var:     0.6421 | E_err:   0.012521
[2025-10-02 23:04:27] [Iter  494/2250] R2[536/600]  | LR: 0.005695 | E: -42.195632 | E_var:     0.8120 | E_err:   0.014080
[2025-10-02 23:04:35] [Iter  495/2250] R2[538/600]  | LR: 0.005653 | E: -42.210172 | E_var:     0.5576 | E_err:   0.011667
[2025-10-02 23:04:43] [Iter  496/2250] R2[540/600]  | LR: 0.005612 | E: -42.191408 | E_var:     0.5448 | E_err:   0.011532
[2025-10-02 23:04:50] [Iter  497/2250] R2[542/600]  | LR: 0.005572 | E: -42.207221 | E_var:     0.7807 | E_err:   0.013806
[2025-10-02 23:04:58] [Iter  498/2250] R2[544/600]  | LR: 0.005534 | E: -42.204786 | E_var:     0.5816 | E_err:   0.011916
[2025-10-02 23:05:06] [Iter  499/2250] R2[546/600]  | LR: 0.005496 | E: -42.219397 | E_var:     0.6105 | E_err:   0.012209
[2025-10-02 23:05:14] [Iter  500/2250] R2[548/600]  | LR: 0.005460 | E: -42.177426 | E_var:     0.8732 | E_err:   0.014601
[2025-10-02 23:05:22] [Iter  501/2250] R2[550/600]  | LR: 0.005426 | E: -42.242457 | E_var:     0.5119 | E_err:   0.011179
[2025-10-02 23:05:29] [Iter  502/2250] R2[552/600]  | LR: 0.005393 | E: -42.218587 | E_var:     0.6002 | E_err:   0.012105
[2025-10-02 23:05:37] [Iter  503/2250] R2[554/600]  | LR: 0.005361 | E: -42.194382 | E_var:     0.6798 | E_err:   0.012883
[2025-10-02 23:05:45] [Iter  504/2250] R2[556/600]  | LR: 0.005330 | E: -42.198997 | E_var:     0.9411 | E_err:   0.015158
[2025-10-02 23:05:53] [Iter  505/2250] R2[558/600]  | LR: 0.005301 | E: -42.221263 | E_var:     0.5687 | E_err:   0.011783
[2025-10-02 23:06:01] [Iter  506/2250] R2[560/600]  | LR: 0.005273 | E: -42.217523 | E_var:     0.5794 | E_err:   0.011894
[2025-10-02 23:06:09] [Iter  507/2250] R2[562/600]  | LR: 0.005247 | E: -42.183089 | E_var:     0.6503 | E_err:   0.012600
[2025-10-02 23:06:16] [Iter  508/2250] R2[564/600]  | LR: 0.005221 | E: -42.229878 | E_var:     0.7078 | E_err:   0.013146
[2025-10-02 23:06:24] [Iter  509/2250] R2[566/600]  | LR: 0.005198 | E: -42.204975 | E_var:     0.9227 | E_err:   0.015009
[2025-10-02 23:06:32] [Iter  510/2250] R2[568/600]  | LR: 0.005175 | E: -42.213532 | E_var:     0.6347 | E_err:   0.012448
[2025-10-02 23:06:40] [Iter  511/2250] R2[570/600]  | LR: 0.005154 | E: -42.182032 | E_var:     0.8500 | E_err:   0.014406
[2025-10-02 23:06:48] [Iter  512/2250] R2[572/600]  | LR: 0.005134 | E: -42.210668 | E_var:     0.5250 | E_err:   0.011322
[2025-10-02 23:06:56] [Iter  513/2250] R2[574/600]  | LR: 0.005116 | E: -42.194638 | E_var:     0.8910 | E_err:   0.014749
[2025-10-02 23:07:03] [Iter  514/2250] R2[576/600]  | LR: 0.005099 | E: -42.232298 | E_var:     0.9450 | E_err:   0.015189
[2025-10-02 23:07:11] [Iter  515/2250] R2[578/600]  | LR: 0.005083 | E: -42.218538 | E_var:     0.5842 | E_err:   0.011943
[2025-10-02 23:07:19] [Iter  516/2250] R2[580/600]  | LR: 0.005068 | E: -42.229569 | E_var:     0.6444 | E_err:   0.012543
[2025-10-02 23:07:27] [Iter  517/2250] R2[582/600]  | LR: 0.005055 | E: -42.211836 | E_var:     0.6388 | E_err:   0.012488
[2025-10-02 23:07:35] [Iter  518/2250] R2[584/600]  | LR: 0.005044 | E: -42.184996 | E_var:     0.7164 | E_err:   0.013225
[2025-10-02 23:07:42] [Iter  519/2250] R2[586/600]  | LR: 0.005034 | E: -42.209612 | E_var:     0.5919 | E_err:   0.012021
[2025-10-02 23:07:50] [Iter  520/2250] R2[588/600]  | LR: 0.005025 | E: -42.223137 | E_var:     0.8081 | E_err:   0.014046
[2025-10-02 23:07:58] [Iter  521/2250] R2[590/600]  | LR: 0.005017 | E: -42.221204 | E_var:     0.5294 | E_err:   0.011369
[2025-10-02 23:08:06] [Iter  522/2250] R2[592/600]  | LR: 0.005011 | E: -42.176973 | E_var:     2.1039 | E_err:   0.022664
[2025-10-02 23:08:14] [Iter  523/2250] R2[594/600]  | LR: 0.005006 | E: -42.215942 | E_var:     0.8320 | E_err:   0.014252
[2025-10-02 23:08:21] [Iter  524/2250] R2[596/600]  | LR: 0.005003 | E: -42.234763 | E_var:     0.6655 | E_err:   0.012747
[2025-10-02 23:08:29] [Iter  525/2250] R2[598/600]  | LR: 0.005001 | E: -42.202397 | E_var:     0.8135 | E_err:   0.014093
[2025-10-02 23:08:29] 🔄 RESTART #3 | Period: 1200
[2025-10-02 23:08:37] [Iter  526/2250] R3[0/1200]   | LR: 0.030000 | E: -42.207477 | E_var:     0.5675 | E_err:   0.011771
[2025-10-02 23:08:45] [Iter  527/2250] R3[2/1200]   | LR: 0.030000 | E: -42.225481 | E_var:     0.5980 | E_err:   0.012082
[2025-10-02 23:08:53] [Iter  528/2250] R3[4/1200]   | LR: 0.029999 | E: -42.201782 | E_var:     0.7810 | E_err:   0.013808
[2025-10-02 23:09:01] [Iter  529/2250] R3[6/1200]   | LR: 0.029998 | E: -42.211772 | E_var:     0.6116 | E_err:   0.012220
[2025-10-02 23:09:08] [Iter  530/2250] R3[8/1200]   | LR: 0.029997 | E: -42.199520 | E_var:     0.6149 | E_err:   0.012252
[2025-10-02 23:09:16] [Iter  531/2250] R3[10/1200]  | LR: 0.029996 | E: -42.217105 | E_var:     0.4862 | E_err:   0.010895
[2025-10-02 23:09:24] [Iter  532/2250] R3[12/1200]  | LR: 0.029994 | E: -42.217578 | E_var:     0.8076 | E_err:   0.014042
[2025-10-02 23:09:32] [Iter  533/2250] R3[14/1200]  | LR: 0.029992 | E: -42.212617 | E_var:     0.8802 | E_err:   0.014659
[2025-10-02 23:09:40] [Iter  534/2250] R3[16/1200]  | LR: 0.029989 | E: -42.232067 | E_var:     0.5558 | E_err:   0.011648
[2025-10-02 23:09:48] [Iter  535/2250] R3[18/1200]  | LR: 0.029986 | E: -42.197965 | E_var:     0.9460 | E_err:   0.015198
[2025-10-02 23:09:55] [Iter  536/2250] R3[20/1200]  | LR: 0.029983 | E: -42.205522 | E_var:     0.4996 | E_err:   0.011044
[2025-10-02 23:10:03] [Iter  537/2250] R3[22/1200]  | LR: 0.029979 | E: -42.208326 | E_var:     0.6333 | E_err:   0.012434
[2025-10-02 23:10:11] [Iter  538/2250] R3[24/1200]  | LR: 0.029975 | E: -42.197477 | E_var:     0.5823 | E_err:   0.011923
[2025-10-02 23:10:19] [Iter  539/2250] R3[26/1200]  | LR: 0.029971 | E: -42.224143 | E_var:     0.9951 | E_err:   0.015587
[2025-10-02 23:10:27] [Iter  540/2250] R3[28/1200]  | LR: 0.029966 | E: -42.237870 | E_var:     0.5348 | E_err:   0.011427
[2025-10-02 23:10:34] [Iter  541/2250] R3[30/1200]  | LR: 0.029961 | E: -42.225495 | E_var:     0.7095 | E_err:   0.013161
[2025-10-02 23:10:42] [Iter  542/2250] R3[32/1200]  | LR: 0.029956 | E: -42.213728 | E_var:     0.5389 | E_err:   0.011470
[2025-10-02 23:10:50] [Iter  543/2250] R3[34/1200]  | LR: 0.029951 | E: -42.229207 | E_var:     0.6327 | E_err:   0.012429
[2025-10-02 23:10:58] [Iter  544/2250] R3[36/1200]  | LR: 0.029945 | E: -42.201190 | E_var:     0.5608 | E_err:   0.011701
[2025-10-02 23:11:06] [Iter  545/2250] R3[38/1200]  | LR: 0.029938 | E: -42.230220 | E_var:     0.6518 | E_err:   0.012615
[2025-10-02 23:11:14] [Iter  546/2250] R3[40/1200]  | LR: 0.029932 | E: -42.218327 | E_var:     1.3066 | E_err:   0.017861
[2025-10-02 23:11:21] [Iter  547/2250] R3[42/1200]  | LR: 0.029925 | E: -42.216970 | E_var:     0.5971 | E_err:   0.012074
[2025-10-02 23:11:29] [Iter  548/2250] R3[44/1200]  | LR: 0.029917 | E: -42.218408 | E_var:     1.0529 | E_err:   0.016033
[2025-10-02 23:11:37] [Iter  549/2250] R3[46/1200]  | LR: 0.029909 | E: -42.228376 | E_var:     0.5767 | E_err:   0.011866
[2025-10-02 23:11:45] [Iter  550/2250] R3[48/1200]  | LR: 0.029901 | E: -42.223649 | E_var:     1.7574 | E_err:   0.020714
[2025-10-02 23:11:53] [Iter  551/2250] R3[50/1200]  | LR: 0.029893 | E: -42.215810 | E_var:     1.3891 | E_err:   0.018416
[2025-10-02 23:12:00] [Iter  552/2250] R3[52/1200]  | LR: 0.029884 | E: -42.239588 | E_var:     0.5111 | E_err:   0.011170
[2025-10-02 23:12:08] [Iter  553/2250] R3[54/1200]  | LR: 0.029875 | E: -42.214832 | E_var:     0.7250 | E_err:   0.013305
[2025-10-02 23:12:16] [Iter  554/2250] R3[56/1200]  | LR: 0.029866 | E: -42.203986 | E_var:     0.9370 | E_err:   0.015125
[2025-10-02 23:12:24] [Iter  555/2250] R3[58/1200]  | LR: 0.029856 | E: -42.189591 | E_var:     0.9448 | E_err:   0.015187
[2025-10-02 23:12:32] [Iter  556/2250] R3[60/1200]  | LR: 0.029846 | E: -42.213189 | E_var:     0.7035 | E_err:   0.013106
[2025-10-02 23:12:40] [Iter  557/2250] R3[62/1200]  | LR: 0.029836 | E: -42.220043 | E_var:     0.5126 | E_err:   0.011187
[2025-10-02 23:12:47] [Iter  558/2250] R3[64/1200]  | LR: 0.029825 | E: -42.225115 | E_var:     0.7477 | E_err:   0.013511
[2025-10-02 23:12:55] [Iter  559/2250] R3[66/1200]  | LR: 0.029814 | E: -42.221096 | E_var:     0.7032 | E_err:   0.013103
[2025-10-02 23:13:03] [Iter  560/2250] R3[68/1200]  | LR: 0.029802 | E: -42.218538 | E_var:     0.6399 | E_err:   0.012499
[2025-10-02 23:13:11] [Iter  561/2250] R3[70/1200]  | LR: 0.029791 | E: -42.212324 | E_var:     0.7357 | E_err:   0.013402
[2025-10-02 23:13:19] [Iter  562/2250] R3[72/1200]  | LR: 0.029779 | E: -42.196531 | E_var:     0.5670 | E_err:   0.011766
[2025-10-02 23:13:26] [Iter  563/2250] R3[74/1200]  | LR: 0.029766 | E: -42.221319 | E_var:     0.5943 | E_err:   0.012046
[2025-10-02 23:13:34] [Iter  564/2250] R3[76/1200]  | LR: 0.029753 | E: -42.235872 | E_var:     0.5258 | E_err:   0.011330
[2025-10-02 23:13:42] [Iter  565/2250] R3[78/1200]  | LR: 0.029740 | E: -42.213970 | E_var:     1.6120 | E_err:   0.019838
[2025-10-02 23:13:50] [Iter  566/2250] R3[80/1200]  | LR: 0.029727 | E: -42.229559 | E_var:     0.5344 | E_err:   0.011422
[2025-10-02 23:13:58] [Iter  567/2250] R3[82/1200]  | LR: 0.029713 | E: -42.213131 | E_var:     0.6023 | E_err:   0.012127
[2025-10-02 23:14:06] [Iter  568/2250] R3[84/1200]  | LR: 0.029699 | E: -42.230625 | E_var:     0.5454 | E_err:   0.011539
[2025-10-02 23:14:13] [Iter  569/2250] R3[86/1200]  | LR: 0.029685 | E: -42.205038 | E_var:     0.6901 | E_err:   0.012980
[2025-10-02 23:14:21] [Iter  570/2250] R3[88/1200]  | LR: 0.029670 | E: -42.233431 | E_var:     0.8316 | E_err:   0.014249
[2025-10-02 23:14:29] [Iter  571/2250] R3[90/1200]  | LR: 0.029655 | E: -42.223513 | E_var:     0.4249 | E_err:   0.010185
[2025-10-02 23:14:37] [Iter  572/2250] R3[92/1200]  | LR: 0.029639 | E: -42.208755 | E_var:     0.6178 | E_err:   0.012281
[2025-10-02 23:14:45] [Iter  573/2250] R3[94/1200]  | LR: 0.029623 | E: -42.215921 | E_var:     0.5700 | E_err:   0.011796
[2025-10-02 23:14:52] [Iter  574/2250] R3[96/1200]  | LR: 0.029607 | E: -42.233299 | E_var:     0.6923 | E_err:   0.013000
[2025-10-02 23:15:00] [Iter  575/2250] R3[98/1200]  | LR: 0.029591 | E: -42.219462 | E_var:     0.7976 | E_err:   0.013955
[2025-10-02 23:15:08] [Iter  576/2250] R3[100/1200] | LR: 0.029574 | E: -42.214272 | E_var:     0.7375 | E_err:   0.013418
[2025-10-02 23:15:16] [Iter  577/2250] R3[102/1200] | LR: 0.029557 | E: -42.223548 | E_var:     0.5116 | E_err:   0.011176
[2025-10-02 23:15:24] [Iter  578/2250] R3[104/1200] | LR: 0.029540 | E: -42.231715 | E_var:     0.5722 | E_err:   0.011820
[2025-10-02 23:15:32] [Iter  579/2250] R3[106/1200] | LR: 0.029522 | E: -42.225196 | E_var:     0.6066 | E_err:   0.012169
[2025-10-02 23:15:39] [Iter  580/2250] R3[108/1200] | LR: 0.029504 | E: -42.230733 | E_var:     0.5880 | E_err:   0.011981
[2025-10-02 23:15:47] [Iter  581/2250] R3[110/1200] | LR: 0.029485 | E: -42.236754 | E_var:     0.7266 | E_err:   0.013319
[2025-10-02 23:15:55] [Iter  582/2250] R3[112/1200] | LR: 0.029466 | E: -42.236207 | E_var:     0.5892 | E_err:   0.011994
[2025-10-02 23:16:03] [Iter  583/2250] R3[114/1200] | LR: 0.029447 | E: -42.234491 | E_var:     0.6467 | E_err:   0.012565
[2025-10-02 23:16:11] [Iter  584/2250] R3[116/1200] | LR: 0.029428 | E: -42.205170 | E_var:     0.5844 | E_err:   0.011944
[2025-10-02 23:16:18] [Iter  585/2250] R3[118/1200] | LR: 0.029408 | E: -42.233023 | E_var:     0.5921 | E_err:   0.012023
[2025-10-02 23:16:26] [Iter  586/2250] R3[120/1200] | LR: 0.029388 | E: -42.226741 | E_var:     0.5331 | E_err:   0.011409
[2025-10-02 23:16:34] [Iter  587/2250] R3[122/1200] | LR: 0.029368 | E: -42.241332 | E_var:     0.5358 | E_err:   0.011437
[2025-10-02 23:16:42] [Iter  588/2250] R3[124/1200] | LR: 0.029347 | E: -42.227991 | E_var:     0.6560 | E_err:   0.012655
[2025-10-02 23:16:50] [Iter  589/2250] R3[126/1200] | LR: 0.029326 | E: -42.253806 | E_var:     1.2927 | E_err:   0.017765
[2025-10-02 23:16:58] [Iter  590/2250] R3[128/1200] | LR: 0.029305 | E: -42.211985 | E_var:     0.6124 | E_err:   0.012228
[2025-10-02 23:17:05] [Iter  591/2250] R3[130/1200] | LR: 0.029283 | E: -42.220287 | E_var:     0.6967 | E_err:   0.013042
[2025-10-02 23:17:13] [Iter  592/2250] R3[132/1200] | LR: 0.029261 | E: -42.230023 | E_var:     0.7280 | E_err:   0.013332
[2025-10-02 23:17:21] [Iter  593/2250] R3[134/1200] | LR: 0.029239 | E: -42.224114 | E_var:     0.5939 | E_err:   0.012041
[2025-10-02 23:17:29] [Iter  594/2250] R3[136/1200] | LR: 0.029216 | E: -42.227496 | E_var:     0.5800 | E_err:   0.011899
[2025-10-02 23:17:37] [Iter  595/2250] R3[138/1200] | LR: 0.029193 | E: -42.242108 | E_var:     0.6955 | E_err:   0.013031
[2025-10-02 23:17:45] [Iter  596/2250] R3[140/1200] | LR: 0.029170 | E: -42.245071 | E_var:     0.6284 | E_err:   0.012386
[2025-10-02 23:17:52] [Iter  597/2250] R3[142/1200] | LR: 0.029146 | E: -42.252901 | E_var:     0.9473 | E_err:   0.015207
[2025-10-02 23:18:00] [Iter  598/2250] R3[144/1200] | LR: 0.029122 | E: -42.248778 | E_var:     0.6432 | E_err:   0.012531
[2025-10-02 23:18:08] [Iter  599/2250] R3[146/1200] | LR: 0.029098 | E: -42.243110 | E_var:     0.6184 | E_err:   0.012287
[2025-10-02 23:18:16] [Iter  600/2250] R3[148/1200] | LR: 0.029073 | E: -42.230578 | E_var:     0.7466 | E_err:   0.013501
[2025-10-02 23:18:16] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-02 23:18:24] [Iter  601/2250] R3[150/1200] | LR: 0.029048 | E: -42.229574 | E_var:     0.5689 | E_err:   0.011786
[2025-10-02 23:18:31] [Iter  602/2250] R3[152/1200] | LR: 0.029023 | E: -42.219841 | E_var:     0.4851 | E_err:   0.010883
[2025-10-02 23:18:39] [Iter  603/2250] R3[154/1200] | LR: 0.028998 | E: -42.207760 | E_var:     0.8301 | E_err:   0.014236
[2025-10-02 23:18:47] [Iter  604/2250] R3[156/1200] | LR: 0.028972 | E: -42.245560 | E_var:     0.5913 | E_err:   0.012015
[2025-10-02 23:18:55] [Iter  605/2250] R3[158/1200] | LR: 0.028946 | E: -42.262234 | E_var:     0.5540 | E_err:   0.011629
[2025-10-02 23:19:03] [Iter  606/2250] R3[160/1200] | LR: 0.028919 | E: -42.238383 | E_var:     0.6110 | E_err:   0.012214
[2025-10-02 23:19:11] [Iter  607/2250] R3[162/1200] | LR: 0.028893 | E: -42.253899 | E_var:     0.7400 | E_err:   0.013441
[2025-10-02 23:19:18] [Iter  608/2250] R3[164/1200] | LR: 0.028865 | E: -42.231108 | E_var:     0.5430 | E_err:   0.011514
[2025-10-02 23:19:26] [Iter  609/2250] R3[166/1200] | LR: 0.028838 | E: -42.245349 | E_var:     0.6252 | E_err:   0.012355
[2025-10-02 23:19:34] [Iter  610/2250] R3[168/1200] | LR: 0.028810 | E: -42.246824 | E_var:     0.6838 | E_err:   0.012921
[2025-10-02 23:19:42] [Iter  611/2250] R3[170/1200] | LR: 0.028782 | E: -42.229570 | E_var:     0.7089 | E_err:   0.013156
[2025-10-02 23:19:50] [Iter  612/2250] R3[172/1200] | LR: 0.028754 | E: -42.242393 | E_var:     0.6398 | E_err:   0.012498
[2025-10-02 23:19:58] [Iter  613/2250] R3[174/1200] | LR: 0.028725 | E: -42.232183 | E_var:     0.6789 | E_err:   0.012875
[2025-10-02 23:20:05] [Iter  614/2250] R3[176/1200] | LR: 0.028696 | E: -42.236384 | E_var:     0.5267 | E_err:   0.011340
[2025-10-02 23:20:13] [Iter  615/2250] R3[178/1200] | LR: 0.028667 | E: -42.243870 | E_var:     0.5862 | E_err:   0.011963
[2025-10-02 23:20:21] [Iter  616/2250] R3[180/1200] | LR: 0.028638 | E: -42.229215 | E_var:     0.5832 | E_err:   0.011933
[2025-10-02 23:20:29] [Iter  617/2250] R3[182/1200] | LR: 0.028608 | E: -42.228804 | E_var:     0.5468 | E_err:   0.011554
[2025-10-02 23:20:37] [Iter  618/2250] R3[184/1200] | LR: 0.028578 | E: -42.231398 | E_var:     0.5528 | E_err:   0.011618
[2025-10-02 23:20:44] [Iter  619/2250] R3[186/1200] | LR: 0.028547 | E: -42.234626 | E_var:     0.9127 | E_err:   0.014928
[2025-10-02 23:20:52] [Iter  620/2250] R3[188/1200] | LR: 0.028516 | E: -42.216528 | E_var:     0.5827 | E_err:   0.011927
[2025-10-02 23:21:00] [Iter  621/2250] R3[190/1200] | LR: 0.028485 | E: -42.234446 | E_var:     0.5848 | E_err:   0.011949
[2025-10-02 23:21:08] [Iter  622/2250] R3[192/1200] | LR: 0.028454 | E: -42.240478 | E_var:     0.5377 | E_err:   0.011457
[2025-10-02 23:21:16] [Iter  623/2250] R3[194/1200] | LR: 0.028422 | E: -42.235480 | E_var:     0.5592 | E_err:   0.011684
[2025-10-02 23:21:23] [Iter  624/2250] R3[196/1200] | LR: 0.028390 | E: -42.249074 | E_var:     0.5980 | E_err:   0.012083
[2025-10-02 23:21:31] [Iter  625/2250] R3[198/1200] | LR: 0.028358 | E: -42.221873 | E_var:     0.6323 | E_err:   0.012424
[2025-10-02 23:21:39] [Iter  626/2250] R3[200/1200] | LR: 0.028325 | E: -42.259047 | E_var:     0.5235 | E_err:   0.011305
[2025-10-02 23:21:47] [Iter  627/2250] R3[202/1200] | LR: 0.028292 | E: -42.200116 | E_var:     0.8257 | E_err:   0.014198
[2025-10-02 23:21:55] [Iter  628/2250] R3[204/1200] | LR: 0.028259 | E: -42.233740 | E_var:     0.6165 | E_err:   0.012269
[2025-10-02 23:22:03] [Iter  629/2250] R3[206/1200] | LR: 0.028226 | E: -42.226472 | E_var:     0.7340 | E_err:   0.013386
[2025-10-02 23:22:10] [Iter  630/2250] R3[208/1200] | LR: 0.028192 | E: -42.240139 | E_var:     0.5460 | E_err:   0.011545
[2025-10-02 23:22:18] [Iter  631/2250] R3[210/1200] | LR: 0.028158 | E: -42.237613 | E_var:     0.4837 | E_err:   0.010867
[2025-10-02 23:22:26] [Iter  632/2250] R3[212/1200] | LR: 0.028124 | E: -42.240534 | E_var:     0.6007 | E_err:   0.012110
[2025-10-02 23:22:34] [Iter  633/2250] R3[214/1200] | LR: 0.028089 | E: -42.225123 | E_var:     0.7385 | E_err:   0.013427
[2025-10-02 23:22:42] [Iter  634/2250] R3[216/1200] | LR: 0.028054 | E: -42.239237 | E_var:     0.5213 | E_err:   0.011281
[2025-10-02 23:22:49] [Iter  635/2250] R3[218/1200] | LR: 0.028019 | E: -42.249166 | E_var:     0.6846 | E_err:   0.012928
[2025-10-02 23:22:57] [Iter  636/2250] R3[220/1200] | LR: 0.027983 | E: -42.249689 | E_var:     0.5860 | E_err:   0.011962
[2025-10-02 23:23:05] [Iter  637/2250] R3[222/1200] | LR: 0.027948 | E: -42.228873 | E_var:     0.5443 | E_err:   0.011528
[2025-10-02 23:23:13] [Iter  638/2250] R3[224/1200] | LR: 0.027912 | E: -42.208800 | E_var:     0.5119 | E_err:   0.011180
[2025-10-02 23:23:21] [Iter  639/2250] R3[226/1200] | LR: 0.027875 | E: -42.245920 | E_var:     0.6459 | E_err:   0.012557
[2025-10-02 23:23:29] [Iter  640/2250] R3[228/1200] | LR: 0.027839 | E: -42.250178 | E_var:     0.8228 | E_err:   0.014173
[2025-10-02 23:23:36] [Iter  641/2250] R3[230/1200] | LR: 0.027802 | E: -42.250440 | E_var:     0.5230 | E_err:   0.011299
[2025-10-02 23:23:44] [Iter  642/2250] R3[232/1200] | LR: 0.027764 | E: -42.244954 | E_var:     0.5294 | E_err:   0.011368
[2025-10-02 23:23:52] [Iter  643/2250] R3[234/1200] | LR: 0.027727 | E: -42.247501 | E_var:     0.6068 | E_err:   0.012171
[2025-10-02 23:24:00] [Iter  644/2250] R3[236/1200] | LR: 0.027689 | E: -42.249780 | E_var:     0.5032 | E_err:   0.011084
[2025-10-02 23:24:08] [Iter  645/2250] R3[238/1200] | LR: 0.027651 | E: -42.243948 | E_var:     0.5455 | E_err:   0.011541
[2025-10-02 23:24:16] [Iter  646/2250] R3[240/1200] | LR: 0.027613 | E: -42.237526 | E_var:     0.5836 | E_err:   0.011936
[2025-10-02 23:24:23] [Iter  647/2250] R3[242/1200] | LR: 0.027574 | E: -42.220858 | E_var:     0.4952 | E_err:   0.010995
[2025-10-02 23:24:31] [Iter  648/2250] R3[244/1200] | LR: 0.027535 | E: -42.230798 | E_var:     0.6064 | E_err:   0.012167
[2025-10-02 23:24:39] [Iter  649/2250] R3[246/1200] | LR: 0.027496 | E: -42.242578 | E_var:     0.5430 | E_err:   0.011514
[2025-10-02 23:24:47] [Iter  650/2250] R3[248/1200] | LR: 0.027457 | E: -42.234722 | E_var:     0.4839 | E_err:   0.010870
[2025-10-02 23:24:55] [Iter  651/2250] R3[250/1200] | LR: 0.027417 | E: -42.246182 | E_var:     0.5459 | E_err:   0.011545
[2025-10-02 23:25:02] [Iter  652/2250] R3[252/1200] | LR: 0.027377 | E: -42.245651 | E_var:     0.9480 | E_err:   0.015214
[2025-10-02 23:25:10] [Iter  653/2250] R3[254/1200] | LR: 0.027337 | E: -42.258321 | E_var:     0.8504 | E_err:   0.014409
[2025-10-02 23:25:18] [Iter  654/2250] R3[256/1200] | LR: 0.027296 | E: -42.237682 | E_var:     0.5994 | E_err:   0.012097
[2025-10-02 23:25:26] [Iter  655/2250] R3[258/1200] | LR: 0.027255 | E: -42.257979 | E_var:     0.6684 | E_err:   0.012775
[2025-10-02 23:25:34] [Iter  656/2250] R3[260/1200] | LR: 0.027214 | E: -42.230965 | E_var:     0.4894 | E_err:   0.010931
[2025-10-02 23:25:42] [Iter  657/2250] R3[262/1200] | LR: 0.027173 | E: -42.249462 | E_var:     0.4943 | E_err:   0.010986
[2025-10-02 23:25:49] [Iter  658/2250] R3[264/1200] | LR: 0.027131 | E: -42.228482 | E_var:     0.6344 | E_err:   0.012445
[2025-10-02 23:25:57] [Iter  659/2250] R3[266/1200] | LR: 0.027090 | E: -42.256475 | E_var:     0.5625 | E_err:   0.011718
[2025-10-02 23:26:05] [Iter  660/2250] R3[268/1200] | LR: 0.027047 | E: -42.247165 | E_var:     0.5722 | E_err:   0.011819
[2025-10-02 23:26:13] [Iter  661/2250] R3[270/1200] | LR: 0.027005 | E: -42.237580 | E_var:     0.5249 | E_err:   0.011320
[2025-10-02 23:26:21] [Iter  662/2250] R3[272/1200] | LR: 0.026962 | E: -42.251143 | E_var:     0.4040 | E_err:   0.009932
[2025-10-02 23:26:28] [Iter  663/2250] R3[274/1200] | LR: 0.026920 | E: -42.249150 | E_var:     0.4798 | E_err:   0.010823
[2025-10-02 23:26:36] [Iter  664/2250] R3[276/1200] | LR: 0.026876 | E: -42.236016 | E_var:     0.6689 | E_err:   0.012779
[2025-10-02 23:26:44] [Iter  665/2250] R3[278/1200] | LR: 0.026833 | E: -42.235160 | E_var:     1.0569 | E_err:   0.016063
[2025-10-02 23:26:52] [Iter  666/2250] R3[280/1200] | LR: 0.026789 | E: -42.242260 | E_var:     0.4721 | E_err:   0.010736
[2025-10-02 23:27:00] [Iter  667/2250] R3[282/1200] | LR: 0.026745 | E: -42.242836 | E_var:     0.5735 | E_err:   0.011833
[2025-10-02 23:27:08] [Iter  668/2250] R3[284/1200] | LR: 0.026701 | E: -42.267974 | E_var:     1.0822 | E_err:   0.016255
[2025-10-02 23:27:15] [Iter  669/2250] R3[286/1200] | LR: 0.026657 | E: -42.251902 | E_var:     0.5109 | E_err:   0.011168
[2025-10-02 23:27:23] [Iter  670/2250] R3[288/1200] | LR: 0.026612 | E: -42.240367 | E_var:     0.5538 | E_err:   0.011627
[2025-10-02 23:27:31] [Iter  671/2250] R3[290/1200] | LR: 0.026567 | E: -42.248747 | E_var:     0.5026 | E_err:   0.011077
[2025-10-02 23:27:39] [Iter  672/2250] R3[292/1200] | LR: 0.026522 | E: -42.251922 | E_var:     0.5105 | E_err:   0.011164
[2025-10-02 23:27:47] [Iter  673/2250] R3[294/1200] | LR: 0.026477 | E: -42.249971 | E_var:     0.4836 | E_err:   0.010866
[2025-10-02 23:27:54] [Iter  674/2250] R3[296/1200] | LR: 0.026431 | E: -42.245899 | E_var:     0.4866 | E_err:   0.010900
[2025-10-02 23:28:02] [Iter  675/2250] R3[298/1200] | LR: 0.026385 | E: -42.262916 | E_var:     0.5622 | E_err:   0.011716
[2025-10-02 23:28:10] [Iter  676/2250] R3[300/1200] | LR: 0.026339 | E: -42.263001 | E_var:     0.6303 | E_err:   0.012404
[2025-10-02 23:28:18] [Iter  677/2250] R3[302/1200] | LR: 0.026292 | E: -42.232683 | E_var:     0.5373 | E_err:   0.011454
[2025-10-02 23:28:26] [Iter  678/2250] R3[304/1200] | LR: 0.026246 | E: -42.224307 | E_var:     0.5428 | E_err:   0.011512
[2025-10-02 23:28:34] [Iter  679/2250] R3[306/1200] | LR: 0.026199 | E: -42.233151 | E_var:     0.6060 | E_err:   0.012163
[2025-10-02 23:28:41] [Iter  680/2250] R3[308/1200] | LR: 0.026152 | E: -42.262609 | E_var:     0.5676 | E_err:   0.011771
[2025-10-02 23:28:49] [Iter  681/2250] R3[310/1200] | LR: 0.026104 | E: -42.248271 | E_var:     0.6054 | E_err:   0.012157
[2025-10-02 23:28:57] [Iter  682/2250] R3[312/1200] | LR: 0.026057 | E: -42.268877 | E_var:     0.7162 | E_err:   0.013223
[2025-10-02 23:29:05] [Iter  683/2250] R3[314/1200] | LR: 0.026009 | E: -42.256225 | E_var:     0.5381 | E_err:   0.011461
[2025-10-02 23:29:13] [Iter  684/2250] R3[316/1200] | LR: 0.025961 | E: -42.237502 | E_var:     0.6312 | E_err:   0.012414
[2025-10-02 23:29:21] [Iter  685/2250] R3[318/1200] | LR: 0.025913 | E: -42.238664 | E_var:     0.6948 | E_err:   0.013024
[2025-10-02 23:29:28] [Iter  686/2250] R3[320/1200] | LR: 0.025864 | E: -42.227753 | E_var:     0.6401 | E_err:   0.012500
[2025-10-02 23:29:36] [Iter  687/2250] R3[322/1200] | LR: 0.025815 | E: -42.265395 | E_var:     0.5752 | E_err:   0.011851
[2025-10-02 23:29:44] [Iter  688/2250] R3[324/1200] | LR: 0.025766 | E: -42.229039 | E_var:     0.6140 | E_err:   0.012244
[2025-10-02 23:29:52] [Iter  689/2250] R3[326/1200] | LR: 0.025717 | E: -42.253491 | E_var:     0.5800 | E_err:   0.011900
[2025-10-02 23:30:00] [Iter  690/2250] R3[328/1200] | LR: 0.025668 | E: -42.236156 | E_var:     0.8260 | E_err:   0.014200
[2025-10-02 23:30:07] [Iter  691/2250] R3[330/1200] | LR: 0.025618 | E: -42.264719 | E_var:     0.5887 | E_err:   0.011988
[2025-10-02 23:30:15] [Iter  692/2250] R3[332/1200] | LR: 0.025568 | E: -42.267418 | E_var:     0.5774 | E_err:   0.011873
[2025-10-02 23:30:23] [Iter  693/2250] R3[334/1200] | LR: 0.025518 | E: -42.248733 | E_var:     0.5733 | E_err:   0.011831
[2025-10-02 23:30:31] [Iter  694/2250] R3[336/1200] | LR: 0.025468 | E: -42.267904 | E_var:     0.5319 | E_err:   0.011395
[2025-10-02 23:30:39] [Iter  695/2250] R3[338/1200] | LR: 0.025417 | E: -42.241019 | E_var:     0.7858 | E_err:   0.013851
[2025-10-02 23:30:47] [Iter  696/2250] R3[340/1200] | LR: 0.025367 | E: -42.262291 | E_var:     0.7178 | E_err:   0.013238
[2025-10-02 23:30:54] [Iter  697/2250] R3[342/1200] | LR: 0.025316 | E: -42.245420 | E_var:     0.5325 | E_err:   0.011402
[2025-10-02 23:31:02] [Iter  698/2250] R3[344/1200] | LR: 0.025264 | E: -42.256386 | E_var:     0.5890 | E_err:   0.011992
[2025-10-02 23:31:10] [Iter  699/2250] R3[346/1200] | LR: 0.025213 | E: -42.237893 | E_var:     0.6452 | E_err:   0.012551
[2025-10-02 23:31:18] [Iter  700/2250] R3[348/1200] | LR: 0.025161 | E: -42.230924 | E_var:     0.6632 | E_err:   0.012724
[2025-10-02 23:31:26] [Iter  701/2250] R3[350/1200] | LR: 0.025110 | E: -42.274199 | E_var:     0.6194 | E_err:   0.012297
[2025-10-02 23:31:33] [Iter  702/2250] R3[352/1200] | LR: 0.025057 | E: -42.227815 | E_var:     0.5022 | E_err:   0.011073
[2025-10-02 23:31:41] [Iter  703/2250] R3[354/1200] | LR: 0.025005 | E: -42.263427 | E_var:     0.6515 | E_err:   0.012612
[2025-10-02 23:31:49] [Iter  704/2250] R3[356/1200] | LR: 0.024953 | E: -42.244226 | E_var:     0.5276 | E_err:   0.011349
[2025-10-02 23:31:57] [Iter  705/2250] R3[358/1200] | LR: 0.024900 | E: -42.245793 | E_var:     0.6683 | E_err:   0.012773
[2025-10-02 23:32:05] [Iter  706/2250] R3[360/1200] | LR: 0.024847 | E: -42.239537 | E_var:     0.6171 | E_err:   0.012275
[2025-10-02 23:32:13] [Iter  707/2250] R3[362/1200] | LR: 0.024794 | E: -42.240660 | E_var:     0.4186 | E_err:   0.010109
[2025-10-02 23:32:20] [Iter  708/2250] R3[364/1200] | LR: 0.024741 | E: -42.262626 | E_var:     0.4769 | E_err:   0.010790
[2025-10-02 23:32:28] [Iter  709/2250] R3[366/1200] | LR: 0.024688 | E: -42.238596 | E_var:     0.6168 | E_err:   0.012271
[2025-10-02 23:32:36] [Iter  710/2250] R3[368/1200] | LR: 0.024634 | E: -42.259115 | E_var:     0.5340 | E_err:   0.011418
[2025-10-02 23:32:44] [Iter  711/2250] R3[370/1200] | LR: 0.024580 | E: -42.256977 | E_var:     0.3892 | E_err:   0.009747
[2025-10-02 23:32:52] [Iter  712/2250] R3[372/1200] | LR: 0.024526 | E: -42.258070 | E_var:     0.5607 | E_err:   0.011700
[2025-10-02 23:32:59] [Iter  713/2250] R3[374/1200] | LR: 0.024472 | E: -42.254598 | E_var:     0.5438 | E_err:   0.011523
[2025-10-02 23:33:07] [Iter  714/2250] R3[376/1200] | LR: 0.024417 | E: -42.271143 | E_var:     1.1499 | E_err:   0.016755
[2025-10-02 23:33:15] [Iter  715/2250] R3[378/1200] | LR: 0.024363 | E: -42.243197 | E_var:     0.6802 | E_err:   0.012886
[2025-10-02 23:33:23] [Iter  716/2250] R3[380/1200] | LR: 0.024308 | E: -42.266017 | E_var:     0.5176 | E_err:   0.011241
[2025-10-02 23:33:31] [Iter  717/2250] R3[382/1200] | LR: 0.024253 | E: -42.235643 | E_var:     0.7226 | E_err:   0.013282
[2025-10-02 23:33:39] [Iter  718/2250] R3[384/1200] | LR: 0.024198 | E: -42.257587 | E_var:     0.6503 | E_err:   0.012600
[2025-10-02 23:33:46] [Iter  719/2250] R3[386/1200] | LR: 0.024142 | E: -42.267102 | E_var:     0.4678 | E_err:   0.010687
[2025-10-02 23:33:54] [Iter  720/2250] R3[388/1200] | LR: 0.024087 | E: -42.252685 | E_var:     0.4517 | E_err:   0.010502
[2025-10-02 23:34:02] [Iter  721/2250] R3[390/1200] | LR: 0.024031 | E: -42.256999 | E_var:     0.5312 | E_err:   0.011388
[2025-10-02 23:34:10] [Iter  722/2250] R3[392/1200] | LR: 0.023975 | E: -42.264110 | E_var:     0.5662 | E_err:   0.011757
[2025-10-02 23:34:18] [Iter  723/2250] R3[394/1200] | LR: 0.023919 | E: -42.258012 | E_var:     0.5076 | E_err:   0.011132
[2025-10-02 23:34:25] [Iter  724/2250] R3[396/1200] | LR: 0.023863 | E: -42.263853 | E_var:     0.7368 | E_err:   0.013412
[2025-10-02 23:34:33] [Iter  725/2250] R3[398/1200] | LR: 0.023807 | E: -42.248889 | E_var:     0.5733 | E_err:   0.011831
[2025-10-02 23:34:41] [Iter  726/2250] R3[400/1200] | LR: 0.023750 | E: -42.254636 | E_var:     0.4987 | E_err:   0.011034
[2025-10-02 23:34:49] [Iter  727/2250] R3[402/1200] | LR: 0.023693 | E: -42.248464 | E_var:     0.5256 | E_err:   0.011328
[2025-10-02 23:34:57] [Iter  728/2250] R3[404/1200] | LR: 0.023636 | E: -42.244637 | E_var:     0.6054 | E_err:   0.012157
[2025-10-02 23:35:05] [Iter  729/2250] R3[406/1200] | LR: 0.023579 | E: -42.236280 | E_var:     0.4442 | E_err:   0.010414
[2025-10-02 23:35:12] [Iter  730/2250] R3[408/1200] | LR: 0.023522 | E: -42.257127 | E_var:     0.5598 | E_err:   0.011690
[2025-10-02 23:35:20] [Iter  731/2250] R3[410/1200] | LR: 0.023464 | E: -42.251333 | E_var:     0.5315 | E_err:   0.011391
[2025-10-02 23:35:28] [Iter  732/2250] R3[412/1200] | LR: 0.023407 | E: -42.247297 | E_var:     0.5924 | E_err:   0.012026
[2025-10-02 23:35:36] [Iter  733/2250] R3[414/1200] | LR: 0.023349 | E: -42.272011 | E_var:     0.5207 | E_err:   0.011275
[2025-10-02 23:35:44] [Iter  734/2250] R3[416/1200] | LR: 0.023291 | E: -42.258862 | E_var:     0.9734 | E_err:   0.015416
[2025-10-02 23:35:51] [Iter  735/2250] R3[418/1200] | LR: 0.023233 | E: -42.252218 | E_var:     0.5135 | E_err:   0.011197
[2025-10-02 23:35:59] [Iter  736/2250] R3[420/1200] | LR: 0.023175 | E: -42.270920 | E_var:     0.5625 | E_err:   0.011719
[2025-10-02 23:36:07] [Iter  737/2250] R3[422/1200] | LR: 0.023116 | E: -42.243585 | E_var:     0.6181 | E_err:   0.012284
[2025-10-02 23:36:15] [Iter  738/2250] R3[424/1200] | LR: 0.023058 | E: -42.248002 | E_var:     0.8167 | E_err:   0.014120
[2025-10-02 23:36:23] [Iter  739/2250] R3[426/1200] | LR: 0.022999 | E: -42.273367 | E_var:     0.5387 | E_err:   0.011468
[2025-10-02 23:36:31] [Iter  740/2250] R3[428/1200] | LR: 0.022940 | E: -42.257434 | E_var:     0.5272 | E_err:   0.011345
[2025-10-02 23:36:38] [Iter  741/2250] R3[430/1200] | LR: 0.022881 | E: -42.240311 | E_var:     0.4821 | E_err:   0.010849
[2025-10-02 23:36:46] [Iter  742/2250] R3[432/1200] | LR: 0.022822 | E: -42.250098 | E_var:     0.4879 | E_err:   0.010914
[2025-10-02 23:36:54] [Iter  743/2250] R3[434/1200] | LR: 0.022763 | E: -42.272506 | E_var:     0.4227 | E_err:   0.010159
[2025-10-02 23:37:02] [Iter  744/2250] R3[436/1200] | LR: 0.022704 | E: -42.251802 | E_var:     0.4710 | E_err:   0.010724
[2025-10-02 23:37:10] [Iter  745/2250] R3[438/1200] | LR: 0.022644 | E: -42.252938 | E_var:     0.5014 | E_err:   0.011064
[2025-10-02 23:37:17] [Iter  746/2250] R3[440/1200] | LR: 0.022584 | E: -42.270910 | E_var:     0.4579 | E_err:   0.010573
[2025-10-02 23:37:25] [Iter  747/2250] R3[442/1200] | LR: 0.022524 | E: -42.264225 | E_var:     0.6260 | E_err:   0.012362
[2025-10-02 23:37:33] [Iter  748/2250] R3[444/1200] | LR: 0.022464 | E: -42.263678 | E_var:     0.6152 | E_err:   0.012255
[2025-10-02 23:37:41] [Iter  749/2250] R3[446/1200] | LR: 0.022404 | E: -42.246031 | E_var:     0.5027 | E_err:   0.011079
[2025-10-02 23:37:49] [Iter  750/2250] R3[448/1200] | LR: 0.022344 | E: -42.255943 | E_var:     0.5606 | E_err:   0.011699
[2025-10-02 23:37:57] [Iter  751/2250] R3[450/1200] | LR: 0.022284 | E: -42.257227 | E_var:     0.5872 | E_err:   0.011974
[2025-10-02 23:38:04] [Iter  752/2250] R3[452/1200] | LR: 0.022223 | E: -42.264607 | E_var:     0.4644 | E_err:   0.010648
[2025-10-02 23:38:12] [Iter  753/2250] R3[454/1200] | LR: 0.022162 | E: -42.250621 | E_var:     0.7993 | E_err:   0.013969
[2025-10-02 23:38:20] [Iter  754/2250] R3[456/1200] | LR: 0.022102 | E: -42.265300 | E_var:     0.6901 | E_err:   0.012980
[2025-10-02 23:38:28] [Iter  755/2250] R3[458/1200] | LR: 0.022041 | E: -42.255766 | E_var:     0.5005 | E_err:   0.011054
[2025-10-02 23:38:36] [Iter  756/2250] R3[460/1200] | LR: 0.021980 | E: -42.266784 | E_var:     0.5141 | E_err:   0.011203
[2025-10-02 23:38:43] [Iter  757/2250] R3[462/1200] | LR: 0.021918 | E: -42.244470 | E_var:     0.5068 | E_err:   0.011124
[2025-10-02 23:38:51] [Iter  758/2250] R3[464/1200] | LR: 0.021857 | E: -42.238376 | E_var:     0.5224 | E_err:   0.011293
[2025-10-02 23:38:59] [Iter  759/2250] R3[466/1200] | LR: 0.021796 | E: -42.245500 | E_var:     0.6872 | E_err:   0.012953
[2025-10-02 23:39:07] [Iter  760/2250] R3[468/1200] | LR: 0.021734 | E: -42.261965 | E_var:     0.5497 | E_err:   0.011584
[2025-10-02 23:39:15] [Iter  761/2250] R3[470/1200] | LR: 0.021673 | E: -42.251818 | E_var:     0.5280 | E_err:   0.011354
[2025-10-02 23:39:23] [Iter  762/2250] R3[472/1200] | LR: 0.021611 | E: -42.256836 | E_var:     0.5010 | E_err:   0.011060
[2025-10-02 23:39:30] [Iter  763/2250] R3[474/1200] | LR: 0.021549 | E: -42.254470 | E_var:     0.6054 | E_err:   0.012157
[2025-10-02 23:39:38] [Iter  764/2250] R3[476/1200] | LR: 0.021487 | E: -42.285043 | E_var:     0.6924 | E_err:   0.013001
[2025-10-02 23:39:46] [Iter  765/2250] R3[478/1200] | LR: 0.021425 | E: -42.260603 | E_var:     0.8991 | E_err:   0.014816
[2025-10-02 23:39:54] [Iter  766/2250] R3[480/1200] | LR: 0.021363 | E: -42.253188 | E_var:     0.4636 | E_err:   0.010639
[2025-10-02 23:40:02] [Iter  767/2250] R3[482/1200] | LR: 0.021300 | E: -42.246739 | E_var:     0.6542 | E_err:   0.012638
[2025-10-02 23:40:09] [Iter  768/2250] R3[484/1200] | LR: 0.021238 | E: -42.271171 | E_var:     0.5891 | E_err:   0.011992
[2025-10-02 23:40:17] [Iter  769/2250] R3[486/1200] | LR: 0.021176 | E: -42.248933 | E_var:     0.8536 | E_err:   0.014436
[2025-10-02 23:40:25] [Iter  770/2250] R3[488/1200] | LR: 0.021113 | E: -42.247385 | E_var:     0.5240 | E_err:   0.011310
[2025-10-02 23:40:33] [Iter  771/2250] R3[490/1200] | LR: 0.021050 | E: -42.268458 | E_var:     0.5313 | E_err:   0.011389
[2025-10-02 23:40:41] [Iter  772/2250] R3[492/1200] | LR: 0.020987 | E: -42.251736 | E_var:     0.5274 | E_err:   0.011347
[2025-10-02 23:40:49] [Iter  773/2250] R3[494/1200] | LR: 0.020924 | E: -42.263522 | E_var:     0.8276 | E_err:   0.014214
[2025-10-02 23:40:56] [Iter  774/2250] R3[496/1200] | LR: 0.020861 | E: -42.261332 | E_var:     0.5619 | E_err:   0.011713
[2025-10-02 23:41:04] [Iter  775/2250] R3[498/1200] | LR: 0.020798 | E: -42.241284 | E_var:     0.5949 | E_err:   0.012052
[2025-10-02 23:41:12] [Iter  776/2250] R3[500/1200] | LR: 0.020735 | E: -42.243558 | E_var:     0.5200 | E_err:   0.011267
[2025-10-02 23:41:20] [Iter  777/2250] R3[502/1200] | LR: 0.020672 | E: -42.246363 | E_var:     0.6138 | E_err:   0.012241
[2025-10-02 23:41:28] [Iter  778/2250] R3[504/1200] | LR: 0.020609 | E: -42.267368 | E_var:     0.6026 | E_err:   0.012129
[2025-10-02 23:41:36] [Iter  779/2250] R3[506/1200] | LR: 0.020545 | E: -42.253816 | E_var:     0.5143 | E_err:   0.011206
[2025-10-02 23:41:43] [Iter  780/2250] R3[508/1200] | LR: 0.020482 | E: -42.258089 | E_var:     0.5459 | E_err:   0.011544
[2025-10-02 23:41:51] [Iter  781/2250] R3[510/1200] | LR: 0.020418 | E: -42.260929 | E_var:     0.6907 | E_err:   0.012985
[2025-10-02 23:41:59] [Iter  782/2250] R3[512/1200] | LR: 0.020354 | E: -42.235469 | E_var:     0.6787 | E_err:   0.012873
[2025-10-02 23:42:07] [Iter  783/2250] R3[514/1200] | LR: 0.020291 | E: -42.246044 | E_var:     0.9970 | E_err:   0.015602
[2025-10-02 23:42:15] [Iter  784/2250] R3[516/1200] | LR: 0.020227 | E: -42.255372 | E_var:     0.5753 | E_err:   0.011852
[2025-10-02 23:42:22] [Iter  785/2250] R3[518/1200] | LR: 0.020163 | E: -42.248812 | E_var:     0.6168 | E_err:   0.012272
[2025-10-02 23:42:30] [Iter  786/2250] R3[520/1200] | LR: 0.020099 | E: -42.286525 | E_var:     0.5574 | E_err:   0.011665
[2025-10-02 23:42:38] [Iter  787/2250] R3[522/1200] | LR: 0.020035 | E: -42.259876 | E_var:     0.5196 | E_err:   0.011263
[2025-10-02 23:42:46] [Iter  788/2250] R3[524/1200] | LR: 0.019971 | E: -42.269952 | E_var:     0.5864 | E_err:   0.011965
[2025-10-02 23:42:54] [Iter  789/2250] R3[526/1200] | LR: 0.019907 | E: -42.230155 | E_var:     0.6756 | E_err:   0.012843
[2025-10-02 23:43:02] [Iter  790/2250] R3[528/1200] | LR: 0.019842 | E: -42.268140 | E_var:     0.5992 | E_err:   0.012095
[2025-10-02 23:43:09] [Iter  791/2250] R3[530/1200] | LR: 0.019778 | E: -42.255633 | E_var:     0.5961 | E_err:   0.012064
[2025-10-02 23:43:17] [Iter  792/2250] R3[532/1200] | LR: 0.019714 | E: -42.255341 | E_var:     0.6204 | E_err:   0.012307
[2025-10-02 23:43:25] [Iter  793/2250] R3[534/1200] | LR: 0.019649 | E: -42.260410 | E_var:     0.4871 | E_err:   0.010905
[2025-10-02 23:43:33] [Iter  794/2250] R3[536/1200] | LR: 0.019585 | E: -42.247127 | E_var:     0.4796 | E_err:   0.010820
[2025-10-02 23:43:41] [Iter  795/2250] R3[538/1200] | LR: 0.019520 | E: -42.242827 | E_var:     0.5832 | E_err:   0.011932
[2025-10-02 23:43:48] [Iter  796/2250] R3[540/1200] | LR: 0.019455 | E: -42.279606 | E_var:     0.5376 | E_err:   0.011457
[2025-10-02 23:43:56] [Iter  797/2250] R3[542/1200] | LR: 0.019391 | E: -42.235728 | E_var:     1.0843 | E_err:   0.016270
[2025-10-02 23:44:04] [Iter  798/2250] R3[544/1200] | LR: 0.019326 | E: -42.260462 | E_var:     0.4664 | E_err:   0.010671
[2025-10-02 23:44:12] [Iter  799/2250] R3[546/1200] | LR: 0.019261 | E: -42.260577 | E_var:     0.4849 | E_err:   0.010880
[2025-10-02 23:44:20] [Iter  800/2250] R3[548/1200] | LR: 0.019196 | E: -42.279694 | E_var:     0.6398 | E_err:   0.012498
[2025-10-02 23:44:20] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-02 23:44:28] [Iter  801/2250] R3[550/1200] | LR: 0.019132 | E: -42.261790 | E_var:     0.5157 | E_err:   0.011221
[2025-10-02 23:44:35] [Iter  802/2250] R3[552/1200] | LR: 0.019067 | E: -42.266853 | E_var:     0.5826 | E_err:   0.011926
[2025-10-02 23:44:43] [Iter  803/2250] R3[554/1200] | LR: 0.019002 | E: -42.259386 | E_var:     0.4341 | E_err:   0.010295
[2025-10-02 23:44:51] [Iter  804/2250] R3[556/1200] | LR: 0.018937 | E: -42.261850 | E_var:     0.4707 | E_err:   0.010720
[2025-10-02 23:44:59] [Iter  805/2250] R3[558/1200] | LR: 0.018872 | E: -42.257031 | E_var:     0.4471 | E_err:   0.010448
[2025-10-02 23:45:07] [Iter  806/2250] R3[560/1200] | LR: 0.018807 | E: -42.236849 | E_var:     0.5904 | E_err:   0.012006
[2025-10-02 23:45:15] [Iter  807/2250] R3[562/1200] | LR: 0.018741 | E: -42.279035 | E_var:     0.4947 | E_err:   0.010990
[2025-10-02 23:45:22] [Iter  808/2250] R3[564/1200] | LR: 0.018676 | E: -42.286874 | E_var:     0.6085 | E_err:   0.012189
[2025-10-02 23:45:30] [Iter  809/2250] R3[566/1200] | LR: 0.018611 | E: -42.253375 | E_var:     0.5776 | E_err:   0.011875
[2025-10-02 23:45:38] [Iter  810/2250] R3[568/1200] | LR: 0.018546 | E: -42.271924 | E_var:     0.5098 | E_err:   0.011156
[2025-10-02 23:45:46] [Iter  811/2250] R3[570/1200] | LR: 0.018481 | E: -42.265758 | E_var:     0.5197 | E_err:   0.011264
[2025-10-02 23:45:54] [Iter  812/2250] R3[572/1200] | LR: 0.018415 | E: -42.276506 | E_var:     0.5228 | E_err:   0.011298
[2025-10-02 23:46:01] [Iter  813/2250] R3[574/1200] | LR: 0.018350 | E: -42.272565 | E_var:     0.6819 | E_err:   0.012903
[2025-10-02 23:46:09] [Iter  814/2250] R3[576/1200] | LR: 0.018285 | E: -42.252306 | E_var:     0.6268 | E_err:   0.012370
[2025-10-02 23:46:17] [Iter  815/2250] R3[578/1200] | LR: 0.018220 | E: -42.273427 | E_var:     0.5265 | E_err:   0.011337
[2025-10-02 23:46:25] [Iter  816/2250] R3[580/1200] | LR: 0.018154 | E: -42.267479 | E_var:     0.5338 | E_err:   0.011415
[2025-10-02 23:46:33] [Iter  817/2250] R3[582/1200] | LR: 0.018089 | E: -42.255783 | E_var:     0.4965 | E_err:   0.011009
[2025-10-02 23:46:41] [Iter  818/2250] R3[584/1200] | LR: 0.018023 | E: -42.245661 | E_var:     0.5528 | E_err:   0.011617
[2025-10-02 23:46:48] [Iter  819/2250] R3[586/1200] | LR: 0.017958 | E: -42.248342 | E_var:     0.7817 | E_err:   0.013815
[2025-10-02 23:46:56] [Iter  820/2250] R3[588/1200] | LR: 0.017893 | E: -42.275076 | E_var:     0.4745 | E_err:   0.010763
[2025-10-02 23:47:04] [Iter  821/2250] R3[590/1200] | LR: 0.017827 | E: -42.271687 | E_var:     0.4907 | E_err:   0.010946
[2025-10-02 23:47:12] [Iter  822/2250] R3[592/1200] | LR: 0.017762 | E: -42.259177 | E_var:     0.4593 | E_err:   0.010589
[2025-10-02 23:47:20] [Iter  823/2250] R3[594/1200] | LR: 0.017696 | E: -42.279891 | E_var:     0.5645 | E_err:   0.011739
[2025-10-02 23:47:27] [Iter  824/2250] R3[596/1200] | LR: 0.017631 | E: -42.259885 | E_var:     0.5486 | E_err:   0.011573
[2025-10-02 23:47:35] [Iter  825/2250] R3[598/1200] | LR: 0.017565 | E: -42.275176 | E_var:     0.5507 | E_err:   0.011595
[2025-10-02 23:47:43] [Iter  826/2250] R3[600/1200] | LR: 0.017500 | E: -42.262413 | E_var:     0.5088 | E_err:   0.011145
[2025-10-02 23:47:51] [Iter  827/2250] R3[602/1200] | LR: 0.017435 | E: -42.281910 | E_var:     0.5342 | E_err:   0.011420
[2025-10-02 23:47:59] [Iter  828/2250] R3[604/1200] | LR: 0.017369 | E: -42.277583 | E_var:     0.4969 | E_err:   0.011015
[2025-10-02 23:48:07] [Iter  829/2250] R3[606/1200] | LR: 0.017304 | E: -42.252468 | E_var:     0.4322 | E_err:   0.010273
[2025-10-02 23:48:14] [Iter  830/2250] R3[608/1200] | LR: 0.017238 | E: -42.269849 | E_var:     0.6187 | E_err:   0.012291
[2025-10-02 23:48:22] [Iter  831/2250] R3[610/1200] | LR: 0.017173 | E: -42.245592 | E_var:     0.6522 | E_err:   0.012619
[2025-10-02 23:48:30] [Iter  832/2250] R3[612/1200] | LR: 0.017107 | E: -42.265847 | E_var:     0.4517 | E_err:   0.010502
[2025-10-02 23:48:38] [Iter  833/2250] R3[614/1200] | LR: 0.017042 | E: -42.282047 | E_var:     0.4650 | E_err:   0.010655
[2025-10-02 23:48:46] [Iter  834/2250] R3[616/1200] | LR: 0.016977 | E: -42.275416 | E_var:     0.5544 | E_err:   0.011634
[2025-10-02 23:48:53] [Iter  835/2250] R3[618/1200] | LR: 0.016911 | E: -42.267980 | E_var:     0.6072 | E_err:   0.012176
[2025-10-02 23:49:01] [Iter  836/2250] R3[620/1200] | LR: 0.016846 | E: -42.258880 | E_var:     1.3670 | E_err:   0.018269
[2025-10-02 23:49:09] [Iter  837/2250] R3[622/1200] | LR: 0.016780 | E: -42.266204 | E_var:     0.5176 | E_err:   0.011242
[2025-10-02 23:49:17] [Iter  838/2250] R3[624/1200] | LR: 0.016715 | E: -42.258881 | E_var:     0.4768 | E_err:   0.010789
[2025-10-02 23:49:25] [Iter  839/2250] R3[626/1200] | LR: 0.016650 | E: -42.253502 | E_var:     0.5041 | E_err:   0.011094
[2025-10-02 23:49:33] [Iter  840/2250] R3[628/1200] | LR: 0.016585 | E: -42.262466 | E_var:     0.5274 | E_err:   0.011347
[2025-10-02 23:49:40] [Iter  841/2250] R3[630/1200] | LR: 0.016519 | E: -42.253979 | E_var:     0.5663 | E_err:   0.011759
[2025-10-02 23:49:48] [Iter  842/2250] R3[632/1200] | LR: 0.016454 | E: -42.254645 | E_var:     0.5966 | E_err:   0.012068
[2025-10-02 23:49:56] [Iter  843/2250] R3[634/1200] | LR: 0.016389 | E: -42.237542 | E_var:     0.6392 | E_err:   0.012493
[2025-10-02 23:50:04] [Iter  844/2250] R3[636/1200] | LR: 0.016324 | E: -42.288852 | E_var:     0.6591 | E_err:   0.012685
[2025-10-02 23:50:12] [Iter  845/2250] R3[638/1200] | LR: 0.016259 | E: -42.266514 | E_var:     0.4620 | E_err:   0.010620
[2025-10-02 23:50:20] [Iter  846/2250] R3[640/1200] | LR: 0.016193 | E: -42.280974 | E_var:     0.5631 | E_err:   0.011725
[2025-10-02 23:50:27] [Iter  847/2250] R3[642/1200] | LR: 0.016128 | E: -42.238959 | E_var:     0.6258 | E_err:   0.012361
[2025-10-02 23:50:35] [Iter  848/2250] R3[644/1200] | LR: 0.016063 | E: -42.248111 | E_var:     0.6402 | E_err:   0.012502
[2025-10-02 23:50:43] [Iter  849/2250] R3[646/1200] | LR: 0.015998 | E: -42.276029 | E_var:     0.5328 | E_err:   0.011406
[2025-10-02 23:50:51] [Iter  850/2250] R3[648/1200] | LR: 0.015933 | E: -42.283217 | E_var:     0.5235 | E_err:   0.011305
[2025-10-02 23:50:59] [Iter  851/2250] R3[650/1200] | LR: 0.015868 | E: -42.242266 | E_var:     1.0167 | E_err:   0.015755
[2025-10-02 23:51:06] [Iter  852/2250] R3[652/1200] | LR: 0.015804 | E: -42.278190 | E_var:     0.9272 | E_err:   0.015045
[2025-10-02 23:51:14] [Iter  853/2250] R3[654/1200] | LR: 0.015739 | E: -42.278381 | E_var:     0.4180 | E_err:   0.010102
[2025-10-02 23:51:22] [Iter  854/2250] R3[656/1200] | LR: 0.015674 | E: -42.252277 | E_var:     0.5140 | E_err:   0.011202
[2025-10-02 23:51:30] [Iter  855/2250] R3[658/1200] | LR: 0.015609 | E: -42.272955 | E_var:     0.6531 | E_err:   0.012627
[2025-10-02 23:51:38] [Iter  856/2250] R3[660/1200] | LR: 0.015545 | E: -42.259283 | E_var:     0.4450 | E_err:   0.010423
[2025-10-02 23:51:46] [Iter  857/2250] R3[662/1200] | LR: 0.015480 | E: -42.247757 | E_var:     0.5131 | E_err:   0.011192
[2025-10-02 23:51:53] [Iter  858/2250] R3[664/1200] | LR: 0.015415 | E: -42.268149 | E_var:     0.4681 | E_err:   0.010690
[2025-10-02 23:52:01] [Iter  859/2250] R3[666/1200] | LR: 0.015351 | E: -42.274740 | E_var:     0.6357 | E_err:   0.012458
[2025-10-02 23:52:09] [Iter  860/2250] R3[668/1200] | LR: 0.015286 | E: -42.267447 | E_var:     0.4902 | E_err:   0.010939
[2025-10-02 23:52:17] [Iter  861/2250] R3[670/1200] | LR: 0.015222 | E: -42.263606 | E_var:     0.4811 | E_err:   0.010838
[2025-10-02 23:52:25] [Iter  862/2250] R3[672/1200] | LR: 0.015158 | E: -42.275916 | E_var:     0.4592 | E_err:   0.010589
[2025-10-02 23:52:32] [Iter  863/2250] R3[674/1200] | LR: 0.015093 | E: -42.264838 | E_var:     0.5369 | E_err:   0.011449
[2025-10-02 23:52:40] [Iter  864/2250] R3[676/1200] | LR: 0.015029 | E: -42.260886 | E_var:     0.5696 | E_err:   0.011793
[2025-10-02 23:52:48] [Iter  865/2250] R3[678/1200] | LR: 0.014965 | E: -42.270495 | E_var:     0.4562 | E_err:   0.010553
[2025-10-02 23:52:56] [Iter  866/2250] R3[680/1200] | LR: 0.014901 | E: -42.285427 | E_var:     0.5518 | E_err:   0.011607
[2025-10-02 23:53:04] [Iter  867/2250] R3[682/1200] | LR: 0.014837 | E: -42.258465 | E_var:     0.4620 | E_err:   0.010620
[2025-10-02 23:53:12] [Iter  868/2250] R3[684/1200] | LR: 0.014773 | E: -42.259584 | E_var:     0.6534 | E_err:   0.012630
[2025-10-02 23:53:19] [Iter  869/2250] R3[686/1200] | LR: 0.014709 | E: -42.274162 | E_var:     0.4551 | E_err:   0.010541
[2025-10-02 23:53:27] [Iter  870/2250] R3[688/1200] | LR: 0.014646 | E: -42.249476 | E_var:     0.4425 | E_err:   0.010394
[2025-10-02 23:53:35] [Iter  871/2250] R3[690/1200] | LR: 0.014582 | E: -42.252390 | E_var:     0.5820 | E_err:   0.011921
[2025-10-02 23:53:43] [Iter  872/2250] R3[692/1200] | LR: 0.014518 | E: -42.267292 | E_var:     0.5935 | E_err:   0.012038
[2025-10-02 23:53:51] [Iter  873/2250] R3[694/1200] | LR: 0.014455 | E: -42.272009 | E_var:     0.4313 | E_err:   0.010262
[2025-10-02 23:53:58] [Iter  874/2250] R3[696/1200] | LR: 0.014391 | E: -42.280762 | E_var:     0.4823 | E_err:   0.010851
[2025-10-02 23:54:06] [Iter  875/2250] R3[698/1200] | LR: 0.014328 | E: -42.275276 | E_var:     0.4805 | E_err:   0.010831
[2025-10-02 23:54:14] [Iter  876/2250] R3[700/1200] | LR: 0.014265 | E: -42.266921 | E_var:     0.4991 | E_err:   0.011039
[2025-10-02 23:54:22] [Iter  877/2250] R3[702/1200] | LR: 0.014202 | E: -42.260162 | E_var:     0.4448 | E_err:   0.010421
[2025-10-02 23:54:30] [Iter  878/2250] R3[704/1200] | LR: 0.014139 | E: -42.258575 | E_var:     0.5613 | E_err:   0.011706
[2025-10-02 23:54:38] [Iter  879/2250] R3[706/1200] | LR: 0.014076 | E: -42.277609 | E_var:     0.5090 | E_err:   0.011148
[2025-10-02 23:54:45] [Iter  880/2250] R3[708/1200] | LR: 0.014013 | E: -42.280830 | E_var:     0.6441 | E_err:   0.012540
[2025-10-02 23:54:53] [Iter  881/2250] R3[710/1200] | LR: 0.013950 | E: -42.251876 | E_var:     0.4434 | E_err:   0.010404
[2025-10-02 23:55:01] [Iter  882/2250] R3[712/1200] | LR: 0.013887 | E: -42.247614 | E_var:     0.5229 | E_err:   0.011299
[2025-10-02 23:55:09] [Iter  883/2250] R3[714/1200] | LR: 0.013824 | E: -42.274397 | E_var:     0.6416 | E_err:   0.012516
[2025-10-02 23:55:17] [Iter  884/2250] R3[716/1200] | LR: 0.013762 | E: -42.261875 | E_var:     0.4499 | E_err:   0.010481
[2025-10-02 23:55:24] [Iter  885/2250] R3[718/1200] | LR: 0.013700 | E: -42.265041 | E_var:     1.3744 | E_err:   0.018318
[2025-10-02 23:55:32] [Iter  886/2250] R3[720/1200] | LR: 0.013637 | E: -42.266297 | E_var:     0.4900 | E_err:   0.010938
[2025-10-02 23:55:40] [Iter  887/2250] R3[722/1200] | LR: 0.013575 | E: -42.266351 | E_var:     0.5017 | E_err:   0.011068
[2025-10-02 23:55:48] [Iter  888/2250] R3[724/1200] | LR: 0.013513 | E: -42.278571 | E_var:     0.4115 | E_err:   0.010024
[2025-10-02 23:55:56] [Iter  889/2250] R3[726/1200] | LR: 0.013451 | E: -42.261888 | E_var:     0.4337 | E_err:   0.010291
[2025-10-02 23:56:04] [Iter  890/2250] R3[728/1200] | LR: 0.013389 | E: -42.263046 | E_var:     0.5045 | E_err:   0.011098
[2025-10-02 23:56:11] [Iter  891/2250] R3[730/1200] | LR: 0.013327 | E: -42.261369 | E_var:     0.4928 | E_err:   0.010968
[2025-10-02 23:56:19] [Iter  892/2250] R3[732/1200] | LR: 0.013266 | E: -42.257372 | E_var:     0.7788 | E_err:   0.013789
[2025-10-02 23:56:27] [Iter  893/2250] R3[734/1200] | LR: 0.013204 | E: -42.259757 | E_var:     0.5885 | E_err:   0.011986
[2025-10-02 23:56:35] [Iter  894/2250] R3[736/1200] | LR: 0.013143 | E: -42.273624 | E_var:     0.4893 | E_err:   0.010930
[2025-10-02 23:56:43] [Iter  895/2250] R3[738/1200] | LR: 0.013082 | E: -42.255638 | E_var:     0.4717 | E_err:   0.010731
[2025-10-02 23:56:51] [Iter  896/2250] R3[740/1200] | LR: 0.013020 | E: -42.267899 | E_var:     0.5509 | E_err:   0.011597
[2025-10-02 23:56:58] [Iter  897/2250] R3[742/1200] | LR: 0.012959 | E: -42.258916 | E_var:     0.7357 | E_err:   0.013402
[2025-10-02 23:57:06] [Iter  898/2250] R3[744/1200] | LR: 0.012898 | E: -42.271999 | E_var:     0.6191 | E_err:   0.012295
[2025-10-02 23:57:14] [Iter  899/2250] R3[746/1200] | LR: 0.012838 | E: -42.275951 | E_var:     0.5486 | E_err:   0.011573
[2025-10-02 23:57:22] [Iter  900/2250] R3[748/1200] | LR: 0.012777 | E: -42.255646 | E_var:     1.0080 | E_err:   0.015688
[2025-10-02 23:57:30] [Iter  901/2250] R3[750/1200] | LR: 0.012716 | E: -42.258635 | E_var:     0.5563 | E_err:   0.011654
[2025-10-02 23:57:37] [Iter  902/2250] R3[752/1200] | LR: 0.012656 | E: -42.280019 | E_var:     0.5071 | E_err:   0.011127
[2025-10-02 23:57:45] [Iter  903/2250] R3[754/1200] | LR: 0.012596 | E: -42.252980 | E_var:     0.6154 | E_err:   0.012257
[2025-10-02 23:57:53] [Iter  904/2250] R3[756/1200] | LR: 0.012536 | E: -42.250382 | E_var:     0.5049 | E_err:   0.011102
[2025-10-02 23:58:01] [Iter  905/2250] R3[758/1200] | LR: 0.012476 | E: -42.257345 | E_var:     0.5975 | E_err:   0.012077
[2025-10-02 23:58:09] [Iter  906/2250] R3[760/1200] | LR: 0.012416 | E: -42.258697 | E_var:     0.6561 | E_err:   0.012657
[2025-10-02 23:58:17] [Iter  907/2250] R3[762/1200] | LR: 0.012356 | E: -42.272915 | E_var:     0.4857 | E_err:   0.010889
[2025-10-02 23:58:24] [Iter  908/2250] R3[764/1200] | LR: 0.012296 | E: -42.268067 | E_var:     0.7803 | E_err:   0.013802
[2025-10-02 23:58:32] [Iter  909/2250] R3[766/1200] | LR: 0.012237 | E: -42.270751 | E_var:     0.5940 | E_err:   0.012042
[2025-10-02 23:58:40] [Iter  910/2250] R3[768/1200] | LR: 0.012178 | E: -42.272262 | E_var:     0.5171 | E_err:   0.011236
[2025-10-02 23:58:48] [Iter  911/2250] R3[770/1200] | LR: 0.012119 | E: -42.269586 | E_var:     0.6352 | E_err:   0.012453
[2025-10-02 23:58:56] [Iter  912/2250] R3[772/1200] | LR: 0.012060 | E: -42.280591 | E_var:     0.5403 | E_err:   0.011485
[2025-10-02 23:59:03] [Iter  913/2250] R3[774/1200] | LR: 0.012001 | E: -42.278337 | E_var:     0.4131 | E_err:   0.010042
[2025-10-02 23:59:11] [Iter  914/2250] R3[776/1200] | LR: 0.011942 | E: -42.268789 | E_var:     0.4031 | E_err:   0.009920
[2025-10-02 23:59:19] [Iter  915/2250] R3[778/1200] | LR: 0.011884 | E: -42.261990 | E_var:     0.4940 | E_err:   0.010982
[2025-10-02 23:59:27] [Iter  916/2250] R3[780/1200] | LR: 0.011825 | E: -42.265793 | E_var:     0.6696 | E_err:   0.012786
[2025-10-02 23:59:35] [Iter  917/2250] R3[782/1200] | LR: 0.011767 | E: -42.241788 | E_var:     0.5516 | E_err:   0.011605
[2025-10-02 23:59:43] [Iter  918/2250] R3[784/1200] | LR: 0.011709 | E: -42.290192 | E_var:     0.6279 | E_err:   0.012381
[2025-10-02 23:59:50] [Iter  919/2250] R3[786/1200] | LR: 0.011651 | E: -42.255630 | E_var:     0.4538 | E_err:   0.010526
[2025-10-02 23:59:58] [Iter  920/2250] R3[788/1200] | LR: 0.011593 | E: -42.263606 | E_var:     0.4918 | E_err:   0.010957
[2025-10-03 00:00:06] [Iter  921/2250] R3[790/1200] | LR: 0.011536 | E: -42.274616 | E_var:     0.4561 | E_err:   0.010552
[2025-10-03 00:00:14] [Iter  922/2250] R3[792/1200] | LR: 0.011478 | E: -42.266376 | E_var:     0.6581 | E_err:   0.012675
[2025-10-03 00:00:22] [Iter  923/2250] R3[794/1200] | LR: 0.011421 | E: -42.266254 | E_var:     0.5174 | E_err:   0.011239
[2025-10-03 00:00:29] [Iter  924/2250] R3[796/1200] | LR: 0.011364 | E: -42.263269 | E_var:     0.5983 | E_err:   0.012086
[2025-10-03 00:00:37] [Iter  925/2250] R3[798/1200] | LR: 0.011307 | E: -42.251144 | E_var:     0.5694 | E_err:   0.011790
[2025-10-03 00:00:45] [Iter  926/2250] R3[800/1200] | LR: 0.011250 | E: -42.298358 | E_var:     0.5569 | E_err:   0.011661
[2025-10-03 00:00:53] [Iter  927/2250] R3[802/1200] | LR: 0.011193 | E: -42.280396 | E_var:     0.4649 | E_err:   0.010654
[2025-10-03 00:01:01] [Iter  928/2250] R3[804/1200] | LR: 0.011137 | E: -42.274466 | E_var:     0.5580 | E_err:   0.011672
[2025-10-03 00:01:09] [Iter  929/2250] R3[806/1200] | LR: 0.011081 | E: -42.246785 | E_var:     0.6617 | E_err:   0.012710
[2025-10-03 00:01:16] [Iter  930/2250] R3[808/1200] | LR: 0.011025 | E: -42.276530 | E_var:     0.4386 | E_err:   0.010348
[2025-10-03 00:01:24] [Iter  931/2250] R3[810/1200] | LR: 0.010969 | E: -42.271328 | E_var:     0.8164 | E_err:   0.014118
[2025-10-03 00:01:32] [Iter  932/2250] R3[812/1200] | LR: 0.010913 | E: -42.255579 | E_var:     0.4593 | E_err:   0.010589
[2025-10-03 00:01:40] [Iter  933/2250] R3[814/1200] | LR: 0.010858 | E: -42.287941 | E_var:     0.6262 | E_err:   0.012365
[2025-10-03 00:01:48] [Iter  934/2250] R3[816/1200] | LR: 0.010802 | E: -42.283897 | E_var:     0.5761 | E_err:   0.011859
[2025-10-03 00:01:56] [Iter  935/2250] R3[818/1200] | LR: 0.010747 | E: -42.286088 | E_var:     1.3941 | E_err:   0.018449
[2025-10-03 00:02:03] [Iter  936/2250] R3[820/1200] | LR: 0.010692 | E: -42.276108 | E_var:     0.6192 | E_err:   0.012296
[2025-10-03 00:02:11] [Iter  937/2250] R3[822/1200] | LR: 0.010637 | E: -42.259564 | E_var:     0.4864 | E_err:   0.010898
[2025-10-03 00:02:19] [Iter  938/2250] R3[824/1200] | LR: 0.010583 | E: -42.301983 | E_var:     0.5095 | E_err:   0.011153
[2025-10-03 00:02:27] [Iter  939/2250] R3[826/1200] | LR: 0.010528 | E: -42.270366 | E_var:     0.5941 | E_err:   0.012043
[2025-10-03 00:02:35] [Iter  940/2250] R3[828/1200] | LR: 0.010474 | E: -42.287391 | E_var:     1.2063 | E_err:   0.017162
[2025-10-03 00:02:42] [Iter  941/2250] R3[830/1200] | LR: 0.010420 | E: -42.269846 | E_var:     0.5032 | E_err:   0.011084
[2025-10-03 00:02:50] [Iter  942/2250] R3[832/1200] | LR: 0.010366 | E: -42.278740 | E_var:     0.4778 | E_err:   0.010801
[2025-10-03 00:02:58] [Iter  943/2250] R3[834/1200] | LR: 0.010312 | E: -42.287466 | E_var:     0.5566 | E_err:   0.011657
[2025-10-03 00:03:06] [Iter  944/2250] R3[836/1200] | LR: 0.010259 | E: -42.288033 | E_var:     0.4559 | E_err:   0.010551
[2025-10-03 00:03:14] [Iter  945/2250] R3[838/1200] | LR: 0.010206 | E: -42.252666 | E_var:     0.8760 | E_err:   0.014625
[2025-10-03 00:03:22] [Iter  946/2250] R3[840/1200] | LR: 0.010153 | E: -42.279519 | E_var:     0.4977 | E_err:   0.011023
[2025-10-03 00:03:29] [Iter  947/2250] R3[842/1200] | LR: 0.010100 | E: -42.277082 | E_var:     0.4194 | E_err:   0.010119
[2025-10-03 00:03:37] [Iter  948/2250] R3[844/1200] | LR: 0.010047 | E: -42.281518 | E_var:     0.5091 | E_err:   0.011149
[2025-10-03 00:03:45] [Iter  949/2250] R3[846/1200] | LR: 0.009995 | E: -42.266286 | E_var:     0.5692 | E_err:   0.011789
[2025-10-03 00:03:53] [Iter  950/2250] R3[848/1200] | LR: 0.009943 | E: -42.282390 | E_var:     0.5158 | E_err:   0.011222
[2025-10-03 00:04:01] [Iter  951/2250] R3[850/1200] | LR: 0.009890 | E: -42.291103 | E_var:     0.6899 | E_err:   0.012978
[2025-10-03 00:04:09] [Iter  952/2250] R3[852/1200] | LR: 0.009839 | E: -42.261429 | E_var:     0.5139 | E_err:   0.011201
[2025-10-03 00:04:16] [Iter  953/2250] R3[854/1200] | LR: 0.009787 | E: -42.278117 | E_var:     0.6021 | E_err:   0.012125
[2025-10-03 00:04:24] [Iter  954/2250] R3[856/1200] | LR: 0.009736 | E: -42.264896 | E_var:     0.5140 | E_err:   0.011203
[2025-10-03 00:04:32] [Iter  955/2250] R3[858/1200] | LR: 0.009684 | E: -42.275448 | E_var:     0.7162 | E_err:   0.013223
[2025-10-03 00:04:40] [Iter  956/2250] R3[860/1200] | LR: 0.009633 | E: -42.280998 | E_var:     0.4179 | E_err:   0.010101
[2025-10-03 00:04:48] [Iter  957/2250] R3[862/1200] | LR: 0.009583 | E: -42.263350 | E_var:     0.9367 | E_err:   0.015123
[2025-10-03 00:04:55] [Iter  958/2250] R3[864/1200] | LR: 0.009532 | E: -42.288068 | E_var:     0.4536 | E_err:   0.010523
[2025-10-03 00:05:03] [Iter  959/2250] R3[866/1200] | LR: 0.009482 | E: -42.268571 | E_var:     0.4142 | E_err:   0.010056
[2025-10-03 00:05:11] [Iter  960/2250] R3[868/1200] | LR: 0.009432 | E: -42.260868 | E_var:     0.4577 | E_err:   0.010570
[2025-10-03 00:05:19] [Iter  961/2250] R3[870/1200] | LR: 0.009382 | E: -42.273548 | E_var:     0.4649 | E_err:   0.010653
[2025-10-03 00:05:27] [Iter  962/2250] R3[872/1200] | LR: 0.009332 | E: -42.280493 | E_var:     0.4579 | E_err:   0.010573
[2025-10-03 00:05:35] [Iter  963/2250] R3[874/1200] | LR: 0.009283 | E: -42.263475 | E_var:     0.6026 | E_err:   0.012129
[2025-10-03 00:05:42] [Iter  964/2250] R3[876/1200] | LR: 0.009234 | E: -42.271932 | E_var:     0.5313 | E_err:   0.011389
[2025-10-03 00:05:50] [Iter  965/2250] R3[878/1200] | LR: 0.009185 | E: -42.256701 | E_var:     0.5289 | E_err:   0.011363
[2025-10-03 00:05:58] [Iter  966/2250] R3[880/1200] | LR: 0.009136 | E: -42.277583 | E_var:     0.4876 | E_err:   0.010911
[2025-10-03 00:06:06] [Iter  967/2250] R3[882/1200] | LR: 0.009087 | E: -42.265001 | E_var:     0.5356 | E_err:   0.011436
[2025-10-03 00:06:14] [Iter  968/2250] R3[884/1200] | LR: 0.009039 | E: -42.286715 | E_var:     0.4973 | E_err:   0.011019
[2025-10-03 00:06:22] [Iter  969/2250] R3[886/1200] | LR: 0.008991 | E: -42.276921 | E_var:     0.4190 | E_err:   0.010114
[2025-10-03 00:06:29] [Iter  970/2250] R3[888/1200] | LR: 0.008943 | E: -42.282315 | E_var:     0.4146 | E_err:   0.010061
[2025-10-03 00:06:37] [Iter  971/2250] R3[890/1200] | LR: 0.008896 | E: -42.287596 | E_var:     0.5390 | E_err:   0.011471
[2025-10-03 00:06:45] [Iter  972/2250] R3[892/1200] | LR: 0.008848 | E: -42.270145 | E_var:     0.4992 | E_err:   0.011039
[2025-10-03 00:06:53] [Iter  973/2250] R3[894/1200] | LR: 0.008801 | E: -42.274363 | E_var:     0.4830 | E_err:   0.010859
[2025-10-03 00:07:01] [Iter  974/2250] R3[896/1200] | LR: 0.008754 | E: -42.278810 | E_var:     0.5150 | E_err:   0.011213
[2025-10-03 00:07:08] [Iter  975/2250] R3[898/1200] | LR: 0.008708 | E: -42.298254 | E_var:     0.4776 | E_err:   0.010798
[2025-10-03 00:07:16] [Iter  976/2250] R3[900/1200] | LR: 0.008661 | E: -42.255040 | E_var:     0.4737 | E_err:   0.010754
[2025-10-03 00:07:24] [Iter  977/2250] R3[902/1200] | LR: 0.008615 | E: -42.267597 | E_var:     0.4579 | E_err:   0.010574
[2025-10-03 00:07:32] [Iter  978/2250] R3[904/1200] | LR: 0.008569 | E: -42.281596 | E_var:     0.4086 | E_err:   0.009988
[2025-10-03 00:07:40] [Iter  979/2250] R3[906/1200] | LR: 0.008523 | E: -42.265370 | E_var:     0.5219 | E_err:   0.011288
[2025-10-03 00:07:48] [Iter  980/2250] R3[908/1200] | LR: 0.008478 | E: -42.278363 | E_var:     0.5436 | E_err:   0.011520
[2025-10-03 00:07:55] [Iter  981/2250] R3[910/1200] | LR: 0.008433 | E: -42.288747 | E_var:     0.3774 | E_err:   0.009599
[2025-10-03 00:08:03] [Iter  982/2250] R3[912/1200] | LR: 0.008388 | E: -42.263659 | E_var:     0.4170 | E_err:   0.010089
[2025-10-03 00:08:11] [Iter  983/2250] R3[914/1200] | LR: 0.008343 | E: -42.265080 | E_var:     0.4083 | E_err:   0.009984
[2025-10-03 00:08:19] [Iter  984/2250] R3[916/1200] | LR: 0.008299 | E: -42.275943 | E_var:     0.5920 | E_err:   0.012022
[2025-10-03 00:08:27] [Iter  985/2250] R3[918/1200] | LR: 0.008255 | E: -42.252957 | E_var:     0.4532 | E_err:   0.010518
[2025-10-03 00:08:35] [Iter  986/2250] R3[920/1200] | LR: 0.008211 | E: -42.270323 | E_var:     0.5819 | E_err:   0.011920
[2025-10-03 00:08:43] [Iter  987/2250] R3[922/1200] | LR: 0.008167 | E: -42.264783 | E_var:     0.5084 | E_err:   0.011141
[2025-10-03 00:08:50] [Iter  988/2250] R3[924/1200] | LR: 0.008124 | E: -42.291717 | E_var:     0.5530 | E_err:   0.011620
[2025-10-03 00:08:58] [Iter  989/2250] R3[926/1200] | LR: 0.008080 | E: -42.257539 | E_var:     0.4095 | E_err:   0.009999
[2025-10-03 00:09:06] [Iter  990/2250] R3[928/1200] | LR: 0.008038 | E: -42.266817 | E_var:     0.6920 | E_err:   0.012998
[2025-10-03 00:09:14] [Iter  991/2250] R3[930/1200] | LR: 0.007995 | E: -42.272448 | E_var:     0.5031 | E_err:   0.011082
[2025-10-03 00:09:22] [Iter  992/2250] R3[932/1200] | LR: 0.007953 | E: -42.272856 | E_var:     0.5971 | E_err:   0.012074
[2025-10-03 00:09:29] [Iter  993/2250] R3[934/1200] | LR: 0.007910 | E: -42.264322 | E_var:     0.4590 | E_err:   0.010586
[2025-10-03 00:09:37] [Iter  994/2250] R3[936/1200] | LR: 0.007869 | E: -42.282653 | E_var:     0.9166 | E_err:   0.014959
[2025-10-03 00:09:45] [Iter  995/2250] R3[938/1200] | LR: 0.007827 | E: -42.267268 | E_var:     0.4960 | E_err:   0.011004
[2025-10-03 00:09:53] [Iter  996/2250] R3[940/1200] | LR: 0.007786 | E: -42.271792 | E_var:     0.5700 | E_err:   0.011796
[2025-10-03 00:10:01] [Iter  997/2250] R3[942/1200] | LR: 0.007745 | E: -42.295059 | E_var:     0.4475 | E_err:   0.010453
[2025-10-03 00:10:09] [Iter  998/2250] R3[944/1200] | LR: 0.007704 | E: -42.271676 | E_var:     0.5436 | E_err:   0.011521
[2025-10-03 00:10:16] [Iter  999/2250] R3[946/1200] | LR: 0.007663 | E: -42.281283 | E_var:     0.9493 | E_err:   0.015224
[2025-10-03 00:10:24] [Iter 1000/2250] R3[948/1200] | LR: 0.007623 | E: -42.294331 | E_var:     0.4489 | E_err:   0.010469
[2025-10-03 00:10:24] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-03 00:10:32] [Iter 1001/2250] R3[950/1200] | LR: 0.007583 | E: -42.266311 | E_var:     0.4569 | E_err:   0.010561
[2025-10-03 00:10:40] [Iter 1002/2250] R3[952/1200] | LR: 0.007543 | E: -42.254695 | E_var:     0.7097 | E_err:   0.013163
[2025-10-03 00:10:48] [Iter 1003/2250] R3[954/1200] | LR: 0.007504 | E: -42.256693 | E_var:     0.6271 | E_err:   0.012373
[2025-10-03 00:10:56] [Iter 1004/2250] R3[956/1200] | LR: 0.007465 | E: -42.271525 | E_var:     0.4680 | E_err:   0.010689
[2025-10-03 00:11:04] [Iter 1005/2250] R3[958/1200] | LR: 0.007426 | E: -42.277858 | E_var:     0.6092 | E_err:   0.012195
[2025-10-03 00:11:11] [Iter 1006/2250] R3[960/1200] | LR: 0.007387 | E: -42.277055 | E_var:     0.4980 | E_err:   0.011027
[2025-10-03 00:11:19] [Iter 1007/2250] R3[962/1200] | LR: 0.007349 | E: -42.293600 | E_var:     0.4391 | E_err:   0.010354
[2025-10-03 00:11:27] [Iter 1008/2250] R3[964/1200] | LR: 0.007311 | E: -42.266690 | E_var:     0.7250 | E_err:   0.013304
[2025-10-03 00:11:35] [Iter 1009/2250] R3[966/1200] | LR: 0.007273 | E: -42.248380 | E_var:     0.4984 | E_err:   0.011031
[2025-10-03 00:11:43] [Iter 1010/2250] R3[968/1200] | LR: 0.007236 | E: -42.283440 | E_var:     0.5312 | E_err:   0.011388
[2025-10-03 00:11:51] [Iter 1011/2250] R3[970/1200] | LR: 0.007198 | E: -42.267504 | E_var:     0.4731 | E_err:   0.010747
[2025-10-03 00:11:58] [Iter 1012/2250] R3[972/1200] | LR: 0.007161 | E: -42.272209 | E_var:     0.4184 | E_err:   0.010107
[2025-10-03 00:12:06] [Iter 1013/2250] R3[974/1200] | LR: 0.007125 | E: -42.258338 | E_var:     0.8115 | E_err:   0.014075
[2025-10-03 00:12:14] [Iter 1014/2250] R3[976/1200] | LR: 0.007088 | E: -42.281112 | E_var:     0.4753 | E_err:   0.010772
[2025-10-03 00:12:22] [Iter 1015/2250] R3[978/1200] | LR: 0.007052 | E: -42.299994 | E_var:     0.4636 | E_err:   0.010639
[2025-10-03 00:12:30] [Iter 1016/2250] R3[980/1200] | LR: 0.007017 | E: -42.275038 | E_var:     0.4725 | E_err:   0.010740
[2025-10-03 00:12:37] [Iter 1017/2250] R3[982/1200] | LR: 0.006981 | E: -42.284158 | E_var:     0.6051 | E_err:   0.012154
[2025-10-03 00:12:45] [Iter 1018/2250] R3[984/1200] | LR: 0.006946 | E: -42.282806 | E_var:     0.3893 | E_err:   0.009749
[2025-10-03 00:12:53] [Iter 1019/2250] R3[986/1200] | LR: 0.006911 | E: -42.278868 | E_var:     0.4928 | E_err:   0.010969
[2025-10-03 00:13:01] [Iter 1020/2250] R3[988/1200] | LR: 0.006876 | E: -42.284339 | E_var:     0.4748 | E_err:   0.010767
[2025-10-03 00:13:09] [Iter 1021/2250] R3[990/1200] | LR: 0.006842 | E: -42.276835 | E_var:     0.4818 | E_err:   0.010846
[2025-10-03 00:13:17] [Iter 1022/2250] R3[992/1200] | LR: 0.006808 | E: -42.276410 | E_var:     0.5276 | E_err:   0.011349
[2025-10-03 00:13:24] [Iter 1023/2250] R3[994/1200] | LR: 0.006774 | E: -42.270653 | E_var:     0.5825 | E_err:   0.011926
[2025-10-03 00:13:32] [Iter 1024/2250] R3[996/1200] | LR: 0.006741 | E: -42.260580 | E_var:     0.5315 | E_err:   0.011392
[2025-10-03 00:13:40] [Iter 1025/2250] R3[998/1200] | LR: 0.006708 | E: -42.296252 | E_var:     0.4124 | E_err:   0.010034
[2025-10-03 00:13:48] [Iter 1026/2250] R3[1000/1200] | LR: 0.006675 | E: -42.285657 | E_var:     0.4407 | E_err:   0.010373
[2025-10-03 00:13:56] [Iter 1027/2250] R3[1002/1200] | LR: 0.006642 | E: -42.281819 | E_var:     0.4673 | E_err:   0.010681
[2025-10-03 00:14:04] [Iter 1028/2250] R3[1004/1200] | LR: 0.006610 | E: -42.265868 | E_var:     0.4904 | E_err:   0.010942
[2025-10-03 00:14:11] [Iter 1029/2250] R3[1006/1200] | LR: 0.006578 | E: -42.288351 | E_var:     0.4574 | E_err:   0.010568
[2025-10-03 00:14:19] [Iter 1030/2250] R3[1008/1200] | LR: 0.006546 | E: -42.287414 | E_var:     0.4426 | E_err:   0.010395
[2025-10-03 00:14:27] [Iter 1031/2250] R3[1010/1200] | LR: 0.006515 | E: -42.294924 | E_var:     0.4439 | E_err:   0.010410
[2025-10-03 00:14:35] [Iter 1032/2250] R3[1012/1200] | LR: 0.006484 | E: -42.258879 | E_var:     0.5938 | E_err:   0.012040
[2025-10-03 00:14:43] [Iter 1033/2250] R3[1014/1200] | LR: 0.006453 | E: -42.281277 | E_var:     0.5589 | E_err:   0.011681
[2025-10-03 00:14:50] [Iter 1034/2250] R3[1016/1200] | LR: 0.006422 | E: -42.284321 | E_var:     0.5763 | E_err:   0.011861
[2025-10-03 00:14:58] [Iter 1035/2250] R3[1018/1200] | LR: 0.006392 | E: -42.288979 | E_var:     0.4009 | E_err:   0.009893
[2025-10-03 00:15:06] [Iter 1036/2250] R3[1020/1200] | LR: 0.006362 | E: -42.278365 | E_var:     0.4589 | E_err:   0.010585
[2025-10-03 00:15:14] [Iter 1037/2250] R3[1022/1200] | LR: 0.006333 | E: -42.278271 | E_var:     0.4225 | E_err:   0.010156
[2025-10-03 00:15:22] [Iter 1038/2250] R3[1024/1200] | LR: 0.006304 | E: -42.292097 | E_var:     0.5242 | E_err:   0.011313
[2025-10-03 00:15:30] [Iter 1039/2250] R3[1026/1200] | LR: 0.006275 | E: -42.268401 | E_var:     0.4662 | E_err:   0.010669
[2025-10-03 00:15:37] [Iter 1040/2250] R3[1028/1200] | LR: 0.006246 | E: -42.272340 | E_var:     0.4947 | E_err:   0.010990
[2025-10-03 00:15:45] [Iter 1041/2250] R3[1030/1200] | LR: 0.006218 | E: -42.266987 | E_var:     0.4328 | E_err:   0.010279
[2025-10-03 00:15:53] [Iter 1042/2250] R3[1032/1200] | LR: 0.006190 | E: -42.277082 | E_var:     0.4844 | E_err:   0.010874
[2025-10-03 00:16:01] [Iter 1043/2250] R3[1034/1200] | LR: 0.006162 | E: -42.271588 | E_var:     0.5684 | E_err:   0.011780
[2025-10-03 00:16:09] [Iter 1044/2250] R3[1036/1200] | LR: 0.006135 | E: -42.291435 | E_var:     0.5340 | E_err:   0.011418
[2025-10-03 00:16:17] [Iter 1045/2250] R3[1038/1200] | LR: 0.006107 | E: -42.286523 | E_var:     0.6594 | E_err:   0.012688
[2025-10-03 00:16:24] [Iter 1046/2250] R3[1040/1200] | LR: 0.006081 | E: -42.272780 | E_var:     0.5421 | E_err:   0.011504
[2025-10-03 00:16:32] [Iter 1047/2250] R3[1042/1200] | LR: 0.006054 | E: -42.267083 | E_var:     0.4754 | E_err:   0.010773
[2025-10-03 00:16:40] [Iter 1048/2250] R3[1044/1200] | LR: 0.006028 | E: -42.264711 | E_var:     0.5088 | E_err:   0.011146
[2025-10-03 00:16:48] [Iter 1049/2250] R3[1046/1200] | LR: 0.006002 | E: -42.280466 | E_var:     0.5173 | E_err:   0.011238
[2025-10-03 00:16:56] [Iter 1050/2250] R3[1048/1200] | LR: 0.005977 | E: -42.277941 | E_var:     0.3961 | E_err:   0.009833
[2025-10-03 00:17:04] [Iter 1051/2250] R3[1050/1200] | LR: 0.005952 | E: -42.284679 | E_var:     0.5651 | E_err:   0.011746
[2025-10-03 00:17:11] [Iter 1052/2250] R3[1052/1200] | LR: 0.005927 | E: -42.277547 | E_var:     0.4777 | E_err:   0.010799
[2025-10-03 00:17:19] [Iter 1053/2250] R3[1054/1200] | LR: 0.005902 | E: -42.283795 | E_var:     0.4161 | E_err:   0.010079
[2025-10-03 00:17:27] [Iter 1054/2250] R3[1056/1200] | LR: 0.005878 | E: -42.266181 | E_var:     0.4264 | E_err:   0.010203
[2025-10-03 00:17:35] [Iter 1055/2250] R3[1058/1200] | LR: 0.005854 | E: -42.286329 | E_var:     0.5442 | E_err:   0.011527
[2025-10-03 00:17:43] [Iter 1056/2250] R3[1060/1200] | LR: 0.005830 | E: -42.275319 | E_var:     0.4323 | E_err:   0.010273
[2025-10-03 00:17:50] [Iter 1057/2250] R3[1062/1200] | LR: 0.005807 | E: -42.279782 | E_var:     0.6195 | E_err:   0.012298
[2025-10-03 00:17:58] [Iter 1058/2250] R3[1064/1200] | LR: 0.005784 | E: -42.269502 | E_var:     0.5100 | E_err:   0.011158
[2025-10-03 00:18:06] [Iter 1059/2250] R3[1066/1200] | LR: 0.005761 | E: -42.285666 | E_var:     0.4606 | E_err:   0.010604
[2025-10-03 00:18:14] [Iter 1060/2250] R3[1068/1200] | LR: 0.005739 | E: -42.278333 | E_var:     0.5194 | E_err:   0.011260
[2025-10-03 00:18:22] [Iter 1061/2250] R3[1070/1200] | LR: 0.005717 | E: -42.279705 | E_var:     0.5465 | E_err:   0.011551
[2025-10-03 00:18:30] [Iter 1062/2250] R3[1072/1200] | LR: 0.005695 | E: -42.266264 | E_var:     0.5863 | E_err:   0.011964
[2025-10-03 00:18:37] [Iter 1063/2250] R3[1074/1200] | LR: 0.005674 | E: -42.292221 | E_var:     0.4643 | E_err:   0.010647
[2025-10-03 00:18:45] [Iter 1064/2250] R3[1076/1200] | LR: 0.005653 | E: -42.291495 | E_var:     0.4681 | E_err:   0.010691
[2025-10-03 00:18:53] [Iter 1065/2250] R3[1078/1200] | LR: 0.005632 | E: -42.278738 | E_var:     0.4835 | E_err:   0.010865
[2025-10-03 00:19:01] [Iter 1066/2250] R3[1080/1200] | LR: 0.005612 | E: -42.285362 | E_var:     0.4529 | E_err:   0.010515
[2025-10-03 00:19:09] [Iter 1067/2250] R3[1082/1200] | LR: 0.005592 | E: -42.277119 | E_var:     0.5860 | E_err:   0.011961
[2025-10-03 00:19:16] [Iter 1068/2250] R3[1084/1200] | LR: 0.005572 | E: -42.290804 | E_var:     0.4187 | E_err:   0.010111
[2025-10-03 00:19:24] [Iter 1069/2250] R3[1086/1200] | LR: 0.005553 | E: -42.277831 | E_var:     0.5271 | E_err:   0.011344
[2025-10-03 00:19:32] [Iter 1070/2250] R3[1088/1200] | LR: 0.005534 | E: -42.290159 | E_var:     0.5164 | E_err:   0.011229
[2025-10-03 00:19:40] [Iter 1071/2250] R3[1090/1200] | LR: 0.005515 | E: -42.279293 | E_var:     0.5617 | E_err:   0.011711
[2025-10-03 00:19:48] [Iter 1072/2250] R3[1092/1200] | LR: 0.005496 | E: -42.274780 | E_var:     0.4607 | E_err:   0.010606
[2025-10-03 00:19:56] [Iter 1073/2250] R3[1094/1200] | LR: 0.005478 | E: -42.272474 | E_var:     0.4494 | E_err:   0.010475
[2025-10-03 00:20:03] [Iter 1074/2250] R3[1096/1200] | LR: 0.005460 | E: -42.273724 | E_var:     0.5732 | E_err:   0.011830
[2025-10-03 00:20:11] [Iter 1075/2250] R3[1098/1200] | LR: 0.005443 | E: -42.278791 | E_var:     0.4633 | E_err:   0.010636
[2025-10-03 00:20:19] [Iter 1076/2250] R3[1100/1200] | LR: 0.005426 | E: -42.276948 | E_var:     0.4963 | E_err:   0.011007
[2025-10-03 00:20:27] [Iter 1077/2250] R3[1102/1200] | LR: 0.005409 | E: -42.262570 | E_var:     0.4489 | E_err:   0.010468
[2025-10-03 00:20:35] [Iter 1078/2250] R3[1104/1200] | LR: 0.005393 | E: -42.281631 | E_var:     0.4401 | E_err:   0.010366
[2025-10-03 00:20:42] [Iter 1079/2250] R3[1106/1200] | LR: 0.005377 | E: -42.290203 | E_var:     0.5783 | E_err:   0.011882
[2025-10-03 00:20:50] [Iter 1080/2250] R3[1108/1200] | LR: 0.005361 | E: -42.270124 | E_var:     0.5154 | E_err:   0.011218
[2025-10-03 00:20:58] [Iter 1081/2250] R3[1110/1200] | LR: 0.005345 | E: -42.278792 | E_var:     0.4589 | E_err:   0.010585
[2025-10-03 00:21:06] [Iter 1082/2250] R3[1112/1200] | LR: 0.005330 | E: -42.272465 | E_var:     0.4189 | E_err:   0.010113
[2025-10-03 00:21:14] [Iter 1083/2250] R3[1114/1200] | LR: 0.005315 | E: -42.268878 | E_var:     0.4325 | E_err:   0.010276
[2025-10-03 00:21:22] [Iter 1084/2250] R3[1116/1200] | LR: 0.005301 | E: -42.275062 | E_var:     0.5164 | E_err:   0.011229
[2025-10-03 00:21:29] [Iter 1085/2250] R3[1118/1200] | LR: 0.005287 | E: -42.268377 | E_var:     0.5033 | E_err:   0.011085
[2025-10-03 00:21:37] [Iter 1086/2250] R3[1120/1200] | LR: 0.005273 | E: -42.274224 | E_var:     0.5975 | E_err:   0.012078
[2025-10-03 00:21:45] [Iter 1087/2250] R3[1122/1200] | LR: 0.005260 | E: -42.267013 | E_var:     0.5259 | E_err:   0.011331
[2025-10-03 00:21:53] [Iter 1088/2250] R3[1124/1200] | LR: 0.005247 | E: -42.278887 | E_var:     0.5688 | E_err:   0.011784
[2025-10-03 00:22:01] [Iter 1089/2250] R3[1126/1200] | LR: 0.005234 | E: -42.284899 | E_var:     0.4193 | E_err:   0.010117
[2025-10-03 00:22:08] [Iter 1090/2250] R3[1128/1200] | LR: 0.005221 | E: -42.291654 | E_var:     0.5431 | E_err:   0.011515
[2025-10-03 00:22:16] [Iter 1091/2250] R3[1130/1200] | LR: 0.005209 | E: -42.295982 | E_var:     0.5051 | E_err:   0.011104
[2025-10-03 00:22:24] [Iter 1092/2250] R3[1132/1200] | LR: 0.005198 | E: -42.274606 | E_var:     0.6549 | E_err:   0.012644
[2025-10-03 00:22:32] [Iter 1093/2250] R3[1134/1200] | LR: 0.005186 | E: -42.310816 | E_var:     0.5247 | E_err:   0.011318
[2025-10-03 00:22:40] [Iter 1094/2250] R3[1136/1200] | LR: 0.005175 | E: -42.282808 | E_var:     0.4316 | E_err:   0.010265
[2025-10-03 00:22:48] [Iter 1095/2250] R3[1138/1200] | LR: 0.005164 | E: -42.288310 | E_var:     0.4526 | E_err:   0.010512
[2025-10-03 00:22:55] [Iter 1096/2250] R3[1140/1200] | LR: 0.005154 | E: -42.285698 | E_var:     0.5310 | E_err:   0.011386
[2025-10-03 00:23:03] [Iter 1097/2250] R3[1142/1200] | LR: 0.005144 | E: -42.273434 | E_var:     0.5449 | E_err:   0.011534
[2025-10-03 00:23:11] [Iter 1098/2250] R3[1144/1200] | LR: 0.005134 | E: -42.272049 | E_var:     0.4483 | E_err:   0.010461
[2025-10-03 00:23:19] [Iter 1099/2250] R3[1146/1200] | LR: 0.005125 | E: -42.288450 | E_var:     0.4443 | E_err:   0.010415
[2025-10-03 00:23:27] [Iter 1100/2250] R3[1148/1200] | LR: 0.005116 | E: -42.282282 | E_var:     0.6209 | E_err:   0.012312
[2025-10-03 00:23:34] [Iter 1101/2250] R3[1150/1200] | LR: 0.005107 | E: -42.257036 | E_var:     0.4712 | E_err:   0.010725
[2025-10-03 00:23:42] [Iter 1102/2250] R3[1152/1200] | LR: 0.005099 | E: -42.271185 | E_var:     0.4304 | E_err:   0.010251
[2025-10-03 00:23:50] [Iter 1103/2250] R3[1154/1200] | LR: 0.005091 | E: -42.274565 | E_var:     0.4575 | E_err:   0.010568
[2025-10-03 00:23:58] [Iter 1104/2250] R3[1156/1200] | LR: 0.005083 | E: -42.306591 | E_var:     0.4647 | E_err:   0.010652
[2025-10-03 00:24:06] [Iter 1105/2250] R3[1158/1200] | LR: 0.005075 | E: -42.271243 | E_var:     0.4643 | E_err:   0.010647
[2025-10-03 00:24:14] [Iter 1106/2250] R3[1160/1200] | LR: 0.005068 | E: -42.281245 | E_var:     0.5833 | E_err:   0.011934
[2025-10-03 00:24:21] [Iter 1107/2250] R3[1162/1200] | LR: 0.005062 | E: -42.286833 | E_var:     0.4575 | E_err:   0.010569
[2025-10-03 00:24:29] [Iter 1108/2250] R3[1164/1200] | LR: 0.005055 | E: -42.262860 | E_var:     0.7096 | E_err:   0.013162
[2025-10-03 00:24:37] [Iter 1109/2250] R3[1166/1200] | LR: 0.005049 | E: -42.292741 | E_var:     0.5585 | E_err:   0.011677
[2025-10-03 00:24:45] [Iter 1110/2250] R3[1168/1200] | LR: 0.005044 | E: -42.273690 | E_var:     0.4616 | E_err:   0.010616
[2025-10-03 00:24:53] [Iter 1111/2250] R3[1170/1200] | LR: 0.005039 | E: -42.280462 | E_var:     0.5410 | E_err:   0.011493
[2025-10-03 00:25:00] [Iter 1112/2250] R3[1172/1200] | LR: 0.005034 | E: -42.276622 | E_var:     0.5175 | E_err:   0.011241
[2025-10-03 00:25:08] [Iter 1113/2250] R3[1174/1200] | LR: 0.005029 | E: -42.266083 | E_var:     0.5741 | E_err:   0.011839
[2025-10-03 00:25:16] [Iter 1114/2250] R3[1176/1200] | LR: 0.005025 | E: -42.276994 | E_var:     0.4897 | E_err:   0.010934
[2025-10-03 00:25:24] [Iter 1115/2250] R3[1178/1200] | LR: 0.005021 | E: -42.277501 | E_var:     0.4170 | E_err:   0.010090
[2025-10-03 00:25:32] [Iter 1116/2250] R3[1180/1200] | LR: 0.005017 | E: -42.261757 | E_var:     0.4668 | E_err:   0.010676
[2025-10-03 00:25:40] [Iter 1117/2250] R3[1182/1200] | LR: 0.005014 | E: -42.279497 | E_var:     0.4639 | E_err:   0.010642
[2025-10-03 00:25:47] [Iter 1118/2250] R3[1184/1200] | LR: 0.005011 | E: -42.298268 | E_var:     0.5477 | E_err:   0.011563
[2025-10-03 00:25:55] [Iter 1119/2250] R3[1186/1200] | LR: 0.005008 | E: -42.291733 | E_var:     0.4910 | E_err:   0.010948
[2025-10-03 00:26:03] [Iter 1120/2250] R3[1188/1200] | LR: 0.005006 | E: -42.279521 | E_var:     0.4365 | E_err:   0.010323
[2025-10-03 00:26:11] [Iter 1121/2250] R3[1190/1200] | LR: 0.005004 | E: -42.288512 | E_var:     0.7599 | E_err:   0.013621
[2025-10-03 00:26:19] [Iter 1122/2250] R3[1192/1200] | LR: 0.005003 | E: -42.264449 | E_var:     0.5034 | E_err:   0.011086
[2025-10-03 00:26:27] [Iter 1123/2250] R3[1194/1200] | LR: 0.005002 | E: -42.286457 | E_var:     0.5077 | E_err:   0.011133
[2025-10-03 00:26:34] [Iter 1124/2250] R3[1196/1200] | LR: 0.005001 | E: -42.285075 | E_var:     0.4461 | E_err:   0.010436
[2025-10-03 00:26:42] [Iter 1125/2250] R3[1198/1200] | LR: 0.005000 | E: -42.280633 | E_var:     0.5728 | E_err:   0.011825
[2025-10-03 00:26:42] 🔄 RESTART #4 | Period: 2400
[2025-10-03 00:26:50] [Iter 1126/2250] R4[0/2400]   | LR: 0.030000 | E: -42.285971 | E_var:     0.5467 | E_err:   0.011553
[2025-10-03 00:26:58] [Iter 1127/2250] R4[2/2400]   | LR: 0.030000 | E: -42.275352 | E_var:     0.4304 | E_err:   0.010251
[2025-10-03 00:27:06] [Iter 1128/2250] R4[4/2400]   | LR: 0.030000 | E: -42.290278 | E_var:     0.3934 | E_err:   0.009801
[2025-10-03 00:27:13] [Iter 1129/2250] R4[6/2400]   | LR: 0.030000 | E: -42.273426 | E_var:     0.5194 | E_err:   0.011261
[2025-10-03 00:27:21] [Iter 1130/2250] R4[8/2400]   | LR: 0.029999 | E: -42.275201 | E_var:     0.6145 | E_err:   0.012249
[2025-10-03 00:27:29] [Iter 1131/2250] R4[10/2400]  | LR: 0.029999 | E: -42.284241 | E_var:     0.4422 | E_err:   0.010391
[2025-10-03 00:27:37] [Iter 1132/2250] R4[12/2400]  | LR: 0.029998 | E: -42.274192 | E_var:     0.4665 | E_err:   0.010673
[2025-10-03 00:27:45] [Iter 1133/2250] R4[14/2400]  | LR: 0.029998 | E: -42.280558 | E_var:     0.5556 | E_err:   0.011646
[2025-10-03 00:27:53] [Iter 1134/2250] R4[16/2400]  | LR: 0.029997 | E: -42.286877 | E_var:     0.4694 | E_err:   0.010705
[2025-10-03 00:28:00] [Iter 1135/2250] R4[18/2400]  | LR: 0.029997 | E: -42.277299 | E_var:     0.4038 | E_err:   0.009929
[2025-10-03 00:28:08] [Iter 1136/2250] R4[20/2400]  | LR: 0.029996 | E: -42.283228 | E_var:     0.4985 | E_err:   0.011032
[2025-10-03 00:28:16] [Iter 1137/2250] R4[22/2400]  | LR: 0.029995 | E: -42.276850 | E_var:     0.6022 | E_err:   0.012126
[2025-10-03 00:28:24] [Iter 1138/2250] R4[24/2400]  | LR: 0.029994 | E: -42.291782 | E_var:     0.6936 | E_err:   0.013013
[2025-10-03 00:28:32] [Iter 1139/2250] R4[26/2400]  | LR: 0.029993 | E: -42.276807 | E_var:     0.5217 | E_err:   0.011286
[2025-10-03 00:28:39] [Iter 1140/2250] R4[28/2400]  | LR: 0.029992 | E: -42.294012 | E_var:     0.5831 | E_err:   0.011931
[2025-10-03 00:28:47] [Iter 1141/2250] R4[30/2400]  | LR: 0.029990 | E: -42.267421 | E_var:     0.5115 | E_err:   0.011175
[2025-10-03 00:28:55] [Iter 1142/2250] R4[32/2400]  | LR: 0.029989 | E: -42.278854 | E_var:     0.5773 | E_err:   0.011872
[2025-10-03 00:29:03] [Iter 1143/2250] R4[34/2400]  | LR: 0.029988 | E: -42.266282 | E_var:     0.4838 | E_err:   0.010868
[2025-10-03 00:29:11] [Iter 1144/2250] R4[36/2400]  | LR: 0.029986 | E: -42.283096 | E_var:     0.4918 | E_err:   0.010958
[2025-10-03 00:29:19] [Iter 1145/2250] R4[38/2400]  | LR: 0.029985 | E: -42.297675 | E_var:     0.4624 | E_err:   0.010625
[2025-10-03 00:29:26] [Iter 1146/2250] R4[40/2400]  | LR: 0.029983 | E: -42.285158 | E_var:     0.4315 | E_err:   0.010264
[2025-10-03 00:29:34] [Iter 1147/2250] R4[42/2400]  | LR: 0.029981 | E: -42.281061 | E_var:     0.4889 | E_err:   0.010925
[2025-10-03 00:29:42] [Iter 1148/2250] R4[44/2400]  | LR: 0.029979 | E: -42.274946 | E_var:     0.4011 | E_err:   0.009896
[2025-10-03 00:29:50] [Iter 1149/2250] R4[46/2400]  | LR: 0.029977 | E: -42.282187 | E_var:     0.4179 | E_err:   0.010101
[2025-10-03 00:29:58] [Iter 1150/2250] R4[48/2400]  | LR: 0.029975 | E: -42.283758 | E_var:     0.4955 | E_err:   0.010999
[2025-10-03 00:30:05] [Iter 1151/2250] R4[50/2400]  | LR: 0.029973 | E: -42.276647 | E_var:     0.4321 | E_err:   0.010271
[2025-10-03 00:30:13] [Iter 1152/2250] R4[52/2400]  | LR: 0.029971 | E: -42.266691 | E_var:     0.4437 | E_err:   0.010407
[2025-10-03 00:30:21] [Iter 1153/2250] R4[54/2400]  | LR: 0.029969 | E: -42.275903 | E_var:     0.4735 | E_err:   0.010752
[2025-10-03 00:30:29] [Iter 1154/2250] R4[56/2400]  | LR: 0.029966 | E: -42.277944 | E_var:     0.5233 | E_err:   0.011303
[2025-10-03 00:30:37] [Iter 1155/2250] R4[58/2400]  | LR: 0.029964 | E: -42.270874 | E_var:     0.4581 | E_err:   0.010576
[2025-10-03 00:30:45] [Iter 1156/2250] R4[60/2400]  | LR: 0.029961 | E: -42.276315 | E_var:     0.4442 | E_err:   0.010414
[2025-10-03 00:30:52] [Iter 1157/2250] R4[62/2400]  | LR: 0.029959 | E: -42.276443 | E_var:     0.3585 | E_err:   0.009355
[2025-10-03 00:31:00] [Iter 1158/2250] R4[64/2400]  | LR: 0.029956 | E: -42.278418 | E_var:     0.4366 | E_err:   0.010325
[2025-10-03 00:31:08] [Iter 1159/2250] R4[66/2400]  | LR: 0.029953 | E: -42.287428 | E_var:     0.6362 | E_err:   0.012463
[2025-10-03 00:31:16] [Iter 1160/2250] R4[68/2400]  | LR: 0.029951 | E: -42.280586 | E_var:     0.5465 | E_err:   0.011551
[2025-10-03 00:31:24] [Iter 1161/2250] R4[70/2400]  | LR: 0.029948 | E: -42.284729 | E_var:     0.3719 | E_err:   0.009528
[2025-10-03 00:31:32] [Iter 1162/2250] R4[72/2400]  | LR: 0.029945 | E: -42.295259 | E_var:     0.4949 | E_err:   0.010992
[2025-10-03 00:31:39] [Iter 1163/2250] R4[74/2400]  | LR: 0.029941 | E: -42.278157 | E_var:     0.3904 | E_err:   0.009763
[2025-10-03 00:31:47] [Iter 1164/2250] R4[76/2400]  | LR: 0.029938 | E: -42.279090 | E_var:     0.4366 | E_err:   0.010325
[2025-10-03 00:31:55] [Iter 1165/2250] R4[78/2400]  | LR: 0.029935 | E: -42.268336 | E_var:     0.4551 | E_err:   0.010540
[2025-10-03 00:32:03] [Iter 1166/2250] R4[80/2400]  | LR: 0.029932 | E: -42.300341 | E_var:     0.4266 | E_err:   0.010205
[2025-10-03 00:32:11] [Iter 1167/2250] R4[82/2400]  | LR: 0.029928 | E: -42.286085 | E_var:     0.4912 | E_err:   0.010951
[2025-10-03 00:32:19] [Iter 1168/2250] R4[84/2400]  | LR: 0.029925 | E: -42.260056 | E_var:     0.4747 | E_err:   0.010765
[2025-10-03 00:32:26] [Iter 1169/2250] R4[86/2400]  | LR: 0.029921 | E: -42.292183 | E_var:     0.5408 | E_err:   0.011490
[2025-10-03 00:32:34] [Iter 1170/2250] R4[88/2400]  | LR: 0.029917 | E: -42.266503 | E_var:     0.6136 | E_err:   0.012240
[2025-10-03 00:32:42] [Iter 1171/2250] R4[90/2400]  | LR: 0.029913 | E: -42.264476 | E_var:     0.5618 | E_err:   0.011712
[2025-10-03 00:32:50] [Iter 1172/2250] R4[92/2400]  | LR: 0.029909 | E: -42.272577 | E_var:     0.4723 | E_err:   0.010738
[2025-10-03 00:32:58] [Iter 1173/2250] R4[94/2400]  | LR: 0.029905 | E: -42.282368 | E_var:     0.5429 | E_err:   0.011513
[2025-10-03 00:33:05] [Iter 1174/2250] R4[96/2400]  | LR: 0.029901 | E: -42.301436 | E_var:     0.4647 | E_err:   0.010651
[2025-10-03 00:33:13] [Iter 1175/2250] R4[98/2400]  | LR: 0.029897 | E: -42.281443 | E_var:     0.4840 | E_err:   0.010870
[2025-10-03 00:33:21] [Iter 1176/2250] R4[100/2400] | LR: 0.029893 | E: -42.270060 | E_var:     0.5105 | E_err:   0.011164
[2025-10-03 00:33:29] [Iter 1177/2250] R4[102/2400] | LR: 0.029889 | E: -42.272795 | E_var:     0.4489 | E_err:   0.010469
[2025-10-03 00:33:37] [Iter 1178/2250] R4[104/2400] | LR: 0.029884 | E: -42.278594 | E_var:     0.4891 | E_err:   0.010927
[2025-10-03 00:33:45] [Iter 1179/2250] R4[106/2400] | LR: 0.029880 | E: -42.293057 | E_var:     0.3974 | E_err:   0.009850
[2025-10-03 00:33:52] [Iter 1180/2250] R4[108/2400] | LR: 0.029875 | E: -42.269569 | E_var:     0.5399 | E_err:   0.011481
[2025-10-03 00:34:00] [Iter 1181/2250] R4[110/2400] | LR: 0.029871 | E: -42.281676 | E_var:     0.4598 | E_err:   0.010595
[2025-10-03 00:34:08] [Iter 1182/2250] R4[112/2400] | LR: 0.029866 | E: -42.280453 | E_var:     0.5388 | E_err:   0.011470
[2025-10-03 00:34:16] [Iter 1183/2250] R4[114/2400] | LR: 0.029861 | E: -42.270680 | E_var:     0.5145 | E_err:   0.011208
[2025-10-03 00:34:24] [Iter 1184/2250] R4[116/2400] | LR: 0.029856 | E: -42.273332 | E_var:     0.4730 | E_err:   0.010746
[2025-10-03 00:34:31] [Iter 1185/2250] R4[118/2400] | LR: 0.029851 | E: -42.305034 | E_var:     0.3793 | E_err:   0.009623
[2025-10-03 00:34:39] [Iter 1186/2250] R4[120/2400] | LR: 0.029846 | E: -42.298628 | E_var:     0.4241 | E_err:   0.010175
[2025-10-03 00:34:47] [Iter 1187/2250] R4[122/2400] | LR: 0.029841 | E: -42.284581 | E_var:     0.5309 | E_err:   0.011385
[2025-10-03 00:34:55] [Iter 1188/2250] R4[124/2400] | LR: 0.029836 | E: -42.292177 | E_var:     0.4927 | E_err:   0.010968
[2025-10-03 00:35:03] [Iter 1189/2250] R4[126/2400] | LR: 0.029830 | E: -42.286964 | E_var:     0.4598 | E_err:   0.010596
[2025-10-03 00:35:11] [Iter 1190/2250] R4[128/2400] | LR: 0.029825 | E: -42.273774 | E_var:     0.5404 | E_err:   0.011487
[2025-10-03 00:35:18] [Iter 1191/2250] R4[130/2400] | LR: 0.029819 | E: -42.283226 | E_var:     0.4680 | E_err:   0.010690
[2025-10-03 00:35:26] [Iter 1192/2250] R4[132/2400] | LR: 0.029814 | E: -42.278515 | E_var:     0.4238 | E_err:   0.010172
[2025-10-03 00:35:34] [Iter 1193/2250] R4[134/2400] | LR: 0.029808 | E: -42.298350 | E_var:     0.4542 | E_err:   0.010530
[2025-10-03 00:35:42] [Iter 1194/2250] R4[136/2400] | LR: 0.029802 | E: -42.294435 | E_var:     0.5246 | E_err:   0.011317
[2025-10-03 00:35:50] [Iter 1195/2250] R4[138/2400] | LR: 0.029797 | E: -42.273848 | E_var:     0.4512 | E_err:   0.010495
[2025-10-03 00:35:57] [Iter 1196/2250] R4[140/2400] | LR: 0.029791 | E: -42.280395 | E_var:     0.4566 | E_err:   0.010558
[2025-10-03 00:36:05] [Iter 1197/2250] R4[142/2400] | LR: 0.029785 | E: -42.276564 | E_var:     0.4442 | E_err:   0.010413
[2025-10-03 00:36:13] [Iter 1198/2250] R4[144/2400] | LR: 0.029779 | E: -42.265899 | E_var:     0.5097 | E_err:   0.011155
[2025-10-03 00:36:21] [Iter 1199/2250] R4[146/2400] | LR: 0.029772 | E: -42.281639 | E_var:     0.5728 | E_err:   0.011826
[2025-10-03 00:36:29] [Iter 1200/2250] R4[148/2400] | LR: 0.029766 | E: -42.284919 | E_var:     0.4488 | E_err:   0.010467
[2025-10-03 00:36:29] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-03 00:36:37] [Iter 1201/2250] R4[150/2400] | LR: 0.029760 | E: -42.273943 | E_var:     0.4789 | E_err:   0.010813
[2025-10-03 00:36:45] [Iter 1202/2250] R4[152/2400] | LR: 0.029753 | E: -42.260765 | E_var:     0.4870 | E_err:   0.010904
[2025-10-03 00:36:52] [Iter 1203/2250] R4[154/2400] | LR: 0.029747 | E: -42.291565 | E_var:     0.7688 | E_err:   0.013700
[2025-10-03 00:37:00] [Iter 1204/2250] R4[156/2400] | LR: 0.029740 | E: -42.290269 | E_var:     0.4713 | E_err:   0.010727
[2025-10-03 00:37:08] [Iter 1205/2250] R4[158/2400] | LR: 0.029734 | E: -42.284774 | E_var:     0.3988 | E_err:   0.009867
[2025-10-03 00:37:16] [Iter 1206/2250] R4[160/2400] | LR: 0.029727 | E: -42.285666 | E_var:     0.5902 | E_err:   0.012004
[2025-10-03 00:37:24] [Iter 1207/2250] R4[162/2400] | LR: 0.029720 | E: -42.292870 | E_var:     0.4695 | E_err:   0.010707
[2025-10-03 00:37:31] [Iter 1208/2250] R4[164/2400] | LR: 0.029713 | E: -42.283334 | E_var:     0.4950 | E_err:   0.010993
[2025-10-03 00:37:39] [Iter 1209/2250] R4[166/2400] | LR: 0.029706 | E: -42.255363 | E_var:     1.0297 | E_err:   0.015855
[2025-10-03 00:37:47] [Iter 1210/2250] R4[168/2400] | LR: 0.029699 | E: -42.299141 | E_var:     0.6329 | E_err:   0.012430
[2025-10-03 00:37:55] [Iter 1211/2250] R4[170/2400] | LR: 0.029692 | E: -42.280027 | E_var:     0.4191 | E_err:   0.010115
[2025-10-03 00:38:03] [Iter 1212/2250] R4[172/2400] | LR: 0.029685 | E: -42.258475 | E_var:     0.4850 | E_err:   0.010881
[2025-10-03 00:38:11] [Iter 1213/2250] R4[174/2400] | LR: 0.029677 | E: -42.297009 | E_var:     0.4533 | E_err:   0.010520
[2025-10-03 00:38:18] [Iter 1214/2250] R4[176/2400] | LR: 0.029670 | E: -42.271866 | E_var:     0.3938 | E_err:   0.009806
[2025-10-03 00:38:26] [Iter 1215/2250] R4[178/2400] | LR: 0.029662 | E: -42.268653 | E_var:     0.6368 | E_err:   0.012469
[2025-10-03 00:38:34] [Iter 1216/2250] R4[180/2400] | LR: 0.029655 | E: -42.293405 | E_var:     0.4083 | E_err:   0.009984
[2025-10-03 00:38:42] [Iter 1217/2250] R4[182/2400] | LR: 0.029647 | E: -42.279940 | E_var:     0.4892 | E_err:   0.010928
[2025-10-03 00:38:50] [Iter 1218/2250] R4[184/2400] | LR: 0.029639 | E: -42.285117 | E_var:     0.5004 | E_err:   0.011053
[2025-10-03 00:38:57] [Iter 1219/2250] R4[186/2400] | LR: 0.029631 | E: -42.291704 | E_var:     0.7597 | E_err:   0.013619
[2025-10-03 00:39:05] [Iter 1220/2250] R4[188/2400] | LR: 0.029623 | E: -42.286411 | E_var:     0.4137 | E_err:   0.010050
[2025-10-03 00:39:13] [Iter 1221/2250] R4[190/2400] | LR: 0.029615 | E: -42.273240 | E_var:     0.4962 | E_err:   0.011007
[2025-10-03 00:39:21] [Iter 1222/2250] R4[192/2400] | LR: 0.029607 | E: -42.268076 | E_var:     0.5540 | E_err:   0.011630
[2025-10-03 00:39:29] [Iter 1223/2250] R4[194/2400] | LR: 0.029599 | E: -42.285161 | E_var:     0.4478 | E_err:   0.010456
[2025-10-03 00:39:37] [Iter 1224/2250] R4[196/2400] | LR: 0.029591 | E: -42.268737 | E_var:     0.5178 | E_err:   0.011244
[2025-10-03 00:39:44] [Iter 1225/2250] R4[198/2400] | LR: 0.029583 | E: -42.289571 | E_var:     0.4099 | E_err:   0.010004
[2025-10-03 00:39:52] [Iter 1226/2250] R4[200/2400] | LR: 0.029574 | E: -42.277649 | E_var:     0.4114 | E_err:   0.010022
[2025-10-03 00:40:00] [Iter 1227/2250] R4[202/2400] | LR: 0.029566 | E: -42.277525 | E_var:     0.5317 | E_err:   0.011393
[2025-10-03 00:40:08] [Iter 1228/2250] R4[204/2400] | LR: 0.029557 | E: -42.280019 | E_var:     0.5472 | E_err:   0.011558
[2025-10-03 00:40:16] [Iter 1229/2250] R4[206/2400] | LR: 0.029548 | E: -42.292791 | E_var:     0.3833 | E_err:   0.009674
[2025-10-03 00:40:23] [Iter 1230/2250] R4[208/2400] | LR: 0.029540 | E: -42.280260 | E_var:     0.4188 | E_err:   0.010111
[2025-10-03 00:40:31] [Iter 1231/2250] R4[210/2400] | LR: 0.029531 | E: -42.266866 | E_var:     0.5748 | E_err:   0.011847
[2025-10-03 00:40:39] [Iter 1232/2250] R4[212/2400] | LR: 0.029522 | E: -42.304955 | E_var:     0.4516 | E_err:   0.010500
[2025-10-03 00:40:47] [Iter 1233/2250] R4[214/2400] | LR: 0.029513 | E: -42.283460 | E_var:     0.5380 | E_err:   0.011461
[2025-10-03 00:40:55] [Iter 1234/2250] R4[216/2400] | LR: 0.029504 | E: -42.276757 | E_var:     0.4770 | E_err:   0.010792
[2025-10-03 00:41:03] [Iter 1235/2250] R4[218/2400] | LR: 0.029494 | E: -42.285292 | E_var:     0.8591 | E_err:   0.014482
[2025-10-03 00:41:10] [Iter 1236/2250] R4[220/2400] | LR: 0.029485 | E: -42.291902 | E_var:     0.4772 | E_err:   0.010793
[2025-10-03 00:41:18] [Iter 1237/2250] R4[222/2400] | LR: 0.029476 | E: -42.277543 | E_var:     0.5473 | E_err:   0.011560
[2025-10-03 00:41:26] [Iter 1238/2250] R4[224/2400] | LR: 0.029466 | E: -42.279497 | E_var:     0.4222 | E_err:   0.010153
[2025-10-03 00:41:34] [Iter 1239/2250] R4[226/2400] | LR: 0.029457 | E: -42.277301 | E_var:     0.4188 | E_err:   0.010112
[2025-10-03 00:41:42] [Iter 1240/2250] R4[228/2400] | LR: 0.029447 | E: -42.289879 | E_var:     0.4363 | E_err:   0.010320
[2025-10-03 00:41:49] [Iter 1241/2250] R4[230/2400] | LR: 0.029438 | E: -42.292062 | E_var:     0.5832 | E_err:   0.011932
[2025-10-03 00:41:57] [Iter 1242/2250] R4[232/2400] | LR: 0.029428 | E: -42.279785 | E_var:     0.3961 | E_err:   0.009834
[2025-10-03 00:42:05] [Iter 1243/2250] R4[234/2400] | LR: 0.029418 | E: -42.287111 | E_var:     0.4866 | E_err:   0.010899
[2025-10-03 00:42:13] [Iter 1244/2250] R4[236/2400] | LR: 0.029408 | E: -42.289105 | E_var:     0.3973 | E_err:   0.009849
[2025-10-03 00:42:21] [Iter 1245/2250] R4[238/2400] | LR: 0.029398 | E: -42.267206 | E_var:     0.5435 | E_err:   0.011519
[2025-10-03 00:42:29] [Iter 1246/2250] R4[240/2400] | LR: 0.029388 | E: -42.275958 | E_var:     0.3923 | E_err:   0.009787
[2025-10-03 00:42:36] [Iter 1247/2250] R4[242/2400] | LR: 0.029378 | E: -42.302943 | E_var:     0.5152 | E_err:   0.011215
[2025-10-03 00:42:44] [Iter 1248/2250] R4[244/2400] | LR: 0.029368 | E: -42.284147 | E_var:     0.4716 | E_err:   0.010730
[2025-10-03 00:42:52] [Iter 1249/2250] R4[246/2400] | LR: 0.029358 | E: -42.292871 | E_var:     0.4697 | E_err:   0.010709
[2025-10-03 00:43:00] [Iter 1250/2250] R4[248/2400] | LR: 0.029347 | E: -42.265680 | E_var:     0.4709 | E_err:   0.010722
[2025-10-03 00:43:08] [Iter 1251/2250] R4[250/2400] | LR: 0.029337 | E: -42.279440 | E_var:     0.5296 | E_err:   0.011371
[2025-10-03 00:43:15] [Iter 1252/2250] R4[252/2400] | LR: 0.029326 | E: -42.282843 | E_var:     0.4197 | E_err:   0.010123
[2025-10-03 00:43:23] [Iter 1253/2250] R4[254/2400] | LR: 0.029315 | E: -42.287605 | E_var:     0.4275 | E_err:   0.010216
[2025-10-03 00:43:31] [Iter 1254/2250] R4[256/2400] | LR: 0.029305 | E: -42.295989 | E_var:     0.4652 | E_err:   0.010657
[2025-10-03 00:43:39] [Iter 1255/2250] R4[258/2400] | LR: 0.029294 | E: -42.291212 | E_var:     0.4147 | E_err:   0.010062
[2025-10-03 00:43:47] [Iter 1256/2250] R4[260/2400] | LR: 0.029283 | E: -42.286769 | E_var:     0.5096 | E_err:   0.011154
[2025-10-03 00:43:55] [Iter 1257/2250] R4[262/2400] | LR: 0.029272 | E: -42.291008 | E_var:     0.4367 | E_err:   0.010325
[2025-10-03 00:44:02] [Iter 1258/2250] R4[264/2400] | LR: 0.029261 | E: -42.266610 | E_var:     0.5809 | E_err:   0.011909
[2025-10-03 00:44:10] [Iter 1259/2250] R4[266/2400] | LR: 0.029250 | E: -42.299115 | E_var:     0.4293 | E_err:   0.010238
[2025-10-03 00:44:18] [Iter 1260/2250] R4[268/2400] | LR: 0.029239 | E: -42.286501 | E_var:     0.4505 | E_err:   0.010487
[2025-10-03 00:44:26] [Iter 1261/2250] R4[270/2400] | LR: 0.029227 | E: -42.297648 | E_var:     0.5426 | E_err:   0.011510
[2025-10-03 00:44:34] [Iter 1262/2250] R4[272/2400] | LR: 0.029216 | E: -42.279766 | E_var:     0.4565 | E_err:   0.010557
[2025-10-03 00:44:41] [Iter 1263/2250] R4[274/2400] | LR: 0.029205 | E: -42.275554 | E_var:     0.8876 | E_err:   0.014720
[2025-10-03 00:44:49] [Iter 1264/2250] R4[276/2400] | LR: 0.029193 | E: -42.280774 | E_var:     0.4785 | E_err:   0.010808
[2025-10-03 00:44:57] [Iter 1265/2250] R4[278/2400] | LR: 0.029181 | E: -42.303486 | E_var:     0.5300 | E_err:   0.011376
[2025-10-03 00:45:05] [Iter 1266/2250] R4[280/2400] | LR: 0.029170 | E: -42.293405 | E_var:     0.4284 | E_err:   0.010227
[2025-10-03 00:45:13] [Iter 1267/2250] R4[282/2400] | LR: 0.029158 | E: -42.273872 | E_var:     0.4550 | E_err:   0.010540
[2025-10-03 00:45:21] [Iter 1268/2250] R4[284/2400] | LR: 0.029146 | E: -42.258245 | E_var:     0.5415 | E_err:   0.011498
[2025-10-03 00:45:28] [Iter 1269/2250] R4[286/2400] | LR: 0.029134 | E: -42.291189 | E_var:     0.5058 | E_err:   0.011113
[2025-10-03 00:45:36] [Iter 1270/2250] R4[288/2400] | LR: 0.029122 | E: -42.294080 | E_var:     0.3616 | E_err:   0.009396
[2025-10-03 00:45:44] [Iter 1271/2250] R4[290/2400] | LR: 0.029110 | E: -42.309140 | E_var:     0.4936 | E_err:   0.010978
[2025-10-03 00:45:52] [Iter 1272/2250] R4[292/2400] | LR: 0.029098 | E: -42.283101 | E_var:     0.4533 | E_err:   0.010520
[2025-10-03 00:46:00] [Iter 1273/2250] R4[294/2400] | LR: 0.029086 | E: -42.289573 | E_var:     0.4285 | E_err:   0.010228
[2025-10-03 00:46:07] [Iter 1274/2250] R4[296/2400] | LR: 0.029073 | E: -42.279333 | E_var:     0.5776 | E_err:   0.011875
[2025-10-03 00:46:15] [Iter 1275/2250] R4[298/2400] | LR: 0.029061 | E: -42.286922 | E_var:     0.4301 | E_err:   0.010248
[2025-10-03 00:46:23] [Iter 1276/2250] R4[300/2400] | LR: 0.029048 | E: -42.298773 | E_var:     0.3955 | E_err:   0.009827
[2025-10-03 00:46:31] [Iter 1277/2250] R4[302/2400] | LR: 0.029036 | E: -42.309075 | E_var:     0.4214 | E_err:   0.010143
[2025-10-03 00:46:39] [Iter 1278/2250] R4[304/2400] | LR: 0.029023 | E: -42.273119 | E_var:     0.4929 | E_err:   0.010970
[2025-10-03 00:46:47] [Iter 1279/2250] R4[306/2400] | LR: 0.029011 | E: -42.302006 | E_var:     0.4094 | E_err:   0.009998
[2025-10-03 00:46:54] [Iter 1280/2250] R4[308/2400] | LR: 0.028998 | E: -42.271307 | E_var:     0.5049 | E_err:   0.011102
[2025-10-03 00:47:02] [Iter 1281/2250] R4[310/2400] | LR: 0.028985 | E: -42.292552 | E_var:     0.4710 | E_err:   0.010724
[2025-10-03 00:47:10] [Iter 1282/2250] R4[312/2400] | LR: 0.028972 | E: -42.288278 | E_var:     0.4057 | E_err:   0.009952
[2025-10-03 00:47:18] [Iter 1283/2250] R4[314/2400] | LR: 0.028959 | E: -42.296292 | E_var:     0.5922 | E_err:   0.012024
[2025-10-03 00:47:26] [Iter 1284/2250] R4[316/2400] | LR: 0.028946 | E: -42.270680 | E_var:     0.4419 | E_err:   0.010386
[2025-10-03 00:47:33] [Iter 1285/2250] R4[318/2400] | LR: 0.028933 | E: -42.287287 | E_var:     0.4160 | E_err:   0.010077
[2025-10-03 00:47:41] [Iter 1286/2250] R4[320/2400] | LR: 0.028919 | E: -42.282326 | E_var:     0.4188 | E_err:   0.010112
[2025-10-03 00:47:49] [Iter 1287/2250] R4[322/2400] | LR: 0.028906 | E: -42.289566 | E_var:     0.4355 | E_err:   0.010311
[2025-10-03 00:47:57] [Iter 1288/2250] R4[324/2400] | LR: 0.028893 | E: -42.278492 | E_var:     0.5339 | E_err:   0.011417
[2025-10-03 00:48:05] [Iter 1289/2250] R4[326/2400] | LR: 0.028879 | E: -42.273353 | E_var:     0.4702 | E_err:   0.010714
[2025-10-03 00:48:13] [Iter 1290/2250] R4[328/2400] | LR: 0.028865 | E: -42.296728 | E_var:     1.1953 | E_err:   0.017083
[2025-10-03 00:48:20] [Iter 1291/2250] R4[330/2400] | LR: 0.028852 | E: -42.288517 | E_var:     0.4862 | E_err:   0.010895
[2025-10-03 00:48:28] [Iter 1292/2250] R4[332/2400] | LR: 0.028838 | E: -42.272410 | E_var:     0.5017 | E_err:   0.011067
[2025-10-03 00:48:36] [Iter 1293/2250] R4[334/2400] | LR: 0.028824 | E: -42.299119 | E_var:     0.4212 | E_err:   0.010140
[2025-10-03 00:48:44] [Iter 1294/2250] R4[336/2400] | LR: 0.028810 | E: -42.282764 | E_var:     0.5683 | E_err:   0.011779
[2025-10-03 00:48:52] [Iter 1295/2250] R4[338/2400] | LR: 0.028796 | E: -42.270655 | E_var:     0.5142 | E_err:   0.011205
[2025-10-03 00:48:59] [Iter 1296/2250] R4[340/2400] | LR: 0.028782 | E: -42.276834 | E_var:     0.5232 | E_err:   0.011302
[2025-10-03 00:49:07] [Iter 1297/2250] R4[342/2400] | LR: 0.028768 | E: -42.294613 | E_var:     0.5042 | E_err:   0.011095
[2025-10-03 00:49:15] [Iter 1298/2250] R4[344/2400] | LR: 0.028754 | E: -42.285810 | E_var:     0.4469 | E_err:   0.010445
[2025-10-03 00:49:23] [Iter 1299/2250] R4[346/2400] | LR: 0.028740 | E: -42.274508 | E_var:     0.4632 | E_err:   0.010635
[2025-10-03 00:49:31] [Iter 1300/2250] R4[348/2400] | LR: 0.028725 | E: -42.300349 | E_var:     0.4392 | E_err:   0.010355
[2025-10-03 00:49:39] [Iter 1301/2250] R4[350/2400] | LR: 0.028711 | E: -42.279233 | E_var:     0.5159 | E_err:   0.011223
[2025-10-03 00:49:46] [Iter 1302/2250] R4[352/2400] | LR: 0.028696 | E: -42.287466 | E_var:     0.3731 | E_err:   0.009544
[2025-10-03 00:49:54] [Iter 1303/2250] R4[354/2400] | LR: 0.028682 | E: -42.283770 | E_var:     0.4291 | E_err:   0.010235
[2025-10-03 00:50:02] [Iter 1304/2250] R4[356/2400] | LR: 0.028667 | E: -42.307687 | E_var:     0.4973 | E_err:   0.011018
[2025-10-03 00:50:10] [Iter 1305/2250] R4[358/2400] | LR: 0.028652 | E: -42.276847 | E_var:     0.4801 | E_err:   0.010826
[2025-10-03 00:50:18] [Iter 1306/2250] R4[360/2400] | LR: 0.028638 | E: -42.284823 | E_var:     0.5238 | E_err:   0.011308
[2025-10-03 00:50:25] [Iter 1307/2250] R4[362/2400] | LR: 0.028623 | E: -42.283156 | E_var:     0.6703 | E_err:   0.012792
[2025-10-03 00:50:33] [Iter 1308/2250] R4[364/2400] | LR: 0.028608 | E: -42.297001 | E_var:     1.0561 | E_err:   0.016057
[2025-10-03 00:50:41] [Iter 1309/2250] R4[366/2400] | LR: 0.028593 | E: -42.294751 | E_var:     0.4702 | E_err:   0.010715
[2025-10-03 00:50:49] [Iter 1310/2250] R4[368/2400] | LR: 0.028578 | E: -42.278271 | E_var:     0.4628 | E_err:   0.010630
[2025-10-03 00:50:57] [Iter 1311/2250] R4[370/2400] | LR: 0.028562 | E: -42.289633 | E_var:     0.5304 | E_err:   0.011379
[2025-10-03 00:51:05] [Iter 1312/2250] R4[372/2400] | LR: 0.028547 | E: -42.280427 | E_var:     0.3574 | E_err:   0.009341
[2025-10-03 00:51:12] [Iter 1313/2250] R4[374/2400] | LR: 0.028532 | E: -42.308788 | E_var:     0.4634 | E_err:   0.010637
[2025-10-03 00:51:20] [Iter 1314/2250] R4[376/2400] | LR: 0.028516 | E: -42.274480 | E_var:     0.4692 | E_err:   0.010703
[2025-10-03 00:51:28] [Iter 1315/2250] R4[378/2400] | LR: 0.028501 | E: -42.284721 | E_var:     0.4470 | E_err:   0.010446
[2025-10-03 00:51:36] [Iter 1316/2250] R4[380/2400] | LR: 0.028485 | E: -42.277731 | E_var:     0.4360 | E_err:   0.010318
[2025-10-03 00:51:44] [Iter 1317/2250] R4[382/2400] | LR: 0.028470 | E: -42.293706 | E_var:     0.5441 | E_err:   0.011525
[2025-10-03 00:51:52] [Iter 1318/2250] R4[384/2400] | LR: 0.028454 | E: -42.290981 | E_var:     0.3713 | E_err:   0.009522
[2025-10-03 00:51:59] [Iter 1319/2250] R4[386/2400] | LR: 0.028438 | E: -42.286582 | E_var:     0.4950 | E_err:   0.010993
[2025-10-03 00:52:07] [Iter 1320/2250] R4[388/2400] | LR: 0.028422 | E: -42.281373 | E_var:     0.4740 | E_err:   0.010757
[2025-10-03 00:52:15] [Iter 1321/2250] R4[390/2400] | LR: 0.028406 | E: -42.298440 | E_var:     0.5409 | E_err:   0.011492
[2025-10-03 00:52:23] [Iter 1322/2250] R4[392/2400] | LR: 0.028390 | E: -42.298466 | E_var:     0.5203 | E_err:   0.011271
[2025-10-03 00:52:31] [Iter 1323/2250] R4[394/2400] | LR: 0.028374 | E: -42.314503 | E_var:     0.4601 | E_err:   0.010598
[2025-10-03 00:52:38] [Iter 1324/2250] R4[396/2400] | LR: 0.028358 | E: -42.290434 | E_var:     0.4857 | E_err:   0.010889
[2025-10-03 00:52:46] [Iter 1325/2250] R4[398/2400] | LR: 0.028342 | E: -42.280066 | E_var:     0.4035 | E_err:   0.009925
[2025-10-03 00:52:54] [Iter 1326/2250] R4[400/2400] | LR: 0.028325 | E: -42.287133 | E_var:     0.5077 | E_err:   0.011134
[2025-10-03 00:53:02] [Iter 1327/2250] R4[402/2400] | LR: 0.028309 | E: -42.299382 | E_var:     0.4209 | E_err:   0.010136
[2025-10-03 00:53:10] [Iter 1328/2250] R4[404/2400] | LR: 0.028292 | E: -42.275135 | E_var:     0.4249 | E_err:   0.010185
[2025-10-03 00:53:18] [Iter 1329/2250] R4[406/2400] | LR: 0.028276 | E: -42.291035 | E_var:     0.5380 | E_err:   0.011461
[2025-10-03 00:53:25] [Iter 1330/2250] R4[408/2400] | LR: 0.028259 | E: -42.277012 | E_var:     0.4126 | E_err:   0.010036
[2025-10-03 00:53:33] [Iter 1331/2250] R4[410/2400] | LR: 0.028243 | E: -42.272516 | E_var:     0.4156 | E_err:   0.010073
[2025-10-03 00:53:41] [Iter 1332/2250] R4[412/2400] | LR: 0.028226 | E: -42.284438 | E_var:     0.4300 | E_err:   0.010246
[2025-10-03 00:53:49] [Iter 1333/2250] R4[414/2400] | LR: 0.028209 | E: -42.299420 | E_var:     0.4454 | E_err:   0.010428
[2025-10-03 00:53:57] [Iter 1334/2250] R4[416/2400] | LR: 0.028192 | E: -42.289953 | E_var:     0.4526 | E_err:   0.010511
[2025-10-03 00:54:04] [Iter 1335/2250] R4[418/2400] | LR: 0.028175 | E: -42.289189 | E_var:     0.5320 | E_err:   0.011397
[2025-10-03 00:54:12] [Iter 1336/2250] R4[420/2400] | LR: 0.028158 | E: -42.286070 | E_var:     0.3948 | E_err:   0.009818
[2025-10-03 00:54:20] [Iter 1337/2250] R4[422/2400] | LR: 0.028141 | E: -42.276455 | E_var:     0.4320 | E_err:   0.010270
[2025-10-03 00:54:28] [Iter 1338/2250] R4[424/2400] | LR: 0.028124 | E: -42.297127 | E_var:     0.3769 | E_err:   0.009592
[2025-10-03 00:54:36] [Iter 1339/2250] R4[426/2400] | LR: 0.028106 | E: -42.277360 | E_var:     0.4398 | E_err:   0.010362
[2025-10-03 00:54:44] [Iter 1340/2250] R4[428/2400] | LR: 0.028089 | E: -42.298723 | E_var:     0.4267 | E_err:   0.010206
[2025-10-03 00:54:51] [Iter 1341/2250] R4[430/2400] | LR: 0.028072 | E: -42.285556 | E_var:     0.5306 | E_err:   0.011381
[2025-10-03 00:54:59] [Iter 1342/2250] R4[432/2400] | LR: 0.028054 | E: -42.298943 | E_var:     0.4445 | E_err:   0.010417
[2025-10-03 00:55:07] [Iter 1343/2250] R4[434/2400] | LR: 0.028037 | E: -42.277118 | E_var:     0.4151 | E_err:   0.010067
[2025-10-03 00:55:15] [Iter 1344/2250] R4[436/2400] | LR: 0.028019 | E: -42.312762 | E_var:     0.5817 | E_err:   0.011917
[2025-10-03 00:55:23] [Iter 1345/2250] R4[438/2400] | LR: 0.028001 | E: -42.301738 | E_var:     0.4846 | E_err:   0.010877
[2025-10-03 00:55:31] [Iter 1346/2250] R4[440/2400] | LR: 0.027983 | E: -42.272861 | E_var:     0.5467 | E_err:   0.011553
[2025-10-03 00:55:38] [Iter 1347/2250] R4[442/2400] | LR: 0.027966 | E: -42.296191 | E_var:     0.3885 | E_err:   0.009739
[2025-10-03 00:55:46] [Iter 1348/2250] R4[444/2400] | LR: 0.027948 | E: -42.292873 | E_var:     0.6317 | E_err:   0.012419
[2025-10-03 00:55:54] [Iter 1349/2250] R4[446/2400] | LR: 0.027930 | E: -42.285554 | E_var:     0.4092 | E_err:   0.009995
[2025-10-03 00:56:02] [Iter 1350/2250] R4[448/2400] | LR: 0.027912 | E: -42.269098 | E_var:     0.4312 | E_err:   0.010260
[2025-10-03 00:56:10] [Iter 1351/2250] R4[450/2400] | LR: 0.027893 | E: -42.276236 | E_var:     0.3677 | E_err:   0.009475
[2025-10-03 00:56:17] [Iter 1352/2250] R4[452/2400] | LR: 0.027875 | E: -42.293623 | E_var:     0.3810 | E_err:   0.009644
[2025-10-03 00:56:25] [Iter 1353/2250] R4[454/2400] | LR: 0.027857 | E: -42.295920 | E_var:     0.4693 | E_err:   0.010704
[2025-10-03 00:56:33] [Iter 1354/2250] R4[456/2400] | LR: 0.027839 | E: -42.268438 | E_var:     0.4865 | E_err:   0.010898
[2025-10-03 00:56:41] [Iter 1355/2250] R4[458/2400] | LR: 0.027820 | E: -42.316424 | E_var:     0.4129 | E_err:   0.010040
[2025-10-03 00:56:49] [Iter 1356/2250] R4[460/2400] | LR: 0.027802 | E: -42.281168 | E_var:     0.4513 | E_err:   0.010497
[2025-10-03 00:56:57] [Iter 1357/2250] R4[462/2400] | LR: 0.027783 | E: -42.288590 | E_var:     0.4526 | E_err:   0.010512
[2025-10-03 00:57:04] [Iter 1358/2250] R4[464/2400] | LR: 0.027764 | E: -42.301969 | E_var:     0.5008 | E_err:   0.011057
[2025-10-03 00:57:12] [Iter 1359/2250] R4[466/2400] | LR: 0.027746 | E: -42.288693 | E_var:     0.3623 | E_err:   0.009405
[2025-10-03 00:57:20] [Iter 1360/2250] R4[468/2400] | LR: 0.027727 | E: -42.304293 | E_var:     0.5407 | E_err:   0.011490
[2025-10-03 00:57:28] [Iter 1361/2250] R4[470/2400] | LR: 0.027708 | E: -42.288639 | E_var:     0.4742 | E_err:   0.010760
[2025-10-03 00:57:36] [Iter 1362/2250] R4[472/2400] | LR: 0.027689 | E: -42.289370 | E_var:     0.5195 | E_err:   0.011262
[2025-10-03 00:57:43] [Iter 1363/2250] R4[474/2400] | LR: 0.027670 | E: -42.300544 | E_var:     0.3980 | E_err:   0.009858
[2025-10-03 00:57:51] [Iter 1364/2250] R4[476/2400] | LR: 0.027651 | E: -42.286553 | E_var:     0.3788 | E_err:   0.009617
[2025-10-03 00:57:59] [Iter 1365/2250] R4[478/2400] | LR: 0.027632 | E: -42.294896 | E_var:     0.3791 | E_err:   0.009620
[2025-10-03 00:58:07] [Iter 1366/2250] R4[480/2400] | LR: 0.027613 | E: -42.297595 | E_var:     0.4582 | E_err:   0.010577
[2025-10-03 00:58:15] [Iter 1367/2250] R4[482/2400] | LR: 0.027593 | E: -42.300974 | E_var:     0.6366 | E_err:   0.012466
[2025-10-03 00:58:23] [Iter 1368/2250] R4[484/2400] | LR: 0.027574 | E: -42.319269 | E_var:     0.6977 | E_err:   0.013051
[2025-10-03 00:58:30] [Iter 1369/2250] R4[486/2400] | LR: 0.027555 | E: -42.285487 | E_var:     0.4939 | E_err:   0.010981
[2025-10-03 00:58:38] [Iter 1370/2250] R4[488/2400] | LR: 0.027535 | E: -42.301661 | E_var:     0.4561 | E_err:   0.010553
[2025-10-03 00:58:46] [Iter 1371/2250] R4[490/2400] | LR: 0.027516 | E: -42.292940 | E_var:     0.3836 | E_err:   0.009678
[2025-10-03 00:58:54] [Iter 1372/2250] R4[492/2400] | LR: 0.027496 | E: -42.269391 | E_var:     0.5896 | E_err:   0.011998
[2025-10-03 00:59:02] [Iter 1373/2250] R4[494/2400] | LR: 0.027476 | E: -42.273285 | E_var:     0.4684 | E_err:   0.010694
[2025-10-03 00:59:09] [Iter 1374/2250] R4[496/2400] | LR: 0.027457 | E: -42.287503 | E_var:     0.4151 | E_err:   0.010067
[2025-10-03 00:59:17] [Iter 1375/2250] R4[498/2400] | LR: 0.027437 | E: -42.289598 | E_var:     0.5157 | E_err:   0.011220
[2025-10-03 00:59:25] [Iter 1376/2250] R4[500/2400] | LR: 0.027417 | E: -42.282704 | E_var:     0.3860 | E_err:   0.009708
[2025-10-03 00:59:33] [Iter 1377/2250] R4[502/2400] | LR: 0.027397 | E: -42.295311 | E_var:     0.4706 | E_err:   0.010719
[2025-10-03 00:59:41] [Iter 1378/2250] R4[504/2400] | LR: 0.027377 | E: -42.288287 | E_var:     0.4684 | E_err:   0.010693
[2025-10-03 00:59:49] [Iter 1379/2250] R4[506/2400] | LR: 0.027357 | E: -42.296021 | E_var:     0.5032 | E_err:   0.011084
[2025-10-03 00:59:56] [Iter 1380/2250] R4[508/2400] | LR: 0.027337 | E: -42.293296 | E_var:     0.4049 | E_err:   0.009943
[2025-10-03 01:00:04] [Iter 1381/2250] R4[510/2400] | LR: 0.027316 | E: -42.287268 | E_var:     0.4147 | E_err:   0.010063
[2025-10-03 01:00:12] [Iter 1382/2250] R4[512/2400] | LR: 0.027296 | E: -42.296301 | E_var:     0.4055 | E_err:   0.009950
[2025-10-03 01:00:20] [Iter 1383/2250] R4[514/2400] | LR: 0.027276 | E: -42.286741 | E_var:     0.4716 | E_err:   0.010730
[2025-10-03 01:00:28] [Iter 1384/2250] R4[516/2400] | LR: 0.027255 | E: -42.281227 | E_var:     0.3845 | E_err:   0.009688
[2025-10-03 01:00:35] [Iter 1385/2250] R4[518/2400] | LR: 0.027235 | E: -42.294115 | E_var:     0.4292 | E_err:   0.010237
[2025-10-03 01:00:43] [Iter 1386/2250] R4[520/2400] | LR: 0.027214 | E: -42.269893 | E_var:     0.4342 | E_err:   0.010296
[2025-10-03 01:00:51] [Iter 1387/2250] R4[522/2400] | LR: 0.027194 | E: -42.302788 | E_var:     0.4265 | E_err:   0.010204
[2025-10-03 01:00:59] [Iter 1388/2250] R4[524/2400] | LR: 0.027173 | E: -42.307715 | E_var:     0.4900 | E_err:   0.010938
[2025-10-03 01:01:07] [Iter 1389/2250] R4[526/2400] | LR: 0.027152 | E: -42.273350 | E_var:     0.4213 | E_err:   0.010142
[2025-10-03 01:01:15] [Iter 1390/2250] R4[528/2400] | LR: 0.027131 | E: -42.270233 | E_var:     0.5042 | E_err:   0.011095
[2025-10-03 01:01:22] [Iter 1391/2250] R4[530/2400] | LR: 0.027111 | E: -42.288180 | E_var:     0.5037 | E_err:   0.011089
[2025-10-03 01:01:30] [Iter 1392/2250] R4[532/2400] | LR: 0.027090 | E: -42.313415 | E_var:     0.3901 | E_err:   0.009759
[2025-10-03 01:01:38] [Iter 1393/2250] R4[534/2400] | LR: 0.027069 | E: -42.290709 | E_var:     0.4674 | E_err:   0.010682
[2025-10-03 01:01:46] [Iter 1394/2250] R4[536/2400] | LR: 0.027047 | E: -42.296750 | E_var:     0.4538 | E_err:   0.010525
[2025-10-03 01:01:54] [Iter 1395/2250] R4[538/2400] | LR: 0.027026 | E: -42.290197 | E_var:     0.4138 | E_err:   0.010051
[2025-10-03 01:02:01] [Iter 1396/2250] R4[540/2400] | LR: 0.027005 | E: -42.277103 | E_var:     1.7279 | E_err:   0.020539
[2025-10-03 01:02:09] [Iter 1397/2250] R4[542/2400] | LR: 0.026984 | E: -42.295583 | E_var:     0.4333 | E_err:   0.010286
[2025-10-03 01:02:17] [Iter 1398/2250] R4[544/2400] | LR: 0.026962 | E: -42.287127 | E_var:     0.6080 | E_err:   0.012184
[2025-10-03 01:02:25] [Iter 1399/2250] R4[546/2400] | LR: 0.026941 | E: -42.303698 | E_var:     0.4715 | E_err:   0.010729
[2025-10-03 01:02:33] [Iter 1400/2250] R4[548/2400] | LR: 0.026920 | E: -42.282366 | E_var:     0.4577 | E_err:   0.010571
[2025-10-03 01:02:33] ✓ Checkpoint saved: checkpoint_iter_001400.pkl
[2025-10-03 01:02:41] [Iter 1401/2250] R4[550/2400] | LR: 0.026898 | E: -42.298581 | E_var:     0.5249 | E_err:   0.011320
[2025-10-03 01:02:48] [Iter 1402/2250] R4[552/2400] | LR: 0.026876 | E: -42.293111 | E_var:     0.4782 | E_err:   0.010805
[2025-10-03 01:02:56] [Iter 1403/2250] R4[554/2400] | LR: 0.026855 | E: -42.302344 | E_var:     0.4754 | E_err:   0.010773
[2025-10-03 01:03:04] [Iter 1404/2250] R4[556/2400] | LR: 0.026833 | E: -42.293677 | E_var:     0.6640 | E_err:   0.012732
[2025-10-03 01:03:12] [Iter 1405/2250] R4[558/2400] | LR: 0.026811 | E: -42.283882 | E_var:     0.4383 | E_err:   0.010344
[2025-10-03 01:03:20] [Iter 1406/2250] R4[560/2400] | LR: 0.026789 | E: -42.285948 | E_var:     0.4251 | E_err:   0.010187
[2025-10-03 01:03:27] [Iter 1407/2250] R4[562/2400] | LR: 0.026767 | E: -42.301753 | E_var:     0.4133 | E_err:   0.010045
[2025-10-03 01:03:35] [Iter 1408/2250] R4[564/2400] | LR: 0.026745 | E: -42.310454 | E_var:     0.4399 | E_err:   0.010364
[2025-10-03 01:03:43] [Iter 1409/2250] R4[566/2400] | LR: 0.026723 | E: -42.297978 | E_var:     0.5368 | E_err:   0.011448
[2025-10-03 01:03:51] [Iter 1410/2250] R4[568/2400] | LR: 0.026701 | E: -42.280435 | E_var:     0.4707 | E_err:   0.010720
[2025-10-03 01:03:59] [Iter 1411/2250] R4[570/2400] | LR: 0.026679 | E: -42.286357 | E_var:     0.4633 | E_err:   0.010636
[2025-10-03 01:04:07] [Iter 1412/2250] R4[572/2400] | LR: 0.026657 | E: -42.288981 | E_var:     0.5188 | E_err:   0.011254
[2025-10-03 01:04:14] [Iter 1413/2250] R4[574/2400] | LR: 0.026634 | E: -42.274692 | E_var:     0.3946 | E_err:   0.009816
[2025-10-03 01:04:22] [Iter 1414/2250] R4[576/2400] | LR: 0.026612 | E: -42.280902 | E_var:     0.4250 | E_err:   0.010187
[2025-10-03 01:04:30] [Iter 1415/2250] R4[578/2400] | LR: 0.026590 | E: -42.290718 | E_var:     0.4391 | E_err:   0.010354
[2025-10-03 01:04:38] [Iter 1416/2250] R4[580/2400] | LR: 0.026567 | E: -42.308468 | E_var:     0.4349 | E_err:   0.010304
[2025-10-03 01:04:46] [Iter 1417/2250] R4[582/2400] | LR: 0.026545 | E: -42.289212 | E_var:     0.3735 | E_err:   0.009549
[2025-10-03 01:04:54] [Iter 1418/2250] R4[584/2400] | LR: 0.026522 | E: -42.294233 | E_var:     0.4709 | E_err:   0.010722
[2025-10-03 01:05:01] [Iter 1419/2250] R4[586/2400] | LR: 0.026499 | E: -42.301109 | E_var:     0.4508 | E_err:   0.010491
[2025-10-03 01:05:09] [Iter 1420/2250] R4[588/2400] | LR: 0.026477 | E: -42.295406 | E_var:     0.4453 | E_err:   0.010426
[2025-10-03 01:05:17] [Iter 1421/2250] R4[590/2400] | LR: 0.026454 | E: -42.306537 | E_var:     0.4222 | E_err:   0.010153
[2025-10-03 01:05:25] [Iter 1422/2250] R4[592/2400] | LR: 0.026431 | E: -42.297811 | E_var:     0.5003 | E_err:   0.011052
[2025-10-03 01:05:33] [Iter 1423/2250] R4[594/2400] | LR: 0.026408 | E: -42.282937 | E_var:     0.4881 | E_err:   0.010917
[2025-10-03 01:05:40] [Iter 1424/2250] R4[596/2400] | LR: 0.026385 | E: -42.284888 | E_var:     0.4623 | E_err:   0.010624
[2025-10-03 01:05:48] [Iter 1425/2250] R4[598/2400] | LR: 0.026362 | E: -42.292417 | E_var:     7.8564 | E_err:   0.043796
[2025-10-03 01:05:56] [Iter 1426/2250] R4[600/2400] | LR: 0.026339 | E: -42.274673 | E_var:     0.5080 | E_err:   0.011136
[2025-10-03 01:06:04] [Iter 1427/2250] R4[602/2400] | LR: 0.026316 | E: -42.291643 | E_var:     0.4573 | E_err:   0.010566
[2025-10-03 01:06:12] [Iter 1428/2250] R4[604/2400] | LR: 0.026292 | E: -42.286501 | E_var:     0.5222 | E_err:   0.011292
[2025-10-03 01:06:19] [Iter 1429/2250] R4[606/2400] | LR: 0.026269 | E: -42.293047 | E_var:     0.5645 | E_err:   0.011740
[2025-10-03 01:06:27] [Iter 1430/2250] R4[608/2400] | LR: 0.026246 | E: -42.281290 | E_var:     0.4962 | E_err:   0.011006
[2025-10-03 01:06:35] [Iter 1431/2250] R4[610/2400] | LR: 0.026222 | E: -42.282666 | E_var:     0.5419 | E_err:   0.011502
[2025-10-03 01:06:43] [Iter 1432/2250] R4[612/2400] | LR: 0.026199 | E: -42.279129 | E_var:     0.5592 | E_err:   0.011685
[2025-10-03 01:06:51] [Iter 1433/2250] R4[614/2400] | LR: 0.026175 | E: -42.293210 | E_var:     0.3564 | E_err:   0.009328
[2025-10-03 01:06:59] [Iter 1434/2250] R4[616/2400] | LR: 0.026152 | E: -42.277578 | E_var:     0.3700 | E_err:   0.009504
[2025-10-03 01:07:06] [Iter 1435/2250] R4[618/2400] | LR: 0.026128 | E: -42.281880 | E_var:     0.4410 | E_err:   0.010376
[2025-10-03 01:07:14] [Iter 1436/2250] R4[620/2400] | LR: 0.026104 | E: -42.284715 | E_var:     0.6345 | E_err:   0.012447
[2025-10-03 01:07:22] [Iter 1437/2250] R4[622/2400] | LR: 0.026081 | E: -42.287712 | E_var:     0.4127 | E_err:   0.010038
[2025-10-03 01:07:30] [Iter 1438/2250] R4[624/2400] | LR: 0.026057 | E: -42.283066 | E_var:     0.4722 | E_err:   0.010737
[2025-10-03 01:07:38] [Iter 1439/2250] R4[626/2400] | LR: 0.026033 | E: -42.287044 | E_var:     0.4522 | E_err:   0.010507
[2025-10-03 01:07:45] [Iter 1440/2250] R4[628/2400] | LR: 0.026009 | E: -42.281988 | E_var:     0.4435 | E_err:   0.010405
[2025-10-03 01:07:53] [Iter 1441/2250] R4[630/2400] | LR: 0.025985 | E: -42.296093 | E_var:     0.4920 | E_err:   0.010959
[2025-10-03 01:08:01] [Iter 1442/2250] R4[632/2400] | LR: 0.025961 | E: -42.295394 | E_var:     0.4757 | E_err:   0.010777
[2025-10-03 01:08:09] [Iter 1443/2250] R4[634/2400] | LR: 0.025937 | E: -42.285339 | E_var:     0.4419 | E_err:   0.010386
[2025-10-03 01:08:17] [Iter 1444/2250] R4[636/2400] | LR: 0.025913 | E: -42.291798 | E_var:     0.6288 | E_err:   0.012391
[2025-10-03 01:08:25] [Iter 1445/2250] R4[638/2400] | LR: 0.025888 | E: -42.293976 | E_var:     0.4199 | E_err:   0.010125
[2025-10-03 01:08:32] [Iter 1446/2250] R4[640/2400] | LR: 0.025864 | E: -42.290547 | E_var:     0.4263 | E_err:   0.010202
[2025-10-03 01:08:40] [Iter 1447/2250] R4[642/2400] | LR: 0.025840 | E: -42.292100 | E_var:     0.4433 | E_err:   0.010403
[2025-10-03 01:08:48] [Iter 1448/2250] R4[644/2400] | LR: 0.025815 | E: -42.290139 | E_var:     0.3826 | E_err:   0.009665
[2025-10-03 01:08:56] [Iter 1449/2250] R4[646/2400] | LR: 0.025791 | E: -42.312977 | E_var:     0.3687 | E_err:   0.009488
[2025-10-03 01:09:04] [Iter 1450/2250] R4[648/2400] | LR: 0.025766 | E: -42.294606 | E_var:     0.4182 | E_err:   0.010104
[2025-10-03 01:09:11] [Iter 1451/2250] R4[650/2400] | LR: 0.025742 | E: -42.297850 | E_var:     0.4444 | E_err:   0.010416
[2025-10-03 01:09:19] [Iter 1452/2250] R4[652/2400] | LR: 0.025717 | E: -42.294275 | E_var:     0.4567 | E_err:   0.010559
[2025-10-03 01:09:27] [Iter 1453/2250] R4[654/2400] | LR: 0.025693 | E: -42.287002 | E_var:     0.3755 | E_err:   0.009574
[2025-10-03 01:09:35] [Iter 1454/2250] R4[656/2400] | LR: 0.025668 | E: -42.286577 | E_var:     0.4192 | E_err:   0.010117
[2025-10-03 01:09:43] [Iter 1455/2250] R4[658/2400] | LR: 0.025643 | E: -42.293139 | E_var:     0.5030 | E_err:   0.011082
[2025-10-03 01:09:51] [Iter 1456/2250] R4[660/2400] | LR: 0.025618 | E: -42.286733 | E_var:     0.4199 | E_err:   0.010126
[2025-10-03 01:09:58] [Iter 1457/2250] R4[662/2400] | LR: 0.025593 | E: -42.284526 | E_var:     0.6118 | E_err:   0.012221
[2025-10-03 01:10:06] [Iter 1458/2250] R4[664/2400] | LR: 0.025568 | E: -42.286157 | E_var:     0.4424 | E_err:   0.010392
[2025-10-03 01:10:14] [Iter 1459/2250] R4[666/2400] | LR: 0.025543 | E: -42.282548 | E_var:     0.4450 | E_err:   0.010423
[2025-10-03 01:10:22] [Iter 1460/2250] R4[668/2400] | LR: 0.025518 | E: -42.279881 | E_var:     0.5003 | E_err:   0.011052
[2025-10-03 01:10:30] [Iter 1461/2250] R4[670/2400] | LR: 0.025493 | E: -42.286809 | E_var:     0.3947 | E_err:   0.009816
[2025-10-03 01:10:38] [Iter 1462/2250] R4[672/2400] | LR: 0.025468 | E: -42.301754 | E_var:     0.3987 | E_err:   0.009866
[2025-10-03 01:10:45] [Iter 1463/2250] R4[674/2400] | LR: 0.025443 | E: -42.294418 | E_var:     0.4936 | E_err:   0.010977
[2025-10-03 01:10:53] [Iter 1464/2250] R4[676/2400] | LR: 0.025417 | E: -42.314551 | E_var:     0.4636 | E_err:   0.010639
[2025-10-03 01:11:01] [Iter 1465/2250] R4[678/2400] | LR: 0.025392 | E: -42.287445 | E_var:     0.4171 | E_err:   0.010092
[2025-10-03 01:11:09] [Iter 1466/2250] R4[680/2400] | LR: 0.025367 | E: -42.297119 | E_var:     0.4127 | E_err:   0.010037
[2025-10-03 01:11:17] [Iter 1467/2250] R4[682/2400] | LR: 0.025341 | E: -42.289095 | E_var:     0.4502 | E_err:   0.010484
[2025-10-03 01:11:24] [Iter 1468/2250] R4[684/2400] | LR: 0.025316 | E: -42.253209 | E_var:     0.4470 | E_err:   0.010446
[2025-10-03 01:11:32] [Iter 1469/2250] R4[686/2400] | LR: 0.025290 | E: -42.298907 | E_var:     0.5325 | E_err:   0.011402
[2025-10-03 01:11:40] [Iter 1470/2250] R4[688/2400] | LR: 0.025264 | E: -42.298626 | E_var:     0.5025 | E_err:   0.011076
[2025-10-03 01:11:48] [Iter 1471/2250] R4[690/2400] | LR: 0.025239 | E: -42.302880 | E_var:     0.4829 | E_err:   0.010858
[2025-10-03 01:11:56] [Iter 1472/2250] R4[692/2400] | LR: 0.025213 | E: -42.298745 | E_var:     0.4882 | E_err:   0.010918
[2025-10-03 01:12:04] [Iter 1473/2250] R4[694/2400] | LR: 0.025187 | E: -42.292376 | E_var:     0.3835 | E_err:   0.009677
[2025-10-03 01:12:11] [Iter 1474/2250] R4[696/2400] | LR: 0.025161 | E: -42.293401 | E_var:     0.4278 | E_err:   0.010220
[2025-10-03 01:12:19] [Iter 1475/2250] R4[698/2400] | LR: 0.025135 | E: -42.305962 | E_var:     0.4769 | E_err:   0.010790
[2025-10-03 01:12:27] [Iter 1476/2250] R4[700/2400] | LR: 0.025110 | E: -42.292321 | E_var:     0.3685 | E_err:   0.009486
[2025-10-03 01:12:35] [Iter 1477/2250] R4[702/2400] | LR: 0.025084 | E: -42.299730 | E_var:     0.5748 | E_err:   0.011846
[2025-10-03 01:12:43] [Iter 1478/2250] R4[704/2400] | LR: 0.025057 | E: -42.312899 | E_var:     0.4224 | E_err:   0.010155
[2025-10-03 01:12:50] [Iter 1479/2250] R4[706/2400] | LR: 0.025031 | E: -42.277120 | E_var:     0.5059 | E_err:   0.011113
[2025-10-03 01:12:58] [Iter 1480/2250] R4[708/2400] | LR: 0.025005 | E: -42.295990 | E_var:     0.5936 | E_err:   0.012038
[2025-10-03 01:13:06] [Iter 1481/2250] R4[710/2400] | LR: 0.024979 | E: -42.294999 | E_var:     0.4346 | E_err:   0.010300
[2025-10-03 01:13:14] [Iter 1482/2250] R4[712/2400] | LR: 0.024953 | E: -42.300643 | E_var:     0.4032 | E_err:   0.009922
[2025-10-03 01:13:22] [Iter 1483/2250] R4[714/2400] | LR: 0.024927 | E: -42.287129 | E_var:     0.4759 | E_err:   0.010779
[2025-10-03 01:13:30] [Iter 1484/2250] R4[716/2400] | LR: 0.024900 | E: -42.291687 | E_var:     0.3848 | E_err:   0.009692
[2025-10-03 01:13:38] [Iter 1485/2250] R4[718/2400] | LR: 0.024874 | E: -42.294204 | E_var:     0.5513 | E_err:   0.011601
[2025-10-03 01:13:46] [Iter 1486/2250] R4[720/2400] | LR: 0.024847 | E: -42.301540 | E_var:     0.4334 | E_err:   0.010287
[2025-10-03 01:13:53] [Iter 1487/2250] R4[722/2400] | LR: 0.024821 | E: -42.309939 | E_var:     0.5601 | E_err:   0.011694
[2025-10-03 01:14:01] [Iter 1488/2250] R4[724/2400] | LR: 0.024794 | E: -42.304027 | E_var:     0.4014 | E_err:   0.009899
[2025-10-03 01:14:09] [Iter 1489/2250] R4[726/2400] | LR: 0.024768 | E: -42.309048 | E_var:     0.4349 | E_err:   0.010305
[2025-10-03 01:14:17] [Iter 1490/2250] R4[728/2400] | LR: 0.024741 | E: -42.307722 | E_var:     0.4405 | E_err:   0.010371
[2025-10-03 01:14:25] [Iter 1491/2250] R4[730/2400] | LR: 0.024714 | E: -42.299808 | E_var:     0.3916 | E_err:   0.009778
[2025-10-03 01:14:33] [Iter 1492/2250] R4[732/2400] | LR: 0.024688 | E: -42.305584 | E_var:     0.4094 | E_err:   0.009997
[2025-10-03 01:14:40] [Iter 1493/2250] R4[734/2400] | LR: 0.024661 | E: -42.282731 | E_var:     0.3215 | E_err:   0.008860
[2025-10-03 01:14:48] [Iter 1494/2250] R4[736/2400] | LR: 0.024634 | E: -42.301293 | E_var:     0.4304 | E_err:   0.010251
[2025-10-03 01:14:56] [Iter 1495/2250] R4[738/2400] | LR: 0.024607 | E: -42.300388 | E_var:     0.4075 | E_err:   0.009975
[2025-10-03 01:15:04] [Iter 1496/2250] R4[740/2400] | LR: 0.024580 | E: -42.305426 | E_var:     0.6053 | E_err:   0.012156
[2025-10-03 01:15:12] [Iter 1497/2250] R4[742/2400] | LR: 0.024553 | E: -42.288137 | E_var:     0.4425 | E_err:   0.010394
[2025-10-03 01:15:19] [Iter 1498/2250] R4[744/2400] | LR: 0.024526 | E: -42.290667 | E_var:     0.4387 | E_err:   0.010349
[2025-10-03 01:15:27] [Iter 1499/2250] R4[746/2400] | LR: 0.024499 | E: -42.289933 | E_var:     0.3380 | E_err:   0.009083
[2025-10-03 01:15:35] [Iter 1500/2250] R4[748/2400] | LR: 0.024472 | E: -42.286794 | E_var:     0.4984 | E_err:   0.011031
[2025-10-03 01:15:43] [Iter 1501/2250] R4[750/2400] | LR: 0.024445 | E: -42.280696 | E_var:     0.4023 | E_err:   0.009911
[2025-10-03 01:15:51] [Iter 1502/2250] R4[752/2400] | LR: 0.024417 | E: -42.288198 | E_var:     0.5245 | E_err:   0.011316
[2025-10-03 01:15:59] [Iter 1503/2250] R4[754/2400] | LR: 0.024390 | E: -42.290616 | E_var:     0.3957 | E_err:   0.009828
[2025-10-03 01:16:06] [Iter 1504/2250] R4[756/2400] | LR: 0.024363 | E: -42.302587 | E_var:     0.3847 | E_err:   0.009692
[2025-10-03 01:16:14] [Iter 1505/2250] R4[758/2400] | LR: 0.024335 | E: -42.289230 | E_var:     0.5183 | E_err:   0.011249
[2025-10-03 01:16:22] [Iter 1506/2250] R4[760/2400] | LR: 0.024308 | E: -42.293315 | E_var:     0.4405 | E_err:   0.010371
[2025-10-03 01:16:30] [Iter 1507/2250] R4[762/2400] | LR: 0.024281 | E: -42.299513 | E_var:     0.4146 | E_err:   0.010061
[2025-10-03 01:16:38] [Iter 1508/2250] R4[764/2400] | LR: 0.024253 | E: -42.305077 | E_var:     0.3508 | E_err:   0.009255
[2025-10-03 01:16:45] [Iter 1509/2250] R4[766/2400] | LR: 0.024225 | E: -42.289118 | E_var:     0.3557 | E_err:   0.009319
[2025-10-03 01:16:53] [Iter 1510/2250] R4[768/2400] | LR: 0.024198 | E: -42.270061 | E_var:     0.6416 | E_err:   0.012516
[2025-10-03 01:17:01] [Iter 1511/2250] R4[770/2400] | LR: 0.024170 | E: -42.296326 | E_var:     0.4892 | E_err:   0.010929
[2025-10-03 01:17:09] [Iter 1512/2250] R4[772/2400] | LR: 0.024142 | E: -42.310623 | E_var:     0.4608 | E_err:   0.010606
[2025-10-03 01:17:17] [Iter 1513/2250] R4[774/2400] | LR: 0.024115 | E: -42.281890 | E_var:     0.4159 | E_err:   0.010077
[2025-10-03 01:17:25] [Iter 1514/2250] R4[776/2400] | LR: 0.024087 | E: -42.300771 | E_var:     0.4558 | E_err:   0.010549
[2025-10-03 01:17:32] [Iter 1515/2250] R4[778/2400] | LR: 0.024059 | E: -42.277268 | E_var:     0.4375 | E_err:   0.010335
[2025-10-03 01:17:40] [Iter 1516/2250] R4[780/2400] | LR: 0.024031 | E: -42.301983 | E_var:     0.3934 | E_err:   0.009800
[2025-10-03 01:17:48] [Iter 1517/2250] R4[782/2400] | LR: 0.024003 | E: -42.298697 | E_var:     0.5145 | E_err:   0.011208
[2025-10-03 01:17:56] [Iter 1518/2250] R4[784/2400] | LR: 0.023975 | E: -42.276537 | E_var:     0.4128 | E_err:   0.010039
[2025-10-03 01:18:04] [Iter 1519/2250] R4[786/2400] | LR: 0.023947 | E: -42.284755 | E_var:     0.4045 | E_err:   0.009937
[2025-10-03 01:18:11] [Iter 1520/2250] R4[788/2400] | LR: 0.023919 | E: -42.285388 | E_var:     0.5540 | E_err:   0.011630
[2025-10-03 01:18:19] [Iter 1521/2250] R4[790/2400] | LR: 0.023891 | E: -42.307482 | E_var:     0.5630 | E_err:   0.011724
[2025-10-03 01:18:27] [Iter 1522/2250] R4[792/2400] | LR: 0.023863 | E: -42.312621 | E_var:     0.5716 | E_err:   0.011813
[2025-10-03 01:18:35] [Iter 1523/2250] R4[794/2400] | LR: 0.023835 | E: -42.288599 | E_var:     0.4488 | E_err:   0.010468
[2025-10-03 01:18:43] [Iter 1524/2250] R4[796/2400] | LR: 0.023807 | E: -42.290565 | E_var:     0.6019 | E_err:   0.012123
[2025-10-03 01:18:51] [Iter 1525/2250] R4[798/2400] | LR: 0.023778 | E: -42.298313 | E_var:     0.3840 | E_err:   0.009683
[2025-10-03 01:18:58] [Iter 1526/2250] R4[800/2400] | LR: 0.023750 | E: -42.285166 | E_var:     0.3928 | E_err:   0.009793
[2025-10-03 01:19:06] [Iter 1527/2250] R4[802/2400] | LR: 0.023722 | E: -42.280007 | E_var:     0.4389 | E_err:   0.010352
[2025-10-03 01:19:14] [Iter 1528/2250] R4[804/2400] | LR: 0.023693 | E: -42.295215 | E_var:     0.4147 | E_err:   0.010062
[2025-10-03 01:19:22] [Iter 1529/2250] R4[806/2400] | LR: 0.023665 | E: -42.282093 | E_var:     0.4046 | E_err:   0.009939
[2025-10-03 01:19:30] [Iter 1530/2250] R4[808/2400] | LR: 0.023636 | E: -42.293971 | E_var:     0.5471 | E_err:   0.011557
[2025-10-03 01:19:37] [Iter 1531/2250] R4[810/2400] | LR: 0.023608 | E: -42.277758 | E_var:     0.3814 | E_err:   0.009650
[2025-10-03 01:19:45] [Iter 1532/2250] R4[812/2400] | LR: 0.023579 | E: -42.288074 | E_var:     0.5123 | E_err:   0.011184
[2025-10-03 01:19:53] [Iter 1533/2250] R4[814/2400] | LR: 0.023551 | E: -42.290626 | E_var:     0.4282 | E_err:   0.010225
[2025-10-03 01:20:01] [Iter 1534/2250] R4[816/2400] | LR: 0.023522 | E: -42.280363 | E_var:     0.4953 | E_err:   0.010997
[2025-10-03 01:20:09] [Iter 1535/2250] R4[818/2400] | LR: 0.023493 | E: -42.295340 | E_var:     0.4528 | E_err:   0.010514
[2025-10-03 01:20:17] [Iter 1536/2250] R4[820/2400] | LR: 0.023464 | E: -42.298301 | E_var:     0.4771 | E_err:   0.010792
[2025-10-03 01:20:24] [Iter 1537/2250] R4[822/2400] | LR: 0.023436 | E: -42.284811 | E_var:     0.4770 | E_err:   0.010791
[2025-10-03 01:20:32] [Iter 1538/2250] R4[824/2400] | LR: 0.023407 | E: -42.288032 | E_var:     0.4817 | E_err:   0.010844
[2025-10-03 01:20:40] [Iter 1539/2250] R4[826/2400] | LR: 0.023378 | E: -42.298151 | E_var:     0.4252 | E_err:   0.010188
[2025-10-03 01:20:48] [Iter 1540/2250] R4[828/2400] | LR: 0.023349 | E: -42.296108 | E_var:     0.6187 | E_err:   0.012290
[2025-10-03 01:20:56] [Iter 1541/2250] R4[830/2400] | LR: 0.023320 | E: -42.263553 | E_var:     0.4282 | E_err:   0.010224
[2025-10-03 01:21:03] [Iter 1542/2250] R4[832/2400] | LR: 0.023291 | E: -42.286013 | E_var:     0.5209 | E_err:   0.011278
[2025-10-03 01:21:11] [Iter 1543/2250] R4[834/2400] | LR: 0.023262 | E: -42.287163 | E_var:     0.4237 | E_err:   0.010170
[2025-10-03 01:21:19] [Iter 1544/2250] R4[836/2400] | LR: 0.023233 | E: -42.287290 | E_var:     0.4350 | E_err:   0.010306
[2025-10-03 01:21:27] [Iter 1545/2250] R4[838/2400] | LR: 0.023204 | E: -42.296481 | E_var:     0.3792 | E_err:   0.009622
[2025-10-03 01:21:35] [Iter 1546/2250] R4[840/2400] | LR: 0.023175 | E: -42.292261 | E_var:     0.3856 | E_err:   0.009702
[2025-10-03 01:21:43] [Iter 1547/2250] R4[842/2400] | LR: 0.023146 | E: -42.282963 | E_var:     0.4389 | E_err:   0.010352
[2025-10-03 01:21:50] [Iter 1548/2250] R4[844/2400] | LR: 0.023116 | E: -42.286344 | E_var:     0.4210 | E_err:   0.010139
[2025-10-03 01:21:58] [Iter 1549/2250] R4[846/2400] | LR: 0.023087 | E: -42.281443 | E_var:     0.3939 | E_err:   0.009807
[2025-10-03 01:22:06] [Iter 1550/2250] R4[848/2400] | LR: 0.023058 | E: -42.297030 | E_var:     0.4250 | E_err:   0.010187
[2025-10-03 01:22:14] [Iter 1551/2250] R4[850/2400] | LR: 0.023029 | E: -42.284819 | E_var:     0.5183 | E_err:   0.011249
[2025-10-03 01:22:22] [Iter 1552/2250] R4[852/2400] | LR: 0.022999 | E: -42.303460 | E_var:     0.3465 | E_err:   0.009197
[2025-10-03 01:22:29] [Iter 1553/2250] R4[854/2400] | LR: 0.022970 | E: -42.285116 | E_var:     0.3904 | E_err:   0.009763
[2025-10-03 01:22:37] [Iter 1554/2250] R4[856/2400] | LR: 0.022940 | E: -42.288951 | E_var:     0.4153 | E_err:   0.010069
[2025-10-03 01:22:45] [Iter 1555/2250] R4[858/2400] | LR: 0.022911 | E: -42.297057 | E_var:     0.4922 | E_err:   0.010962
[2025-10-03 01:22:53] [Iter 1556/2250] R4[860/2400] | LR: 0.022881 | E: -42.302893 | E_var:     0.3895 | E_err:   0.009752
[2025-10-03 01:23:01] [Iter 1557/2250] R4[862/2400] | LR: 0.022852 | E: -42.277243 | E_var:     0.5127 | E_err:   0.011188
[2025-10-03 01:23:09] [Iter 1558/2250] R4[864/2400] | LR: 0.022822 | E: -42.312076 | E_var:     0.5505 | E_err:   0.011593
[2025-10-03 01:23:16] [Iter 1559/2250] R4[866/2400] | LR: 0.022793 | E: -42.290166 | E_var:     0.5162 | E_err:   0.011226
[2025-10-03 01:23:24] [Iter 1560/2250] R4[868/2400] | LR: 0.022763 | E: -42.270183 | E_var:     0.5836 | E_err:   0.011937
[2025-10-03 01:23:32] [Iter 1561/2250] R4[870/2400] | LR: 0.022733 | E: -42.303507 | E_var:     0.4346 | E_err:   0.010300
[2025-10-03 01:23:40] [Iter 1562/2250] R4[872/2400] | LR: 0.022704 | E: -42.287652 | E_var:     0.4377 | E_err:   0.010337
[2025-10-03 01:23:48] [Iter 1563/2250] R4[874/2400] | LR: 0.022674 | E: -42.307586 | E_var:     0.4577 | E_err:   0.010570
[2025-10-03 01:23:55] [Iter 1564/2250] R4[876/2400] | LR: 0.022644 | E: -42.279099 | E_var:     0.4261 | E_err:   0.010200
[2025-10-03 01:24:03] [Iter 1565/2250] R4[878/2400] | LR: 0.022614 | E: -42.281986 | E_var:     0.4732 | E_err:   0.010748
[2025-10-03 01:24:11] [Iter 1566/2250] R4[880/2400] | LR: 0.022584 | E: -42.297212 | E_var:     0.5237 | E_err:   0.011307
[2025-10-03 01:24:19] [Iter 1567/2250] R4[882/2400] | LR: 0.022554 | E: -42.297014 | E_var:     0.4448 | E_err:   0.010421
[2025-10-03 01:24:27] [Iter 1568/2250] R4[884/2400] | LR: 0.022524 | E: -42.291490 | E_var:     0.4209 | E_err:   0.010137
[2025-10-03 01:24:35] [Iter 1569/2250] R4[886/2400] | LR: 0.022494 | E: -42.293358 | E_var:     0.4935 | E_err:   0.010976
[2025-10-03 01:24:42] [Iter 1570/2250] R4[888/2400] | LR: 0.022464 | E: -42.306153 | E_var:     0.4615 | E_err:   0.010615
[2025-10-03 01:24:50] [Iter 1571/2250] R4[890/2400] | LR: 0.022434 | E: -42.314690 | E_var:     0.4777 | E_err:   0.010799
[2025-10-03 01:24:58] [Iter 1572/2250] R4[892/2400] | LR: 0.022404 | E: -42.289022 | E_var:     0.5678 | E_err:   0.011774
[2025-10-03 01:25:06] [Iter 1573/2250] R4[894/2400] | LR: 0.022374 | E: -42.294802 | E_var:     0.5102 | E_err:   0.011160
[2025-10-03 01:25:14] [Iter 1574/2250] R4[896/2400] | LR: 0.022344 | E: -42.300084 | E_var:     0.4394 | E_err:   0.010357
[2025-10-03 01:25:21] [Iter 1575/2250] R4[898/2400] | LR: 0.022314 | E: -42.286558 | E_var:     0.4602 | E_err:   0.010600
[2025-10-03 01:25:29] [Iter 1576/2250] R4[900/2400] | LR: 0.022284 | E: -42.301174 | E_var:     0.4490 | E_err:   0.010470
[2025-10-03 01:25:37] [Iter 1577/2250] R4[902/2400] | LR: 0.022253 | E: -42.279472 | E_var:     0.3621 | E_err:   0.009402
[2025-10-03 01:25:45] [Iter 1578/2250] R4[904/2400] | LR: 0.022223 | E: -42.278441 | E_var:     0.4471 | E_err:   0.010448
[2025-10-03 01:25:53] [Iter 1579/2250] R4[906/2400] | LR: 0.022193 | E: -42.282934 | E_var:     0.5787 | E_err:   0.011887
[2025-10-03 01:26:01] [Iter 1580/2250] R4[908/2400] | LR: 0.022162 | E: -42.287729 | E_var:     0.4801 | E_err:   0.010826
[2025-10-03 01:26:08] [Iter 1581/2250] R4[910/2400] | LR: 0.022132 | E: -42.286690 | E_var:     0.3696 | E_err:   0.009499
[2025-10-03 01:26:16] [Iter 1582/2250] R4[912/2400] | LR: 0.022102 | E: -42.288239 | E_var:     0.4509 | E_err:   0.010492
[2025-10-03 01:26:24] [Iter 1583/2250] R4[914/2400] | LR: 0.022071 | E: -42.295249 | E_var:     0.3861 | E_err:   0.009709
[2025-10-03 01:26:32] [Iter 1584/2250] R4[916/2400] | LR: 0.022041 | E: -42.304825 | E_var:     0.3899 | E_err:   0.009757
[2025-10-03 01:26:40] [Iter 1585/2250] R4[918/2400] | LR: 0.022010 | E: -42.279767 | E_var:     0.3278 | E_err:   0.008945
[2025-10-03 01:26:48] [Iter 1586/2250] R4[920/2400] | LR: 0.021980 | E: -42.289038 | E_var:     0.3748 | E_err:   0.009565
[2025-10-03 01:26:55] [Iter 1587/2250] R4[922/2400] | LR: 0.021949 | E: -42.286800 | E_var:     0.4424 | E_err:   0.010392
[2025-10-03 01:27:03] [Iter 1588/2250] R4[924/2400] | LR: 0.021918 | E: -42.294402 | E_var:     0.4552 | E_err:   0.010542
[2025-10-03 01:27:11] [Iter 1589/2250] R4[926/2400] | LR: 0.021888 | E: -42.294082 | E_var:     0.4646 | E_err:   0.010650
[2025-10-03 01:27:19] [Iter 1590/2250] R4[928/2400] | LR: 0.021857 | E: -42.307675 | E_var:     0.4268 | E_err:   0.010208
[2025-10-03 01:27:27] [Iter 1591/2250] R4[930/2400] | LR: 0.021826 | E: -42.293226 | E_var:     0.4091 | E_err:   0.009994
[2025-10-03 01:27:34] [Iter 1592/2250] R4[932/2400] | LR: 0.021796 | E: -42.296172 | E_var:     0.4223 | E_err:   0.010154
[2025-10-03 01:27:42] [Iter 1593/2250] R4[934/2400] | LR: 0.021765 | E: -42.290484 | E_var:     0.4036 | E_err:   0.009926
[2025-10-03 01:27:50] [Iter 1594/2250] R4[936/2400] | LR: 0.021734 | E: -42.301360 | E_var:     0.4606 | E_err:   0.010604
[2025-10-03 01:27:58] [Iter 1595/2250] R4[938/2400] | LR: 0.021703 | E: -42.291599 | E_var:     0.4409 | E_err:   0.010375
[2025-10-03 01:28:06] [Iter 1596/2250] R4[940/2400] | LR: 0.021673 | E: -42.285268 | E_var:     0.4767 | E_err:   0.010788
[2025-10-03 01:28:14] [Iter 1597/2250] R4[942/2400] | LR: 0.021642 | E: -42.296738 | E_var:     0.4552 | E_err:   0.010542
[2025-10-03 01:28:21] [Iter 1598/2250] R4[944/2400] | LR: 0.021611 | E: -42.280542 | E_var:     0.4436 | E_err:   0.010406
[2025-10-03 01:28:29] [Iter 1599/2250] R4[946/2400] | LR: 0.021580 | E: -42.304361 | E_var:     0.3830 | E_err:   0.009670
[2025-10-03 01:28:37] [Iter 1600/2250] R4[948/2400] | LR: 0.021549 | E: -42.276971 | E_var:     0.6570 | E_err:   0.012665
[2025-10-03 01:28:37] ✓ Checkpoint saved: checkpoint_iter_001600.pkl
[2025-10-03 01:28:45] [Iter 1601/2250] R4[950/2400] | LR: 0.021518 | E: -42.287194 | E_var:     0.4167 | E_err:   0.010087
[2025-10-03 01:28:53] [Iter 1602/2250] R4[952/2400] | LR: 0.021487 | E: -42.288265 | E_var:     0.4583 | E_err:   0.010577
[2025-10-03 01:29:01] [Iter 1603/2250] R4[954/2400] | LR: 0.021456 | E: -42.305880 | E_var:     0.6359 | E_err:   0.012460
[2025-10-03 01:29:08] [Iter 1604/2250] R4[956/2400] | LR: 0.021425 | E: -42.302194 | E_var:     0.3668 | E_err:   0.009464
[2025-10-03 01:29:16] [Iter 1605/2250] R4[958/2400] | LR: 0.021394 | E: -42.325383 | E_var:     0.3988 | E_err:   0.009867
[2025-10-03 01:29:24] [Iter 1606/2250] R4[960/2400] | LR: 0.021363 | E: -42.282780 | E_var:     0.3997 | E_err:   0.009878
[2025-10-03 01:29:32] [Iter 1607/2250] R4[962/2400] | LR: 0.021332 | E: -42.293152 | E_var:     0.4278 | E_err:   0.010220
[2025-10-03 01:29:40] [Iter 1608/2250] R4[964/2400] | LR: 0.021300 | E: -42.299914 | E_var:     0.4335 | E_err:   0.010288
[2025-10-03 01:29:47] [Iter 1609/2250] R4[966/2400] | LR: 0.021269 | E: -42.316104 | E_var:     0.5369 | E_err:   0.011449
[2025-10-03 01:29:55] [Iter 1610/2250] R4[968/2400] | LR: 0.021238 | E: -42.295835 | E_var:     0.5115 | E_err:   0.011175
[2025-10-03 01:30:03] [Iter 1611/2250] R4[970/2400] | LR: 0.021207 | E: -42.301226 | E_var:     0.4189 | E_err:   0.010112
[2025-10-03 01:30:11] [Iter 1612/2250] R4[972/2400] | LR: 0.021176 | E: -42.291219 | E_var:     0.3908 | E_err:   0.009768
[2025-10-03 01:30:19] [Iter 1613/2250] R4[974/2400] | LR: 0.021144 | E: -42.303099 | E_var:     0.4184 | E_err:   0.010107
[2025-10-03 01:30:27] [Iter 1614/2250] R4[976/2400] | LR: 0.021113 | E: -42.297919 | E_var:     0.4063 | E_err:   0.009960
[2025-10-03 01:30:34] [Iter 1615/2250] R4[978/2400] | LR: 0.021082 | E: -42.304935 | E_var:     0.4968 | E_err:   0.011013
[2025-10-03 01:30:42] [Iter 1616/2250] R4[980/2400] | LR: 0.021050 | E: -42.297375 | E_var:     0.4426 | E_err:   0.010396
[2025-10-03 01:30:50] [Iter 1617/2250] R4[982/2400] | LR: 0.021019 | E: -42.284692 | E_var:     0.4316 | E_err:   0.010265
[2025-10-03 01:30:58] [Iter 1618/2250] R4[984/2400] | LR: 0.020987 | E: -42.285697 | E_var:     0.4284 | E_err:   0.010226
[2025-10-03 01:31:06] [Iter 1619/2250] R4[986/2400] | LR: 0.020956 | E: -42.298027 | E_var:     0.4175 | E_err:   0.010096
[2025-10-03 01:31:13] [Iter 1620/2250] R4[988/2400] | LR: 0.020924 | E: -42.315509 | E_var:     0.3583 | E_err:   0.009352
[2025-10-03 01:31:21] [Iter 1621/2250] R4[990/2400] | LR: 0.020893 | E: -42.290690 | E_var:     0.6302 | E_err:   0.012404
[2025-10-03 01:31:29] [Iter 1622/2250] R4[992/2400] | LR: 0.020861 | E: -42.299682 | E_var:     0.4448 | E_err:   0.010421
[2025-10-03 01:31:37] [Iter 1623/2250] R4[994/2400] | LR: 0.020830 | E: -42.302328 | E_var:     0.3949 | E_err:   0.009819
[2025-10-03 01:31:45] [Iter 1624/2250] R4[996/2400] | LR: 0.020798 | E: -42.289153 | E_var:     0.4141 | E_err:   0.010055
[2025-10-03 01:31:53] [Iter 1625/2250] R4[998/2400] | LR: 0.020767 | E: -42.286084 | E_var:     0.4851 | E_err:   0.010883
[2025-10-03 01:32:00] [Iter 1626/2250] R4[1000/2400] | LR: 0.020735 | E: -42.273665 | E_var:     0.4228 | E_err:   0.010160
[2025-10-03 01:32:08] [Iter 1627/2250] R4[1002/2400] | LR: 0.020704 | E: -42.287199 | E_var:     0.4395 | E_err:   0.010358
[2025-10-03 01:32:16] [Iter 1628/2250] R4[1004/2400] | LR: 0.020672 | E: -42.294234 | E_var:     0.4028 | E_err:   0.009916
[2025-10-03 01:32:24] [Iter 1629/2250] R4[1006/2400] | LR: 0.020640 | E: -42.299576 | E_var:     0.4617 | E_err:   0.010617
[2025-10-03 01:32:32] [Iter 1630/2250] R4[1008/2400] | LR: 0.020609 | E: -42.290879 | E_var:     0.5206 | E_err:   0.011274
[2025-10-03 01:32:39] [Iter 1631/2250] R4[1010/2400] | LR: 0.020577 | E: -42.299181 | E_var:     0.4515 | E_err:   0.010499
[2025-10-03 01:32:47] [Iter 1632/2250] R4[1012/2400] | LR: 0.020545 | E: -42.302629 | E_var:     0.3833 | E_err:   0.009674
[2025-10-03 01:32:55] [Iter 1633/2250] R4[1014/2400] | LR: 0.020513 | E: -42.288388 | E_var:     0.4973 | E_err:   0.011019
[2025-10-03 01:33:03] [Iter 1634/2250] R4[1016/2400] | LR: 0.020482 | E: -42.290621 | E_var:     0.4656 | E_err:   0.010662
[2025-10-03 01:33:11] [Iter 1635/2250] R4[1018/2400] | LR: 0.020450 | E: -42.283601 | E_var:     0.3489 | E_err:   0.009229
[2025-10-03 01:33:19] [Iter 1636/2250] R4[1020/2400] | LR: 0.020418 | E: -42.303236 | E_var:     0.5691 | E_err:   0.011787
[2025-10-03 01:33:26] [Iter 1637/2250] R4[1022/2400] | LR: 0.020386 | E: -42.291906 | E_var:     0.4962 | E_err:   0.011006
[2025-10-03 01:33:34] [Iter 1638/2250] R4[1024/2400] | LR: 0.020354 | E: -42.302358 | E_var:     0.4280 | E_err:   0.010222
[2025-10-03 01:33:42] [Iter 1639/2250] R4[1026/2400] | LR: 0.020323 | E: -42.305355 | E_var:     0.3990 | E_err:   0.009870
[2025-10-03 01:33:50] [Iter 1640/2250] R4[1028/2400] | LR: 0.020291 | E: -42.299099 | E_var:     0.6737 | E_err:   0.012825
[2025-10-03 01:33:58] [Iter 1641/2250] R4[1030/2400] | LR: 0.020259 | E: -42.269558 | E_var:     0.4886 | E_err:   0.010922
[2025-10-03 01:34:05] [Iter 1642/2250] R4[1032/2400] | LR: 0.020227 | E: -42.283197 | E_var:     0.4625 | E_err:   0.010627
[2025-10-03 01:34:13] [Iter 1643/2250] R4[1034/2400] | LR: 0.020195 | E: -42.316220 | E_var:     0.4758 | E_err:   0.010778
[2025-10-03 01:34:21] [Iter 1644/2250] R4[1036/2400] | LR: 0.020163 | E: -42.295360 | E_var:     0.5044 | E_err:   0.011097
[2025-10-03 01:34:29] [Iter 1645/2250] R4[1038/2400] | LR: 0.020131 | E: -42.318307 | E_var:     0.3698 | E_err:   0.009502
[2025-10-03 01:34:37] [Iter 1646/2250] R4[1040/2400] | LR: 0.020099 | E: -42.322765 | E_var:     0.3907 | E_err:   0.009766
[2025-10-03 01:34:45] [Iter 1647/2250] R4[1042/2400] | LR: 0.020067 | E: -42.298459 | E_var:     0.4084 | E_err:   0.009986
[2025-10-03 01:34:52] [Iter 1648/2250] R4[1044/2400] | LR: 0.020035 | E: -42.279771 | E_var:     0.4070 | E_err:   0.009969
[2025-10-03 01:35:00] [Iter 1649/2250] R4[1046/2400] | LR: 0.020003 | E: -42.291592 | E_var:     0.3818 | E_err:   0.009654
[2025-10-03 01:35:08] [Iter 1650/2250] R4[1048/2400] | LR: 0.019971 | E: -42.285918 | E_var:     0.4823 | E_err:   0.010851
[2025-10-03 01:35:16] [Iter 1651/2250] R4[1050/2400] | LR: 0.019939 | E: -42.316933 | E_var:     0.4087 | E_err:   0.009989
[2025-10-03 01:35:24] [Iter 1652/2250] R4[1052/2400] | LR: 0.019907 | E: -42.292934 | E_var:     0.4487 | E_err:   0.010466
[2025-10-03 01:35:31] [Iter 1653/2250] R4[1054/2400] | LR: 0.019874 | E: -42.290417 | E_var:     0.3672 | E_err:   0.009468
[2025-10-03 01:35:39] [Iter 1654/2250] R4[1056/2400] | LR: 0.019842 | E: -42.293145 | E_var:     0.4726 | E_err:   0.010742
[2025-10-03 01:35:47] [Iter 1655/2250] R4[1058/2400] | LR: 0.019810 | E: -42.276764 | E_var:     0.5206 | E_err:   0.011274
[2025-10-03 01:35:55] [Iter 1656/2250] R4[1060/2400] | LR: 0.019778 | E: -42.290981 | E_var:     0.4013 | E_err:   0.009899
[2025-10-03 01:36:03] [Iter 1657/2250] R4[1062/2400] | LR: 0.019746 | E: -42.296152 | E_var:     0.3981 | E_err:   0.009859
[2025-10-03 01:36:11] [Iter 1658/2250] R4[1064/2400] | LR: 0.019714 | E: -42.286842 | E_var:     0.3915 | E_err:   0.009777
[2025-10-03 01:36:18] [Iter 1659/2250] R4[1066/2400] | LR: 0.019681 | E: -42.298115 | E_var:     0.4824 | E_err:   0.010852
[2025-10-03 01:36:26] [Iter 1660/2250] R4[1068/2400] | LR: 0.019649 | E: -42.310852 | E_var:     0.4884 | E_err:   0.010920
[2025-10-03 01:36:34] [Iter 1661/2250] R4[1070/2400] | LR: 0.019617 | E: -42.313163 | E_var:     0.4169 | E_err:   0.010089
[2025-10-03 01:36:42] [Iter 1662/2250] R4[1072/2400] | LR: 0.019585 | E: -42.288180 | E_var:     0.4300 | E_err:   0.010246
[2025-10-03 01:36:50] [Iter 1663/2250] R4[1074/2400] | LR: 0.019552 | E: -42.294784 | E_var:     0.3959 | E_err:   0.009832
[2025-10-03 01:36:57] [Iter 1664/2250] R4[1076/2400] | LR: 0.019520 | E: -42.299099 | E_var:     0.5035 | E_err:   0.011087
[2025-10-03 01:37:05] [Iter 1665/2250] R4[1078/2400] | LR: 0.019488 | E: -42.316976 | E_var:     0.3996 | E_err:   0.009877
[2025-10-03 01:37:13] [Iter 1666/2250] R4[1080/2400] | LR: 0.019455 | E: -42.288927 | E_var:     0.4994 | E_err:   0.011042
[2025-10-03 01:37:21] [Iter 1667/2250] R4[1082/2400] | LR: 0.019423 | E: -42.306851 | E_var:     0.3740 | E_err:   0.009556
[2025-10-03 01:37:29] [Iter 1668/2250] R4[1084/2400] | LR: 0.019391 | E: -42.298846 | E_var:     0.3791 | E_err:   0.009621
[2025-10-03 01:37:37] [Iter 1669/2250] R4[1086/2400] | LR: 0.019358 | E: -42.311261 | E_var:     0.4027 | E_err:   0.009916
[2025-10-03 01:37:44] [Iter 1670/2250] R4[1088/2400] | LR: 0.019326 | E: -42.287751 | E_var:     0.4581 | E_err:   0.010575
[2025-10-03 01:37:52] [Iter 1671/2250] R4[1090/2400] | LR: 0.019294 | E: -42.304423 | E_var:     0.3871 | E_err:   0.009722
[2025-10-03 01:38:00] [Iter 1672/2250] R4[1092/2400] | LR: 0.019261 | E: -42.291543 | E_var:     0.5259 | E_err:   0.011331
[2025-10-03 01:38:08] [Iter 1673/2250] R4[1094/2400] | LR: 0.019229 | E: -42.306222 | E_var:     0.4031 | E_err:   0.009920
[2025-10-03 01:38:16] [Iter 1674/2250] R4[1096/2400] | LR: 0.019196 | E: -42.286548 | E_var:     0.6088 | E_err:   0.012192
[2025-10-03 01:38:23] [Iter 1675/2250] R4[1098/2400] | LR: 0.019164 | E: -42.287550 | E_var:     0.4208 | E_err:   0.010136
[2025-10-03 01:38:31] [Iter 1676/2250] R4[1100/2400] | LR: 0.019132 | E: -42.294267 | E_var:     0.5024 | E_err:   0.011075
[2025-10-03 01:38:39] [Iter 1677/2250] R4[1102/2400] | LR: 0.019099 | E: -42.285171 | E_var:     0.3540 | E_err:   0.009297
[2025-10-03 01:38:47] [Iter 1678/2250] R4[1104/2400] | LR: 0.019067 | E: -42.289256 | E_var:     0.4924 | E_err:   0.010965
[2025-10-03 01:38:55] [Iter 1679/2250] R4[1106/2400] | LR: 0.019034 | E: -42.313772 | E_var:     0.3892 | E_err:   0.009748
[2025-10-03 01:39:03] [Iter 1680/2250] R4[1108/2400] | LR: 0.019002 | E: -42.314616 | E_var:     0.5357 | E_err:   0.011436
[2025-10-03 01:39:10] [Iter 1681/2250] R4[1110/2400] | LR: 0.018969 | E: -42.310610 | E_var:     0.4894 | E_err:   0.010931
[2025-10-03 01:39:18] [Iter 1682/2250] R4[1112/2400] | LR: 0.018937 | E: -42.294323 | E_var:     0.6164 | E_err:   0.012267
[2025-10-03 01:39:26] [Iter 1683/2250] R4[1114/2400] | LR: 0.018904 | E: -42.297993 | E_var:     0.4986 | E_err:   0.011033
[2025-10-03 01:39:34] [Iter 1684/2250] R4[1116/2400] | LR: 0.018872 | E: -42.298547 | E_var:     0.3897 | E_err:   0.009754
[2025-10-03 01:39:42] [Iter 1685/2250] R4[1118/2400] | LR: 0.018839 | E: -42.309527 | E_var:     0.4717 | E_err:   0.010731
[2025-10-03 01:39:50] [Iter 1686/2250] R4[1120/2400] | LR: 0.018807 | E: -42.297310 | E_var:     0.4543 | E_err:   0.010531
[2025-10-03 01:39:57] [Iter 1687/2250] R4[1122/2400] | LR: 0.018774 | E: -42.319316 | E_var:     0.4345 | E_err:   0.010299
[2025-10-03 01:40:05] [Iter 1688/2250] R4[1124/2400] | LR: 0.018741 | E: -42.293545 | E_var:     0.6638 | E_err:   0.012730
[2025-10-03 01:40:13] [Iter 1689/2250] R4[1126/2400] | LR: 0.018709 | E: -42.290451 | E_var:     0.3472 | E_err:   0.009207
[2025-10-03 01:40:21] [Iter 1690/2250] R4[1128/2400] | LR: 0.018676 | E: -42.296968 | E_var:     0.3797 | E_err:   0.009629
[2025-10-03 01:40:29] [Iter 1691/2250] R4[1130/2400] | LR: 0.018644 | E: -42.296341 | E_var:     0.4598 | E_err:   0.010595
[2025-10-03 01:40:36] [Iter 1692/2250] R4[1132/2400] | LR: 0.018611 | E: -42.317549 | E_var:     0.4484 | E_err:   0.010463
[2025-10-03 01:40:44] [Iter 1693/2250] R4[1134/2400] | LR: 0.018579 | E: -42.293591 | E_var:     0.6823 | E_err:   0.012906
[2025-10-03 01:40:52] [Iter 1694/2250] R4[1136/2400] | LR: 0.018546 | E: -42.295946 | E_var:     0.4180 | E_err:   0.010103
[2025-10-03 01:41:00] [Iter 1695/2250] R4[1138/2400] | LR: 0.018513 | E: -42.315774 | E_var:     0.5815 | E_err:   0.011915
[2025-10-03 01:41:08] [Iter 1696/2250] R4[1140/2400] | LR: 0.018481 | E: -42.299055 | E_var:     0.4133 | E_err:   0.010045
[2025-10-03 01:41:16] [Iter 1697/2250] R4[1142/2400] | LR: 0.018448 | E: -42.289268 | E_var:     0.4727 | E_err:   0.010742
[2025-10-03 01:41:23] [Iter 1698/2250] R4[1144/2400] | LR: 0.018415 | E: -42.289110 | E_var:     0.4532 | E_err:   0.010519
[2025-10-03 01:41:31] [Iter 1699/2250] R4[1146/2400] | LR: 0.018383 | E: -42.314106 | E_var:     0.3799 | E_err:   0.009631
[2025-10-03 01:41:39] [Iter 1700/2250] R4[1148/2400] | LR: 0.018350 | E: -42.299460 | E_var:     0.4744 | E_err:   0.010762
[2025-10-03 01:41:47] [Iter 1701/2250] R4[1150/2400] | LR: 0.018318 | E: -42.301088 | E_var:     0.3882 | E_err:   0.009736
[2025-10-03 01:41:55] [Iter 1702/2250] R4[1152/2400] | LR: 0.018285 | E: -42.289650 | E_var:     0.3431 | E_err:   0.009152
[2025-10-03 01:42:02] [Iter 1703/2250] R4[1154/2400] | LR: 0.018252 | E: -42.298298 | E_var:     0.3439 | E_err:   0.009163
[2025-10-03 01:42:10] [Iter 1704/2250] R4[1156/2400] | LR: 0.018220 | E: -42.272459 | E_var:     0.5237 | E_err:   0.011308
[2025-10-03 01:42:18] [Iter 1705/2250] R4[1158/2400] | LR: 0.018187 | E: -42.281971 | E_var:     0.4281 | E_err:   0.010223
[2025-10-03 01:42:26] [Iter 1706/2250] R4[1160/2400] | LR: 0.018154 | E: -42.294971 | E_var:     0.5438 | E_err:   0.011522
[2025-10-03 01:42:34] [Iter 1707/2250] R4[1162/2400] | LR: 0.018122 | E: -42.299657 | E_var:     0.3731 | E_err:   0.009544
[2025-10-03 01:42:42] [Iter 1708/2250] R4[1164/2400] | LR: 0.018089 | E: -42.306671 | E_var:     0.4062 | E_err:   0.009958
[2025-10-03 01:42:49] [Iter 1709/2250] R4[1166/2400] | LR: 0.018056 | E: -42.291200 | E_var:     0.5285 | E_err:   0.011359
[2025-10-03 01:42:57] [Iter 1710/2250] R4[1168/2400] | LR: 0.018023 | E: -42.283880 | E_var:     0.4285 | E_err:   0.010228
[2025-10-03 01:43:05] [Iter 1711/2250] R4[1170/2400] | LR: 0.017991 | E: -42.283758 | E_var:     0.4116 | E_err:   0.010024
[2025-10-03 01:43:13] [Iter 1712/2250] R4[1172/2400] | LR: 0.017958 | E: -42.302453 | E_var:     0.4017 | E_err:   0.009904
[2025-10-03 01:43:21] [Iter 1713/2250] R4[1174/2400] | LR: 0.017925 | E: -42.302593 | E_var:     0.4029 | E_err:   0.009918
[2025-10-03 01:43:28] [Iter 1714/2250] R4[1176/2400] | LR: 0.017893 | E: -42.283581 | E_var:     0.4119 | E_err:   0.010027
[2025-10-03 01:43:36] [Iter 1715/2250] R4[1178/2400] | LR: 0.017860 | E: -42.297516 | E_var:     0.4111 | E_err:   0.010018
[2025-10-03 01:43:44] [Iter 1716/2250] R4[1180/2400] | LR: 0.017827 | E: -42.295543 | E_var:     0.3885 | E_err:   0.009740
[2025-10-03 01:43:52] [Iter 1717/2250] R4[1182/2400] | LR: 0.017794 | E: -42.301114 | E_var:     0.5334 | E_err:   0.011411
[2025-10-03 01:44:00] [Iter 1718/2250] R4[1184/2400] | LR: 0.017762 | E: -42.272826 | E_var:     0.4149 | E_err:   0.010064
[2025-10-03 01:44:08] [Iter 1719/2250] R4[1186/2400] | LR: 0.017729 | E: -42.301750 | E_var:     0.4344 | E_err:   0.010298
[2025-10-03 01:44:15] [Iter 1720/2250] R4[1188/2400] | LR: 0.017696 | E: -42.295927 | E_var:     0.4434 | E_err:   0.010404
[2025-10-03 01:44:23] [Iter 1721/2250] R4[1190/2400] | LR: 0.017664 | E: -42.287484 | E_var:     0.3720 | E_err:   0.009529
[2025-10-03 01:44:31] [Iter 1722/2250] R4[1192/2400] | LR: 0.017631 | E: -42.309127 | E_var:     0.3409 | E_err:   0.009122
[2025-10-03 01:44:39] [Iter 1723/2250] R4[1194/2400] | LR: 0.017598 | E: -42.300539 | E_var:     0.3872 | E_err:   0.009723
[2025-10-03 01:44:47] [Iter 1724/2250] R4[1196/2400] | LR: 0.017565 | E: -42.312519 | E_var:     0.4239 | E_err:   0.010174
[2025-10-03 01:44:54] [Iter 1725/2250] R4[1198/2400] | LR: 0.017533 | E: -42.321167 | E_var:     0.4345 | E_err:   0.010300
[2025-10-03 01:45:02] [Iter 1726/2250] R4[1200/2400] | LR: 0.017500 | E: -42.305107 | E_var:     0.8321 | E_err:   0.014253
[2025-10-03 01:45:10] [Iter 1727/2250] R4[1202/2400] | LR: 0.017467 | E: -42.292951 | E_var:     0.4246 | E_err:   0.010181
[2025-10-03 01:45:18] [Iter 1728/2250] R4[1204/2400] | LR: 0.017435 | E: -42.306692 | E_var:     0.3972 | E_err:   0.009848
[2025-10-03 01:45:26] [Iter 1729/2250] R4[1206/2400] | LR: 0.017402 | E: -42.299713 | E_var:     0.5658 | E_err:   0.011753
[2025-10-03 01:45:34] [Iter 1730/2250] R4[1208/2400] | LR: 0.017369 | E: -42.318308 | E_var:     0.6162 | E_err:   0.012265
[2025-10-03 01:45:41] [Iter 1731/2250] R4[1210/2400] | LR: 0.017336 | E: -42.300649 | E_var:     0.4828 | E_err:   0.010857
[2025-10-03 01:45:49] [Iter 1732/2250] R4[1212/2400] | LR: 0.017304 | E: -42.286388 | E_var:     0.6852 | E_err:   0.012934
[2025-10-03 01:45:57] [Iter 1733/2250] R4[1214/2400] | LR: 0.017271 | E: -42.303143 | E_var:     0.4203 | E_err:   0.010130
[2025-10-03 01:46:05] [Iter 1734/2250] R4[1216/2400] | LR: 0.017238 | E: -42.295846 | E_var:     0.3375 | E_err:   0.009077
[2025-10-03 01:46:13] [Iter 1735/2250] R4[1218/2400] | LR: 0.017206 | E: -42.292871 | E_var:     0.4465 | E_err:   0.010440
[2025-10-03 01:46:20] [Iter 1736/2250] R4[1220/2400] | LR: 0.017173 | E: -42.300719 | E_var:     0.3960 | E_err:   0.009832
[2025-10-03 01:46:28] [Iter 1737/2250] R4[1222/2400] | LR: 0.017140 | E: -42.300632 | E_var:     0.3620 | E_err:   0.009401
[2025-10-03 01:46:36] [Iter 1738/2250] R4[1224/2400] | LR: 0.017107 | E: -42.304799 | E_var:     0.4198 | E_err:   0.010124
[2025-10-03 01:46:44] [Iter 1739/2250] R4[1226/2400] | LR: 0.017075 | E: -42.302237 | E_var:     0.4214 | E_err:   0.010143
[2025-10-03 01:46:52] [Iter 1740/2250] R4[1228/2400] | LR: 0.017042 | E: -42.300904 | E_var:     0.3711 | E_err:   0.009519
[2025-10-03 01:47:00] [Iter 1741/2250] R4[1230/2400] | LR: 0.017009 | E: -42.306773 | E_var:     0.4461 | E_err:   0.010437
[2025-10-03 01:47:07] [Iter 1742/2250] R4[1232/2400] | LR: 0.016977 | E: -42.282328 | E_var:     0.4466 | E_err:   0.010442
[2025-10-03 01:47:15] [Iter 1743/2250] R4[1234/2400] | LR: 0.016944 | E: -42.277922 | E_var:     0.5613 | E_err:   0.011707
[2025-10-03 01:47:23] [Iter 1744/2250] R4[1236/2400] | LR: 0.016911 | E: -42.290080 | E_var:     0.5310 | E_err:   0.011386
[2025-10-03 01:47:31] [Iter 1745/2250] R4[1238/2400] | LR: 0.016878 | E: -42.300353 | E_var:     0.3967 | E_err:   0.009841
[2025-10-03 01:47:39] [Iter 1746/2250] R4[1240/2400] | LR: 0.016846 | E: -42.289316 | E_var:     0.3780 | E_err:   0.009606
[2025-10-03 01:47:46] [Iter 1747/2250] R4[1242/2400] | LR: 0.016813 | E: -42.293988 | E_var:     0.4667 | E_err:   0.010675
[2025-10-03 01:47:54] [Iter 1748/2250] R4[1244/2400] | LR: 0.016780 | E: -42.299918 | E_var:     0.4459 | E_err:   0.010433
[2025-10-03 01:48:02] [Iter 1749/2250] R4[1246/2400] | LR: 0.016748 | E: -42.275198 | E_var:     1.0840 | E_err:   0.016268
[2025-10-03 01:48:10] [Iter 1750/2250] R4[1248/2400] | LR: 0.016715 | E: -42.313239 | E_var:     0.7995 | E_err:   0.013971
[2025-10-03 01:48:18] [Iter 1751/2250] R4[1250/2400] | LR: 0.016682 | E: -42.294352 | E_var:     0.4762 | E_err:   0.010783
[2025-10-03 01:48:26] [Iter 1752/2250] R4[1252/2400] | LR: 0.016650 | E: -42.303311 | E_var:     0.3755 | E_err:   0.009575
[2025-10-03 01:48:33] [Iter 1753/2250] R4[1254/2400] | LR: 0.016617 | E: -42.315538 | E_var:     0.4921 | E_err:   0.010961
[2025-10-03 01:48:41] [Iter 1754/2250] R4[1256/2400] | LR: 0.016585 | E: -42.285233 | E_var:     0.4151 | E_err:   0.010067
[2025-10-03 01:48:49] [Iter 1755/2250] R4[1258/2400] | LR: 0.016552 | E: -42.295688 | E_var:     0.4329 | E_err:   0.010280
[2025-10-03 01:48:57] [Iter 1756/2250] R4[1260/2400] | LR: 0.016519 | E: -42.301290 | E_var:     0.4688 | E_err:   0.010698
[2025-10-03 01:49:05] [Iter 1757/2250] R4[1262/2400] | LR: 0.016487 | E: -42.299831 | E_var:     0.5594 | E_err:   0.011686
[2025-10-03 01:49:12] [Iter 1758/2250] R4[1264/2400] | LR: 0.016454 | E: -42.309255 | E_var:     0.3264 | E_err:   0.008927
[2025-10-03 01:49:20] [Iter 1759/2250] R4[1266/2400] | LR: 0.016421 | E: -42.313632 | E_var:     0.4307 | E_err:   0.010255
[2025-10-03 01:49:28] [Iter 1760/2250] R4[1268/2400] | LR: 0.016389 | E: -42.287534 | E_var:     0.4719 | E_err:   0.010734
[2025-10-03 01:49:36] [Iter 1761/2250] R4[1270/2400] | LR: 0.016356 | E: -42.296310 | E_var:     0.4906 | E_err:   0.010944
[2025-10-03 01:49:44] [Iter 1762/2250] R4[1272/2400] | LR: 0.016324 | E: -42.321857 | E_var:     0.4487 | E_err:   0.010466
[2025-10-03 01:49:52] [Iter 1763/2250] R4[1274/2400] | LR: 0.016291 | E: -42.284601 | E_var:     0.3245 | E_err:   0.008901
[2025-10-03 01:49:59] [Iter 1764/2250] R4[1276/2400] | LR: 0.016259 | E: -42.277884 | E_var:     0.4416 | E_err:   0.010383
[2025-10-03 01:50:07] [Iter 1765/2250] R4[1278/2400] | LR: 0.016226 | E: -42.305689 | E_var:     0.3391 | E_err:   0.009099
[2025-10-03 01:50:15] [Iter 1766/2250] R4[1280/2400] | LR: 0.016193 | E: -42.300229 | E_var:     0.5654 | E_err:   0.011749
[2025-10-03 01:50:23] [Iter 1767/2250] R4[1282/2400] | LR: 0.016161 | E: -42.302199 | E_var:     0.4958 | E_err:   0.011002
[2025-10-03 01:50:31] [Iter 1768/2250] R4[1284/2400] | LR: 0.016128 | E: -42.298372 | E_var:     0.4019 | E_err:   0.009905
[2025-10-03 01:50:38] [Iter 1769/2250] R4[1286/2400] | LR: 0.016096 | E: -42.304865 | E_var:     0.4706 | E_err:   0.010718
[2025-10-03 01:50:46] [Iter 1770/2250] R4[1288/2400] | LR: 0.016063 | E: -42.310827 | E_var:     0.4407 | E_err:   0.010373
[2025-10-03 01:50:54] [Iter 1771/2250] R4[1290/2400] | LR: 0.016031 | E: -42.293998 | E_var:     0.3908 | E_err:   0.009768
[2025-10-03 01:51:02] [Iter 1772/2250] R4[1292/2400] | LR: 0.015998 | E: -42.309319 | E_var:     0.4160 | E_err:   0.010078
[2025-10-03 01:51:10] [Iter 1773/2250] R4[1294/2400] | LR: 0.015966 | E: -42.291619 | E_var:     0.4305 | E_err:   0.010252
[2025-10-03 01:51:18] [Iter 1774/2250] R4[1296/2400] | LR: 0.015933 | E: -42.297587 | E_var:     0.3838 | E_err:   0.009680
[2025-10-03 01:51:25] [Iter 1775/2250] R4[1298/2400] | LR: 0.015901 | E: -42.283669 | E_var:     0.4493 | E_err:   0.010473
[2025-10-03 01:51:33] [Iter 1776/2250] R4[1300/2400] | LR: 0.015868 | E: -42.284899 | E_var:     0.4796 | E_err:   0.010821
[2025-10-03 01:51:41] [Iter 1777/2250] R4[1302/2400] | LR: 0.015836 | E: -42.295014 | E_var:     0.5219 | E_err:   0.011288
[2025-10-03 01:51:49] [Iter 1778/2250] R4[1304/2400] | LR: 0.015804 | E: -42.298565 | E_var:     0.3987 | E_err:   0.009866
[2025-10-03 01:51:57] [Iter 1779/2250] R4[1306/2400] | LR: 0.015771 | E: -42.297261 | E_var:     0.5543 | E_err:   0.011633
[2025-10-03 01:52:05] [Iter 1780/2250] R4[1308/2400] | LR: 0.015739 | E: -42.293660 | E_var:     0.4168 | E_err:   0.010087
[2025-10-03 01:52:12] [Iter 1781/2250] R4[1310/2400] | LR: 0.015706 | E: -42.277026 | E_var:     0.4739 | E_err:   0.010757
[2025-10-03 01:52:20] [Iter 1782/2250] R4[1312/2400] | LR: 0.015674 | E: -42.316677 | E_var:     2.6577 | E_err:   0.025473
[2025-10-03 01:52:28] [Iter 1783/2250] R4[1314/2400] | LR: 0.015642 | E: -42.296489 | E_var:     0.4260 | E_err:   0.010198
[2025-10-03 01:52:36] [Iter 1784/2250] R4[1316/2400] | LR: 0.015609 | E: -42.309001 | E_var:     0.6990 | E_err:   0.013064
[2025-10-03 01:52:44] [Iter 1785/2250] R4[1318/2400] | LR: 0.015577 | E: -42.299221 | E_var:     0.4959 | E_err:   0.011003
[2025-10-03 01:52:51] [Iter 1786/2250] R4[1320/2400] | LR: 0.015545 | E: -42.312772 | E_var:     0.4253 | E_err:   0.010190
[2025-10-03 01:52:59] [Iter 1787/2250] R4[1322/2400] | LR: 0.015512 | E: -42.302288 | E_var:     0.3777 | E_err:   0.009603
[2025-10-03 01:53:07] [Iter 1788/2250] R4[1324/2400] | LR: 0.015480 | E: -42.292925 | E_var:     0.3714 | E_err:   0.009522
[2025-10-03 01:53:15] [Iter 1789/2250] R4[1326/2400] | LR: 0.015448 | E: -42.299731 | E_var:     0.4647 | E_err:   0.010652
[2025-10-03 01:53:23] [Iter 1790/2250] R4[1328/2400] | LR: 0.015415 | E: -42.319721 | E_var:     0.4931 | E_err:   0.010972
[2025-10-03 01:53:31] [Iter 1791/2250] R4[1330/2400] | LR: 0.015383 | E: -42.289689 | E_var:     0.4305 | E_err:   0.010252
[2025-10-03 01:53:38] [Iter 1792/2250] R4[1332/2400] | LR: 0.015351 | E: -42.287166 | E_var:     0.4889 | E_err:   0.010925
[2025-10-03 01:53:46] [Iter 1793/2250] R4[1334/2400] | LR: 0.015319 | E: -42.308568 | E_var:     0.4224 | E_err:   0.010155
[2025-10-03 01:53:54] [Iter 1794/2250] R4[1336/2400] | LR: 0.015286 | E: -42.298051 | E_var:     0.3777 | E_err:   0.009603
[2025-10-03 01:54:02] [Iter 1795/2250] R4[1338/2400] | LR: 0.015254 | E: -42.284009 | E_var:     0.3543 | E_err:   0.009300
[2025-10-03 01:54:10] [Iter 1796/2250] R4[1340/2400] | LR: 0.015222 | E: -42.309125 | E_var:     0.3662 | E_err:   0.009456
[2025-10-03 01:54:17] [Iter 1797/2250] R4[1342/2400] | LR: 0.015190 | E: -42.301396 | E_var:     0.3965 | E_err:   0.009839
[2025-10-03 01:54:25] [Iter 1798/2250] R4[1344/2400] | LR: 0.015158 | E: -42.294786 | E_var:     0.4010 | E_err:   0.009894
[2025-10-03 01:54:33] [Iter 1799/2250] R4[1346/2400] | LR: 0.015126 | E: -42.309827 | E_var:     0.4625 | E_err:   0.010626
[2025-10-03 01:54:41] [Iter 1800/2250] R4[1348/2400] | LR: 0.015093 | E: -42.301191 | E_var:     0.3497 | E_err:   0.009240
[2025-10-03 01:54:41] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-03 01:54:49] [Iter 1801/2250] R4[1350/2400] | LR: 0.015061 | E: -42.301196 | E_var:     0.3800 | E_err:   0.009632
[2025-10-03 01:54:57] [Iter 1802/2250] R4[1352/2400] | LR: 0.015029 | E: -42.298011 | E_var:     0.4130 | E_err:   0.010042
[2025-10-03 01:55:04] [Iter 1803/2250] R4[1354/2400] | LR: 0.014997 | E: -42.272771 | E_var:     0.4057 | E_err:   0.009953
[2025-10-03 01:55:12] [Iter 1804/2250] R4[1356/2400] | LR: 0.014965 | E: -42.303677 | E_var:     0.3392 | E_err:   0.009100
[2025-10-03 01:55:20] [Iter 1805/2250] R4[1358/2400] | LR: 0.014933 | E: -42.300845 | E_var:     0.3491 | E_err:   0.009232
[2025-10-03 01:55:28] [Iter 1806/2250] R4[1360/2400] | LR: 0.014901 | E: -42.300290 | E_var:     0.4367 | E_err:   0.010325
[2025-10-03 01:55:36] [Iter 1807/2250] R4[1362/2400] | LR: 0.014869 | E: -42.291954 | E_var:     0.4229 | E_err:   0.010161
[2025-10-03 01:55:44] [Iter 1808/2250] R4[1364/2400] | LR: 0.014837 | E: -42.303241 | E_var:     0.3807 | E_err:   0.009641
[2025-10-03 01:55:51] [Iter 1809/2250] R4[1366/2400] | LR: 0.014805 | E: -42.298433 | E_var:     0.3814 | E_err:   0.009650
[2025-10-03 01:55:59] [Iter 1810/2250] R4[1368/2400] | LR: 0.014773 | E: -42.303494 | E_var:     0.3973 | E_err:   0.009849
[2025-10-03 01:56:07] [Iter 1811/2250] R4[1370/2400] | LR: 0.014741 | E: -42.289142 | E_var:     0.4753 | E_err:   0.010772
[2025-10-03 01:56:15] [Iter 1812/2250] R4[1372/2400] | LR: 0.014709 | E: -42.291980 | E_var:     0.3672 | E_err:   0.009469
[2025-10-03 01:56:23] [Iter 1813/2250] R4[1374/2400] | LR: 0.014677 | E: -42.310792 | E_var:     0.3703 | E_err:   0.009509
[2025-10-03 01:56:30] [Iter 1814/2250] R4[1376/2400] | LR: 0.014646 | E: -42.288358 | E_var:     0.4906 | E_err:   0.010944
[2025-10-03 01:56:38] [Iter 1815/2250] R4[1378/2400] | LR: 0.014614 | E: -42.297733 | E_var:     0.3643 | E_err:   0.009431
[2025-10-03 01:56:46] [Iter 1816/2250] R4[1380/2400] | LR: 0.014582 | E: -42.320157 | E_var:     0.3480 | E_err:   0.009217
[2025-10-03 01:56:54] [Iter 1817/2250] R4[1382/2400] | LR: 0.014550 | E: -42.301420 | E_var:     0.4175 | E_err:   0.010096
[2025-10-03 01:57:02] [Iter 1818/2250] R4[1384/2400] | LR: 0.014518 | E: -42.295246 | E_var:     0.7842 | E_err:   0.013837
[2025-10-03 01:57:10] [Iter 1819/2250] R4[1386/2400] | LR: 0.014487 | E: -42.306579 | E_var:     0.4692 | E_err:   0.010703
[2025-10-03 01:57:17] [Iter 1820/2250] R4[1388/2400] | LR: 0.014455 | E: -42.281181 | E_var:     0.4214 | E_err:   0.010143
[2025-10-03 01:57:25] [Iter 1821/2250] R4[1390/2400] | LR: 0.014423 | E: -42.292929 | E_var:     0.4319 | E_err:   0.010269
[2025-10-03 01:57:33] [Iter 1822/2250] R4[1392/2400] | LR: 0.014391 | E: -42.298641 | E_var:     0.5372 | E_err:   0.011452
[2025-10-03 01:57:41] [Iter 1823/2250] R4[1394/2400] | LR: 0.014360 | E: -42.302272 | E_var:     0.4270 | E_err:   0.010211
[2025-10-03 01:57:49] [Iter 1824/2250] R4[1396/2400] | LR: 0.014328 | E: -42.294744 | E_var:     0.3675 | E_err:   0.009472
[2025-10-03 01:57:56] [Iter 1825/2250] R4[1398/2400] | LR: 0.014296 | E: -42.301049 | E_var:     0.4483 | E_err:   0.010462
[2025-10-03 01:58:04] [Iter 1826/2250] R4[1400/2400] | LR: 0.014265 | E: -42.302864 | E_var:     0.3706 | E_err:   0.009512
[2025-10-03 01:58:12] [Iter 1827/2250] R4[1402/2400] | LR: 0.014233 | E: -42.305431 | E_var:     0.4317 | E_err:   0.010266
[2025-10-03 01:58:20] [Iter 1828/2250] R4[1404/2400] | LR: 0.014202 | E: -42.305978 | E_var:     0.4896 | E_err:   0.010933
[2025-10-03 01:58:28] [Iter 1829/2250] R4[1406/2400] | LR: 0.014170 | E: -42.284398 | E_var:     0.3987 | E_err:   0.009865
[2025-10-03 01:58:36] [Iter 1830/2250] R4[1408/2400] | LR: 0.014139 | E: -42.304694 | E_var:     0.4014 | E_err:   0.009899
[2025-10-03 01:58:43] [Iter 1831/2250] R4[1410/2400] | LR: 0.014107 | E: -42.299472 | E_var:     0.4561 | E_err:   0.010553
[2025-10-03 01:58:51] [Iter 1832/2250] R4[1412/2400] | LR: 0.014076 | E: -42.308229 | E_var:     0.3876 | E_err:   0.009727
[2025-10-03 01:58:59] [Iter 1833/2250] R4[1414/2400] | LR: 0.014044 | E: -42.320520 | E_var:     0.3857 | E_err:   0.009704
[2025-10-03 01:59:07] [Iter 1834/2250] R4[1416/2400] | LR: 0.014013 | E: -42.306186 | E_var:     0.3335 | E_err:   0.009024
[2025-10-03 01:59:15] [Iter 1835/2250] R4[1418/2400] | LR: 0.013981 | E: -42.321496 | E_var:     0.3865 | E_err:   0.009714
[2025-10-03 01:59:22] [Iter 1836/2250] R4[1420/2400] | LR: 0.013950 | E: -42.300238 | E_var:     0.3766 | E_err:   0.009588
[2025-10-03 01:59:30] [Iter 1837/2250] R4[1422/2400] | LR: 0.013918 | E: -42.301886 | E_var:     0.4227 | E_err:   0.010159
[2025-10-03 01:59:38] [Iter 1838/2250] R4[1424/2400] | LR: 0.013887 | E: -42.288887 | E_var:     0.3493 | E_err:   0.009235
[2025-10-03 01:59:46] [Iter 1839/2250] R4[1426/2400] | LR: 0.013856 | E: -42.286591 | E_var:     0.5198 | E_err:   0.011265
[2025-10-03 01:59:54] [Iter 1840/2250] R4[1428/2400] | LR: 0.013824 | E: -42.300917 | E_var:     0.3830 | E_err:   0.009670
[2025-10-03 02:00:02] [Iter 1841/2250] R4[1430/2400] | LR: 0.013793 | E: -42.290196 | E_var:     0.3724 | E_err:   0.009535
[2025-10-03 02:00:09] [Iter 1842/2250] R4[1432/2400] | LR: 0.013762 | E: -42.294965 | E_var:     0.3793 | E_err:   0.009623
[2025-10-03 02:00:17] [Iter 1843/2250] R4[1434/2400] | LR: 0.013731 | E: -42.294734 | E_var:     0.5327 | E_err:   0.011404
[2025-10-03 02:00:25] [Iter 1844/2250] R4[1436/2400] | LR: 0.013700 | E: -42.298700 | E_var:     0.4921 | E_err:   0.010961
[2025-10-03 02:00:33] [Iter 1845/2250] R4[1438/2400] | LR: 0.013668 | E: -42.290415 | E_var:     0.4446 | E_err:   0.010419
[2025-10-03 02:00:41] [Iter 1846/2250] R4[1440/2400] | LR: 0.013637 | E: -42.326208 | E_var:     0.6451 | E_err:   0.012550
[2025-10-03 02:00:48] [Iter 1847/2250] R4[1442/2400] | LR: 0.013606 | E: -42.281733 | E_var:     0.4506 | E_err:   0.010488
[2025-10-03 02:00:56] [Iter 1848/2250] R4[1444/2400] | LR: 0.013575 | E: -42.300498 | E_var:     0.5636 | E_err:   0.011730
[2025-10-03 02:01:04] [Iter 1849/2250] R4[1446/2400] | LR: 0.013544 | E: -42.294948 | E_var:     0.4037 | E_err:   0.009927
[2025-10-03 02:01:12] [Iter 1850/2250] R4[1448/2400] | LR: 0.013513 | E: -42.294382 | E_var:     0.4135 | E_err:   0.010047
[2025-10-03 02:01:20] [Iter 1851/2250] R4[1450/2400] | LR: 0.013482 | E: -42.287986 | E_var:     0.6181 | E_err:   0.012284
[2025-10-03 02:01:28] [Iter 1852/2250] R4[1452/2400] | LR: 0.013451 | E: -42.307195 | E_var:     0.4013 | E_err:   0.009899
[2025-10-03 02:01:35] [Iter 1853/2250] R4[1454/2400] | LR: 0.013420 | E: -42.310862 | E_var:     0.4173 | E_err:   0.010093
[2025-10-03 02:01:43] [Iter 1854/2250] R4[1456/2400] | LR: 0.013389 | E: -42.291961 | E_var:     0.4589 | E_err:   0.010585
[2025-10-03 02:01:51] [Iter 1855/2250] R4[1458/2400] | LR: 0.013358 | E: -42.299166 | E_var:     0.3582 | E_err:   0.009352
[2025-10-03 02:01:59] [Iter 1856/2250] R4[1460/2400] | LR: 0.013327 | E: -42.301758 | E_var:     0.4235 | E_err:   0.010168
[2025-10-03 02:02:07] [Iter 1857/2250] R4[1462/2400] | LR: 0.013297 | E: -42.294848 | E_var:     0.4476 | E_err:   0.010454
[2025-10-03 02:02:14] [Iter 1858/2250] R4[1464/2400] | LR: 0.013266 | E: -42.278324 | E_var:     0.7501 | E_err:   0.013533
[2025-10-03 02:02:22] [Iter 1859/2250] R4[1466/2400] | LR: 0.013235 | E: -42.307062 | E_var:     0.4929 | E_err:   0.010969
[2025-10-03 02:02:30] [Iter 1860/2250] R4[1468/2400] | LR: 0.013204 | E: -42.315082 | E_var:     0.4471 | E_err:   0.010448
[2025-10-03 02:02:38] [Iter 1861/2250] R4[1470/2400] | LR: 0.013174 | E: -42.305454 | E_var:     0.3600 | E_err:   0.009375
[2025-10-03 02:02:46] [Iter 1862/2250] R4[1472/2400] | LR: 0.013143 | E: -42.318101 | E_var:     0.4004 | E_err:   0.009887
[2025-10-03 02:02:54] [Iter 1863/2250] R4[1474/2400] | LR: 0.013112 | E: -42.305344 | E_var:     0.3753 | E_err:   0.009572
[2025-10-03 02:03:01] [Iter 1864/2250] R4[1476/2400] | LR: 0.013082 | E: -42.296110 | E_var:     0.4307 | E_err:   0.010255
[2025-10-03 02:03:09] [Iter 1865/2250] R4[1478/2400] | LR: 0.013051 | E: -42.313190 | E_var:     0.3959 | E_err:   0.009831
[2025-10-03 02:03:17] [Iter 1866/2250] R4[1480/2400] | LR: 0.013020 | E: -42.283613 | E_var:     0.4389 | E_err:   0.010351
[2025-10-03 02:03:25] [Iter 1867/2250] R4[1482/2400] | LR: 0.012990 | E: -42.303007 | E_var:     0.4651 | E_err:   0.010655
[2025-10-03 02:03:33] [Iter 1868/2250] R4[1484/2400] | LR: 0.012959 | E: -42.300414 | E_var:     0.3889 | E_err:   0.009744
[2025-10-03 02:03:40] [Iter 1869/2250] R4[1486/2400] | LR: 0.012929 | E: -42.303242 | E_var:     0.9976 | E_err:   0.015606
[2025-10-03 02:03:48] [Iter 1870/2250] R4[1488/2400] | LR: 0.012898 | E: -42.296639 | E_var:     0.4072 | E_err:   0.009971
[2025-10-03 02:03:56] [Iter 1871/2250] R4[1490/2400] | LR: 0.012868 | E: -42.308255 | E_var:     0.5908 | E_err:   0.012010
[2025-10-03 02:04:04] [Iter 1872/2250] R4[1492/2400] | LR: 0.012838 | E: -42.284307 | E_var:     0.4416 | E_err:   0.010384
[2025-10-03 02:04:12] [Iter 1873/2250] R4[1494/2400] | LR: 0.012807 | E: -42.294786 | E_var:     0.4626 | E_err:   0.010628
[2025-10-03 02:04:20] [Iter 1874/2250] R4[1496/2400] | LR: 0.012777 | E: -42.305641 | E_var:     0.3843 | E_err:   0.009686
[2025-10-03 02:04:27] [Iter 1875/2250] R4[1498/2400] | LR: 0.012747 | E: -42.303491 | E_var:     0.3681 | E_err:   0.009479
[2025-10-03 02:04:35] [Iter 1876/2250] R4[1500/2400] | LR: 0.012716 | E: -42.303990 | E_var:     0.4784 | E_err:   0.010807
[2025-10-03 02:04:43] [Iter 1877/2250] R4[1502/2400] | LR: 0.012686 | E: -42.299158 | E_var:     0.3851 | E_err:   0.009697
[2025-10-03 02:04:51] [Iter 1878/2250] R4[1504/2400] | LR: 0.012656 | E: -42.277880 | E_var:     0.3904 | E_err:   0.009763
[2025-10-03 02:04:59] [Iter 1879/2250] R4[1506/2400] | LR: 0.012626 | E: -42.273406 | E_var:     0.3525 | E_err:   0.009277
[2025-10-03 02:05:06] [Iter 1880/2250] R4[1508/2400] | LR: 0.012596 | E: -42.295428 | E_var:     0.4997 | E_err:   0.011046
[2025-10-03 02:05:14] [Iter 1881/2250] R4[1510/2400] | LR: 0.012566 | E: -42.316422 | E_var:     0.5078 | E_err:   0.011134
[2025-10-03 02:05:22] [Iter 1882/2250] R4[1512/2400] | LR: 0.012536 | E: -42.293555 | E_var:     0.3243 | E_err:   0.008898
[2025-10-03 02:05:30] [Iter 1883/2250] R4[1514/2400] | LR: 0.012506 | E: -42.287851 | E_var:     0.4247 | E_err:   0.010183
[2025-10-03 02:05:38] [Iter 1884/2250] R4[1516/2400] | LR: 0.012476 | E: -42.300628 | E_var:     0.6725 | E_err:   0.012814
[2025-10-03 02:05:46] [Iter 1885/2250] R4[1518/2400] | LR: 0.012446 | E: -42.303639 | E_var:     0.3835 | E_err:   0.009676
[2025-10-03 02:05:53] [Iter 1886/2250] R4[1520/2400] | LR: 0.012416 | E: -42.304212 | E_var:     0.4074 | E_err:   0.009973
[2025-10-03 02:06:01] [Iter 1887/2250] R4[1522/2400] | LR: 0.012386 | E: -42.305270 | E_var:     0.4257 | E_err:   0.010195
[2025-10-03 02:06:09] [Iter 1888/2250] R4[1524/2400] | LR: 0.012356 | E: -42.301022 | E_var:     0.4014 | E_err:   0.009899
[2025-10-03 02:06:17] [Iter 1889/2250] R4[1526/2400] | LR: 0.012326 | E: -42.307728 | E_var:     0.3619 | E_err:   0.009400
[2025-10-03 02:06:25] [Iter 1890/2250] R4[1528/2400] | LR: 0.012296 | E: -42.293518 | E_var:     0.4152 | E_err:   0.010069
[2025-10-03 02:06:32] [Iter 1891/2250] R4[1530/2400] | LR: 0.012267 | E: -42.298519 | E_var:     0.3585 | E_err:   0.009355
[2025-10-03 02:06:40] [Iter 1892/2250] R4[1532/2400] | LR: 0.012237 | E: -42.317027 | E_var:     0.4333 | E_err:   0.010285
[2025-10-03 02:06:48] [Iter 1893/2250] R4[1534/2400] | LR: 0.012207 | E: -42.302704 | E_var:     0.4061 | E_err:   0.009957
[2025-10-03 02:06:56] [Iter 1894/2250] R4[1536/2400] | LR: 0.012178 | E: -42.305310 | E_var:     0.3658 | E_err:   0.009450
[2025-10-03 02:07:04] [Iter 1895/2250] R4[1538/2400] | LR: 0.012148 | E: -42.294677 | E_var:     0.3750 | E_err:   0.009569
[2025-10-03 02:07:12] [Iter 1896/2250] R4[1540/2400] | LR: 0.012119 | E: -42.280725 | E_var:     0.4369 | E_err:   0.010328
[2025-10-03 02:07:19] [Iter 1897/2250] R4[1542/2400] | LR: 0.012089 | E: -42.323235 | E_var:     0.3583 | E_err:   0.009352
[2025-10-03 02:07:27] [Iter 1898/2250] R4[1544/2400] | LR: 0.012060 | E: -42.297491 | E_var:     0.3398 | E_err:   0.009108
[2025-10-03 02:07:35] [Iter 1899/2250] R4[1546/2400] | LR: 0.012030 | E: -42.291007 | E_var:     0.3325 | E_err:   0.009010
[2025-10-03 02:07:43] [Iter 1900/2250] R4[1548/2400] | LR: 0.012001 | E: -42.290925 | E_var:     0.3854 | E_err:   0.009700
[2025-10-03 02:07:51] [Iter 1901/2250] R4[1550/2400] | LR: 0.011971 | E: -42.300873 | E_var:     0.4825 | E_err:   0.010853
[2025-10-03 02:07:58] [Iter 1902/2250] R4[1552/2400] | LR: 0.011942 | E: -42.310608 | E_var:     0.9345 | E_err:   0.015104
[2025-10-03 02:08:06] [Iter 1903/2250] R4[1554/2400] | LR: 0.011913 | E: -42.298022 | E_var:     0.5809 | E_err:   0.011909
[2025-10-03 02:08:14] [Iter 1904/2250] R4[1556/2400] | LR: 0.011884 | E: -42.287126 | E_var:     0.3644 | E_err:   0.009433
[2025-10-03 02:08:22] [Iter 1905/2250] R4[1558/2400] | LR: 0.011854 | E: -42.298917 | E_var:     0.3825 | E_err:   0.009664
[2025-10-03 02:08:30] [Iter 1906/2250] R4[1560/2400] | LR: 0.011825 | E: -42.298472 | E_var:     0.3696 | E_err:   0.009499
[2025-10-03 02:08:38] [Iter 1907/2250] R4[1562/2400] | LR: 0.011796 | E: -42.296270 | E_var:     0.4669 | E_err:   0.010676
[2025-10-03 02:08:45] [Iter 1908/2250] R4[1564/2400] | LR: 0.011767 | E: -42.311960 | E_var:     0.4045 | E_err:   0.009938
[2025-10-03 02:08:53] [Iter 1909/2250] R4[1566/2400] | LR: 0.011738 | E: -42.312708 | E_var:     0.4931 | E_err:   0.010972
[2025-10-03 02:09:01] [Iter 1910/2250] R4[1568/2400] | LR: 0.011709 | E: -42.282335 | E_var:     0.5446 | E_err:   0.011531
[2025-10-03 02:09:09] [Iter 1911/2250] R4[1570/2400] | LR: 0.011680 | E: -42.312180 | E_var:     0.3884 | E_err:   0.009737
[2025-10-03 02:09:17] [Iter 1912/2250] R4[1572/2400] | LR: 0.011651 | E: -42.307927 | E_var:     0.5040 | E_err:   0.011092
[2025-10-03 02:09:25] [Iter 1913/2250] R4[1574/2400] | LR: 0.011622 | E: -42.292991 | E_var:     0.4968 | E_err:   0.011013
[2025-10-03 02:09:32] [Iter 1914/2250] R4[1576/2400] | LR: 0.011593 | E: -42.296858 | E_var:     0.4237 | E_err:   0.010171
[2025-10-03 02:09:40] [Iter 1915/2250] R4[1578/2400] | LR: 0.011564 | E: -42.313748 | E_var:     0.3386 | E_err:   0.009092
[2025-10-03 02:09:48] [Iter 1916/2250] R4[1580/2400] | LR: 0.011536 | E: -42.272822 | E_var:     0.4992 | E_err:   0.011039
[2025-10-03 02:09:56] [Iter 1917/2250] R4[1582/2400] | LR: 0.011507 | E: -42.313379 | E_var:     0.3407 | E_err:   0.009121
[2025-10-03 02:10:04] [Iter 1918/2250] R4[1584/2400] | LR: 0.011478 | E: -42.298978 | E_var:     0.6102 | E_err:   0.012205
[2025-10-03 02:10:11] [Iter 1919/2250] R4[1586/2400] | LR: 0.011449 | E: -42.312442 | E_var:     0.5023 | E_err:   0.011074
[2025-10-03 02:10:19] [Iter 1920/2250] R4[1588/2400] | LR: 0.011421 | E: -42.286056 | E_var:     0.4269 | E_err:   0.010209
[2025-10-03 02:10:27] [Iter 1921/2250] R4[1590/2400] | LR: 0.011392 | E: -42.307397 | E_var:     0.3281 | E_err:   0.008950
[2025-10-03 02:10:35] [Iter 1922/2250] R4[1592/2400] | LR: 0.011364 | E: -42.280073 | E_var:     0.4269 | E_err:   0.010210
[2025-10-03 02:10:43] [Iter 1923/2250] R4[1594/2400] | LR: 0.011335 | E: -42.303061 | E_var:     0.3734 | E_err:   0.009548
[2025-10-03 02:10:51] [Iter 1924/2250] R4[1596/2400] | LR: 0.011307 | E: -42.300302 | E_var:     0.3998 | E_err:   0.009879
[2025-10-03 02:10:58] [Iter 1925/2250] R4[1598/2400] | LR: 0.011278 | E: -42.309788 | E_var:     0.3821 | E_err:   0.009659
[2025-10-03 02:11:06] [Iter 1926/2250] R4[1600/2400] | LR: 0.011250 | E: -42.312292 | E_var:     0.3603 | E_err:   0.009378
[2025-10-03 02:11:14] [Iter 1927/2250] R4[1602/2400] | LR: 0.011222 | E: -42.299819 | E_var:     0.3385 | E_err:   0.009091
[2025-10-03 02:11:22] [Iter 1928/2250] R4[1604/2400] | LR: 0.011193 | E: -42.316404 | E_var:     0.3791 | E_err:   0.009621
[2025-10-03 02:11:30] [Iter 1929/2250] R4[1606/2400] | LR: 0.011165 | E: -42.289391 | E_var:     0.4612 | E_err:   0.010612
[2025-10-03 02:11:37] [Iter 1930/2250] R4[1608/2400] | LR: 0.011137 | E: -42.287374 | E_var:     0.4011 | E_err:   0.009895
[2025-10-03 02:11:45] [Iter 1931/2250] R4[1610/2400] | LR: 0.011109 | E: -42.314383 | E_var:     0.3882 | E_err:   0.009735
[2025-10-03 02:11:53] [Iter 1932/2250] R4[1612/2400] | LR: 0.011081 | E: -42.295495 | E_var:     0.4693 | E_err:   0.010704
[2025-10-03 02:12:01] [Iter 1933/2250] R4[1614/2400] | LR: 0.011053 | E: -42.317005 | E_var:     0.3726 | E_err:   0.009538
[2025-10-03 02:12:09] [Iter 1934/2250] R4[1616/2400] | LR: 0.011025 | E: -42.299102 | E_var:     0.3645 | E_err:   0.009433
[2025-10-03 02:12:17] [Iter 1935/2250] R4[1618/2400] | LR: 0.010997 | E: -42.304733 | E_var:     0.7021 | E_err:   0.013092
[2025-10-03 02:12:24] [Iter 1936/2250] R4[1620/2400] | LR: 0.010969 | E: -42.312048 | E_var:     0.3582 | E_err:   0.009351
[2025-10-03 02:12:32] [Iter 1937/2250] R4[1622/2400] | LR: 0.010941 | E: -42.306896 | E_var:     0.3425 | E_err:   0.009145
[2025-10-03 02:12:40] [Iter 1938/2250] R4[1624/2400] | LR: 0.010913 | E: -42.296854 | E_var:     0.3974 | E_err:   0.009850
[2025-10-03 02:12:48] [Iter 1939/2250] R4[1626/2400] | LR: 0.010885 | E: -42.313707 | E_var:     0.4447 | E_err:   0.010420
[2025-10-03 02:12:56] [Iter 1940/2250] R4[1628/2400] | LR: 0.010858 | E: -42.317130 | E_var:     0.4510 | E_err:   0.010494
[2025-10-03 02:13:03] [Iter 1941/2250] R4[1630/2400] | LR: 0.010830 | E: -42.294769 | E_var:     0.3999 | E_err:   0.009881
[2025-10-03 02:13:11] [Iter 1942/2250] R4[1632/2400] | LR: 0.010802 | E: -42.303418 | E_var:     0.3999 | E_err:   0.009881
[2025-10-03 02:13:19] [Iter 1943/2250] R4[1634/2400] | LR: 0.010775 | E: -42.308135 | E_var:     0.3531 | E_err:   0.009284
[2025-10-03 02:13:27] [Iter 1944/2250] R4[1636/2400] | LR: 0.010747 | E: -42.283768 | E_var:     0.4175 | E_err:   0.010096
[2025-10-03 02:13:35] [Iter 1945/2250] R4[1638/2400] | LR: 0.010719 | E: -42.307474 | E_var:     0.3577 | E_err:   0.009345
[2025-10-03 02:13:43] [Iter 1946/2250] R4[1640/2400] | LR: 0.010692 | E: -42.301577 | E_var:     0.3890 | E_err:   0.009745
[2025-10-03 02:13:50] [Iter 1947/2250] R4[1642/2400] | LR: 0.010665 | E: -42.301223 | E_var:     0.4054 | E_err:   0.009948
[2025-10-03 02:13:58] [Iter 1948/2250] R4[1644/2400] | LR: 0.010637 | E: -42.281254 | E_var:     0.5161 | E_err:   0.011225
[2025-10-03 02:14:06] [Iter 1949/2250] R4[1646/2400] | LR: 0.010610 | E: -42.313927 | E_var:     0.4022 | E_err:   0.009909
[2025-10-03 02:14:14] [Iter 1950/2250] R4[1648/2400] | LR: 0.010583 | E: -42.313957 | E_var:     0.3565 | E_err:   0.009330
[2025-10-03 02:14:22] [Iter 1951/2250] R4[1650/2400] | LR: 0.010555 | E: -42.304571 | E_var:     0.4328 | E_err:   0.010280
[2025-10-03 02:14:29] [Iter 1952/2250] R4[1652/2400] | LR: 0.010528 | E: -42.295783 | E_var:     0.5944 | E_err:   0.012046
[2025-10-03 02:14:37] [Iter 1953/2250] R4[1654/2400] | LR: 0.010501 | E: -42.296641 | E_var:     0.3928 | E_err:   0.009792
[2025-10-03 02:14:45] [Iter 1954/2250] R4[1656/2400] | LR: 0.010474 | E: -42.304255 | E_var:     0.4511 | E_err:   0.010494
[2025-10-03 02:14:53] [Iter 1955/2250] R4[1658/2400] | LR: 0.010447 | E: -42.293874 | E_var:     0.4670 | E_err:   0.010677
[2025-10-03 02:15:01] [Iter 1956/2250] R4[1660/2400] | LR: 0.010420 | E: -42.313986 | E_var:     0.4115 | E_err:   0.010023
[2025-10-03 02:15:09] [Iter 1957/2250] R4[1662/2400] | LR: 0.010393 | E: -42.292125 | E_var:     0.4345 | E_err:   0.010299
[2025-10-03 02:15:16] [Iter 1958/2250] R4[1664/2400] | LR: 0.010366 | E: -42.296623 | E_var:     0.4390 | E_err:   0.010353
[2025-10-03 02:15:24] [Iter 1959/2250] R4[1666/2400] | LR: 0.010339 | E: -42.314065 | E_var:     0.5176 | E_err:   0.011241
[2025-10-03 02:15:32] [Iter 1960/2250] R4[1668/2400] | LR: 0.010312 | E: -42.303449 | E_var:     0.3822 | E_err:   0.009659
[2025-10-03 02:15:40] [Iter 1961/2250] R4[1670/2400] | LR: 0.010286 | E: -42.309976 | E_var:     0.6157 | E_err:   0.012260
[2025-10-03 02:15:48] [Iter 1962/2250] R4[1672/2400] | LR: 0.010259 | E: -42.306578 | E_var:     0.4195 | E_err:   0.010120
[2025-10-03 02:15:55] [Iter 1963/2250] R4[1674/2400] | LR: 0.010232 | E: -42.303464 | E_var:     0.5797 | E_err:   0.011896
[2025-10-03 02:16:03] [Iter 1964/2250] R4[1676/2400] | LR: 0.010206 | E: -42.330867 | E_var:     0.3791 | E_err:   0.009620
[2025-10-03 02:16:11] [Iter 1965/2250] R4[1678/2400] | LR: 0.010179 | E: -42.325386 | E_var:     0.5952 | E_err:   0.012055
[2025-10-03 02:16:19] [Iter 1966/2250] R4[1680/2400] | LR: 0.010153 | E: -42.306046 | E_var:     0.3416 | E_err:   0.009132
[2025-10-03 02:16:27] [Iter 1967/2250] R4[1682/2400] | LR: 0.010126 | E: -42.299423 | E_var:     0.3674 | E_err:   0.009471
[2025-10-03 02:16:35] [Iter 1968/2250] R4[1684/2400] | LR: 0.010100 | E: -42.306711 | E_var:     0.3945 | E_err:   0.009814
[2025-10-03 02:16:42] [Iter 1969/2250] R4[1686/2400] | LR: 0.010073 | E: -42.317140 | E_var:     0.4147 | E_err:   0.010062
[2025-10-03 02:16:50] [Iter 1970/2250] R4[1688/2400] | LR: 0.010047 | E: -42.288782 | E_var:     0.4743 | E_err:   0.010761
[2025-10-03 02:16:58] [Iter 1971/2250] R4[1690/2400] | LR: 0.010021 | E: -42.293576 | E_var:     0.4795 | E_err:   0.010820
[2025-10-03 02:17:06] [Iter 1972/2250] R4[1692/2400] | LR: 0.009995 | E: -42.298096 | E_var:     0.4126 | E_err:   0.010036
[2025-10-03 02:17:14] [Iter 1973/2250] R4[1694/2400] | LR: 0.009969 | E: -42.310381 | E_var:     1.3650 | E_err:   0.018255
[2025-10-03 02:17:21] [Iter 1974/2250] R4[1696/2400] | LR: 0.009943 | E: -42.304343 | E_var:     0.4540 | E_err:   0.010528
[2025-10-03 02:17:29] [Iter 1975/2250] R4[1698/2400] | LR: 0.009916 | E: -42.302201 | E_var:     0.4174 | E_err:   0.010095
[2025-10-03 02:17:37] [Iter 1976/2250] R4[1700/2400] | LR: 0.009890 | E: -42.323547 | E_var:     0.5903 | E_err:   0.012005
[2025-10-03 02:17:45] [Iter 1977/2250] R4[1702/2400] | LR: 0.009865 | E: -42.305720 | E_var:     0.3816 | E_err:   0.009652
[2025-10-03 02:17:53] [Iter 1978/2250] R4[1704/2400] | LR: 0.009839 | E: -42.307645 | E_var:     0.4105 | E_err:   0.010011
[2025-10-03 02:18:01] [Iter 1979/2250] R4[1706/2400] | LR: 0.009813 | E: -42.296876 | E_var:     0.4233 | E_err:   0.010165
[2025-10-03 02:18:08] [Iter 1980/2250] R4[1708/2400] | LR: 0.009787 | E: -42.312127 | E_var:     0.4571 | E_err:   0.010564
[2025-10-03 02:18:16] [Iter 1981/2250] R4[1710/2400] | LR: 0.009761 | E: -42.299880 | E_var:     0.5473 | E_err:   0.011559
[2025-10-03 02:18:24] [Iter 1982/2250] R4[1712/2400] | LR: 0.009736 | E: -42.312252 | E_var:     0.4619 | E_err:   0.010619
[2025-10-03 02:18:32] [Iter 1983/2250] R4[1714/2400] | LR: 0.009710 | E: -42.304501 | E_var:     0.3157 | E_err:   0.008780
[2025-10-03 02:18:40] [Iter 1984/2250] R4[1716/2400] | LR: 0.009684 | E: -42.301876 | E_var:     0.4222 | E_err:   0.010153
[2025-10-03 02:18:47] [Iter 1985/2250] R4[1718/2400] | LR: 0.009659 | E: -42.295389 | E_var:     0.3806 | E_err:   0.009639
[2025-10-03 02:18:55] [Iter 1986/2250] R4[1720/2400] | LR: 0.009633 | E: -42.320212 | E_var:     0.4076 | E_err:   0.009976
[2025-10-03 02:19:03] [Iter 1987/2250] R4[1722/2400] | LR: 0.009608 | E: -42.298196 | E_var:     0.4178 | E_err:   0.010100
[2025-10-03 02:19:11] [Iter 1988/2250] R4[1724/2400] | LR: 0.009583 | E: -42.301033 | E_var:     0.4564 | E_err:   0.010556
[2025-10-03 02:19:19] [Iter 1989/2250] R4[1726/2400] | LR: 0.009557 | E: -42.326375 | E_var:     0.3998 | E_err:   0.009880
[2025-10-03 02:19:27] [Iter 1990/2250] R4[1728/2400] | LR: 0.009532 | E: -42.302530 | E_var:     0.4197 | E_err:   0.010122
[2025-10-03 02:19:34] [Iter 1991/2250] R4[1730/2400] | LR: 0.009507 | E: -42.308799 | E_var:     0.6010 | E_err:   0.012113
[2025-10-03 02:19:42] [Iter 1992/2250] R4[1732/2400] | LR: 0.009482 | E: -42.306615 | E_var:     0.5647 | E_err:   0.011742
[2025-10-03 02:19:50] [Iter 1993/2250] R4[1734/2400] | LR: 0.009457 | E: -42.325145 | E_var:     0.5615 | E_err:   0.011709
[2025-10-03 02:19:58] [Iter 1994/2250] R4[1736/2400] | LR: 0.009432 | E: -42.296800 | E_var:     0.3960 | E_err:   0.009833
[2025-10-03 02:20:06] [Iter 1995/2250] R4[1738/2400] | LR: 0.009407 | E: -42.317190 | E_var:     0.5714 | E_err:   0.011811
[2025-10-03 02:20:13] [Iter 1996/2250] R4[1740/2400] | LR: 0.009382 | E: -42.305595 | E_var:     0.4892 | E_err:   0.010929
[2025-10-03 02:20:21] [Iter 1997/2250] R4[1742/2400] | LR: 0.009357 | E: -42.299629 | E_var:     0.4602 | E_err:   0.010600
[2025-10-03 02:20:29] [Iter 1998/2250] R4[1744/2400] | LR: 0.009332 | E: -42.296387 | E_var:     0.4902 | E_err:   0.010939
[2025-10-03 02:20:37] [Iter 1999/2250] R4[1746/2400] | LR: 0.009307 | E: -42.291440 | E_var:     0.4413 | E_err:   0.010379
[2025-10-03 02:20:45] [Iter 2000/2250] R4[1748/2400] | LR: 0.009283 | E: -42.321603 | E_var:     0.3483 | E_err:   0.009221
[2025-10-03 02:20:45] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-10-03 02:20:53] [Iter 2001/2250] R4[1750/2400] | LR: 0.009258 | E: -42.288301 | E_var:     0.4481 | E_err:   0.010460
[2025-10-03 02:21:00] [Iter 2002/2250] R4[1752/2400] | LR: 0.009234 | E: -42.321099 | E_var:     0.3755 | E_err:   0.009575
[2025-10-03 02:21:08] [Iter 2003/2250] R4[1754/2400] | LR: 0.009209 | E: -42.289788 | E_var:     0.4411 | E_err:   0.010377
[2025-10-03 02:21:16] [Iter 2004/2250] R4[1756/2400] | LR: 0.009185 | E: -42.309524 | E_var:     0.3722 | E_err:   0.009532
[2025-10-03 02:21:24] [Iter 2005/2250] R4[1758/2400] | LR: 0.009160 | E: -42.290238 | E_var:     0.3616 | E_err:   0.009396
[2025-10-03 02:21:32] [Iter 2006/2250] R4[1760/2400] | LR: 0.009136 | E: -42.292491 | E_var:     0.4147 | E_err:   0.010062
[2025-10-03 02:21:40] [Iter 2007/2250] R4[1762/2400] | LR: 0.009112 | E: -42.287414 | E_var:     0.4039 | E_err:   0.009930
[2025-10-03 02:21:47] [Iter 2008/2250] R4[1764/2400] | LR: 0.009087 | E: -42.315888 | E_var:     0.4589 | E_err:   0.010585
[2025-10-03 02:21:55] [Iter 2009/2250] R4[1766/2400] | LR: 0.009063 | E: -42.307693 | E_var:     0.5551 | E_err:   0.011641
[2025-10-03 02:22:03] [Iter 2010/2250] R4[1768/2400] | LR: 0.009039 | E: -42.302944 | E_var:     0.4414 | E_err:   0.010381
[2025-10-03 02:22:11] [Iter 2011/2250] R4[1770/2400] | LR: 0.009015 | E: -42.292891 | E_var:     0.4888 | E_err:   0.010924
[2025-10-03 02:22:19] [Iter 2012/2250] R4[1772/2400] | LR: 0.008991 | E: -42.307285 | E_var:     0.4853 | E_err:   0.010885
[2025-10-03 02:22:27] [Iter 2013/2250] R4[1774/2400] | LR: 0.008967 | E: -42.295825 | E_var:     0.4226 | E_err:   0.010158
[2025-10-03 02:22:34] [Iter 2014/2250] R4[1776/2400] | LR: 0.008943 | E: -42.331335 | E_var:     0.4251 | E_err:   0.010187
[2025-10-03 02:22:42] [Iter 2015/2250] R4[1778/2400] | LR: 0.008919 | E: -42.301778 | E_var:     0.4898 | E_err:   0.010935
[2025-10-03 02:22:50] [Iter 2016/2250] R4[1780/2400] | LR: 0.008896 | E: -42.311267 | E_var:     0.3814 | E_err:   0.009650
[2025-10-03 02:22:58] [Iter 2017/2250] R4[1782/2400] | LR: 0.008872 | E: -42.300102 | E_var:     0.7636 | E_err:   0.013654
[2025-10-03 02:23:06] [Iter 2018/2250] R4[1784/2400] | LR: 0.008848 | E: -42.316259 | E_var:     0.3902 | E_err:   0.009761
[2025-10-03 02:23:13] [Iter 2019/2250] R4[1786/2400] | LR: 0.008825 | E: -42.304306 | E_var:     0.3829 | E_err:   0.009668
[2025-10-03 02:23:21] [Iter 2020/2250] R4[1788/2400] | LR: 0.008801 | E: -42.303399 | E_var:     0.4167 | E_err:   0.010087
[2025-10-03 02:23:29] [Iter 2021/2250] R4[1790/2400] | LR: 0.008778 | E: -42.312674 | E_var:     0.3305 | E_err:   0.008983
[2025-10-03 02:23:37] [Iter 2022/2250] R4[1792/2400] | LR: 0.008754 | E: -42.297247 | E_var:     0.4274 | E_err:   0.010215
[2025-10-03 02:23:45] [Iter 2023/2250] R4[1794/2400] | LR: 0.008731 | E: -42.307107 | E_var:     0.4083 | E_err:   0.009984
[2025-10-03 02:23:53] [Iter 2024/2250] R4[1796/2400] | LR: 0.008708 | E: -42.301951 | E_var:     0.4469 | E_err:   0.010445
[2025-10-03 02:24:00] [Iter 2025/2250] R4[1798/2400] | LR: 0.008684 | E: -42.290750 | E_var:     0.4528 | E_err:   0.010515
[2025-10-03 02:24:08] [Iter 2026/2250] R4[1800/2400] | LR: 0.008661 | E: -42.306454 | E_var:     0.4651 | E_err:   0.010656
[2025-10-03 02:24:16] [Iter 2027/2250] R4[1802/2400] | LR: 0.008638 | E: -42.299250 | E_var:     0.3510 | E_err:   0.009258
[2025-10-03 02:24:24] [Iter 2028/2250] R4[1804/2400] | LR: 0.008615 | E: -42.309101 | E_var:     0.4642 | E_err:   0.010646
[2025-10-03 02:24:32] [Iter 2029/2250] R4[1806/2400] | LR: 0.008592 | E: -42.300375 | E_var:     0.3983 | E_err:   0.009861
[2025-10-03 02:24:39] [Iter 2030/2250] R4[1808/2400] | LR: 0.008569 | E: -42.307776 | E_var:     0.4735 | E_err:   0.010752
[2025-10-03 02:24:47] [Iter 2031/2250] R4[1810/2400] | LR: 0.008546 | E: -42.289904 | E_var:     0.4507 | E_err:   0.010490
[2025-10-03 02:24:55] [Iter 2032/2250] R4[1812/2400] | LR: 0.008523 | E: -42.305712 | E_var:     0.4307 | E_err:   0.010255
[2025-10-03 02:25:03] [Iter 2033/2250] R4[1814/2400] | LR: 0.008501 | E: -42.297265 | E_var:     0.3980 | E_err:   0.009858
[2025-10-03 02:25:11] [Iter 2034/2250] R4[1816/2400] | LR: 0.008478 | E: -42.319513 | E_var:     0.4743 | E_err:   0.010761
[2025-10-03 02:25:19] [Iter 2035/2250] R4[1818/2400] | LR: 0.008455 | E: -42.285792 | E_var:     0.4158 | E_err:   0.010076
[2025-10-03 02:25:26] [Iter 2036/2250] R4[1820/2400] | LR: 0.008433 | E: -42.303085 | E_var:     0.4641 | E_err:   0.010645
[2025-10-03 02:25:34] [Iter 2037/2250] R4[1822/2400] | LR: 0.008410 | E: -42.300630 | E_var:     0.4332 | E_err:   0.010284
[2025-10-03 02:25:42] [Iter 2038/2250] R4[1824/2400] | LR: 0.008388 | E: -42.301725 | E_var:     0.4604 | E_err:   0.010602
[2025-10-03 02:25:50] [Iter 2039/2250] R4[1826/2400] | LR: 0.008366 | E: -42.312625 | E_var:     0.3745 | E_err:   0.009562
[2025-10-03 02:25:58] [Iter 2040/2250] R4[1828/2400] | LR: 0.008343 | E: -42.282247 | E_var:     0.4296 | E_err:   0.010241
[2025-10-03 02:26:05] [Iter 2041/2250] R4[1830/2400] | LR: 0.008321 | E: -42.312754 | E_var:     0.3623 | E_err:   0.009405
[2025-10-03 02:26:13] [Iter 2042/2250] R4[1832/2400] | LR: 0.008299 | E: -42.295230 | E_var:     0.3698 | E_err:   0.009502
[2025-10-03 02:26:21] [Iter 2043/2250] R4[1834/2400] | LR: 0.008277 | E: -42.300560 | E_var:     0.4095 | E_err:   0.009998
[2025-10-03 02:26:29] [Iter 2044/2250] R4[1836/2400] | LR: 0.008255 | E: -42.296934 | E_var:     0.5264 | E_err:   0.011337
[2025-10-03 02:26:37] [Iter 2045/2250] R4[1838/2400] | LR: 0.008233 | E: -42.307978 | E_var:     0.3267 | E_err:   0.008932
[2025-10-03 02:26:45] [Iter 2046/2250] R4[1840/2400] | LR: 0.008211 | E: -42.318927 | E_var:     0.3801 | E_err:   0.009633
[2025-10-03 02:26:52] [Iter 2047/2250] R4[1842/2400] | LR: 0.008189 | E: -42.297063 | E_var:     0.4001 | E_err:   0.009883
[2025-10-03 02:27:00] [Iter 2048/2250] R4[1844/2400] | LR: 0.008167 | E: -42.293395 | E_var:     0.4526 | E_err:   0.010512
[2025-10-03 02:27:08] [Iter 2049/2250] R4[1846/2400] | LR: 0.008145 | E: -42.311773 | E_var:     0.4184 | E_err:   0.010107
[2025-10-03 02:27:16] [Iter 2050/2250] R4[1848/2400] | LR: 0.008124 | E: -42.304469 | E_var:     0.3917 | E_err:   0.009779
[2025-10-03 02:27:24] [Iter 2051/2250] R4[1850/2400] | LR: 0.008102 | E: -42.309644 | E_var:     0.6870 | E_err:   0.012951
[2025-10-03 02:27:31] [Iter 2052/2250] R4[1852/2400] | LR: 0.008080 | E: -42.306653 | E_var:     0.4446 | E_err:   0.010418
[2025-10-03 02:27:39] [Iter 2053/2250] R4[1854/2400] | LR: 0.008059 | E: -42.300660 | E_var:     0.5151 | E_err:   0.011214
[2025-10-03 02:27:47] [Iter 2054/2250] R4[1856/2400] | LR: 0.008038 | E: -42.293102 | E_var:     0.5114 | E_err:   0.011174
[2025-10-03 02:27:55] [Iter 2055/2250] R4[1858/2400] | LR: 0.008016 | E: -42.296529 | E_var:     0.3595 | E_err:   0.009369
[2025-10-03 02:28:03] [Iter 2056/2250] R4[1860/2400] | LR: 0.007995 | E: -42.307378 | E_var:     0.4226 | E_err:   0.010157
[2025-10-03 02:28:11] [Iter 2057/2250] R4[1862/2400] | LR: 0.007974 | E: -42.296266 | E_var:     0.4231 | E_err:   0.010164
[2025-10-03 02:28:18] [Iter 2058/2250] R4[1864/2400] | LR: 0.007953 | E: -42.298338 | E_var:     0.4317 | E_err:   0.010266
[2025-10-03 02:28:26] [Iter 2059/2250] R4[1866/2400] | LR: 0.007931 | E: -42.313240 | E_var:     0.3437 | E_err:   0.009160
[2025-10-03 02:28:34] [Iter 2060/2250] R4[1868/2400] | LR: 0.007910 | E: -42.291395 | E_var:     0.3402 | E_err:   0.009114
[2025-10-03 02:28:42] [Iter 2061/2250] R4[1870/2400] | LR: 0.007889 | E: -42.297798 | E_var:     0.4638 | E_err:   0.010641
[2025-10-03 02:28:50] [Iter 2062/2250] R4[1872/2400] | LR: 0.007869 | E: -42.313066 | E_var:     0.4014 | E_err:   0.009900
[2025-10-03 02:28:57] [Iter 2063/2250] R4[1874/2400] | LR: 0.007848 | E: -42.295295 | E_var:     0.4013 | E_err:   0.009899
[2025-10-03 02:29:05] [Iter 2064/2250] R4[1876/2400] | LR: 0.007827 | E: -42.311656 | E_var:     0.3480 | E_err:   0.009218
[2025-10-03 02:29:13] [Iter 2065/2250] R4[1878/2400] | LR: 0.007806 | E: -42.294725 | E_var:     0.4758 | E_err:   0.010778
[2025-10-03 02:29:21] [Iter 2066/2250] R4[1880/2400] | LR: 0.007786 | E: -42.311706 | E_var:     0.4791 | E_err:   0.010816
[2025-10-03 02:29:29] [Iter 2067/2250] R4[1882/2400] | LR: 0.007765 | E: -42.311272 | E_var:     0.3805 | E_err:   0.009639
[2025-10-03 02:29:37] [Iter 2068/2250] R4[1884/2400] | LR: 0.007745 | E: -42.308014 | E_var:     0.4284 | E_err:   0.010227
[2025-10-03 02:29:44] [Iter 2069/2250] R4[1886/2400] | LR: 0.007724 | E: -42.300327 | E_var:     0.3876 | E_err:   0.009727
[2025-10-03 02:29:52] [Iter 2070/2250] R4[1888/2400] | LR: 0.007704 | E: -42.290657 | E_var:     0.3835 | E_err:   0.009677
[2025-10-03 02:30:00] [Iter 2071/2250] R4[1890/2400] | LR: 0.007684 | E: -42.297545 | E_var:     0.4411 | E_err:   0.010378
[2025-10-03 02:30:08] [Iter 2072/2250] R4[1892/2400] | LR: 0.007663 | E: -42.290013 | E_var:     0.4565 | E_err:   0.010557
[2025-10-03 02:30:16] [Iter 2073/2250] R4[1894/2400] | LR: 0.007643 | E: -42.297952 | E_var:     0.4026 | E_err:   0.009914
[2025-10-03 02:30:23] [Iter 2074/2250] R4[1896/2400] | LR: 0.007623 | E: -42.288420 | E_var:     0.3878 | E_err:   0.009730
[2025-10-03 02:30:31] [Iter 2075/2250] R4[1898/2400] | LR: 0.007603 | E: -42.292163 | E_var:     0.3552 | E_err:   0.009312
[2025-10-03 02:30:39] [Iter 2076/2250] R4[1900/2400] | LR: 0.007583 | E: -42.304410 | E_var:     0.4934 | E_err:   0.010975
[2025-10-03 02:30:47] [Iter 2077/2250] R4[1902/2400] | LR: 0.007563 | E: -42.315664 | E_var:     0.4380 | E_err:   0.010341
[2025-10-03 02:30:55] [Iter 2078/2250] R4[1904/2400] | LR: 0.007543 | E: -42.311527 | E_var:     0.3656 | E_err:   0.009448
[2025-10-03 02:31:03] [Iter 2079/2250] R4[1906/2400] | LR: 0.007524 | E: -42.311196 | E_var:     0.3821 | E_err:   0.009659
[2025-10-03 02:31:10] [Iter 2080/2250] R4[1908/2400] | LR: 0.007504 | E: -42.272499 | E_var:     0.5405 | E_err:   0.011487
[2025-10-03 02:31:18] [Iter 2081/2250] R4[1910/2400] | LR: 0.007484 | E: -42.288466 | E_var:     0.3835 | E_err:   0.009676
[2025-10-03 02:31:26] [Iter 2082/2250] R4[1912/2400] | LR: 0.007465 | E: -42.309705 | E_var:     0.5195 | E_err:   0.011262
[2025-10-03 02:31:34] [Iter 2083/2250] R4[1914/2400] | LR: 0.007445 | E: -42.321522 | E_var:     0.4349 | E_err:   0.010305
[2025-10-03 02:31:42] [Iter 2084/2250] R4[1916/2400] | LR: 0.007426 | E: -42.295274 | E_var:     0.4162 | E_err:   0.010080
[2025-10-03 02:31:49] [Iter 2085/2250] R4[1918/2400] | LR: 0.007407 | E: -42.316316 | E_var:     0.4538 | E_err:   0.010525
[2025-10-03 02:31:57] [Iter 2086/2250] R4[1920/2400] | LR: 0.007387 | E: -42.306248 | E_var:     0.3113 | E_err:   0.008717
[2025-10-03 02:32:05] [Iter 2087/2250] R4[1922/2400] | LR: 0.007368 | E: -42.310246 | E_var:     0.3514 | E_err:   0.009263
[2025-10-03 02:32:13] [Iter 2088/2250] R4[1924/2400] | LR: 0.007349 | E: -42.290991 | E_var:     0.4358 | E_err:   0.010314
[2025-10-03 02:32:21] [Iter 2089/2250] R4[1926/2400] | LR: 0.007330 | E: -42.300808 | E_var:     0.4358 | E_err:   0.010315
[2025-10-03 02:32:29] [Iter 2090/2250] R4[1928/2400] | LR: 0.007311 | E: -42.304897 | E_var:     0.4859 | E_err:   0.010891
[2025-10-03 02:32:36] [Iter 2091/2250] R4[1930/2400] | LR: 0.007292 | E: -42.301480 | E_var:     0.4036 | E_err:   0.009927
[2025-10-03 02:32:44] [Iter 2092/2250] R4[1932/2400] | LR: 0.007273 | E: -42.313846 | E_var:     0.3640 | E_err:   0.009427
[2025-10-03 02:32:52] [Iter 2093/2250] R4[1934/2400] | LR: 0.007254 | E: -42.303061 | E_var:     0.3490 | E_err:   0.009231
[2025-10-03 02:33:00] [Iter 2094/2250] R4[1936/2400] | LR: 0.007236 | E: -42.301386 | E_var:     0.3532 | E_err:   0.009285
[2025-10-03 02:33:08] [Iter 2095/2250] R4[1938/2400] | LR: 0.007217 | E: -42.294903 | E_var:     0.4215 | E_err:   0.010144
[2025-10-03 02:33:15] [Iter 2096/2250] R4[1940/2400] | LR: 0.007198 | E: -42.304578 | E_var:     0.4531 | E_err:   0.010517
[2025-10-03 02:33:23] [Iter 2097/2250] R4[1942/2400] | LR: 0.007180 | E: -42.308455 | E_var:     0.4026 | E_err:   0.009915
[2025-10-03 02:33:31] [Iter 2098/2250] R4[1944/2400] | LR: 0.007161 | E: -42.317052 | E_var:     0.5102 | E_err:   0.011160
[2025-10-03 02:33:39] [Iter 2099/2250] R4[1946/2400] | LR: 0.007143 | E: -42.296113 | E_var:     0.4751 | E_err:   0.010770
[2025-10-03 02:33:47] [Iter 2100/2250] R4[1948/2400] | LR: 0.007125 | E: -42.318251 | E_var:     0.3509 | E_err:   0.009256
[2025-10-03 02:33:55] [Iter 2101/2250] R4[1950/2400] | LR: 0.007107 | E: -42.309503 | E_var:     0.3558 | E_err:   0.009320
[2025-10-03 02:34:02] [Iter 2102/2250] R4[1952/2400] | LR: 0.007088 | E: -42.306434 | E_var:     0.4490 | E_err:   0.010470
[2025-10-03 02:34:10] [Iter 2103/2250] R4[1954/2400] | LR: 0.007070 | E: -42.293972 | E_var:     0.4116 | E_err:   0.010024
[2025-10-03 02:34:18] [Iter 2104/2250] R4[1956/2400] | LR: 0.007052 | E: -42.302631 | E_var:     0.3786 | E_err:   0.009614
[2025-10-03 02:34:26] [Iter 2105/2250] R4[1958/2400] | LR: 0.007034 | E: -42.320172 | E_var:     0.3973 | E_err:   0.009849
[2025-10-03 02:34:34] [Iter 2106/2250] R4[1960/2400] | LR: 0.007017 | E: -42.306793 | E_var:     0.4688 | E_err:   0.010698
[2025-10-03 02:34:41] [Iter 2107/2250] R4[1962/2400] | LR: 0.006999 | E: -42.304241 | E_var:     0.3835 | E_err:   0.009676
[2025-10-03 02:34:49] [Iter 2108/2250] R4[1964/2400] | LR: 0.006981 | E: -42.317463 | E_var:     0.3635 | E_err:   0.009421
[2025-10-03 02:34:57] [Iter 2109/2250] R4[1966/2400] | LR: 0.006963 | E: -42.298435 | E_var:     0.3409 | E_err:   0.009123
[2025-10-03 02:35:05] [Iter 2110/2250] R4[1968/2400] | LR: 0.006946 | E: -42.309303 | E_var:     0.3654 | E_err:   0.009445
[2025-10-03 02:35:13] [Iter 2111/2250] R4[1970/2400] | LR: 0.006928 | E: -42.301293 | E_var:     0.3751 | E_err:   0.009570
[2025-10-03 02:35:21] [Iter 2112/2250] R4[1972/2400] | LR: 0.006911 | E: -42.298945 | E_var:     0.4880 | E_err:   0.010915
[2025-10-03 02:35:28] [Iter 2113/2250] R4[1974/2400] | LR: 0.006894 | E: -42.324207 | E_var:     1.0809 | E_err:   0.016245
[2025-10-03 02:35:36] [Iter 2114/2250] R4[1976/2400] | LR: 0.006876 | E: -42.307535 | E_var:     0.4155 | E_err:   0.010072
[2025-10-03 02:35:44] [Iter 2115/2250] R4[1978/2400] | LR: 0.006859 | E: -42.316595 | E_var:     0.3792 | E_err:   0.009622
[2025-10-03 02:35:52] [Iter 2116/2250] R4[1980/2400] | LR: 0.006842 | E: -42.295922 | E_var:     0.5230 | E_err:   0.011300
[2025-10-03 02:36:00] [Iter 2117/2250] R4[1982/2400] | LR: 0.006825 | E: -42.305398 | E_var:     0.4300 | E_err:   0.010245
[2025-10-03 02:36:07] [Iter 2118/2250] R4[1984/2400] | LR: 0.006808 | E: -42.314252 | E_var:     0.3344 | E_err:   0.009036
[2025-10-03 02:36:15] [Iter 2119/2250] R4[1986/2400] | LR: 0.006791 | E: -42.313083 | E_var:     0.3956 | E_err:   0.009828
[2025-10-03 02:36:23] [Iter 2120/2250] R4[1988/2400] | LR: 0.006774 | E: -42.311137 | E_var:     0.3754 | E_err:   0.009573
[2025-10-03 02:36:31] [Iter 2121/2250] R4[1990/2400] | LR: 0.006757 | E: -42.309588 | E_var:     0.4672 | E_err:   0.010680
[2025-10-03 02:36:39] [Iter 2122/2250] R4[1992/2400] | LR: 0.006741 | E: -42.296409 | E_var:     0.3715 | E_err:   0.009523
[2025-10-03 02:36:47] [Iter 2123/2250] R4[1994/2400] | LR: 0.006724 | E: -42.310391 | E_var:     0.3268 | E_err:   0.008933
[2025-10-03 02:36:54] [Iter 2124/2250] R4[1996/2400] | LR: 0.006708 | E: -42.314531 | E_var:     0.3907 | E_err:   0.009767
[2025-10-03 02:37:02] [Iter 2125/2250] R4[1998/2400] | LR: 0.006691 | E: -42.309422 | E_var:     0.3838 | E_err:   0.009680
[2025-10-03 02:37:10] [Iter 2126/2250] R4[2000/2400] | LR: 0.006675 | E: -42.316000 | E_var:     0.3699 | E_err:   0.009504
[2025-10-03 02:37:18] [Iter 2127/2250] R4[2002/2400] | LR: 0.006658 | E: -42.291314 | E_var:     0.3390 | E_err:   0.009098
[2025-10-03 02:37:26] [Iter 2128/2250] R4[2004/2400] | LR: 0.006642 | E: -42.323953 | E_var:     0.6779 | E_err:   0.012865
[2025-10-03 02:37:33] [Iter 2129/2250] R4[2006/2400] | LR: 0.006626 | E: -42.305832 | E_var:     0.4428 | E_err:   0.010398
[2025-10-03 02:37:41] [Iter 2130/2250] R4[2008/2400] | LR: 0.006610 | E: -42.319626 | E_var:     0.4847 | E_err:   0.010878
[2025-10-03 02:37:49] [Iter 2131/2250] R4[2010/2400] | LR: 0.006594 | E: -42.313001 | E_var:     0.3615 | E_err:   0.009394
[2025-10-03 02:37:57] [Iter 2132/2250] R4[2012/2400] | LR: 0.006578 | E: -42.288833 | E_var:     0.4675 | E_err:   0.010683
[2025-10-03 02:38:05] [Iter 2133/2250] R4[2014/2400] | LR: 0.006562 | E: -42.301923 | E_var:     0.3935 | E_err:   0.009801
[2025-10-03 02:38:13] [Iter 2134/2250] R4[2016/2400] | LR: 0.006546 | E: -42.304503 | E_var:     0.4175 | E_err:   0.010096
[2025-10-03 02:38:20] [Iter 2135/2250] R4[2018/2400] | LR: 0.006530 | E: -42.297786 | E_var:     0.3642 | E_err:   0.009430
[2025-10-03 02:38:28] [Iter 2136/2250] R4[2020/2400] | LR: 0.006515 | E: -42.298219 | E_var:     0.3224 | E_err:   0.008872
[2025-10-03 02:38:36] [Iter 2137/2250] R4[2022/2400] | LR: 0.006499 | E: -42.305162 | E_var:     0.6977 | E_err:   0.013051
[2025-10-03 02:38:44] [Iter 2138/2250] R4[2024/2400] | LR: 0.006484 | E: -42.303583 | E_var:     0.4779 | E_err:   0.010802
[2025-10-03 02:38:52] [Iter 2139/2250] R4[2026/2400] | LR: 0.006468 | E: -42.311072 | E_var:     0.4186 | E_err:   0.010110
[2025-10-03 02:38:59] [Iter 2140/2250] R4[2028/2400] | LR: 0.006453 | E: -42.294884 | E_var:     0.3303 | E_err:   0.008980
[2025-10-03 02:39:07] [Iter 2141/2250] R4[2030/2400] | LR: 0.006438 | E: -42.316550 | E_var:     0.3818 | E_err:   0.009655
[2025-10-03 02:39:15] [Iter 2142/2250] R4[2032/2400] | LR: 0.006422 | E: -42.295910 | E_var:     0.3375 | E_err:   0.009078
[2025-10-03 02:39:23] [Iter 2143/2250] R4[2034/2400] | LR: 0.006407 | E: -42.310616 | E_var:     0.5989 | E_err:   0.012092
[2025-10-03 02:39:31] [Iter 2144/2250] R4[2036/2400] | LR: 0.006392 | E: -42.297168 | E_var:     0.4753 | E_err:   0.010772
[2025-10-03 02:39:39] [Iter 2145/2250] R4[2038/2400] | LR: 0.006377 | E: -42.296651 | E_var:     0.3451 | E_err:   0.009179
[2025-10-03 02:39:46] [Iter 2146/2250] R4[2040/2400] | LR: 0.006362 | E: -42.322062 | E_var:     0.3689 | E_err:   0.009490
[2025-10-03 02:39:54] [Iter 2147/2250] R4[2042/2400] | LR: 0.006348 | E: -42.316228 | E_var:     0.3562 | E_err:   0.009326
[2025-10-03 02:40:02] [Iter 2148/2250] R4[2044/2400] | LR: 0.006333 | E: -42.315241 | E_var:     0.3768 | E_err:   0.009591
[2025-10-03 02:40:10] [Iter 2149/2250] R4[2046/2400] | LR: 0.006318 | E: -42.314688 | E_var:     0.4170 | E_err:   0.010090
[2025-10-03 02:40:18] [Iter 2150/2250] R4[2048/2400] | LR: 0.006304 | E: -42.308671 | E_var:     0.4134 | E_err:   0.010046
[2025-10-03 02:40:26] [Iter 2151/2250] R4[2050/2400] | LR: 0.006289 | E: -42.304791 | E_var:     0.4171 | E_err:   0.010091
[2025-10-03 02:40:33] [Iter 2152/2250] R4[2052/2400] | LR: 0.006275 | E: -42.315037 | E_var:     0.4181 | E_err:   0.010103
[2025-10-03 02:40:41] [Iter 2153/2250] R4[2054/2400] | LR: 0.006260 | E: -42.312155 | E_var:     0.4268 | E_err:   0.010208
[2025-10-03 02:40:49] [Iter 2154/2250] R4[2056/2400] | LR: 0.006246 | E: -42.293664 | E_var:     0.4476 | E_err:   0.010453
[2025-10-03 02:40:57] [Iter 2155/2250] R4[2058/2400] | LR: 0.006232 | E: -42.305395 | E_var:     0.3565 | E_err:   0.009329
[2025-10-03 02:41:05] [Iter 2156/2250] R4[2060/2400] | LR: 0.006218 | E: -42.321108 | E_var:     0.4738 | E_err:   0.010755
[2025-10-03 02:41:12] [Iter 2157/2250] R4[2062/2400] | LR: 0.006204 | E: -42.297056 | E_var:     0.3791 | E_err:   0.009620
[2025-10-03 02:41:20] [Iter 2158/2250] R4[2064/2400] | LR: 0.006190 | E: -42.323473 | E_var:     0.4176 | E_err:   0.010097
[2025-10-03 02:41:28] [Iter 2159/2250] R4[2066/2400] | LR: 0.006176 | E: -42.307375 | E_var:     0.4824 | E_err:   0.010852
[2025-10-03 02:41:36] [Iter 2160/2250] R4[2068/2400] | LR: 0.006162 | E: -42.292183 | E_var:     0.4254 | E_err:   0.010191
[2025-10-03 02:41:44] [Iter 2161/2250] R4[2070/2400] | LR: 0.006148 | E: -42.303573 | E_var:     0.4005 | E_err:   0.009888
[2025-10-03 02:41:52] [Iter 2162/2250] R4[2072/2400] | LR: 0.006135 | E: -42.294725 | E_var:     0.3593 | E_err:   0.009366
[2025-10-03 02:41:59] [Iter 2163/2250] R4[2074/2400] | LR: 0.006121 | E: -42.299755 | E_var:     0.3327 | E_err:   0.009013
[2025-10-03 02:42:07] [Iter 2164/2250] R4[2076/2400] | LR: 0.006107 | E: -42.318801 | E_var:     0.3574 | E_err:   0.009342
[2025-10-03 02:42:15] [Iter 2165/2250] R4[2078/2400] | LR: 0.006094 | E: -42.311051 | E_var:     0.4745 | E_err:   0.010763
[2025-10-03 02:42:23] [Iter 2166/2250] R4[2080/2400] | LR: 0.006081 | E: -42.292872 | E_var:     0.3775 | E_err:   0.009600
[2025-10-03 02:42:31] [Iter 2167/2250] R4[2082/2400] | LR: 0.006067 | E: -42.311331 | E_var:     0.4966 | E_err:   0.011011
[2025-10-03 02:42:38] [Iter 2168/2250] R4[2084/2400] | LR: 0.006054 | E: -42.300178 | E_var:     0.5405 | E_err:   0.011487
[2025-10-03 02:42:46] [Iter 2169/2250] R4[2086/2400] | LR: 0.006041 | E: -42.312576 | E_var:     0.4424 | E_err:   0.010392
[2025-10-03 02:42:54] [Iter 2170/2250] R4[2088/2400] | LR: 0.006028 | E: -42.314835 | E_var:     0.4483 | E_err:   0.010462
[2025-10-03 02:43:02] [Iter 2171/2250] R4[2090/2400] | LR: 0.006015 | E: -42.304007 | E_var:     0.3711 | E_err:   0.009518
[2025-10-03 02:43:10] [Iter 2172/2250] R4[2092/2400] | LR: 0.006002 | E: -42.314906 | E_var:     0.4944 | E_err:   0.010987
[2025-10-03 02:43:18] [Iter 2173/2250] R4[2094/2400] | LR: 0.005989 | E: -42.312462 | E_var:     0.4504 | E_err:   0.010486
[2025-10-03 02:43:25] [Iter 2174/2250] R4[2096/2400] | LR: 0.005977 | E: -42.315913 | E_var:     0.4785 | E_err:   0.010809
[2025-10-03 02:43:33] [Iter 2175/2250] R4[2098/2400] | LR: 0.005964 | E: -42.317212 | E_var:     0.3813 | E_err:   0.009648
[2025-10-03 02:43:41] [Iter 2176/2250] R4[2100/2400] | LR: 0.005952 | E: -42.312783 | E_var:     0.5297 | E_err:   0.011372
[2025-10-03 02:43:49] [Iter 2177/2250] R4[2102/2400] | LR: 0.005939 | E: -42.302322 | E_var:     0.3961 | E_err:   0.009834
[2025-10-03 02:43:57] [Iter 2178/2250] R4[2104/2400] | LR: 0.005927 | E: -42.319477 | E_var:     0.3417 | E_err:   0.009133
[2025-10-03 02:44:04] [Iter 2179/2250] R4[2106/2400] | LR: 0.005914 | E: -42.294318 | E_var:     0.3809 | E_err:   0.009644
[2025-10-03 02:44:12] [Iter 2180/2250] R4[2108/2400] | LR: 0.005902 | E: -42.300918 | E_var:     0.3565 | E_err:   0.009329
[2025-10-03 02:44:20] [Iter 2181/2250] R4[2110/2400] | LR: 0.005890 | E: -42.320790 | E_var:     0.8604 | E_err:   0.014494
[2025-10-03 02:44:28] [Iter 2182/2250] R4[2112/2400] | LR: 0.005878 | E: -42.309516 | E_var:     0.4125 | E_err:   0.010035
[2025-10-03 02:44:36] [Iter 2183/2250] R4[2114/2400] | LR: 0.005866 | E: -42.313840 | E_var:     0.3854 | E_err:   0.009700
[2025-10-03 02:44:44] [Iter 2184/2250] R4[2116/2400] | LR: 0.005854 | E: -42.309066 | E_var:     0.3299 | E_err:   0.008975
[2025-10-03 02:44:51] [Iter 2185/2250] R4[2118/2400] | LR: 0.005842 | E: -42.313245 | E_var:     0.3241 | E_err:   0.008896
[2025-10-03 02:44:59] [Iter 2186/2250] R4[2120/2400] | LR: 0.005830 | E: -42.320717 | E_var:     0.3572 | E_err:   0.009338
[2025-10-03 02:45:07] [Iter 2187/2250] R4[2122/2400] | LR: 0.005819 | E: -42.313681 | E_var:     0.4731 | E_err:   0.010748
[2025-10-03 02:45:15] [Iter 2188/2250] R4[2124/2400] | LR: 0.005807 | E: -42.301666 | E_var:     0.4616 | E_err:   0.010616
[2025-10-03 02:45:23] [Iter 2189/2250] R4[2126/2400] | LR: 0.005795 | E: -42.297117 | E_var:     0.5127 | E_err:   0.011188
[2025-10-03 02:45:31] [Iter 2190/2250] R4[2128/2400] | LR: 0.005784 | E: -42.312795 | E_var:     0.4471 | E_err:   0.010448
[2025-10-03 02:45:38] [Iter 2191/2250] R4[2130/2400] | LR: 0.005773 | E: -42.306181 | E_var:     0.3405 | E_err:   0.009117
[2025-10-03 02:45:46] [Iter 2192/2250] R4[2132/2400] | LR: 0.005761 | E: -42.283912 | E_var:     0.4065 | E_err:   0.009962
[2025-10-03 02:45:54] [Iter 2193/2250] R4[2134/2400] | LR: 0.005750 | E: -42.313629 | E_var:     0.5172 | E_err:   0.011237
[2025-10-03 02:46:02] [Iter 2194/2250] R4[2136/2400] | LR: 0.005739 | E: -42.303598 | E_var:     0.2987 | E_err:   0.008539
[2025-10-03 02:46:10] [Iter 2195/2250] R4[2138/2400] | LR: 0.005728 | E: -42.305673 | E_var:     0.3907 | E_err:   0.009767
[2025-10-03 02:46:17] [Iter 2196/2250] R4[2140/2400] | LR: 0.005717 | E: -42.298346 | E_var:     0.4483 | E_err:   0.010462
[2025-10-03 02:46:25] [Iter 2197/2250] R4[2142/2400] | LR: 0.005706 | E: -42.316129 | E_var:     0.4099 | E_err:   0.010003
[2025-10-03 02:46:33] [Iter 2198/2250] R4[2144/2400] | LR: 0.005695 | E: -42.309661 | E_var:     0.3584 | E_err:   0.009354
[2025-10-03 02:46:41] [Iter 2199/2250] R4[2146/2400] | LR: 0.005685 | E: -42.302189 | E_var:     0.5097 | E_err:   0.011155
[2025-10-03 02:46:49] [Iter 2200/2250] R4[2148/2400] | LR: 0.005674 | E: -42.321712 | E_var:     0.4186 | E_err:   0.010109
[2025-10-03 02:46:49] ✓ Checkpoint saved: checkpoint_iter_002200.pkl
[2025-10-03 02:46:57] [Iter 2201/2250] R4[2150/2400] | LR: 0.005663 | E: -42.325541 | E_var:     0.4410 | E_err:   0.010377
[2025-10-03 02:47:04] [Iter 2202/2250] R4[2152/2400] | LR: 0.005653 | E: -42.312611 | E_var:     0.3844 | E_err:   0.009688
[2025-10-03 02:47:12] [Iter 2203/2250] R4[2154/2400] | LR: 0.005642 | E: -42.311563 | E_var:     0.3740 | E_err:   0.009556
[2025-10-03 02:47:20] [Iter 2204/2250] R4[2156/2400] | LR: 0.005632 | E: -42.311422 | E_var:     0.3974 | E_err:   0.009849
[2025-10-03 02:47:28] [Iter 2205/2250] R4[2158/2400] | LR: 0.005622 | E: -42.302141 | E_var:     0.3727 | E_err:   0.009538
[2025-10-03 02:47:36] [Iter 2206/2250] R4[2160/2400] | LR: 0.005612 | E: -42.301563 | E_var:     0.3680 | E_err:   0.009479
[2025-10-03 02:47:43] [Iter 2207/2250] R4[2162/2400] | LR: 0.005602 | E: -42.307570 | E_var:     0.3934 | E_err:   0.009801
[2025-10-03 02:47:51] [Iter 2208/2250] R4[2164/2400] | LR: 0.005592 | E: -42.300518 | E_var:     0.4239 | E_err:   0.010173
[2025-10-03 02:47:59] [Iter 2209/2250] R4[2166/2400] | LR: 0.005582 | E: -42.294936 | E_var:     0.5872 | E_err:   0.011973
[2025-10-03 02:48:07] [Iter 2210/2250] R4[2168/2400] | LR: 0.005572 | E: -42.298995 | E_var:     0.4392 | E_err:   0.010355
[2025-10-03 02:48:15] [Iter 2211/2250] R4[2170/2400] | LR: 0.005562 | E: -42.305209 | E_var:     0.3566 | E_err:   0.009331
[2025-10-03 02:48:23] [Iter 2212/2250] R4[2172/2400] | LR: 0.005553 | E: -42.306799 | E_var:     0.4086 | E_err:   0.009988
[2025-10-03 02:48:30] [Iter 2213/2250] R4[2174/2400] | LR: 0.005543 | E: -42.284197 | E_var:     0.4436 | E_err:   0.010407
[2025-10-03 02:48:38] [Iter 2214/2250] R4[2176/2400] | LR: 0.005534 | E: -42.307363 | E_var:     0.4272 | E_err:   0.010212
[2025-10-03 02:48:46] [Iter 2215/2250] R4[2178/2400] | LR: 0.005524 | E: -42.297060 | E_var:     0.4274 | E_err:   0.010215
[2025-10-03 02:48:54] [Iter 2216/2250] R4[2180/2400] | LR: 0.005515 | E: -42.298957 | E_var:     0.3734 | E_err:   0.009548
[2025-10-03 02:49:02] [Iter 2217/2250] R4[2182/2400] | LR: 0.005506 | E: -42.307814 | E_var:     0.4630 | E_err:   0.010632
[2025-10-03 02:49:09] [Iter 2218/2250] R4[2184/2400] | LR: 0.005496 | E: -42.303572 | E_var:     0.3729 | E_err:   0.009541
[2025-10-03 02:49:17] [Iter 2219/2250] R4[2186/2400] | LR: 0.005487 | E: -42.303730 | E_var:     0.3916 | E_err:   0.009778
[2025-10-03 02:49:25] [Iter 2220/2250] R4[2188/2400] | LR: 0.005478 | E: -42.308005 | E_var:     0.3928 | E_err:   0.009793
[2025-10-03 02:49:33] [Iter 2221/2250] R4[2190/2400] | LR: 0.005469 | E: -42.309301 | E_var:     0.4015 | E_err:   0.009901
[2025-10-03 02:49:41] [Iter 2222/2250] R4[2192/2400] | LR: 0.005460 | E: -42.297311 | E_var:     0.5721 | E_err:   0.011819
[2025-10-03 02:49:49] [Iter 2223/2250] R4[2194/2400] | LR: 0.005452 | E: -42.318860 | E_var:     0.4645 | E_err:   0.010650
[2025-10-03 02:49:56] [Iter 2224/2250] R4[2196/2400] | LR: 0.005443 | E: -42.306644 | E_var:     0.4370 | E_err:   0.010329
[2025-10-03 02:50:04] [Iter 2225/2250] R4[2198/2400] | LR: 0.005434 | E: -42.318532 | E_var:     0.3780 | E_err:   0.009607
[2025-10-03 02:50:12] [Iter 2226/2250] R4[2200/2400] | LR: 0.005426 | E: -42.317648 | E_var:     0.3642 | E_err:   0.009430
[2025-10-03 02:50:20] [Iter 2227/2250] R4[2202/2400] | LR: 0.005417 | E: -42.314329 | E_var:     0.4147 | E_err:   0.010062
[2025-10-03 02:50:28] [Iter 2228/2250] R4[2204/2400] | LR: 0.005409 | E: -42.300410 | E_var:     0.4038 | E_err:   0.009929
[2025-10-03 02:50:35] [Iter 2229/2250] R4[2206/2400] | LR: 0.005401 | E: -42.300506 | E_var:     0.4548 | E_err:   0.010537
[2025-10-03 02:50:43] [Iter 2230/2250] R4[2208/2400] | LR: 0.005393 | E: -42.310220 | E_var:     0.4733 | E_err:   0.010749
[2025-10-03 02:50:51] [Iter 2231/2250] R4[2210/2400] | LR: 0.005385 | E: -42.315720 | E_var:     0.5905 | E_err:   0.012007
[2025-10-03 02:50:59] [Iter 2232/2250] R4[2212/2400] | LR: 0.005377 | E: -42.305312 | E_var:     0.4350 | E_err:   0.010305
[2025-10-03 02:51:07] [Iter 2233/2250] R4[2214/2400] | LR: 0.005369 | E: -42.316576 | E_var:     0.6526 | E_err:   0.012623
[2025-10-03 02:51:15] [Iter 2234/2250] R4[2216/2400] | LR: 0.005361 | E: -42.316050 | E_var:     0.4065 | E_err:   0.009962
[2025-10-03 02:51:22] [Iter 2235/2250] R4[2218/2400] | LR: 0.005353 | E: -42.290727 | E_var:     0.4096 | E_err:   0.010000
[2025-10-03 02:51:30] [Iter 2236/2250] R4[2220/2400] | LR: 0.005345 | E: -42.310494 | E_var:     0.3443 | E_err:   0.009168
[2025-10-03 02:51:38] [Iter 2237/2250] R4[2222/2400] | LR: 0.005338 | E: -42.317916 | E_var:     0.4741 | E_err:   0.010758
[2025-10-03 02:51:46] [Iter 2238/2250] R4[2224/2400] | LR: 0.005330 | E: -42.310465 | E_var:     0.3995 | E_err:   0.009876
[2025-10-03 02:51:54] [Iter 2239/2250] R4[2226/2400] | LR: 0.005323 | E: -42.284760 | E_var:     0.5950 | E_err:   0.012052
[2025-10-03 02:52:01] [Iter 2240/2250] R4[2228/2400] | LR: 0.005315 | E: -42.309907 | E_var:     0.3893 | E_err:   0.009749
[2025-10-03 02:52:09] [Iter 2241/2250] R4[2230/2400] | LR: 0.005308 | E: -42.325192 | E_var:     0.3925 | E_err:   0.009789
[2025-10-03 02:52:17] [Iter 2242/2250] R4[2232/2400] | LR: 0.005301 | E: -42.309226 | E_var:     0.4029 | E_err:   0.009918
[2025-10-03 02:52:25] [Iter 2243/2250] R4[2234/2400] | LR: 0.005294 | E: -42.288227 | E_var:     0.3872 | E_err:   0.009723
[2025-10-03 02:52:33] [Iter 2244/2250] R4[2236/2400] | LR: 0.005287 | E: -42.321969 | E_var:     0.4989 | E_err:   0.011037
[2025-10-03 02:52:41] [Iter 2245/2250] R4[2238/2400] | LR: 0.005280 | E: -42.317369 | E_var:     0.4089 | E_err:   0.009992
[2025-10-03 02:52:48] [Iter 2246/2250] R4[2240/2400] | LR: 0.005273 | E: -42.304843 | E_var:     0.4860 | E_err:   0.010893
[2025-10-03 02:52:56] [Iter 2247/2250] R4[2242/2400] | LR: 0.005266 | E: -42.323851 | E_var:     0.4853 | E_err:   0.010885
[2025-10-03 02:53:04] [Iter 2248/2250] R4[2244/2400] | LR: 0.005260 | E: -42.300762 | E_var:     0.3785 | E_err:   0.009613
[2025-10-03 02:53:12] [Iter 2249/2250] R4[2246/2400] | LR: 0.005253 | E: -42.307879 | E_var:     0.3648 | E_err:   0.009437
[2025-10-03 02:53:20] [Iter 2250/2250] R4[2248/2400] | LR: 0.005247 | E: -42.304412 | E_var:     0.4094 | E_err:   0.009998
[2025-10-03 02:53:20] ================================================================================
[2025-10-03 02:53:20] ✅ Training completed successfully
[2025-10-03 02:53:20] Total restarts: 4
[2025-10-03 02:53:22] Final Energy: -42.30441161 ± 0.00999789
[2025-10-03 02:53:22] Final Variance: 0.409427
[2025-10-03 02:53:22] ================================================================================
[2025-10-03 02:53:22] ============================================================
[2025-10-03 02:53:22] Training completed | Runtime: 17630.3s
[2025-10-03 02:53:25] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-03 02:53:25] ============================================================
