[2025-10-06 00:15:20] 使用checkpoint文件: results/L=5/J2=1.00/J1=0.76/model_L8F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 00:15:41] ✓ 从checkpoint加载参数: final
[2025-10-06 00:15:41]   - 能量: -42.368225+0.000375j ± 0.007609
[2025-10-06 00:15:41] !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
[2025-10-06 00:15:41] 处理 L=5, J2=1.00, J1=0.76 时出错: 
The structure of the parameters does not match the expected structure.

Expected structure: PyTreeDef({'dense_symm': {'bias': *, 'kernel': *}, 'equivariant_layers_0': {'bias': *, 'kernel': *}, 'equivariant_layers_1': {'bias': *, 'kernel': *}, 'equivariant_layers_2': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_0': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_1': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_2': {'bias': *, 'kernel': *}})
Structure of the parameters: PyTreeDef({'dense_symm': {'bias': *, 'kernel': *}, 'equivariant_layers_0': {'bias': *, 'kernel': *}, 'equivariant_layers_1': {'bias': *, 'kernel': *}, 'equivariant_layers_2': {'bias': *, 'kernel': *}, 'equivariant_layers_3': {'bias': *, 'kernel': *}, 'equivariant_layers_4': {'bias': *, 'kernel': *}, 'equivariant_layers_5': {'bias': *, 'kernel': *}, 'equivariant_layers_6': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_0': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_1': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_2': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_3': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_4': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_5': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_6': {'bias': *, 'kernel': *}})

This error is because you attempted to modify the ``parameters`` or ``variables`` attribute of a
variational state with a structure that does not match the previous structure.

To fix this error, you should ensure that the structure of the parameters you are trying to assign
matches the structure of the parameters that were already present in the variational state.

If you believe this error was thrown in error, or it prevents you from doing something, please open an issue.


-------------------------------------------------------
For more detailed informations, visit the following link:
	 https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.ParameterMismatchError.html
or the list of all common errors at
	 https://netket.readthedocs.io/en/latest/api/errors.html
-------------------------------------------------------

[2025-10-06 00:15:41] Traceback (most recent call last):
  File "/home/<USER>/Repositories/Shastry-Sutherland_Extra/scripts/analyze.py", line 131, in main
    vqs, lattice, _, _ = load_quantum_state_from_checkpoint(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Repositories/Shastry-Sutherland_Extra/scripts/analyze.py", line 83, in load_quantum_state_from_checkpoint
    vqs.parameters = parameters
    ^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/vqs/base.py", line 96, in parameters
    raise nkerrors.ParameterMismatchError(
netket.errors.ParameterMismatchError: 
The structure of the parameters does not match the expected structure.

Expected structure: PyTreeDef({'dense_symm': {'bias': *, 'kernel': *}, 'equivariant_layers_0': {'bias': *, 'kernel': *}, 'equivariant_layers_1': {'bias': *, 'kernel': *}, 'equivariant_layers_2': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_0': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_1': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_2': {'bias': *, 'kernel': *}})
Structure of the parameters: PyTreeDef({'dense_symm': {'bias': *, 'kernel': *}, 'equivariant_layers_0': {'bias': *, 'kernel': *}, 'equivariant_layers_1': {'bias': *, 'kernel': *}, 'equivariant_layers_2': {'bias': *, 'kernel': *}, 'equivariant_layers_3': {'bias': *, 'kernel': *}, 'equivariant_layers_4': {'bias': *, 'kernel': *}, 'equivariant_layers_5': {'bias': *, 'kernel': *}, 'equivariant_layers_6': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_0': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_1': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_2': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_3': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_4': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_5': {'bias': *, 'kernel': *}, 'equivariant_layers_flip_6': {'bias': *, 'kernel': *}})

This error is because you attempted to modify the ``parameters`` or ``variables`` attribute of a
variational state with a structure that does not match the previous structure.

To fix this error, you should ensure that the structure of the parameters you are trying to assign
matches the structure of the parameters that were already present in the variational state.

If you believe this error was thrown in error, or it prevents you from doing something, please open an issue.


-------------------------------------------------------
For more detailed informations, visit the following link:
	 https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.ParameterMismatchError.html
or the list of all common errors at
	 https://netket.readthedocs.io/en/latest/api/errors.html
-------------------------------------------------------


[2025-10-06 00:15:41] !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
