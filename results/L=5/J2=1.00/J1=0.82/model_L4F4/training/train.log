[2025-10-06 13:42:47] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.81/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 13:42:47]   - 迭代次数: final
[2025-10-06 13:42:48]   - 能量: -45.513471+0.000746j ± 0.007145, Var: 0.209091
[2025-10-06 13:42:48]   - 时间戳: 2025-10-06T13:42:30.186044+08:00
[2025-10-06 13:43:07] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 13:43:07] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 13:43:07] ======================================================================================================
[2025-10-06 13:43:07] GCNN for Shastry-Sutherland Model
[2025-10-06 13:43:07] ======================================================================================================
[2025-10-06 13:43:07] System parameters:
[2025-10-06 13:43:07]   - System size: L=5, N=100
[2025-10-06 13:43:07]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-10-06 13:43:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 13:43:07] Model parameters:
[2025-10-06 13:43:07]   - Number of layers = 4
[2025-10-06 13:43:07]   - Number of features = 4
[2025-10-06 13:43:07]   - Total parameters = 19628
[2025-10-06 13:43:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 13:43:07] Training parameters:
[2025-10-06 13:43:07]   - Total iterations: 1050
[2025-10-06 13:43:07]   - Annealing cycles: 3
[2025-10-06 13:43:07]   - Initial period: 150
[2025-10-06 13:43:07]   - Period multiplier: 2.0
[2025-10-06 13:43:07]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 13:43:07]   - Samples: 4096
[2025-10-06 13:43:07]   - Discarded samples: 0
[2025-10-06 13:43:07]   - Chunk size: 4096
[2025-10-06 13:43:07]   - Diagonal shift: 0.15
[2025-10-06 13:43:07]   - Gradient clipping: 1.0
[2025-10-06 13:43:07]   - Checkpoint enabled: interval=100
[2025-10-06 13:43:07]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.82/model_L4F4/training/checkpoints
[2025-10-06 13:43:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 13:43:07] Device status:
[2025-10-06 13:43:07]   - Devices model: NVIDIA H200 NVL
[2025-10-06 13:43:07]   - Number of devices: 1
[2025-10-06 13:43:07]   - Sharding: True
[2025-10-06 13:43:07] ======================================================================================================
[2025-10-06 13:43:38] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -44.425668 | E_var:    99.8506 | E_err:   0.156133
[2025-10-06 13:43:59] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -46.023620 | E_var:     8.2208 | E_err:   0.044800
[2025-10-06 13:44:04] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -46.165528 | E_var:     0.3880 | E_err:   0.009733
[2025-10-06 13:44:09] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -46.152675 | E_var:     0.3284 | E_err:   0.008955
[2025-10-06 13:44:14] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -46.156723 | E_var:     0.2461 | E_err:   0.007751
[2025-10-06 13:44:19] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -46.176678 | E_var:     0.3466 | E_err:   0.009199
[2025-10-06 13:44:24] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -46.153078 | E_var:     0.2350 | E_err:   0.007575
[2025-10-06 13:44:29] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -46.172072 | E_var:     0.2421 | E_err:   0.007688
[2025-10-06 13:44:34] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -46.167439 | E_var:     0.2502 | E_err:   0.007816
[2025-10-06 13:44:40] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -46.162015 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 13:44:45] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -46.186841 | E_var:     0.4806 | E_err:   0.010833
[2025-10-06 13:44:50] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -46.161464 | E_var:     0.2949 | E_err:   0.008485
[2025-10-06 13:44:55] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -46.160424 | E_var:     0.1963 | E_err:   0.006922
[2025-10-06 13:45:00] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -46.156609 | E_var:     0.3553 | E_err:   0.009314
[2025-10-06 13:45:05] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -46.175145 | E_var:     0.2734 | E_err:   0.008170
[2025-10-06 13:45:10] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -46.171893 | E_var:     0.2032 | E_err:   0.007044
[2025-10-06 13:45:15] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -46.180060 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 13:45:21] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -46.165271 | E_var:     0.2095 | E_err:   0.007152
[2025-10-06 13:45:26] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -46.162052 | E_var:     0.2203 | E_err:   0.007333
[2025-10-06 13:45:31] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -46.183432 | E_var:     0.1954 | E_err:   0.006906
[2025-10-06 13:45:36] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -46.172749 | E_var:     0.2824 | E_err:   0.008303
[2025-10-06 13:45:41] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -46.153831 | E_var:     0.2703 | E_err:   0.008124
[2025-10-06 13:45:46] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -46.160625 | E_var:     0.2499 | E_err:   0.007812
[2025-10-06 13:45:51] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -46.168879 | E_var:     0.1600 | E_err:   0.006251
[2025-10-06 13:45:56] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -46.163290 | E_var:     0.1797 | E_err:   0.006623
[2025-10-06 13:46:02] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -46.165395 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 13:46:07] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -46.173325 | E_var:     0.2447 | E_err:   0.007730
[2025-10-06 13:46:12] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -46.154916 | E_var:     0.2690 | E_err:   0.008105
[2025-10-06 13:46:17] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -46.174280 | E_var:     0.1986 | E_err:   0.006964
[2025-10-06 13:46:22] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -46.176271 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 13:46:27] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -46.156880 | E_var:     0.1778 | E_err:   0.006589
[2025-10-06 13:46:32] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -46.166624 | E_var:     0.2037 | E_err:   0.007052
[2025-10-06 13:46:38] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -46.170107 | E_var:     0.2171 | E_err:   0.007280
[2025-10-06 13:46:43] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -46.166042 | E_var:     0.2202 | E_err:   0.007332
[2025-10-06 13:46:48] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -46.164982 | E_var:     0.2696 | E_err:   0.008112
[2025-10-06 13:46:53] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -46.167046 | E_var:     0.1652 | E_err:   0.006350
[2025-10-06 13:46:58] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -46.176415 | E_var:     0.1689 | E_err:   0.006422
[2025-10-06 13:47:03] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -46.166361 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 13:47:08] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -46.168029 | E_var:     0.1777 | E_err:   0.006587
[2025-10-06 13:47:13] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -46.169078 | E_var:     0.1890 | E_err:   0.006793
[2025-10-06 13:47:19] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -46.158395 | E_var:     0.1971 | E_err:   0.006936
[2025-10-06 13:47:24] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -46.169610 | E_var:     0.3428 | E_err:   0.009148
[2025-10-06 13:47:29] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -46.171719 | E_var:     0.2099 | E_err:   0.007158
[2025-10-06 13:47:34] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -46.165486 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 13:47:39] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -46.162166 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 13:47:44] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -46.158337 | E_var:     0.2496 | E_err:   0.007805
[2025-10-06 13:47:49] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -46.169216 | E_var:     0.2354 | E_err:   0.007581
[2025-10-06 13:47:54] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -46.166499 | E_var:     0.1905 | E_err:   0.006821
[2025-10-06 13:48:00] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -46.163562 | E_var:     0.2032 | E_err:   0.007044
[2025-10-06 13:48:05] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -46.170979 | E_var:     0.1955 | E_err:   0.006908
[2025-10-06 13:48:10] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -46.172214 | E_var:     0.1724 | E_err:   0.006488
[2025-10-06 13:48:15] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -46.173382 | E_var:     0.1740 | E_err:   0.006518
[2025-10-06 13:48:20] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -46.158977 | E_var:     0.1975 | E_err:   0.006943
[2025-10-06 13:48:25] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -46.153725 | E_var:     0.2470 | E_err:   0.007765
[2025-10-06 13:48:30] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -46.160653 | E_var:     0.1819 | E_err:   0.006663
[2025-10-06 13:48:36] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -46.170188 | E_var:     0.2516 | E_err:   0.007837
[2025-10-06 13:48:41] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -46.181901 | E_var:     0.2259 | E_err:   0.007426
[2025-10-06 13:48:46] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -46.165334 | E_var:     0.2517 | E_err:   0.007839
[2025-10-06 13:48:51] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -46.165812 | E_var:     0.2092 | E_err:   0.007146
[2025-10-06 13:48:56] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -46.158066 | E_var:     0.2379 | E_err:   0.007621
[2025-10-06 13:49:01] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -46.173921 | E_var:     0.2211 | E_err:   0.007347
[2025-10-06 13:49:06] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -46.169934 | E_var:     0.2118 | E_err:   0.007192
[2025-10-06 13:49:11] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -46.171214 | E_var:     0.2098 | E_err:   0.007158
[2025-10-06 13:49:17] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -46.164483 | E_var:     0.2096 | E_err:   0.007153
[2025-10-06 13:49:22] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -46.168232 | E_var:     0.2112 | E_err:   0.007180
[2025-10-06 13:49:27] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -46.165606 | E_var:     0.2440 | E_err:   0.007717
[2025-10-06 13:49:32] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -46.163392 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 13:49:37] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -46.159904 | E_var:     0.2343 | E_err:   0.007563
[2025-10-06 13:49:42] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -46.160027 | E_var:     0.1594 | E_err:   0.006238
[2025-10-06 13:49:47] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -46.169398 | E_var:     0.2862 | E_err:   0.008360
[2025-10-06 13:49:52] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -46.174545 | E_var:     0.1808 | E_err:   0.006644
[2025-10-06 13:49:57] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -46.159321 | E_var:     0.3661 | E_err:   0.009455
[2025-10-06 13:50:03] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -46.168766 | E_var:     0.1532 | E_err:   0.006116
[2025-10-06 13:50:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -46.185455 | E_var:     0.2888 | E_err:   0.008397
[2025-10-06 13:50:13] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -46.162555 | E_var:     0.2373 | E_err:   0.007612
[2025-10-06 13:50:18] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -46.159327 | E_var:     0.2079 | E_err:   0.007124
[2025-10-06 13:50:23] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -46.165273 | E_var:     0.1687 | E_err:   0.006417
[2025-10-06 13:50:28] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -46.171302 | E_var:     0.1910 | E_err:   0.006829
[2025-10-06 13:50:33] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -46.156917 | E_var:     0.2309 | E_err:   0.007508
[2025-10-06 13:50:39] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -46.154933 | E_var:     0.1854 | E_err:   0.006728
[2025-10-06 13:50:44] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -46.162336 | E_var:     0.1959 | E_err:   0.006916
[2025-10-06 13:50:49] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -46.158543 | E_var:     0.1923 | E_err:   0.006852
[2025-10-06 13:50:54] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -46.175047 | E_var:     0.1819 | E_err:   0.006664
[2025-10-06 13:50:59] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -46.169024 | E_var:     0.2227 | E_err:   0.007374
