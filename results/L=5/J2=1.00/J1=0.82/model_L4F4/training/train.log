[2025-10-06 13:42:47] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.81/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 13:42:47]   - 迭代次数: final
[2025-10-06 13:42:48]   - 能量: -45.513471+0.000746j ± 0.007145, Var: 0.209091
[2025-10-06 13:42:48]   - 时间戳: 2025-10-06T13:42:30.186044+08:00
[2025-10-06 13:43:07] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 13:43:07] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 13:43:07] ======================================================================================================
[2025-10-06 13:43:07] GCNN for Shastry-Sutherland Model
[2025-10-06 13:43:07] ======================================================================================================
[2025-10-06 13:43:07] System parameters:
[2025-10-06 13:43:07]   - System size: L=5, N=100
[2025-10-06 13:43:07]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-10-06 13:43:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 13:43:07] Model parameters:
[2025-10-06 13:43:07]   - Number of layers = 4
[2025-10-06 13:43:07]   - Number of features = 4
[2025-10-06 13:43:07]   - Total parameters = 19628
[2025-10-06 13:43:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 13:43:07] Training parameters:
[2025-10-06 13:43:07]   - Total iterations: 1050
[2025-10-06 13:43:07]   - Annealing cycles: 3
[2025-10-06 13:43:07]   - Initial period: 150
[2025-10-06 13:43:07]   - Period multiplier: 2.0
[2025-10-06 13:43:07]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 13:43:07]   - Samples: 4096
[2025-10-06 13:43:07]   - Discarded samples: 0
[2025-10-06 13:43:07]   - Chunk size: 4096
[2025-10-06 13:43:07]   - Diagonal shift: 0.15
[2025-10-06 13:43:07]   - Gradient clipping: 1.0
[2025-10-06 13:43:07]   - Checkpoint enabled: interval=100
[2025-10-06 13:43:07]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.82/model_L4F4/training/checkpoints
[2025-10-06 13:43:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 13:43:07] Device status:
[2025-10-06 13:43:07]   - Devices model: NVIDIA H200 NVL
[2025-10-06 13:43:07]   - Number of devices: 1
[2025-10-06 13:43:07]   - Sharding: True
[2025-10-06 13:43:07] ======================================================================================================
[2025-10-06 13:43:38] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -44.425668 | E_var:    99.8506 | E_err:   0.156133
[2025-10-06 13:43:59] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -46.023620 | E_var:     8.2208 | E_err:   0.044800
[2025-10-06 13:44:04] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -46.165528 | E_var:     0.3880 | E_err:   0.009733
[2025-10-06 13:44:09] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -46.152675 | E_var:     0.3284 | E_err:   0.008955
[2025-10-06 13:44:14] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -46.156723 | E_var:     0.2461 | E_err:   0.007751
[2025-10-06 13:44:19] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -46.176678 | E_var:     0.3466 | E_err:   0.009199
[2025-10-06 13:44:24] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -46.153078 | E_var:     0.2350 | E_err:   0.007575
[2025-10-06 13:44:29] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -46.172072 | E_var:     0.2421 | E_err:   0.007688
[2025-10-06 13:44:34] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -46.167439 | E_var:     0.2502 | E_err:   0.007816
[2025-10-06 13:44:40] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -46.162015 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 13:44:45] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -46.186841 | E_var:     0.4806 | E_err:   0.010833
[2025-10-06 13:44:50] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -46.161464 | E_var:     0.2949 | E_err:   0.008485
[2025-10-06 13:44:55] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -46.160424 | E_var:     0.1963 | E_err:   0.006922
[2025-10-06 13:45:00] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -46.156609 | E_var:     0.3553 | E_err:   0.009314
[2025-10-06 13:45:05] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -46.175145 | E_var:     0.2734 | E_err:   0.008170
[2025-10-06 13:45:10] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -46.171893 | E_var:     0.2032 | E_err:   0.007044
[2025-10-06 13:45:15] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -46.180060 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 13:45:21] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -46.165271 | E_var:     0.2095 | E_err:   0.007152
[2025-10-06 13:45:26] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -46.162052 | E_var:     0.2203 | E_err:   0.007333
[2025-10-06 13:45:31] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -46.183432 | E_var:     0.1954 | E_err:   0.006906
[2025-10-06 13:45:36] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -46.172749 | E_var:     0.2824 | E_err:   0.008303
[2025-10-06 13:45:41] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -46.153831 | E_var:     0.2703 | E_err:   0.008124
[2025-10-06 13:45:46] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -46.160625 | E_var:     0.2499 | E_err:   0.007812
[2025-10-06 13:45:51] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -46.168879 | E_var:     0.1600 | E_err:   0.006251
[2025-10-06 13:45:56] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -46.163290 | E_var:     0.1797 | E_err:   0.006623
[2025-10-06 13:46:02] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -46.165395 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 13:46:07] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -46.173325 | E_var:     0.2447 | E_err:   0.007730
[2025-10-06 13:46:12] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -46.154916 | E_var:     0.2690 | E_err:   0.008105
[2025-10-06 13:46:17] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -46.174280 | E_var:     0.1986 | E_err:   0.006964
[2025-10-06 13:46:22] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -46.176271 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 13:46:27] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -46.156880 | E_var:     0.1778 | E_err:   0.006589
[2025-10-06 13:46:32] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -46.166624 | E_var:     0.2037 | E_err:   0.007052
[2025-10-06 13:46:38] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -46.170107 | E_var:     0.2171 | E_err:   0.007280
[2025-10-06 13:46:43] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -46.166042 | E_var:     0.2202 | E_err:   0.007332
[2025-10-06 13:46:48] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -46.164982 | E_var:     0.2696 | E_err:   0.008112
[2025-10-06 13:46:53] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -46.167046 | E_var:     0.1652 | E_err:   0.006350
[2025-10-06 13:46:58] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -46.176415 | E_var:     0.1689 | E_err:   0.006422
[2025-10-06 13:47:03] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -46.166361 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 13:47:08] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -46.168029 | E_var:     0.1777 | E_err:   0.006587
[2025-10-06 13:47:13] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -46.169078 | E_var:     0.1890 | E_err:   0.006793
[2025-10-06 13:47:19] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -46.158395 | E_var:     0.1971 | E_err:   0.006936
[2025-10-06 13:47:24] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -46.169610 | E_var:     0.3428 | E_err:   0.009148
[2025-10-06 13:47:29] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -46.171719 | E_var:     0.2099 | E_err:   0.007158
[2025-10-06 13:47:34] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -46.165486 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 13:47:39] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -46.162166 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 13:47:44] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -46.158337 | E_var:     0.2496 | E_err:   0.007805
[2025-10-06 13:47:49] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -46.169216 | E_var:     0.2354 | E_err:   0.007581
[2025-10-06 13:47:54] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -46.166499 | E_var:     0.1905 | E_err:   0.006821
[2025-10-06 13:48:00] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -46.163562 | E_var:     0.2032 | E_err:   0.007044
[2025-10-06 13:48:05] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -46.170979 | E_var:     0.1955 | E_err:   0.006908
[2025-10-06 13:48:10] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -46.172214 | E_var:     0.1724 | E_err:   0.006488
[2025-10-06 13:48:15] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -46.173382 | E_var:     0.1740 | E_err:   0.006518
[2025-10-06 13:48:20] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -46.158977 | E_var:     0.1975 | E_err:   0.006943
[2025-10-06 13:48:25] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -46.153725 | E_var:     0.2470 | E_err:   0.007765
[2025-10-06 13:48:30] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -46.160653 | E_var:     0.1819 | E_err:   0.006663
[2025-10-06 13:48:36] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -46.170188 | E_var:     0.2516 | E_err:   0.007837
[2025-10-06 13:48:41] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -46.181901 | E_var:     0.2259 | E_err:   0.007426
[2025-10-06 13:48:46] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -46.165334 | E_var:     0.2517 | E_err:   0.007839
[2025-10-06 13:48:51] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -46.165812 | E_var:     0.2092 | E_err:   0.007146
[2025-10-06 13:48:56] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -46.158066 | E_var:     0.2379 | E_err:   0.007621
[2025-10-06 13:49:01] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -46.173921 | E_var:     0.2211 | E_err:   0.007347
[2025-10-06 13:49:06] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -46.169934 | E_var:     0.2118 | E_err:   0.007192
[2025-10-06 13:49:11] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -46.171214 | E_var:     0.2098 | E_err:   0.007158
[2025-10-06 13:49:17] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -46.164483 | E_var:     0.2096 | E_err:   0.007153
[2025-10-06 13:49:22] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -46.168232 | E_var:     0.2112 | E_err:   0.007180
[2025-10-06 13:49:27] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -46.165606 | E_var:     0.2440 | E_err:   0.007717
[2025-10-06 13:49:32] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -46.163392 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 13:49:37] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -46.159904 | E_var:     0.2343 | E_err:   0.007563
[2025-10-06 13:49:42] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -46.160027 | E_var:     0.1594 | E_err:   0.006238
[2025-10-06 13:49:47] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -46.169398 | E_var:     0.2862 | E_err:   0.008360
[2025-10-06 13:49:52] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -46.174545 | E_var:     0.1808 | E_err:   0.006644
[2025-10-06 13:49:57] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -46.159321 | E_var:     0.3661 | E_err:   0.009455
[2025-10-06 13:50:03] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -46.168766 | E_var:     0.1532 | E_err:   0.006116
[2025-10-06 13:50:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -46.185455 | E_var:     0.2888 | E_err:   0.008397
[2025-10-06 13:50:13] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -46.162555 | E_var:     0.2373 | E_err:   0.007612
[2025-10-06 13:50:18] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -46.159327 | E_var:     0.2079 | E_err:   0.007124
[2025-10-06 13:50:23] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -46.165273 | E_var:     0.1687 | E_err:   0.006417
[2025-10-06 13:50:28] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -46.171302 | E_var:     0.1910 | E_err:   0.006829
[2025-10-06 13:50:33] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -46.156917 | E_var:     0.2309 | E_err:   0.007508
[2025-10-06 13:50:39] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -46.154933 | E_var:     0.1854 | E_err:   0.006728
[2025-10-06 13:50:44] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -46.162336 | E_var:     0.1959 | E_err:   0.006916
[2025-10-06 13:50:49] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -46.158543 | E_var:     0.1923 | E_err:   0.006852
[2025-10-06 13:50:54] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -46.175047 | E_var:     0.1819 | E_err:   0.006664
[2025-10-06 13:50:59] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -46.169024 | E_var:     0.2227 | E_err:   0.007374
[2025-10-06 13:51:04] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -46.161896 | E_var:     0.2058 | E_err:   0.007088
[2025-10-06 13:51:09] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -46.162639 | E_var:     0.1647 | E_err:   0.006342
[2025-10-06 13:51:14] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -46.175874 | E_var:     0.2068 | E_err:   0.007106
[2025-10-06 13:51:20] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -46.165302 | E_var:     0.1933 | E_err:   0.006871
[2025-10-06 13:51:25] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -46.171992 | E_var:     0.2308 | E_err:   0.007506
[2025-10-06 13:51:30] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -46.174778 | E_var:     0.1968 | E_err:   0.006931
[2025-10-06 13:51:35] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -46.167460 | E_var:     0.1993 | E_err:   0.006975
[2025-10-06 13:51:40] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -46.160860 | E_var:     0.1741 | E_err:   0.006519
[2025-10-06 13:51:45] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -46.163238 | E_var:     0.2458 | E_err:   0.007747
[2025-10-06 13:51:50] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -46.175820 | E_var:     0.1793 | E_err:   0.006615
[2025-10-06 13:51:55] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -46.167720 | E_var:     0.1959 | E_err:   0.006915
[2025-10-06 13:52:01] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -46.160304 | E_var:     0.1744 | E_err:   0.006525
[2025-10-06 13:52:06] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -46.165596 | E_var:     0.1667 | E_err:   0.006379
[2025-10-06 13:52:11] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -46.165252 | E_var:     0.2042 | E_err:   0.007060
[2025-10-06 13:52:16] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -46.179664 | E_var:     0.2952 | E_err:   0.008490
[2025-10-06 13:52:21] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -46.175007 | E_var:     0.2361 | E_err:   0.007593
[2025-10-06 13:52:21] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 13:52:26] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -46.164670 | E_var:     0.2346 | E_err:   0.007568
[2025-10-06 13:52:31] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -46.176203 | E_var:     0.2318 | E_err:   0.007523
[2025-10-06 13:52:37] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -46.158953 | E_var:     0.2585 | E_err:   0.007944
[2025-10-06 13:52:42] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -46.170241 | E_var:     0.3435 | E_err:   0.009158
[2025-10-06 13:52:47] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -46.157638 | E_var:     0.2887 | E_err:   0.008395
[2025-10-06 13:52:52] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -46.176976 | E_var:     0.1648 | E_err:   0.006342
[2025-10-06 13:52:57] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -46.156512 | E_var:     0.1970 | E_err:   0.006934
[2025-10-06 13:53:02] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -46.170024 | E_var:     0.2019 | E_err:   0.007020
[2025-10-06 13:53:07] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -46.150730 | E_var:     0.2011 | E_err:   0.007006
[2025-10-06 13:53:12] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -46.164544 | E_var:     0.3783 | E_err:   0.009610
[2025-10-06 13:53:18] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -46.173029 | E_var:     0.2486 | E_err:   0.007790
[2025-10-06 13:53:23] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -46.177030 | E_var:     0.4026 | E_err:   0.009914
[2025-10-06 13:53:28] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -46.161245 | E_var:     0.1730 | E_err:   0.006499
[2025-10-06 13:53:33] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -46.164479 | E_var:     0.1836 | E_err:   0.006695
[2025-10-06 13:53:38] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -46.157722 | E_var:     0.2450 | E_err:   0.007734
[2025-10-06 13:53:43] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -46.156807 | E_var:     0.1659 | E_err:   0.006364
[2025-10-06 13:53:48] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -46.160022 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 13:53:53] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -46.169118 | E_var:     0.4309 | E_err:   0.010256
[2025-10-06 13:53:59] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -46.172214 | E_var:     0.2233 | E_err:   0.007384
[2025-10-06 13:54:04] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -46.166060 | E_var:     0.2665 | E_err:   0.008066
[2025-10-06 13:54:09] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -46.176794 | E_var:     0.1782 | E_err:   0.006596
[2025-10-06 13:54:14] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -46.153324 | E_var:     0.1963 | E_err:   0.006923
[2025-10-06 13:54:19] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -46.149755 | E_var:     0.4737 | E_err:   0.010753
[2025-10-06 13:54:24] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -46.169020 | E_var:     0.1879 | E_err:   0.006772
[2025-10-06 13:54:29] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -46.174205 | E_var:     0.2270 | E_err:   0.007445
[2025-10-06 13:54:34] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -46.161299 | E_var:     0.2106 | E_err:   0.007171
[2025-10-06 13:54:40] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -46.163172 | E_var:     0.2162 | E_err:   0.007265
[2025-10-06 13:54:45] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -46.175581 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 13:54:50] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -46.176455 | E_var:     0.2376 | E_err:   0.007617
[2025-10-06 13:54:55] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -46.172865 | E_var:     0.1744 | E_err:   0.006525
[2025-10-06 13:55:00] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -46.171143 | E_var:     0.1959 | E_err:   0.006915
[2025-10-06 13:55:05] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -46.169800 | E_var:     0.1845 | E_err:   0.006712
[2025-10-06 13:55:10] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -46.173317 | E_var:     0.2007 | E_err:   0.006999
[2025-10-06 13:55:15] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -46.163551 | E_var:     0.1567 | E_err:   0.006186
[2025-10-06 13:55:21] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -46.164775 | E_var:     0.1984 | E_err:   0.006960
[2025-10-06 13:55:26] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -46.170247 | E_var:     0.2363 | E_err:   0.007595
[2025-10-06 13:55:31] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -46.165242 | E_var:     0.3004 | E_err:   0.008564
[2025-10-06 13:55:36] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -46.155184 | E_var:     0.2517 | E_err:   0.007840
[2025-10-06 13:55:41] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -46.172310 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 13:55:46] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -46.160656 | E_var:     0.1904 | E_err:   0.006818
[2025-10-06 13:55:51] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -46.158976 | E_var:     0.2276 | E_err:   0.007454
[2025-10-06 13:55:56] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -46.164650 | E_var:     0.2078 | E_err:   0.007122
[2025-10-06 13:56:01] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -46.166566 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 13:56:07] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -46.166024 | E_var:     0.2115 | E_err:   0.007186
[2025-10-06 13:56:12] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -46.180387 | E_var:     0.2681 | E_err:   0.008091
[2025-10-06 13:56:17] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -46.176732 | E_var:     0.2290 | E_err:   0.007477
[2025-10-06 13:56:22] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -46.163306 | E_var:     0.3279 | E_err:   0.008947
[2025-10-06 13:56:27] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -46.171246 | E_var:     0.1870 | E_err:   0.006756
[2025-10-06 13:56:32] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -46.174382 | E_var:     0.1953 | E_err:   0.006905
[2025-10-06 13:56:37] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -46.166781 | E_var:     0.1978 | E_err:   0.006949
[2025-10-06 13:56:37] 🔄 RESTART #1 | Period: 300
[2025-10-06 13:56:42] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -46.160451 | E_var:     0.1826 | E_err:   0.006677
[2025-10-06 13:56:48] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -46.171827 | E_var:     0.1986 | E_err:   0.006964
[2025-10-06 13:56:53] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -46.158037 | E_var:     0.2105 | E_err:   0.007168
[2025-10-06 13:56:58] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -46.164692 | E_var:     0.2062 | E_err:   0.007095
[2025-10-06 13:57:03] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -46.164387 | E_var:     0.1924 | E_err:   0.006854
[2025-10-06 13:57:08] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -46.163698 | E_var:     0.1960 | E_err:   0.006918
[2025-10-06 13:57:13] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -46.181065 | E_var:     0.2769 | E_err:   0.008222
[2025-10-06 13:57:18] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -46.176171 | E_var:     0.2224 | E_err:   0.007368
[2025-10-06 13:57:23] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -46.171904 | E_var:     0.1979 | E_err:   0.006950
[2025-10-06 13:57:29] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -46.155250 | E_var:     0.1742 | E_err:   0.006522
[2025-10-06 13:57:34] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -46.159480 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 13:57:39] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -46.158343 | E_var:     0.1822 | E_err:   0.006670
[2025-10-06 13:57:44] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -46.173658 | E_var:     0.2941 | E_err:   0.008474
[2025-10-06 13:57:49] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -46.158196 | E_var:     0.1726 | E_err:   0.006491
[2025-10-06 13:57:54] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -46.165577 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 13:57:59] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -46.171607 | E_var:     0.2230 | E_err:   0.007378
[2025-10-06 13:58:05] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -46.177634 | E_var:     0.2485 | E_err:   0.007789
[2025-10-06 13:58:10] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -46.168050 | E_var:     0.2346 | E_err:   0.007568
[2025-10-06 13:58:15] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -46.164278 | E_var:     0.2108 | E_err:   0.007175
[2025-10-06 13:58:20] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -46.173955 | E_var:     0.1967 | E_err:   0.006930
[2025-10-06 13:58:25] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -46.183200 | E_var:     0.1751 | E_err:   0.006538
[2025-10-06 13:58:30] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -46.171583 | E_var:     0.2510 | E_err:   0.007827
[2025-10-06 13:58:35] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -46.173335 | E_var:     0.1644 | E_err:   0.006335
[2025-10-06 13:58:40] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -46.153085 | E_var:     0.1837 | E_err:   0.006697
[2025-10-06 13:58:46] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -46.173226 | E_var:     0.1981 | E_err:   0.006954
[2025-10-06 13:58:51] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -46.165381 | E_var:     0.2655 | E_err:   0.008050
[2025-10-06 13:58:56] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -46.172246 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 13:59:01] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -46.170184 | E_var:     0.1761 | E_err:   0.006557
[2025-10-06 13:59:06] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -46.159587 | E_var:     0.2876 | E_err:   0.008379
[2025-10-06 13:59:11] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -46.168176 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 13:59:16] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -46.180560 | E_var:     0.1879 | E_err:   0.006774
[2025-10-06 13:59:21] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -46.173140 | E_var:     0.2680 | E_err:   0.008089
[2025-10-06 13:59:27] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -46.170527 | E_var:     0.1769 | E_err:   0.006571
[2025-10-06 13:59:32] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -46.159659 | E_var:     0.1848 | E_err:   0.006717
[2025-10-06 13:59:37] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -46.162372 | E_var:     0.4680 | E_err:   0.010690
[2025-10-06 13:59:42] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -46.159998 | E_var:     0.1854 | E_err:   0.006729
[2025-10-06 13:59:47] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -46.173826 | E_var:     0.2220 | E_err:   0.007362
[2025-10-06 13:59:52] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -46.159856 | E_var:     0.1868 | E_err:   0.006753
[2025-10-06 13:59:57] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -46.160987 | E_var:     0.1595 | E_err:   0.006240
[2025-10-06 14:00:02] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -46.170297 | E_var:     0.2126 | E_err:   0.007204
[2025-10-06 14:00:08] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -46.163280 | E_var:     0.1720 | E_err:   0.006480
[2025-10-06 14:00:13] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -46.167078 | E_var:     0.1832 | E_err:   0.006688
[2025-10-06 14:00:18] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -46.177814 | E_var:     0.1991 | E_err:   0.006973
[2025-10-06 14:00:23] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -46.161402 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 14:00:28] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -46.171336 | E_var:     0.2495 | E_err:   0.007805
[2025-10-06 14:00:33] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -46.162587 | E_var:     0.1862 | E_err:   0.006742
[2025-10-06 14:00:38] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -46.166224 | E_var:     0.1421 | E_err:   0.005891
[2025-10-06 14:00:44] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -46.171568 | E_var:     0.2076 | E_err:   0.007120
[2025-10-06 14:00:49] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -46.164946 | E_var:     0.1940 | E_err:   0.006883
[2025-10-06 14:00:54] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -46.178988 | E_var:     0.1885 | E_err:   0.006784
[2025-10-06 14:00:54] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 14:00:59] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -46.165597 | E_var:     0.2126 | E_err:   0.007205
[2025-10-06 14:01:04] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -46.163593 | E_var:     0.2273 | E_err:   0.007450
[2025-10-06 14:01:09] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -46.178372 | E_var:     0.1790 | E_err:   0.006611
[2025-10-06 14:01:14] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -46.169688 | E_var:     0.2704 | E_err:   0.008125
[2025-10-06 14:01:20] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -46.172899 | E_var:     0.1962 | E_err:   0.006920
[2025-10-06 14:01:25] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -46.181739 | E_var:     0.1953 | E_err:   0.006905
[2025-10-06 14:01:30] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -46.174001 | E_var:     0.2004 | E_err:   0.006995
[2025-10-06 14:01:35] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -46.159661 | E_var:     0.1739 | E_err:   0.006515
[2025-10-06 14:01:40] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -46.158246 | E_var:     0.2351 | E_err:   0.007576
[2025-10-06 14:01:45] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -46.171759 | E_var:     0.2042 | E_err:   0.007060
[2025-10-06 14:01:50] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -46.154903 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 14:01:55] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -46.166028 | E_var:     0.1937 | E_err:   0.006876
[2025-10-06 14:02:00] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -46.156115 | E_var:     0.1661 | E_err:   0.006367
[2025-10-06 14:02:06] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -46.164496 | E_var:     0.1768 | E_err:   0.006571
[2025-10-06 14:02:11] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -46.172322 | E_var:     0.1841 | E_err:   0.006705
[2025-10-06 14:02:16] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -46.158005 | E_var:     0.2337 | E_err:   0.007553
[2025-10-06 14:02:21] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -46.176366 | E_var:     0.2220 | E_err:   0.007361
[2025-10-06 14:02:26] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -46.178253 | E_var:     0.1720 | E_err:   0.006480
[2025-10-06 14:02:31] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -46.165090 | E_var:     0.1951 | E_err:   0.006901
[2025-10-06 14:02:36] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -46.165550 | E_var:     0.1746 | E_err:   0.006528
[2025-10-06 14:02:41] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -46.177148 | E_var:     0.1899 | E_err:   0.006808
[2025-10-06 14:02:47] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -46.166687 | E_var:     0.2400 | E_err:   0.007655
[2025-10-06 14:02:52] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -46.155651 | E_var:     0.1809 | E_err:   0.006645
[2025-10-06 14:02:57] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -46.169662 | E_var:     0.1820 | E_err:   0.006667
[2025-10-06 14:03:02] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -46.166899 | E_var:     0.2422 | E_err:   0.007689
[2025-10-06 14:03:07] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -46.169901 | E_var:     0.1669 | E_err:   0.006384
[2025-10-06 14:03:12] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -46.175929 | E_var:     0.2474 | E_err:   0.007772
[2025-10-06 14:03:17] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -46.174337 | E_var:     0.2101 | E_err:   0.007163
[2025-10-06 14:03:22] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -46.164988 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 14:03:28] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -46.171418 | E_var:     0.1620 | E_err:   0.006288
[2025-10-06 14:03:33] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -46.167052 | E_var:     0.1665 | E_err:   0.006376
[2025-10-06 14:03:38] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -46.177468 | E_var:     0.1950 | E_err:   0.006899
[2025-10-06 14:03:43] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -46.167332 | E_var:     0.2017 | E_err:   0.007018
[2025-10-06 14:03:48] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -46.159571 | E_var:     0.1780 | E_err:   0.006593
[2025-10-06 14:03:53] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -46.168142 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 14:03:58] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -46.169090 | E_var:     0.1748 | E_err:   0.006532
[2025-10-06 14:04:03] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -46.163239 | E_var:     0.1671 | E_err:   0.006386
[2025-10-06 14:04:09] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -46.161743 | E_var:     0.2937 | E_err:   0.008468
[2025-10-06 14:04:14] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -46.159168 | E_var:     0.2275 | E_err:   0.007452
[2025-10-06 14:04:19] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -46.163205 | E_var:     0.1902 | E_err:   0.006814
[2025-10-06 14:04:24] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -46.166626 | E_var:     0.2633 | E_err:   0.008018
[2025-10-06 14:04:29] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -46.179513 | E_var:     0.2331 | E_err:   0.007543
[2025-10-06 14:04:34] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -46.166447 | E_var:     0.1990 | E_err:   0.006971
[2025-10-06 14:04:39] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -46.165269 | E_var:     0.2647 | E_err:   0.008038
[2025-10-06 14:04:44] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -46.164869 | E_var:     0.1738 | E_err:   0.006514
[2025-10-06 14:04:50] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -46.166425 | E_var:     0.1940 | E_err:   0.006882
[2025-10-06 14:04:55] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -46.170454 | E_var:     0.2099 | E_err:   0.007159
[2025-10-06 14:05:00] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -46.166623 | E_var:     0.1726 | E_err:   0.006491
[2025-10-06 14:05:05] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -46.153559 | E_var:     0.2707 | E_err:   0.008129
[2025-10-06 14:05:10] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -46.169783 | E_var:     0.1509 | E_err:   0.006069
[2025-10-06 14:05:15] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -46.170668 | E_var:     0.1708 | E_err:   0.006457
[2025-10-06 14:05:20] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -46.168393 | E_var:     0.1966 | E_err:   0.006929
[2025-10-06 14:05:25] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -46.176526 | E_var:     0.2355 | E_err:   0.007582
[2025-10-06 14:05:31] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -46.171537 | E_var:     0.1910 | E_err:   0.006829
[2025-10-06 14:05:36] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -46.177256 | E_var:     0.1957 | E_err:   0.006912
[2025-10-06 14:05:41] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -46.167014 | E_var:     0.1892 | E_err:   0.006796
[2025-10-06 14:05:46] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -46.160817 | E_var:     0.1812 | E_err:   0.006652
[2025-10-06 14:05:51] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -46.174044 | E_var:     0.1777 | E_err:   0.006587
[2025-10-06 14:05:56] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -46.159226 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 14:06:01] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -46.178200 | E_var:     0.7981 | E_err:   0.013959
[2025-10-06 14:06:06] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -46.174779 | E_var:     0.2043 | E_err:   0.007063
[2025-10-06 14:06:12] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -46.169130 | E_var:     0.2645 | E_err:   0.008036
[2025-10-06 14:06:17] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -46.159887 | E_var:     0.1964 | E_err:   0.006924
[2025-10-06 14:06:22] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -46.181542 | E_var:     0.1727 | E_err:   0.006493
[2025-10-06 14:06:27] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -46.170561 | E_var:     0.1564 | E_err:   0.006179
[2025-10-06 14:06:32] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -46.174577 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 14:06:37] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -46.150276 | E_var:     1.8390 | E_err:   0.021189
[2025-10-06 14:06:42] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -46.166013 | E_var:     0.2170 | E_err:   0.007279
[2025-10-06 14:06:47] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -46.163558 | E_var:     0.1913 | E_err:   0.006834
[2025-10-06 14:06:53] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -46.175859 | E_var:     0.1630 | E_err:   0.006308
[2025-10-06 14:06:58] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -46.161357 | E_var:     0.1800 | E_err:   0.006628
[2025-10-06 14:07:03] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -46.172525 | E_var:     0.1794 | E_err:   0.006617
[2025-10-06 14:07:08] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -46.174357 | E_var:     0.1599 | E_err:   0.006248
[2025-10-06 14:07:13] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -46.160241 | E_var:     0.2649 | E_err:   0.008042
[2025-10-06 14:07:18] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -46.168880 | E_var:     0.1988 | E_err:   0.006966
[2025-10-06 14:07:23] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -46.181555 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 14:07:28] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -46.175704 | E_var:     0.1851 | E_err:   0.006723
[2025-10-06 14:07:34] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -46.183064 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 14:07:39] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -46.172918 | E_var:     0.2551 | E_err:   0.007891
[2025-10-06 14:07:44] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -46.165903 | E_var:     0.2066 | E_err:   0.007102
[2025-10-06 14:07:49] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -46.166536 | E_var:     0.3606 | E_err:   0.009382
[2025-10-06 14:07:54] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -46.158739 | E_var:     0.2828 | E_err:   0.008310
[2025-10-06 14:07:59] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -46.169648 | E_var:     0.1814 | E_err:   0.006655
[2025-10-06 14:08:04] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -46.168364 | E_var:     0.2029 | E_err:   0.007038
[2025-10-06 14:08:09] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -46.163806 | E_var:     0.2195 | E_err:   0.007321
[2025-10-06 14:08:15] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -46.169479 | E_var:     0.2037 | E_err:   0.007052
[2025-10-06 14:08:20] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -46.164737 | E_var:     0.1622 | E_err:   0.006292
[2025-10-06 14:08:25] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -46.152913 | E_var:     0.1998 | E_err:   0.006984
[2025-10-06 14:08:30] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -46.176218 | E_var:     0.2162 | E_err:   0.007264
[2025-10-06 14:08:35] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -46.171031 | E_var:     0.2122 | E_err:   0.007198
[2025-10-06 14:08:40] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -46.164740 | E_var:     0.1915 | E_err:   0.006838
[2025-10-06 14:08:45] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -46.151361 | E_var:     0.2269 | E_err:   0.007442
[2025-10-06 14:08:50] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -46.164460 | E_var:     0.1854 | E_err:   0.006727
[2025-10-06 14:08:56] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -46.182714 | E_var:     0.1727 | E_err:   0.006494
[2025-10-06 14:09:01] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -46.161052 | E_var:     0.1952 | E_err:   0.006904
[2025-10-06 14:09:06] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -46.168856 | E_var:     0.1981 | E_err:   0.006955
[2025-10-06 14:09:11] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -46.175364 | E_var:     0.1892 | E_err:   0.006796
[2025-10-06 14:09:16] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -46.171679 | E_var:     0.2202 | E_err:   0.007332
[2025-10-06 14:09:21] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -46.163309 | E_var:     0.1873 | E_err:   0.006763
[2025-10-06 14:09:26] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -46.160336 | E_var:     0.1835 | E_err:   0.006693
[2025-10-06 14:09:26] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 14:09:31] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -46.155580 | E_var:     0.1875 | E_err:   0.006766
[2025-10-06 14:09:37] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -46.166493 | E_var:     0.2267 | E_err:   0.007439
[2025-10-06 14:09:42] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -46.166133 | E_var:     0.1962 | E_err:   0.006921
[2025-10-06 14:09:47] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -46.165276 | E_var:     0.1747 | E_err:   0.006531
[2025-10-06 14:09:52] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -46.177868 | E_var:     0.2613 | E_err:   0.007987
[2025-10-06 14:09:57] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -46.162645 | E_var:     0.2527 | E_err:   0.007854
[2025-10-06 14:10:02] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -46.173746 | E_var:     0.1961 | E_err:   0.006919
[2025-10-06 14:10:07] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -46.169890 | E_var:     0.2111 | E_err:   0.007179
[2025-10-06 14:10:12] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -46.180713 | E_var:     0.2070 | E_err:   0.007110
[2025-10-06 14:10:18] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -46.177837 | E_var:     0.2094 | E_err:   0.007151
[2025-10-06 14:10:23] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -46.170326 | E_var:     0.2790 | E_err:   0.008254
[2025-10-06 14:10:28] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -46.161100 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 14:10:33] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -46.171178 | E_var:     0.2782 | E_err:   0.008241
[2025-10-06 14:10:38] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -46.159221 | E_var:     0.2059 | E_err:   0.007091
[2025-10-06 14:10:43] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -46.157345 | E_var:     0.3869 | E_err:   0.009719
[2025-10-06 14:10:48] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -46.165119 | E_var:     0.1932 | E_err:   0.006868
[2025-10-06 14:10:53] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -46.181275 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 14:10:59] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -46.168413 | E_var:     0.1700 | E_err:   0.006443
[2025-10-06 14:11:04] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -46.164105 | E_var:     0.1629 | E_err:   0.006306
[2025-10-06 14:11:09] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -46.169031 | E_var:     0.2224 | E_err:   0.007369
[2025-10-06 14:11:14] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -46.166034 | E_var:     0.2038 | E_err:   0.007054
[2025-10-06 14:11:19] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -46.172276 | E_var:     0.2556 | E_err:   0.007900
[2025-10-06 14:11:24] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -46.163536 | E_var:     0.1668 | E_err:   0.006381
[2025-10-06 14:11:29] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -46.163510 | E_var:     0.1714 | E_err:   0.006470
[2025-10-06 14:11:34] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -46.160637 | E_var:     0.1865 | E_err:   0.006749
[2025-10-06 14:11:40] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -46.172644 | E_var:     0.1681 | E_err:   0.006405
[2025-10-06 14:11:45] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -46.170249 | E_var:     0.1953 | E_err:   0.006905
[2025-10-06 14:11:50] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -46.175156 | E_var:     0.2494 | E_err:   0.007803
[2025-10-06 14:11:55] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -46.160009 | E_var:     0.2057 | E_err:   0.007086
[2025-10-06 14:12:00] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -46.168709 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 14:12:05] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -46.156463 | E_var:     0.2152 | E_err:   0.007249
[2025-10-06 14:12:10] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -46.170734 | E_var:     0.2325 | E_err:   0.007534
[2025-10-06 14:12:15] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -46.163827 | E_var:     0.2997 | E_err:   0.008554
[2025-10-06 14:12:20] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -46.167435 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 14:12:26] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -46.176968 | E_var:     0.1756 | E_err:   0.006547
[2025-10-06 14:12:31] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -46.164438 | E_var:     0.2101 | E_err:   0.007163
[2025-10-06 14:12:36] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -46.164928 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 14:12:41] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -46.170824 | E_var:     0.1788 | E_err:   0.006606
[2025-10-06 14:12:46] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -46.175317 | E_var:     0.1474 | E_err:   0.005998
[2025-10-06 14:12:51] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -46.169241 | E_var:     0.2612 | E_err:   0.007985
[2025-10-06 14:12:56] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -46.176175 | E_var:     0.2470 | E_err:   0.007766
[2025-10-06 14:13:01] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -46.161383 | E_var:     0.1965 | E_err:   0.006925
[2025-10-06 14:13:07] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -46.177437 | E_var:     0.2357 | E_err:   0.007586
[2025-10-06 14:13:12] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -46.165236 | E_var:     0.1846 | E_err:   0.006712
[2025-10-06 14:13:17] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -46.169073 | E_var:     0.1761 | E_err:   0.006558
[2025-10-06 14:13:22] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -46.179325 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 14:13:27] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -46.167283 | E_var:     0.2777 | E_err:   0.008234
[2025-10-06 14:13:32] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -46.173203 | E_var:     0.2480 | E_err:   0.007782
[2025-10-06 14:13:37] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -46.170714 | E_var:     0.1755 | E_err:   0.006546
[2025-10-06 14:13:42] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -46.164207 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 14:13:48] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -46.168135 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 14:13:53] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -46.166001 | E_var:     0.2910 | E_err:   0.008428
[2025-10-06 14:13:58] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -46.175807 | E_var:     0.1762 | E_err:   0.006558
[2025-10-06 14:14:03] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -46.166866 | E_var:     0.2237 | E_err:   0.007390
[2025-10-06 14:14:08] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -46.168754 | E_var:     0.2250 | E_err:   0.007411
[2025-10-06 14:14:13] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -46.148461 | E_var:     0.6549 | E_err:   0.012644
[2025-10-06 14:14:18] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -46.163979 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 14:14:23] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -46.171456 | E_var:     0.1951 | E_err:   0.006902
[2025-10-06 14:14:29] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -46.165719 | E_var:     0.2048 | E_err:   0.007071
[2025-10-06 14:14:34] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -46.169571 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 14:14:39] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -46.168443 | E_var:     0.1801 | E_err:   0.006631
[2025-10-06 14:14:44] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -46.166124 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 14:14:49] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -46.157942 | E_var:     0.2061 | E_err:   0.007093
[2025-10-06 14:14:54] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -46.185824 | E_var:     0.1977 | E_err:   0.006948
[2025-10-06 14:14:59] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -46.161909 | E_var:     0.1965 | E_err:   0.006925
[2025-10-06 14:15:04] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -46.177907 | E_var:     0.2041 | E_err:   0.007059
[2025-10-06 14:15:09] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -46.165401 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 14:15:15] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -46.153218 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 14:15:20] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -46.163350 | E_var:     0.1778 | E_err:   0.006589
[2025-10-06 14:15:25] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -46.165312 | E_var:     0.2799 | E_err:   0.008267
[2025-10-06 14:15:30] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -46.170683 | E_var:     0.2746 | E_err:   0.008188
[2025-10-06 14:15:35] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -46.169653 | E_var:     0.1904 | E_err:   0.006818
[2025-10-06 14:15:40] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -46.160457 | E_var:     0.1957 | E_err:   0.006912
[2025-10-06 14:15:45] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -46.161738 | E_var:     0.1591 | E_err:   0.006233
[2025-10-06 14:15:50] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -46.163983 | E_var:     0.2232 | E_err:   0.007382
[2025-10-06 14:15:56] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -46.170317 | E_var:     0.1821 | E_err:   0.006668
[2025-10-06 14:16:01] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -46.174326 | E_var:     0.2951 | E_err:   0.008489
[2025-10-06 14:16:06] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -46.161655 | E_var:     0.1843 | E_err:   0.006708
[2025-10-06 14:16:11] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -46.160386 | E_var:     0.2198 | E_err:   0.007326
[2025-10-06 14:16:16] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -46.171697 | E_var:     0.2349 | E_err:   0.007573
[2025-10-06 14:16:21] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -46.163612 | E_var:     0.2396 | E_err:   0.007648
[2025-10-06 14:16:26] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -46.153404 | E_var:     0.1819 | E_err:   0.006663
[2025-10-06 14:16:31] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -46.164839 | E_var:     0.1949 | E_err:   0.006898
[2025-10-06 14:16:36] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -46.160143 | E_var:     0.1619 | E_err:   0.006286
[2025-10-06 14:16:42] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -46.171019 | E_var:     0.2677 | E_err:   0.008084
[2025-10-06 14:16:47] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -46.155794 | E_var:     0.2341 | E_err:   0.007560
[2025-10-06 14:16:52] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -46.170039 | E_var:     0.2665 | E_err:   0.008067
[2025-10-06 14:16:57] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -46.168232 | E_var:     0.2210 | E_err:   0.007345
[2025-10-06 14:17:02] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -46.166608 | E_var:     0.2448 | E_err:   0.007731
[2025-10-06 14:17:07] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -46.157793 | E_var:     0.1987 | E_err:   0.006964
[2025-10-06 14:17:12] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -46.154838 | E_var:     0.2124 | E_err:   0.007200
[2025-10-06 14:17:17] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -46.167165 | E_var:     0.1702 | E_err:   0.006446
[2025-10-06 14:17:23] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -46.158338 | E_var:     0.2095 | E_err:   0.007153
[2025-10-06 14:17:28] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -46.170460 | E_var:     0.1673 | E_err:   0.006390
[2025-10-06 14:17:33] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -46.174668 | E_var:     0.1600 | E_err:   0.006250
[2025-10-06 14:17:38] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -46.167540 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 14:17:43] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -46.167507 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 14:17:48] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -46.169650 | E_var:     0.2210 | E_err:   0.007345
[2025-10-06 14:17:53] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -46.170017 | E_var:     0.1994 | E_err:   0.006977
[2025-10-06 14:17:58] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -46.159453 | E_var:     0.1803 | E_err:   0.006635
[2025-10-06 14:17:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 14:18:04] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -46.164050 | E_var:     0.1779 | E_err:   0.006590
[2025-10-06 14:18:09] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -46.152900 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 14:18:14] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -46.170100 | E_var:     0.1709 | E_err:   0.006460
[2025-10-06 14:18:19] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -46.165543 | E_var:     0.2160 | E_err:   0.007261
[2025-10-06 14:18:24] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -46.171573 | E_var:     0.1800 | E_err:   0.006629
[2025-10-06 14:18:29] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -46.160141 | E_var:     0.1946 | E_err:   0.006893
[2025-10-06 14:18:34] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -46.169194 | E_var:     0.2237 | E_err:   0.007390
[2025-10-06 14:18:40] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -46.158121 | E_var:     0.2195 | E_err:   0.007321
[2025-10-06 14:18:45] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -46.167148 | E_var:     0.1878 | E_err:   0.006771
[2025-10-06 14:18:50] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -46.171918 | E_var:     0.1855 | E_err:   0.006730
[2025-10-06 14:18:55] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -46.169932 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 14:19:00] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -46.169220 | E_var:     0.1861 | E_err:   0.006740
[2025-10-06 14:19:05] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -46.160425 | E_var:     0.2411 | E_err:   0.007673
[2025-10-06 14:19:10] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -46.158278 | E_var:     0.1781 | E_err:   0.006595
[2025-10-06 14:19:15] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -46.172825 | E_var:     0.2019 | E_err:   0.007022
[2025-10-06 14:19:21] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -46.150364 | E_var:     0.1921 | E_err:   0.006848
[2025-10-06 14:19:26] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -46.163379 | E_var:     0.1925 | E_err:   0.006855
[2025-10-06 14:19:31] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -46.181927 | E_var:     0.1710 | E_err:   0.006462
[2025-10-06 14:19:36] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -46.161746 | E_var:     0.1955 | E_err:   0.006909
[2025-10-06 14:19:41] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -46.165061 | E_var:     0.1977 | E_err:   0.006947
[2025-10-06 14:19:46] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -46.170748 | E_var:     0.2485 | E_err:   0.007788
[2025-10-06 14:19:51] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -46.166366 | E_var:     0.1970 | E_err:   0.006935
[2025-10-06 14:19:56] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -46.170969 | E_var:     0.1983 | E_err:   0.006958
[2025-10-06 14:20:01] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -46.156281 | E_var:     0.1741 | E_err:   0.006520
[2025-10-06 14:20:07] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -46.176391 | E_var:     0.1559 | E_err:   0.006170
[2025-10-06 14:20:12] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -46.172197 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 14:20:17] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -46.173924 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 14:20:22] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -46.165185 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 14:20:27] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -46.167479 | E_var:     0.2413 | E_err:   0.007675
[2025-10-06 14:20:32] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -46.168072 | E_var:     0.1675 | E_err:   0.006394
[2025-10-06 14:20:37] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -46.157606 | E_var:     0.2036 | E_err:   0.007050
[2025-10-06 14:20:42] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -46.148633 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 14:20:48] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -46.176446 | E_var:     0.1709 | E_err:   0.006460
[2025-10-06 14:20:53] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -46.179930 | E_var:     0.2357 | E_err:   0.007585
[2025-10-06 14:20:58] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -46.177461 | E_var:     0.1878 | E_err:   0.006771
[2025-10-06 14:21:03] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -46.169110 | E_var:     0.2609 | E_err:   0.007981
[2025-10-06 14:21:08] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -46.159627 | E_var:     0.2251 | E_err:   0.007414
[2025-10-06 14:21:13] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -46.167935 | E_var:     0.1598 | E_err:   0.006246
[2025-10-06 14:21:18] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -46.171954 | E_var:     0.1893 | E_err:   0.006798
[2025-10-06 14:21:23] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -46.169577 | E_var:     0.1872 | E_err:   0.006760
[2025-10-06 14:21:28] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -46.165674 | E_var:     0.1743 | E_err:   0.006524
[2025-10-06 14:21:34] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -46.174991 | E_var:     0.2319 | E_err:   0.007525
[2025-10-06 14:21:39] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -46.169240 | E_var:     0.1796 | E_err:   0.006622
[2025-10-06 14:21:44] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -46.169265 | E_var:     0.1735 | E_err:   0.006509
[2025-10-06 14:21:49] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -46.165499 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 14:21:54] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -46.174966 | E_var:     0.2214 | E_err:   0.007351
[2025-10-06 14:21:59] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -46.177800 | E_var:     0.2865 | E_err:   0.008364
[2025-10-06 14:22:04] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -46.164031 | E_var:     0.1832 | E_err:   0.006687
[2025-10-06 14:22:09] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -46.171822 | E_var:     0.2063 | E_err:   0.007097
[2025-10-06 14:22:14] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -46.172502 | E_var:     0.1503 | E_err:   0.006058
[2025-10-06 14:22:14] 🔄 RESTART #2 | Period: 600
[2025-10-06 14:22:20] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -46.161067 | E_var:     0.1581 | E_err:   0.006213
[2025-10-06 14:22:25] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -46.173546 | E_var:     0.1941 | E_err:   0.006884
[2025-10-06 14:22:30] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -46.164358 | E_var:     0.2232 | E_err:   0.007382
[2025-10-06 14:22:35] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -46.172684 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 14:22:40] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -46.180245 | E_var:     0.2339 | E_err:   0.007557
[2025-10-06 14:22:45] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -46.168667 | E_var:     0.1879 | E_err:   0.006772
[2025-10-06 14:22:50] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -46.170548 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 14:22:55] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -46.163663 | E_var:     0.1899 | E_err:   0.006809
[2025-10-06 14:23:01] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -46.165486 | E_var:     0.2871 | E_err:   0.008372
[2025-10-06 14:23:06] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -46.176873 | E_var:     0.2165 | E_err:   0.007271
[2025-10-06 14:23:11] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -46.161959 | E_var:     0.2173 | E_err:   0.007283
[2025-10-06 14:23:16] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -46.178830 | E_var:     0.2773 | E_err:   0.008228
[2025-10-06 14:23:21] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -46.164117 | E_var:     0.2111 | E_err:   0.007178
[2025-10-06 14:23:26] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -46.184030 | E_var:     0.2132 | E_err:   0.007215
[2025-10-06 14:23:31] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -46.165247 | E_var:     0.2336 | E_err:   0.007551
[2025-10-06 14:23:36] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -46.158183 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 14:23:41] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -46.160776 | E_var:     0.1653 | E_err:   0.006353
[2025-10-06 14:23:47] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -46.161555 | E_var:     0.2025 | E_err:   0.007031
[2025-10-06 14:23:52] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -46.163151 | E_var:     0.2262 | E_err:   0.007431
[2025-10-06 14:23:57] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -46.166567 | E_var:     0.1993 | E_err:   0.006976
[2025-10-06 14:24:02] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -46.168617 | E_var:     0.1965 | E_err:   0.006926
[2025-10-06 14:24:07] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -46.175675 | E_var:     0.1473 | E_err:   0.005997
[2025-10-06 14:24:12] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -46.176096 | E_var:     0.2065 | E_err:   0.007100
[2025-10-06 14:24:17] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -46.187351 | E_var:     0.2044 | E_err:   0.007064
[2025-10-06 14:24:22] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -46.151730 | E_var:     0.1665 | E_err:   0.006375
[2025-10-06 14:24:28] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -46.169457 | E_var:     0.1713 | E_err:   0.006467
[2025-10-06 14:24:33] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -46.174458 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 14:24:38] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -46.175987 | E_var:     0.1782 | E_err:   0.006596
[2025-10-06 14:24:43] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -46.160670 | E_var:     0.2403 | E_err:   0.007659
[2025-10-06 14:24:48] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -46.170098 | E_var:     0.2331 | E_err:   0.007544
[2025-10-06 14:24:53] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -46.172523 | E_var:     0.1904 | E_err:   0.006817
[2025-10-06 14:24:58] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -46.168031 | E_var:     0.4233 | E_err:   0.010166
[2025-10-06 14:25:03] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -46.167735 | E_var:     0.2189 | E_err:   0.007311
[2025-10-06 14:25:08] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -46.176958 | E_var:     0.2553 | E_err:   0.007896
[2025-10-06 14:25:14] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -46.166168 | E_var:     0.2018 | E_err:   0.007020
[2025-10-06 14:25:19] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -46.171744 | E_var:     0.1601 | E_err:   0.006251
[2025-10-06 14:25:24] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -46.173052 | E_var:     0.2479 | E_err:   0.007780
[2025-10-06 14:25:29] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -46.170719 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 14:25:34] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -46.169469 | E_var:     0.1631 | E_err:   0.006311
[2025-10-06 14:25:39] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -46.169800 | E_var:     0.1959 | E_err:   0.006916
[2025-10-06 14:25:44] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -46.162366 | E_var:     0.1763 | E_err:   0.006560
[2025-10-06 14:25:49] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -46.155370 | E_var:     0.1850 | E_err:   0.006720
[2025-10-06 14:25:55] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -46.164810 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 14:26:00] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -46.161995 | E_var:     0.2034 | E_err:   0.007047
[2025-10-06 14:26:05] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -46.175044 | E_var:     0.1646 | E_err:   0.006339
[2025-10-06 14:26:10] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -46.158438 | E_var:     0.1659 | E_err:   0.006363
[2025-10-06 14:26:15] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -46.159167 | E_var:     0.1889 | E_err:   0.006791
[2025-10-06 14:26:20] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -46.174793 | E_var:     0.2056 | E_err:   0.007084
[2025-10-06 14:26:25] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -46.160040 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 14:26:30] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -46.182761 | E_var:     0.2636 | E_err:   0.008023
[2025-10-06 14:26:30] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 14:26:35] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -46.173666 | E_var:     0.1760 | E_err:   0.006554
[2025-10-06 14:26:41] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -46.171024 | E_var:     0.3066 | E_err:   0.008651
[2025-10-06 14:26:46] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -46.178511 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 14:26:51] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -46.162065 | E_var:     0.1889 | E_err:   0.006792
[2025-10-06 14:26:56] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -46.158950 | E_var:     0.2398 | E_err:   0.007651
[2025-10-06 14:27:01] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -46.164488 | E_var:     0.1857 | E_err:   0.006733
[2025-10-06 14:27:06] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -46.156734 | E_var:     0.1769 | E_err:   0.006571
[2025-10-06 14:27:11] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -46.169073 | E_var:     0.1719 | E_err:   0.006479
[2025-10-06 14:27:16] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -46.159029 | E_var:     0.3241 | E_err:   0.008896
[2025-10-06 14:27:21] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -46.173601 | E_var:     0.1646 | E_err:   0.006340
[2025-10-06 14:27:27] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -46.167875 | E_var:     0.2264 | E_err:   0.007435
[2025-10-06 14:27:32] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -46.153781 | E_var:     0.1958 | E_err:   0.006914
[2025-10-06 14:27:37] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -46.156774 | E_var:     0.1961 | E_err:   0.006919
[2025-10-06 14:27:42] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -46.164820 | E_var:     0.2760 | E_err:   0.008209
[2025-10-06 14:27:47] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -46.168781 | E_var:     0.1984 | E_err:   0.006960
[2025-10-06 14:27:52] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -46.168058 | E_var:     0.1846 | E_err:   0.006713
[2025-10-06 14:27:57] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -46.168212 | E_var:     0.2814 | E_err:   0.008288
[2025-10-06 14:28:02] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -46.165104 | E_var:     0.1620 | E_err:   0.006290
[2025-10-06 14:28:08] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -46.160130 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 14:28:13] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -46.162737 | E_var:     0.2149 | E_err:   0.007243
[2025-10-06 14:28:18] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -46.158087 | E_var:     0.1744 | E_err:   0.006525
[2025-10-06 14:28:23] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -46.173785 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 14:28:28] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -46.168058 | E_var:     0.1793 | E_err:   0.006616
[2025-10-06 14:28:33] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -46.165242 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 14:28:38] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -46.164453 | E_var:     0.1550 | E_err:   0.006152
[2025-10-06 14:28:43] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -46.161141 | E_var:     0.2027 | E_err:   0.007034
[2025-10-06 14:28:48] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -46.160235 | E_var:     0.1661 | E_err:   0.006368
[2025-10-06 14:28:54] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -46.165952 | E_var:     0.1810 | E_err:   0.006647
[2025-10-06 14:28:59] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -46.164775 | E_var:     0.2015 | E_err:   0.007014
[2025-10-06 14:29:04] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -46.168826 | E_var:     0.2042 | E_err:   0.007060
[2025-10-06 14:29:09] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -46.162394 | E_var:     0.2183 | E_err:   0.007301
[2025-10-06 14:29:14] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -46.173440 | E_var:     0.2374 | E_err:   0.007614
[2025-10-06 14:29:19] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -46.168301 | E_var:     0.2154 | E_err:   0.007251
[2025-10-06 14:29:24] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -46.170539 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 14:29:29] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -46.163362 | E_var:     0.1874 | E_err:   0.006764
[2025-10-06 14:29:34] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -46.171827 | E_var:     0.1623 | E_err:   0.006294
[2025-10-06 14:29:40] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -46.163560 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 14:29:45] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -46.159563 | E_var:     0.2177 | E_err:   0.007290
[2025-10-06 14:29:50] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -46.168984 | E_var:     0.1742 | E_err:   0.006521
[2025-10-06 14:29:55] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -46.155443 | E_var:     0.2071 | E_err:   0.007111
[2025-10-06 14:30:00] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -46.168214 | E_var:     0.1723 | E_err:   0.006486
[2025-10-06 14:30:05] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -46.165168 | E_var:     0.1918 | E_err:   0.006842
[2025-10-06 14:30:11] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -46.174919 | E_var:     0.2474 | E_err:   0.007771
[2025-10-06 14:30:16] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -46.167801 | E_var:     0.2044 | E_err:   0.007065
[2025-10-06 14:30:21] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -46.169997 | E_var:     0.2240 | E_err:   0.007395
[2025-10-06 14:30:26] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -46.163191 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 14:30:31] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -46.156563 | E_var:     0.1969 | E_err:   0.006934
[2025-10-06 14:30:36] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -46.173559 | E_var:     0.1833 | E_err:   0.006690
[2025-10-06 14:30:41] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -46.164439 | E_var:     0.1792 | E_err:   0.006614
[2025-10-06 14:30:47] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -46.167494 | E_var:     0.1980 | E_err:   0.006953
[2025-10-06 14:30:52] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -46.172729 | E_var:     0.2174 | E_err:   0.007286
[2025-10-06 14:30:57] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -46.173943 | E_var:     0.2037 | E_err:   0.007053
[2025-10-06 14:31:02] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -46.158112 | E_var:     0.1688 | E_err:   0.006419
[2025-10-06 14:31:07] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -46.182582 | E_var:     0.2674 | E_err:   0.008080
[2025-10-06 14:31:12] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -46.162437 | E_var:     0.2345 | E_err:   0.007566
[2025-10-06 14:31:17] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -46.170859 | E_var:     0.1869 | E_err:   0.006755
[2025-10-06 14:31:22] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -46.173169 | E_var:     0.1799 | E_err:   0.006628
[2025-10-06 14:31:27] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -46.161535 | E_var:     0.1710 | E_err:   0.006461
[2025-10-06 14:31:33] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -46.154968 | E_var:     0.1635 | E_err:   0.006317
[2025-10-06 14:31:38] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -46.168246 | E_var:     0.2670 | E_err:   0.008074
[2025-10-06 14:31:43] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -46.157634 | E_var:     0.1802 | E_err:   0.006633
[2025-10-06 14:31:48] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -46.157711 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 14:31:53] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -46.159400 | E_var:     0.2992 | E_err:   0.008547
[2025-10-06 14:31:58] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -46.167932 | E_var:     0.1811 | E_err:   0.006649
[2025-10-06 14:32:03] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -46.180397 | E_var:     0.1862 | E_err:   0.006742
[2025-10-06 14:32:08] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -46.174016 | E_var:     0.2136 | E_err:   0.007221
[2025-10-06 14:32:13] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -46.165943 | E_var:     0.2991 | E_err:   0.008545
[2025-10-06 14:32:19] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -46.177378 | E_var:     0.1590 | E_err:   0.006230
[2025-10-06 14:32:24] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -46.172513 | E_var:     0.1786 | E_err:   0.006603
[2025-10-06 14:32:29] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -46.171509 | E_var:     0.1636 | E_err:   0.006320
[2025-10-06 14:32:34] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -46.154670 | E_var:     0.1849 | E_err:   0.006719
[2025-10-06 14:32:39] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -46.178191 | E_var:     0.5235 | E_err:   0.011305
[2025-10-06 14:32:44] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -46.154091 | E_var:     0.1737 | E_err:   0.006513
[2025-10-06 14:32:49] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -46.176448 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 14:32:54] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -46.160753 | E_var:     0.2523 | E_err:   0.007848
[2025-10-06 14:32:59] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -46.171568 | E_var:     0.2481 | E_err:   0.007783
[2025-10-06 14:33:05] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -46.168858 | E_var:     0.2019 | E_err:   0.007020
[2025-10-06 14:33:10] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -46.169824 | E_var:     0.1747 | E_err:   0.006530
[2025-10-06 14:33:15] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -46.178296 | E_var:     0.1775 | E_err:   0.006582
[2025-10-06 14:33:20] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -46.168058 | E_var:     0.2044 | E_err:   0.007063
[2025-10-06 14:33:25] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -46.166429 | E_var:     0.2376 | E_err:   0.007616
[2025-10-06 14:33:30] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -46.169350 | E_var:     0.1650 | E_err:   0.006347
[2025-10-06 14:33:35] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -46.170710 | E_var:     0.1885 | E_err:   0.006783
[2025-10-06 14:33:40] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -46.165297 | E_var:     0.1862 | E_err:   0.006742
[2025-10-06 14:33:46] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -46.165111 | E_var:     0.1765 | E_err:   0.006565
[2025-10-06 14:33:51] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -46.171259 | E_var:     0.1891 | E_err:   0.006795
[2025-10-06 14:33:56] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -46.170537 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 14:34:01] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -46.160604 | E_var:     0.1924 | E_err:   0.006854
[2025-10-06 14:34:06] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -46.163909 | E_var:     0.2233 | E_err:   0.007383
[2025-10-06 14:34:11] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -46.168056 | E_var:     0.1683 | E_err:   0.006410
[2025-10-06 14:34:16] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -46.169308 | E_var:     0.1922 | E_err:   0.006850
[2025-10-06 14:34:21] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -46.164880 | E_var:     0.1807 | E_err:   0.006642
[2025-10-06 14:34:26] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -46.173056 | E_var:     0.1907 | E_err:   0.006823
[2025-10-06 14:34:32] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -46.161496 | E_var:     0.1636 | E_err:   0.006320
[2025-10-06 14:34:37] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -46.166649 | E_var:     0.2369 | E_err:   0.007605
[2025-10-06 14:34:42] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -46.155456 | E_var:     0.2586 | E_err:   0.007946
[2025-10-06 14:34:47] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -46.163420 | E_var:     0.1970 | E_err:   0.006935
[2025-10-06 14:34:52] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -46.167466 | E_var:     0.2016 | E_err:   0.007015
[2025-10-06 14:34:57] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -46.168778 | E_var:     0.2553 | E_err:   0.007895
[2025-10-06 14:35:02] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -46.185754 | E_var:     0.1580 | E_err:   0.006210
[2025-10-06 14:35:02] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 14:35:07] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -46.165926 | E_var:     0.2474 | E_err:   0.007773
[2025-10-06 14:35:13] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -46.165529 | E_var:     0.1653 | E_err:   0.006353
[2025-10-06 14:35:18] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -46.170761 | E_var:     0.1997 | E_err:   0.006982
[2025-10-06 14:35:23] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -46.173353 | E_var:     0.1616 | E_err:   0.006280
[2025-10-06 14:35:28] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -46.159544 | E_var:     0.2520 | E_err:   0.007844
[2025-10-06 14:35:33] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -46.163202 | E_var:     0.1880 | E_err:   0.006775
[2025-10-06 14:35:38] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -46.172970 | E_var:     0.1691 | E_err:   0.006425
[2025-10-06 14:35:43] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -46.173637 | E_var:     0.1915 | E_err:   0.006837
[2025-10-06 14:35:48] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -46.165470 | E_var:     0.1875 | E_err:   0.006766
[2025-10-06 14:35:53] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -46.172608 | E_var:     0.1785 | E_err:   0.006601
[2025-10-06 14:35:59] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -46.171228 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 14:36:04] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -46.165344 | E_var:     0.2279 | E_err:   0.007460
[2025-10-06 14:36:09] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -46.170077 | E_var:     0.2266 | E_err:   0.007437
[2025-10-06 14:36:14] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -46.164426 | E_var:     0.2014 | E_err:   0.007013
[2025-10-06 14:36:19] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -46.165841 | E_var:     0.1807 | E_err:   0.006642
[2025-10-06 14:36:24] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -46.152216 | E_var:     0.3248 | E_err:   0.008904
[2025-10-06 14:36:29] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -46.169837 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 14:36:34] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -46.167293 | E_var:     0.1699 | E_err:   0.006440
[2025-10-06 14:36:39] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -46.176105 | E_var:     0.1943 | E_err:   0.006887
[2025-10-06 14:36:45] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -46.167381 | E_var:     0.2127 | E_err:   0.007207
[2025-10-06 14:36:50] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -46.171167 | E_var:     0.1529 | E_err:   0.006110
[2025-10-06 14:36:55] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -46.174806 | E_var:     0.2076 | E_err:   0.007120
[2025-10-06 14:37:00] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -46.183315 | E_var:     0.1538 | E_err:   0.006128
[2025-10-06 14:37:05] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -46.168825 | E_var:     0.2428 | E_err:   0.007699
[2025-10-06 14:37:10] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -46.173546 | E_var:     0.1776 | E_err:   0.006586
[2025-10-06 14:37:15] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -46.169519 | E_var:     0.1771 | E_err:   0.006576
[2025-10-06 14:37:20] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -46.172590 | E_var:     0.1970 | E_err:   0.006935
[2025-10-06 14:37:25] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -46.160845 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 14:37:31] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -46.171338 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 14:37:36] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -46.168396 | E_var:     0.2175 | E_err:   0.007287
[2025-10-06 14:37:41] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -46.168855 | E_var:     0.1568 | E_err:   0.006188
[2025-10-06 14:37:46] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -46.169331 | E_var:     0.1722 | E_err:   0.006483
[2025-10-06 14:37:51] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -46.179689 | E_var:     0.2173 | E_err:   0.007284
[2025-10-06 14:37:56] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -46.168827 | E_var:     0.2615 | E_err:   0.007991
[2025-10-06 14:38:01] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -46.172141 | E_var:     0.1956 | E_err:   0.006911
[2025-10-06 14:38:06] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -46.162666 | E_var:     0.4864 | E_err:   0.010897
[2025-10-06 14:38:12] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -46.170254 | E_var:     0.2306 | E_err:   0.007503
[2025-10-06 14:38:17] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -46.164208 | E_var:     0.2268 | E_err:   0.007441
[2025-10-06 14:38:22] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -46.158688 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 14:38:27] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -46.178086 | E_var:     0.1689 | E_err:   0.006421
[2025-10-06 14:38:32] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -46.172069 | E_var:     0.2008 | E_err:   0.007002
[2025-10-06 14:38:37] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -46.162207 | E_var:     0.1863 | E_err:   0.006743
[2025-10-06 14:38:42] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -46.167841 | E_var:     0.1831 | E_err:   0.006686
[2025-10-06 14:38:47] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -46.157742 | E_var:     0.1999 | E_err:   0.006986
[2025-10-06 14:38:52] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -46.180394 | E_var:     0.1634 | E_err:   0.006316
[2025-10-06 14:38:58] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -46.167776 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 14:39:03] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -46.167292 | E_var:     0.2190 | E_err:   0.007313
[2025-10-06 14:39:08] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -46.162784 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 14:39:13] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -46.161881 | E_var:     0.2052 | E_err:   0.007077
[2025-10-06 14:39:18] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -46.173543 | E_var:     0.1842 | E_err:   0.006706
[2025-10-06 14:39:23] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -46.167061 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 14:39:28] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -46.170720 | E_var:     0.2227 | E_err:   0.007374
[2025-10-06 14:39:33] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -46.163092 | E_var:     0.1719 | E_err:   0.006479
[2025-10-06 14:39:38] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -46.167580 | E_var:     0.1849 | E_err:   0.006718
[2025-10-06 14:39:44] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -46.156495 | E_var:     0.1836 | E_err:   0.006696
[2025-10-06 14:39:49] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -46.165619 | E_var:     0.1694 | E_err:   0.006431
[2025-10-06 14:39:54] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -46.157900 | E_var:     0.3397 | E_err:   0.009106
[2025-10-06 14:39:59] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -46.158012 | E_var:     0.2125 | E_err:   0.007202
[2025-10-06 14:40:04] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -46.160844 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 14:40:09] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -46.166680 | E_var:     0.2064 | E_err:   0.007098
[2025-10-06 14:40:14] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -46.169688 | E_var:     0.1633 | E_err:   0.006313
[2025-10-06 14:40:19] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -46.178046 | E_var:     0.1908 | E_err:   0.006825
[2025-10-06 14:40:25] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -46.170271 | E_var:     0.1853 | E_err:   0.006726
[2025-10-06 14:40:30] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -46.183209 | E_var:     0.2836 | E_err:   0.008321
[2025-10-06 14:40:35] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -46.181257 | E_var:     0.2264 | E_err:   0.007435
[2025-10-06 14:40:40] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -46.168622 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 14:40:45] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -46.164143 | E_var:     0.1748 | E_err:   0.006532
[2025-10-06 14:40:50] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -46.166530 | E_var:     0.1777 | E_err:   0.006586
[2025-10-06 14:40:55] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -46.168787 | E_var:     0.2381 | E_err:   0.007624
[2025-10-06 14:41:00] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -46.152522 | E_var:     0.1817 | E_err:   0.006660
[2025-10-06 14:41:05] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -46.166092 | E_var:     0.2347 | E_err:   0.007570
[2025-10-06 14:41:11] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -46.184320 | E_var:     0.2095 | E_err:   0.007151
[2025-10-06 14:41:16] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -46.174399 | E_var:     0.1661 | E_err:   0.006368
[2025-10-06 14:41:21] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -46.171311 | E_var:     0.3413 | E_err:   0.009128
[2025-10-06 14:41:26] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -46.164234 | E_var:     0.1719 | E_err:   0.006477
[2025-10-06 14:41:31] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -46.152055 | E_var:     0.3390 | E_err:   0.009097
[2025-10-06 14:41:36] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -46.166780 | E_var:     0.1529 | E_err:   0.006109
[2025-10-06 14:41:41] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -46.164895 | E_var:     0.1787 | E_err:   0.006605
[2025-10-06 14:41:46] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -46.176286 | E_var:     0.1704 | E_err:   0.006451
[2025-10-06 14:41:52] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -46.160931 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 14:41:57] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -46.171349 | E_var:     0.2322 | E_err:   0.007530
[2025-10-06 14:42:02] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -46.162502 | E_var:     0.1884 | E_err:   0.006782
[2025-10-06 14:42:07] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -46.165707 | E_var:     0.2313 | E_err:   0.007515
[2025-10-06 14:42:12] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -46.162626 | E_var:     0.2300 | E_err:   0.007493
[2025-10-06 14:42:17] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -46.162452 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 14:42:22] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -46.173859 | E_var:     0.2655 | E_err:   0.008052
[2025-10-06 14:42:27] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -46.163403 | E_var:     0.2119 | E_err:   0.007193
[2025-10-06 14:42:32] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -46.160510 | E_var:     0.1800 | E_err:   0.006629
[2025-10-06 14:42:38] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -46.175358 | E_var:     0.2120 | E_err:   0.007194
[2025-10-06 14:42:43] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -46.176309 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 14:42:48] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -46.170985 | E_var:     0.3769 | E_err:   0.009593
[2025-10-06 14:42:53] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -46.177187 | E_var:     0.4469 | E_err:   0.010445
[2025-10-06 14:42:58] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -46.156071 | E_var:     0.2317 | E_err:   0.007521
[2025-10-06 14:43:03] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -46.168219 | E_var:     0.1752 | E_err:   0.006540
[2025-10-06 14:43:08] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -46.175214 | E_var:     0.1829 | E_err:   0.006683
[2025-10-06 14:43:13] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -46.163604 | E_var:     0.1711 | E_err:   0.006463
[2025-10-06 14:43:19] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -46.174276 | E_var:     0.1975 | E_err:   0.006943
[2025-10-06 14:43:24] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -46.170242 | E_var:     0.3315 | E_err:   0.008997
[2025-10-06 14:43:29] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -46.173792 | E_var:     0.1744 | E_err:   0.006526
[2025-10-06 14:43:34] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -46.173219 | E_var:     0.2175 | E_err:   0.007288
[2025-10-06 14:43:34] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 14:43:39] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -46.164380 | E_var:     0.1865 | E_err:   0.006747
[2025-10-06 14:43:44] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -46.174023 | E_var:     0.1534 | E_err:   0.006119
[2025-10-06 14:43:49] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -46.159632 | E_var:     0.2538 | E_err:   0.007872
[2025-10-06 14:43:54] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -46.160000 | E_var:     0.2710 | E_err:   0.008135
[2025-10-06 14:43:59] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -46.153979 | E_var:     0.3531 | E_err:   0.009284
[2025-10-06 14:44:05] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -46.161165 | E_var:     0.1971 | E_err:   0.006936
[2025-10-06 14:44:10] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -46.173822 | E_var:     0.2489 | E_err:   0.007796
[2025-10-06 14:44:15] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -46.168320 | E_var:     0.1815 | E_err:   0.006657
[2025-10-06 14:44:20] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -46.174620 | E_var:     0.2971 | E_err:   0.008517
[2025-10-06 14:44:25] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -46.175757 | E_var:     0.1640 | E_err:   0.006328
[2025-10-06 14:44:30] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -46.182248 | E_var:     0.1703 | E_err:   0.006448
[2025-10-06 14:44:35] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -46.183285 | E_var:     0.2488 | E_err:   0.007794
[2025-10-06 14:44:40] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -46.163644 | E_var:     0.2164 | E_err:   0.007268
[2025-10-06 14:44:46] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -46.161704 | E_var:     0.1734 | E_err:   0.006506
[2025-10-06 14:44:51] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -46.170773 | E_var:     0.1833 | E_err:   0.006690
[2025-10-06 14:44:56] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -46.187006 | E_var:     0.2100 | E_err:   0.007160
[2025-10-06 14:45:01] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -46.162683 | E_var:     0.2749 | E_err:   0.008192
[2025-10-06 14:45:07] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -46.172346 | E_var:     0.1545 | E_err:   0.006141
[2025-10-06 14:45:12] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -46.149098 | E_var:     0.2065 | E_err:   0.007100
[2025-10-06 14:45:17] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -46.165699 | E_var:     0.2083 | E_err:   0.007131
[2025-10-06 14:45:22] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -46.160078 | E_var:     0.1578 | E_err:   0.006207
[2025-10-06 14:45:27] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -46.179462 | E_var:     0.1508 | E_err:   0.006067
[2025-10-06 14:45:33] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -46.173698 | E_var:     0.1852 | E_err:   0.006724
[2025-10-06 14:45:38] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -46.152888 | E_var:     0.2313 | E_err:   0.007514
[2025-10-06 14:45:43] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -46.163669 | E_var:     0.3862 | E_err:   0.009710
[2025-10-06 14:45:48] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -46.164365 | E_var:     0.1980 | E_err:   0.006953
[2025-10-06 14:45:53] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -46.169288 | E_var:     0.1942 | E_err:   0.006885
[2025-10-06 14:45:58] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -46.161339 | E_var:     0.2158 | E_err:   0.007259
[2025-10-06 14:46:03] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -46.176495 | E_var:     0.1517 | E_err:   0.006085
[2025-10-06 14:46:08] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -46.167864 | E_var:     0.1956 | E_err:   0.006910
[2025-10-06 14:46:13] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -46.169062 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 14:46:19] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -46.176737 | E_var:     0.2050 | E_err:   0.007075
[2025-10-06 14:46:24] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -46.158399 | E_var:     0.1772 | E_err:   0.006578
[2025-10-06 14:46:29] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -46.180325 | E_var:     0.1799 | E_err:   0.006627
[2025-10-06 14:46:34] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -46.168905 | E_var:     0.4048 | E_err:   0.009941
[2025-10-06 14:46:39] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -46.172387 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 14:46:44] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -46.181000 | E_var:     0.2052 | E_err:   0.007078
[2025-10-06 14:46:49] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -46.178863 | E_var:     0.1585 | E_err:   0.006220
[2025-10-06 14:46:54] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -46.165888 | E_var:     0.2627 | E_err:   0.008009
[2025-10-06 14:46:59] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -46.156275 | E_var:     0.1778 | E_err:   0.006589
[2025-10-06 14:47:05] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -46.168834 | E_var:     0.2068 | E_err:   0.007106
[2025-10-06 14:47:10] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -46.178494 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 14:47:15] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -46.159594 | E_var:     0.1931 | E_err:   0.006866
[2025-10-06 14:47:20] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -46.176142 | E_var:     0.2001 | E_err:   0.006990
[2025-10-06 14:47:25] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -46.166113 | E_var:     0.2254 | E_err:   0.007418
[2025-10-06 14:47:30] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -46.180861 | E_var:     0.2331 | E_err:   0.007545
[2025-10-06 14:47:35] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -46.167236 | E_var:     0.2225 | E_err:   0.007371
[2025-10-06 14:47:40] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -46.174231 | E_var:     0.2168 | E_err:   0.007275
[2025-10-06 14:47:46] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -46.166760 | E_var:     0.1698 | E_err:   0.006438
[2025-10-06 14:47:51] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -46.169380 | E_var:     0.1751 | E_err:   0.006538
[2025-10-06 14:47:56] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -46.152447 | E_var:     0.1979 | E_err:   0.006950
[2025-10-06 14:48:01] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -46.172858 | E_var:     0.1880 | E_err:   0.006774
[2025-10-06 14:48:06] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -46.173365 | E_var:     0.2019 | E_err:   0.007021
[2025-10-06 14:48:11] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -46.156803 | E_var:     0.2407 | E_err:   0.007666
[2025-10-06 14:48:16] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -46.169812 | E_var:     0.1906 | E_err:   0.006822
[2025-10-06 14:48:21] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -46.157421 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 14:48:26] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -46.175802 | E_var:     0.1866 | E_err:   0.006750
[2025-10-06 14:48:32] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -46.164969 | E_var:     0.2109 | E_err:   0.007176
[2025-10-06 14:48:37] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -46.163755 | E_var:     0.2185 | E_err:   0.007304
[2025-10-06 14:48:42] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -46.169246 | E_var:     0.1839 | E_err:   0.006701
[2025-10-06 14:48:47] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -46.165065 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 14:48:52] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -46.180114 | E_var:     0.1812 | E_err:   0.006651
[2025-10-06 14:48:57] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -46.168464 | E_var:     0.1668 | E_err:   0.006381
[2025-10-06 14:49:02] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -46.164692 | E_var:     0.1870 | E_err:   0.006756
[2025-10-06 14:49:07] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -46.166232 | E_var:     0.1646 | E_err:   0.006340
[2025-10-06 14:49:12] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -46.164385 | E_var:     0.2310 | E_err:   0.007509
[2025-10-06 14:49:18] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -46.174525 | E_var:     0.2178 | E_err:   0.007292
[2025-10-06 14:49:23] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -46.164024 | E_var:     0.2617 | E_err:   0.007994
[2025-10-06 14:49:28] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -46.170292 | E_var:     0.1706 | E_err:   0.006454
[2025-10-06 14:49:33] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -46.162048 | E_var:     0.2158 | E_err:   0.007259
[2025-10-06 14:49:38] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -46.166677 | E_var:     0.2169 | E_err:   0.007276
[2025-10-06 14:49:43] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -46.168775 | E_var:     0.2088 | E_err:   0.007140
[2025-10-06 14:49:48] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -46.161195 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 14:49:53] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -46.161454 | E_var:     0.3068 | E_err:   0.008655
[2025-10-06 14:49:58] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -46.172919 | E_var:     0.3232 | E_err:   0.008884
[2025-10-06 14:50:04] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -46.177582 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 14:50:09] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -46.173980 | E_var:     0.3328 | E_err:   0.009014
[2025-10-06 14:50:14] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -46.166089 | E_var:     0.2113 | E_err:   0.007183
[2025-10-06 14:50:19] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -46.163314 | E_var:     0.1912 | E_err:   0.006833
[2025-10-06 14:50:24] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -46.163264 | E_var:     0.2110 | E_err:   0.007177
[2025-10-06 14:50:29] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -46.165793 | E_var:     0.1918 | E_err:   0.006843
[2025-10-06 14:50:34] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -46.167113 | E_var:     0.2121 | E_err:   0.007195
[2025-10-06 14:50:39] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -46.166204 | E_var:     0.1580 | E_err:   0.006211
[2025-10-06 14:50:45] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -46.177736 | E_var:     0.2454 | E_err:   0.007740
[2025-10-06 14:50:50] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -46.159715 | E_var:     0.2731 | E_err:   0.008165
[2025-10-06 14:50:55] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -46.165584 | E_var:     0.1697 | E_err:   0.006436
[2025-10-06 14:51:00] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -46.155931 | E_var:     0.1800 | E_err:   0.006630
[2025-10-06 14:51:05] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -46.177784 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 14:51:10] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -46.171917 | E_var:     0.1612 | E_err:   0.006273
[2025-10-06 14:51:15] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -46.186010 | E_var:     0.1805 | E_err:   0.006638
[2025-10-06 14:51:20] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -46.158351 | E_var:     0.2142 | E_err:   0.007232
[2025-10-06 14:51:25] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -46.160826 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 14:51:31] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -46.163031 | E_var:     0.2706 | E_err:   0.008129
[2025-10-06 14:51:36] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -46.162642 | E_var:     0.2262 | E_err:   0.007431
[2025-10-06 14:51:41] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -46.154572 | E_var:     0.2007 | E_err:   0.006999
