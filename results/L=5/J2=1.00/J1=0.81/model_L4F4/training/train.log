[2025-10-06 12:11:12] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.80/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 12:11:12]   - 迭代次数: final
[2025-10-06 12:11:12]   - 能量: -44.875588-0.000406j ± 0.007285, Var: 0.217381
[2025-10-06 12:11:12]   - 时间戳: 2025-10-06T12:10:59.937035+08:00
[2025-10-06 12:11:31] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 12:11:31] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 12:11:31] ======================================================================================================
[2025-10-06 12:11:31] GCNN for Shastry-Sutherland Model
[2025-10-06 12:11:31] ======================================================================================================
[2025-10-06 12:11:31] System parameters:
[2025-10-06 12:11:31]   - System size: L=5, N=100
[2025-10-06 12:11:31]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-10-06 12:11:31] ------------------------------------------------------------------------------------------------------
[2025-10-06 12:11:31] Model parameters:
[2025-10-06 12:11:31]   - Number of layers = 4
[2025-10-06 12:11:31]   - Number of features = 4
[2025-10-06 12:11:31]   - Total parameters = 19628
[2025-10-06 12:11:31] ------------------------------------------------------------------------------------------------------
[2025-10-06 12:11:31] Training parameters:
[2025-10-06 12:11:31]   - Total iterations: 1050
[2025-10-06 12:11:31]   - Annealing cycles: 3
[2025-10-06 12:11:31]   - Initial period: 150
[2025-10-06 12:11:31]   - Period multiplier: 2.0
[2025-10-06 12:11:31]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 12:11:31]   - Samples: 4096
[2025-10-06 12:11:31]   - Discarded samples: 0
[2025-10-06 12:11:31]   - Chunk size: 4096
[2025-10-06 12:11:31]   - Diagonal shift: 0.15
[2025-10-06 12:11:31]   - Gradient clipping: 1.0
[2025-10-06 12:11:31]   - Checkpoint enabled: interval=100
[2025-10-06 12:11:31]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.81/model_L4F4/training/checkpoints
[2025-10-06 12:11:31] ------------------------------------------------------------------------------------------------------
[2025-10-06 12:11:31] Device status:
[2025-10-06 12:11:31]   - Devices model: NVIDIA H200 NVL
[2025-10-06 12:11:31]   - Number of devices: 1
[2025-10-06 12:11:31]   - Sharding: True
[2025-10-06 12:11:31] ======================================================================================================
[2025-10-06 12:12:02] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -45.443453 | E_var:     6.0305 | E_err:   0.038370
[2025-10-06 12:12:23] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -45.517717 | E_var:     0.4166 | E_err:   0.010085
[2025-10-06 12:12:28] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -45.515572 | E_var:     0.4230 | E_err:   0.010162
[2025-10-06 12:12:33] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -45.526652 | E_var:     0.3713 | E_err:   0.009521
[2025-10-06 12:12:38] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -45.531272 | E_var:     0.2340 | E_err:   0.007558
[2025-10-06 12:12:43] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -45.538184 | E_var:     0.2366 | E_err:   0.007600
[2025-10-06 12:12:49] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -45.525147 | E_var:     0.4125 | E_err:   0.010035
[2025-10-06 12:12:54] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -45.527861 | E_var:     0.2129 | E_err:   0.007209
[2025-10-06 12:12:59] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -45.531731 | E_var:     0.2147 | E_err:   0.007240
[2025-10-06 12:13:04] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -45.510047 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 12:13:09] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -45.517799 | E_var:     0.2012 | E_err:   0.007009
[2025-10-06 12:13:14] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -45.517084 | E_var:     0.2403 | E_err:   0.007660
[2025-10-06 12:13:19] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -45.520841 | E_var:     0.2883 | E_err:   0.008389
[2025-10-06 12:13:25] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -45.539443 | E_var:     0.1985 | E_err:   0.006961
[2025-10-06 12:13:30] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -45.536399 | E_var:     0.1956 | E_err:   0.006911
[2025-10-06 12:13:35] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -45.519528 | E_var:     0.1888 | E_err:   0.006789
[2025-10-06 12:13:40] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -45.523772 | E_var:     0.3033 | E_err:   0.008605
[2025-10-06 12:13:45] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -45.529069 | E_var:     0.2693 | E_err:   0.008109
[2025-10-06 12:13:50] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -45.534497 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 12:13:56] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -45.523311 | E_var:     0.1780 | E_err:   0.006593
[2025-10-06 12:14:01] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -45.527597 | E_var:     0.2543 | E_err:   0.007880
[2025-10-06 12:14:06] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -45.525489 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 12:14:11] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -45.512552 | E_var:     0.2128 | E_err:   0.007207
[2025-10-06 12:14:16] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -45.517716 | E_var:     0.2495 | E_err:   0.007804
[2025-10-06 12:14:21] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -45.518804 | E_var:     0.2408 | E_err:   0.007668
[2025-10-06 12:14:27] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -45.528071 | E_var:     0.2377 | E_err:   0.007618
[2025-10-06 12:14:32] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -45.515339 | E_var:     0.2461 | E_err:   0.007751
[2025-10-06 12:14:37] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -45.527621 | E_var:     0.1844 | E_err:   0.006710
[2025-10-06 12:14:42] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -45.525103 | E_var:     0.1965 | E_err:   0.006927
[2025-10-06 12:14:47] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -45.525984 | E_var:     0.2660 | E_err:   0.008058
[2025-10-06 12:14:52] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -45.523663 | E_var:     0.2190 | E_err:   0.007312
[2025-10-06 12:14:57] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -45.520513 | E_var:     0.1983 | E_err:   0.006958
[2025-10-06 12:15:03] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -45.522572 | E_var:     0.1904 | E_err:   0.006817
[2025-10-06 12:15:08] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -45.536344 | E_var:     0.2527 | E_err:   0.007855
[2025-10-06 12:15:13] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -45.521525 | E_var:     0.2632 | E_err:   0.008016
[2025-10-06 12:15:18] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -45.524720 | E_var:     0.2041 | E_err:   0.007060
[2025-10-06 12:15:23] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -45.521129 | E_var:     0.2085 | E_err:   0.007135
[2025-10-06 12:15:28] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -45.521518 | E_var:     0.2517 | E_err:   0.007839
[2025-10-06 12:15:34] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -45.518517 | E_var:     0.2110 | E_err:   0.007178
[2025-10-06 12:15:39] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -45.527552 | E_var:     0.1930 | E_err:   0.006864
[2025-10-06 12:15:44] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -45.527498 | E_var:     0.2255 | E_err:   0.007419
[2025-10-06 12:15:49] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -45.518432 | E_var:     0.2074 | E_err:   0.007116
[2025-10-06 12:15:54] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -45.519708 | E_var:     0.2121 | E_err:   0.007195
[2025-10-06 12:15:59] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -45.522246 | E_var:     0.3024 | E_err:   0.008592
[2025-10-06 12:16:05] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -45.507985 | E_var:     0.1868 | E_err:   0.006752
[2025-10-06 12:16:10] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -45.529070 | E_var:     0.2363 | E_err:   0.007595
[2025-10-06 12:16:15] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -45.532283 | E_var:     0.2441 | E_err:   0.007720
[2025-10-06 12:16:20] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -45.518275 | E_var:     0.2216 | E_err:   0.007355
[2025-10-06 12:16:25] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -45.528367 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 12:16:30] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -45.523487 | E_var:     0.2541 | E_err:   0.007877
[2025-10-06 12:16:35] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -45.508804 | E_var:     0.2027 | E_err:   0.007034
[2025-10-06 12:16:41] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -45.530914 | E_var:     0.2529 | E_err:   0.007857
[2025-10-06 12:16:46] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -45.518606 | E_var:     0.2340 | E_err:   0.007559
[2025-10-06 12:16:51] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -45.522274 | E_var:     0.2438 | E_err:   0.007715
[2025-10-06 12:16:56] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -45.508965 | E_var:     0.2084 | E_err:   0.007132
[2025-10-06 12:17:01] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -45.515405 | E_var:     0.1877 | E_err:   0.006770
[2025-10-06 12:17:06] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -45.528456 | E_var:     0.2403 | E_err:   0.007659
[2025-10-06 12:17:12] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -45.526470 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 12:17:17] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -45.507621 | E_var:     0.2643 | E_err:   0.008033
[2025-10-06 12:17:22] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -45.510419 | E_var:     0.2406 | E_err:   0.007663
[2025-10-06 12:17:27] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -45.532971 | E_var:     0.2296 | E_err:   0.007487
[2025-10-06 12:17:32] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -45.520450 | E_var:     0.2107 | E_err:   0.007173
[2025-10-06 12:17:37] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -45.522858 | E_var:     0.2284 | E_err:   0.007468
[2025-10-06 12:17:43] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -45.512526 | E_var:     0.1962 | E_err:   0.006921
[2025-10-06 12:17:48] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -45.521041 | E_var:     0.1752 | E_err:   0.006541
[2025-10-06 12:17:53] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -45.532532 | E_var:     0.2137 | E_err:   0.007224
[2025-10-06 12:17:58] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -45.521461 | E_var:     0.2280 | E_err:   0.007461
[2025-10-06 12:18:03] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -45.521238 | E_var:     0.2384 | E_err:   0.007629
[2025-10-06 12:18:08] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -45.517138 | E_var:     0.2701 | E_err:   0.008120
[2025-10-06 12:18:13] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -45.514884 | E_var:     0.2425 | E_err:   0.007695
[2025-10-06 12:18:19] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -45.529234 | E_var:     0.1986 | E_err:   0.006963
[2025-10-06 12:18:24] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -45.522704 | E_var:     0.2085 | E_err:   0.007134
[2025-10-06 12:18:29] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -45.521538 | E_var:     0.1686 | E_err:   0.006416
[2025-10-06 12:18:34] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -45.521664 | E_var:     0.2115 | E_err:   0.007186
[2025-10-06 12:18:39] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -45.530573 | E_var:     0.1999 | E_err:   0.006986
[2025-10-06 12:18:44] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -45.533534 | E_var:     0.3280 | E_err:   0.008949
[2025-10-06 12:18:50] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -45.521625 | E_var:     0.1748 | E_err:   0.006532
[2025-10-06 12:18:55] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -45.520304 | E_var:     0.2505 | E_err:   0.007821
[2025-10-06 12:19:00] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -45.525047 | E_var:     0.2362 | E_err:   0.007594
[2025-10-06 12:19:05] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -45.534639 | E_var:     0.1768 | E_err:   0.006570
[2025-10-06 12:19:10] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -45.520195 | E_var:     0.2653 | E_err:   0.008049
[2025-10-06 12:19:15] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -45.524106 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 12:19:20] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -45.519481 | E_var:     0.1995 | E_err:   0.006980
[2025-10-06 12:19:26] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -45.529801 | E_var:     0.1993 | E_err:   0.006975
[2025-10-06 12:19:31] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -45.519211 | E_var:     0.2057 | E_err:   0.007086
[2025-10-06 12:19:36] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -45.528205 | E_var:     0.2598 | E_err:   0.007964
[2025-10-06 12:19:41] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -45.508690 | E_var:     0.2484 | E_err:   0.007787
[2025-10-06 12:19:46] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -45.517890 | E_var:     0.2022 | E_err:   0.007025
[2025-10-06 12:19:51] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -45.518561 | E_var:     0.3190 | E_err:   0.008825
[2025-10-06 12:19:57] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -45.530564 | E_var:     0.1784 | E_err:   0.006600
[2025-10-06 12:20:02] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -45.534511 | E_var:     0.1795 | E_err:   0.006621
[2025-10-06 12:20:07] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -45.528208 | E_var:     0.2274 | E_err:   0.007452
[2025-10-06 12:20:12] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -45.525515 | E_var:     0.2082 | E_err:   0.007129
[2025-10-06 12:20:17] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -45.529852 | E_var:     0.2146 | E_err:   0.007239
[2025-10-06 12:20:22] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -45.517453 | E_var:     0.2179 | E_err:   0.007294
[2025-10-06 12:20:27] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -45.543592 | E_var:     0.2138 | E_err:   0.007226
[2025-10-06 12:20:33] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -45.531092 | E_var:     0.1969 | E_err:   0.006933
[2025-10-06 12:20:38] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -45.524628 | E_var:     0.2011 | E_err:   0.007007
[2025-10-06 12:20:43] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -45.529019 | E_var:     0.3576 | E_err:   0.009344
[2025-10-06 12:20:48] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -45.526412 | E_var:     0.3635 | E_err:   0.009420
[2025-10-06 12:20:48] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 12:20:53] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -45.516108 | E_var:     0.1955 | E_err:   0.006909
[2025-10-06 12:20:59] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -45.515729 | E_var:     0.2707 | E_err:   0.008130
[2025-10-06 12:21:04] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -45.529883 | E_var:     0.3016 | E_err:   0.008581
[2025-10-06 12:21:09] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -45.530322 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 12:21:14] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -45.528127 | E_var:     0.3560 | E_err:   0.009323
[2025-10-06 12:21:19] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -45.505832 | E_var:     0.6064 | E_err:   0.012168
[2025-10-06 12:21:24] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -45.527350 | E_var:     0.2047 | E_err:   0.007070
[2025-10-06 12:21:30] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -45.532170 | E_var:     0.2413 | E_err:   0.007675
[2025-10-06 12:21:35] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -45.524716 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 12:21:40] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -45.523020 | E_var:     0.2423 | E_err:   0.007692
[2025-10-06 12:21:45] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -45.520041 | E_var:     0.1974 | E_err:   0.006942
[2025-10-06 12:21:50] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -45.518236 | E_var:     0.2232 | E_err:   0.007381
[2025-10-06 12:21:55] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -45.524397 | E_var:     0.1756 | E_err:   0.006548
[2025-10-06 12:22:00] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -45.517710 | E_var:     1.3337 | E_err:   0.018045
[2025-10-06 12:22:06] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -45.531873 | E_var:     0.1824 | E_err:   0.006673
[2025-10-06 12:22:11] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -45.527904 | E_var:     0.3757 | E_err:   0.009577
[2025-10-06 12:22:16] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -45.529045 | E_var:     0.2942 | E_err:   0.008475
[2025-10-06 12:22:21] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -45.520061 | E_var:     0.2951 | E_err:   0.008489
[2025-10-06 12:22:26] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -45.528355 | E_var:     0.1746 | E_err:   0.006529
[2025-10-06 12:22:31] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -45.533103 | E_var:     0.2250 | E_err:   0.007412
[2025-10-06 12:22:37] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -45.528621 | E_var:     0.2200 | E_err:   0.007329
[2025-10-06 12:22:42] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -45.524482 | E_var:     0.2107 | E_err:   0.007172
[2025-10-06 12:22:47] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -45.520571 | E_var:     0.2350 | E_err:   0.007575
[2025-10-06 12:22:52] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -45.514730 | E_var:     0.1892 | E_err:   0.006796
[2025-10-06 12:22:57] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -45.521877 | E_var:     0.2152 | E_err:   0.007248
[2025-10-06 12:23:02] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -45.523949 | E_var:     0.2467 | E_err:   0.007760
[2025-10-06 12:23:08] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -45.534462 | E_var:     0.2647 | E_err:   0.008040
[2025-10-06 12:23:13] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -45.516082 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 12:23:18] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -45.516933 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 12:23:23] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -45.537832 | E_var:     0.1857 | E_err:   0.006733
[2025-10-06 12:23:28] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -45.528495 | E_var:     0.2010 | E_err:   0.007004
[2025-10-06 12:23:33] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -45.523183 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 12:23:39] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -45.513948 | E_var:     0.2113 | E_err:   0.007182
[2025-10-06 12:23:44] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -45.518446 | E_var:     0.2184 | E_err:   0.007301
[2025-10-06 12:23:49] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -45.513146 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 12:23:54] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -45.525724 | E_var:     0.2354 | E_err:   0.007582
[2025-10-06 12:23:59] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -45.537066 | E_var:     0.2701 | E_err:   0.008120
[2025-10-06 12:24:04] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -45.520608 | E_var:     0.2468 | E_err:   0.007763
[2025-10-06 12:24:10] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -45.523928 | E_var:     0.2828 | E_err:   0.008310
[2025-10-06 12:24:15] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -45.508329 | E_var:     0.2411 | E_err:   0.007672
[2025-10-06 12:24:20] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -45.512018 | E_var:     0.2232 | E_err:   0.007381
[2025-10-06 12:24:25] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -45.526947 | E_var:     0.2365 | E_err:   0.007599
[2025-10-06 12:24:30] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -45.517795 | E_var:     0.1984 | E_err:   0.006960
[2025-10-06 12:24:35] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -45.519650 | E_var:     0.2368 | E_err:   0.007604
[2025-10-06 12:24:40] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -45.519793 | E_var:     0.2179 | E_err:   0.007293
[2025-10-06 12:24:46] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -45.512891 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 12:24:51] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -45.516920 | E_var:     0.1908 | E_err:   0.006824
[2025-10-06 12:24:56] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -45.518069 | E_var:     0.3070 | E_err:   0.008658
[2025-10-06 12:25:01] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -45.511348 | E_var:     0.1865 | E_err:   0.006748
[2025-10-06 12:25:06] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -45.532233 | E_var:     0.2613 | E_err:   0.007987
[2025-10-06 12:25:06] 🔄 RESTART #1 | Period: 300
[2025-10-06 12:25:11] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -45.514971 | E_var:     0.2577 | E_err:   0.007932
[2025-10-06 12:25:17] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -45.516133 | E_var:     0.1867 | E_err:   0.006751
[2025-10-06 12:25:22] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -45.516458 | E_var:     0.2913 | E_err:   0.008434
[2025-10-06 12:25:27] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -45.523105 | E_var:     0.2531 | E_err:   0.007861
[2025-10-06 12:25:32] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -45.527792 | E_var:     0.2106 | E_err:   0.007171
[2025-10-06 12:25:37] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -45.528636 | E_var:     0.2733 | E_err:   0.008168
[2025-10-06 12:25:42] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -45.512169 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 12:25:48] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -45.517623 | E_var:     0.2509 | E_err:   0.007827
[2025-10-06 12:25:53] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -45.520900 | E_var:     0.3759 | E_err:   0.009580
[2025-10-06 12:25:58] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -45.520171 | E_var:     0.2192 | E_err:   0.007315
[2025-10-06 12:26:03] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -45.513919 | E_var:     0.2737 | E_err:   0.008175
[2025-10-06 12:26:08] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -45.527334 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 12:26:13] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -45.514305 | E_var:     0.2396 | E_err:   0.007649
[2025-10-06 12:26:18] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -45.524846 | E_var:     0.1763 | E_err:   0.006560
[2025-10-06 12:26:24] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -45.528568 | E_var:     0.1835 | E_err:   0.006694
[2025-10-06 12:26:29] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -45.524229 | E_var:     0.2026 | E_err:   0.007033
[2025-10-06 12:26:34] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -45.531537 | E_var:     0.1972 | E_err:   0.006939
[2025-10-06 12:26:39] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -45.538650 | E_var:     0.2100 | E_err:   0.007160
[2025-10-06 12:26:44] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -45.535986 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 12:26:50] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -45.525558 | E_var:     0.2177 | E_err:   0.007290
[2025-10-06 12:26:55] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -45.533634 | E_var:     0.2136 | E_err:   0.007222
[2025-10-06 12:27:00] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -45.528158 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 12:27:05] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -45.529240 | E_var:     0.2253 | E_err:   0.007416
[2025-10-06 12:27:10] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -45.525988 | E_var:     0.2905 | E_err:   0.008421
[2025-10-06 12:27:15] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -45.513178 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 12:27:20] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -45.513932 | E_var:     0.2168 | E_err:   0.007275
[2025-10-06 12:27:26] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -45.533028 | E_var:     0.3586 | E_err:   0.009357
[2025-10-06 12:27:31] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -45.519948 | E_var:     0.2141 | E_err:   0.007230
[2025-10-06 12:27:36] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -45.534130 | E_var:     0.2006 | E_err:   0.006999
[2025-10-06 12:27:41] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -45.528844 | E_var:     0.1766 | E_err:   0.006566
[2025-10-06 12:27:46] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -45.537874 | E_var:     0.2002 | E_err:   0.006992
[2025-10-06 12:27:51] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -45.525873 | E_var:     0.4112 | E_err:   0.010020
[2025-10-06 12:27:57] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -45.532246 | E_var:     0.2073 | E_err:   0.007114
[2025-10-06 12:28:02] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -45.529270 | E_var:     0.2459 | E_err:   0.007749
[2025-10-06 12:28:07] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -45.521397 | E_var:     0.2335 | E_err:   0.007551
[2025-10-06 12:28:12] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -45.521542 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 12:28:17] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -45.528935 | E_var:     0.2299 | E_err:   0.007492
[2025-10-06 12:28:22] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -45.537083 | E_var:     0.1769 | E_err:   0.006571
[2025-10-06 12:28:27] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -45.521499 | E_var:     0.2485 | E_err:   0.007789
[2025-10-06 12:28:33] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -45.526063 | E_var:     0.3346 | E_err:   0.009039
[2025-10-06 12:28:38] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -45.527765 | E_var:     0.2057 | E_err:   0.007087
[2025-10-06 12:28:43] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -45.526994 | E_var:     0.2131 | E_err:   0.007212
[2025-10-06 12:28:48] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -45.511004 | E_var:     0.1975 | E_err:   0.006944
[2025-10-06 12:28:53] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -45.528910 | E_var:     0.1975 | E_err:   0.006943
[2025-10-06 12:28:58] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -45.523575 | E_var:     0.2049 | E_err:   0.007073
[2025-10-06 12:29:03] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -45.540682 | E_var:     0.1727 | E_err:   0.006493
[2025-10-06 12:29:09] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -45.518074 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 12:29:14] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -45.527958 | E_var:     0.1825 | E_err:   0.006674
[2025-10-06 12:29:19] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -45.532369 | E_var:     0.2614 | E_err:   0.007989
[2025-10-06 12:29:24] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -45.524746 | E_var:     0.1903 | E_err:   0.006815
[2025-10-06 12:29:24] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 12:29:29] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -45.522483 | E_var:     0.2035 | E_err:   0.007049
[2025-10-06 12:29:34] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -45.532979 | E_var:     0.1886 | E_err:   0.006785
[2025-10-06 12:29:40] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -45.525599 | E_var:     0.2281 | E_err:   0.007463
[2025-10-06 12:29:45] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -45.524107 | E_var:     0.3353 | E_err:   0.009048
[2025-10-06 12:29:50] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -45.538601 | E_var:     0.3026 | E_err:   0.008595
[2025-10-06 12:29:55] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -45.524046 | E_var:     0.2098 | E_err:   0.007157
[2025-10-06 12:30:00] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -45.520289 | E_var:     0.2003 | E_err:   0.006993
[2025-10-06 12:30:05] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -45.520617 | E_var:     0.1970 | E_err:   0.006936
[2025-10-06 12:30:11] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -45.521688 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 12:30:16] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -45.520112 | E_var:     0.2174 | E_err:   0.007286
[2025-10-06 12:30:21] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -45.523017 | E_var:     0.2769 | E_err:   0.008223
[2025-10-06 12:30:26] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -45.517650 | E_var:     0.2515 | E_err:   0.007836
[2025-10-06 12:30:31] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -45.525822 | E_var:     0.1857 | E_err:   0.006734
[2025-10-06 12:30:36] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -45.522949 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 12:30:41] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -45.526427 | E_var:     0.1839 | E_err:   0.006701
[2025-10-06 12:30:47] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -45.530655 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 12:30:52] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -45.522326 | E_var:     0.2258 | E_err:   0.007425
[2025-10-06 12:30:57] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -45.522058 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 12:31:02] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -45.525647 | E_var:     0.2056 | E_err:   0.007084
[2025-10-06 12:31:07] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -45.524175 | E_var:     0.1894 | E_err:   0.006801
[2025-10-06 12:31:12] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -45.519984 | E_var:     0.2064 | E_err:   0.007098
[2025-10-06 12:31:18] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -45.529122 | E_var:     0.2355 | E_err:   0.007582
[2025-10-06 12:31:23] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -45.537414 | E_var:     0.4385 | E_err:   0.010347
[2025-10-06 12:31:28] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -45.520210 | E_var:     0.1988 | E_err:   0.006966
[2025-10-06 12:31:33] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -45.520284 | E_var:     0.6747 | E_err:   0.012834
[2025-10-06 12:31:38] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -45.518979 | E_var:     0.7577 | E_err:   0.013601
[2025-10-06 12:31:43] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -45.497620 | E_var:     0.9040 | E_err:   0.014856
[2025-10-06 12:31:48] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -45.537217 | E_var:     0.2036 | E_err:   0.007051
[2025-10-06 12:31:54] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -45.529090 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 12:31:59] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -45.535461 | E_var:     0.1918 | E_err:   0.006843
[2025-10-06 12:32:04] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -45.526477 | E_var:     0.2441 | E_err:   0.007720
[2025-10-06 12:32:09] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -45.522460 | E_var:     0.2589 | E_err:   0.007950
[2025-10-06 12:32:14] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -45.524586 | E_var:     0.2832 | E_err:   0.008314
[2025-10-06 12:32:20] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -45.533228 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 12:32:25] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -45.519750 | E_var:     0.1752 | E_err:   0.006541
[2025-10-06 12:32:30] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -45.535740 | E_var:     0.1593 | E_err:   0.006237
[2025-10-06 12:32:35] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -45.523129 | E_var:     0.2973 | E_err:   0.008520
[2025-10-06 12:32:40] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -45.526166 | E_var:     0.2696 | E_err:   0.008113
[2025-10-06 12:32:45] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -45.529690 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 12:32:51] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -45.520197 | E_var:     0.2152 | E_err:   0.007248
[2025-10-06 12:32:56] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -45.513563 | E_var:     0.4099 | E_err:   0.010003
[2025-10-06 12:33:01] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -45.515956 | E_var:     0.2138 | E_err:   0.007226
[2025-10-06 12:33:06] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -45.521330 | E_var:     0.1889 | E_err:   0.006790
[2025-10-06 12:33:11] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -45.522001 | E_var:     0.3194 | E_err:   0.008830
[2025-10-06 12:33:16] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -45.519476 | E_var:     0.1898 | E_err:   0.006807
[2025-10-06 12:33:21] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -45.519575 | E_var:     0.2409 | E_err:   0.007669
[2025-10-06 12:33:27] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -45.526752 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 12:33:32] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -45.530290 | E_var:     0.2396 | E_err:   0.007648
[2025-10-06 12:33:37] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -45.516871 | E_var:     0.2133 | E_err:   0.007217
[2025-10-06 12:33:42] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -45.533714 | E_var:     0.2602 | E_err:   0.007970
[2025-10-06 12:33:47] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -45.513663 | E_var:     0.2090 | E_err:   0.007142
[2025-10-06 12:33:52] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -45.527044 | E_var:     0.1874 | E_err:   0.006764
[2025-10-06 12:33:58] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -45.521534 | E_var:     0.1866 | E_err:   0.006749
[2025-10-06 12:34:03] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -45.532269 | E_var:     0.1709 | E_err:   0.006459
[2025-10-06 12:34:08] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -45.515122 | E_var:     0.2077 | E_err:   0.007121
[2025-10-06 12:34:13] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -45.523870 | E_var:     0.1738 | E_err:   0.006514
[2025-10-06 12:34:18] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -45.541204 | E_var:     0.2262 | E_err:   0.007432
[2025-10-06 12:34:23] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -45.534940 | E_var:     0.2367 | E_err:   0.007602
[2025-10-06 12:34:29] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -45.530380 | E_var:     0.2027 | E_err:   0.007036
[2025-10-06 12:34:34] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -45.520043 | E_var:     0.2536 | E_err:   0.007869
[2025-10-06 12:34:39] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -45.534183 | E_var:     0.1716 | E_err:   0.006473
[2025-10-06 12:34:44] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -45.510280 | E_var:     0.3627 | E_err:   0.009410
[2025-10-06 12:34:49] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -45.523307 | E_var:     0.2713 | E_err:   0.008139
[2025-10-06 12:34:54] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -45.523119 | E_var:     0.2323 | E_err:   0.007532
[2025-10-06 12:34:59] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -45.531419 | E_var:     0.2214 | E_err:   0.007352
[2025-10-06 12:35:05] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -45.520041 | E_var:     0.2119 | E_err:   0.007193
[2025-10-06 12:35:10] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -45.518408 | E_var:     0.2009 | E_err:   0.007003
[2025-10-06 12:35:15] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -45.522050 | E_var:     0.3616 | E_err:   0.009396
[2025-10-06 12:35:20] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -45.526794 | E_var:     0.1834 | E_err:   0.006692
[2025-10-06 12:35:25] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -45.513466 | E_var:     0.2448 | E_err:   0.007731
[2025-10-06 12:35:30] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -45.530593 | E_var:     0.3079 | E_err:   0.008671
[2025-10-06 12:35:36] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -45.518635 | E_var:     0.2306 | E_err:   0.007504
[2025-10-06 12:35:41] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -45.525140 | E_var:     0.1931 | E_err:   0.006865
[2025-10-06 12:35:46] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -45.522612 | E_var:     0.2179 | E_err:   0.007293
[2025-10-06 12:35:51] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -45.524817 | E_var:     0.2264 | E_err:   0.007435
[2025-10-06 12:35:56] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -45.526945 | E_var:     0.2054 | E_err:   0.007081
[2025-10-06 12:36:01] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -45.521811 | E_var:     0.2036 | E_err:   0.007050
[2025-10-06 12:36:07] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -45.533814 | E_var:     0.3182 | E_err:   0.008814
[2025-10-06 12:36:12] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -45.536345 | E_var:     0.3007 | E_err:   0.008568
[2025-10-06 12:36:17] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -45.518745 | E_var:     0.1682 | E_err:   0.006408
[2025-10-06 12:36:22] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -45.529448 | E_var:     0.2499 | E_err:   0.007812
[2025-10-06 12:36:27] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -45.526678 | E_var:     0.1767 | E_err:   0.006569
[2025-10-06 12:36:32] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -45.521343 | E_var:     0.2161 | E_err:   0.007263
[2025-10-06 12:36:37] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -45.512634 | E_var:     0.1932 | E_err:   0.006868
[2025-10-06 12:36:43] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -45.525976 | E_var:     0.2110 | E_err:   0.007177
[2025-10-06 12:36:48] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -45.525687 | E_var:     0.2425 | E_err:   0.007694
[2025-10-06 12:36:53] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -45.529989 | E_var:     0.1820 | E_err:   0.006666
[2025-10-06 12:36:58] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -45.521191 | E_var:     0.1963 | E_err:   0.006922
[2025-10-06 12:37:03] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -45.525822 | E_var:     0.2184 | E_err:   0.007302
[2025-10-06 12:37:08] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -45.528777 | E_var:     0.2263 | E_err:   0.007434
[2025-10-06 12:37:13] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -45.535909 | E_var:     0.1964 | E_err:   0.006925
[2025-10-06 12:37:19] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -45.524738 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 12:37:24] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -45.517118 | E_var:     0.3001 | E_err:   0.008559
[2025-10-06 12:37:29] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -45.526793 | E_var:     0.1845 | E_err:   0.006711
[2025-10-06 12:37:34] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -45.518109 | E_var:     0.2379 | E_err:   0.007621
[2025-10-06 12:37:39] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -45.529853 | E_var:     0.1866 | E_err:   0.006749
[2025-10-06 12:37:44] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -45.515575 | E_var:     0.1959 | E_err:   0.006916
[2025-10-06 12:37:50] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -45.531350 | E_var:     0.2218 | E_err:   0.007359
[2025-10-06 12:37:55] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -45.524348 | E_var:     0.1782 | E_err:   0.006596
[2025-10-06 12:38:00] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -45.522899 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 12:38:00] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 12:38:05] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -45.509814 | E_var:     0.2304 | E_err:   0.007500
[2025-10-06 12:38:10] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -45.518408 | E_var:     0.2352 | E_err:   0.007578
[2025-10-06 12:38:15] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -45.533803 | E_var:     0.2245 | E_err:   0.007404
[2025-10-06 12:38:21] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -45.534757 | E_var:     0.1920 | E_err:   0.006846
[2025-10-06 12:38:26] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -45.532834 | E_var:     0.2149 | E_err:   0.007244
[2025-10-06 12:38:31] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -45.523375 | E_var:     0.2187 | E_err:   0.007307
[2025-10-06 12:38:36] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -45.522770 | E_var:     0.2146 | E_err:   0.007239
[2025-10-06 12:38:41] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -45.529829 | E_var:     0.1943 | E_err:   0.006888
[2025-10-06 12:38:46] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -45.535439 | E_var:     0.1825 | E_err:   0.006675
[2025-10-06 12:38:52] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -45.543268 | E_var:     0.2564 | E_err:   0.007912
[2025-10-06 12:38:57] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -45.521914 | E_var:     0.1949 | E_err:   0.006898
[2025-10-06 12:39:02] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -45.539563 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 12:39:07] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -45.530846 | E_var:     0.1750 | E_err:   0.006537
[2025-10-06 12:39:12] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -45.528388 | E_var:     0.2218 | E_err:   0.007359
[2025-10-06 12:39:17] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -45.512431 | E_var:     0.2014 | E_err:   0.007012
[2025-10-06 12:39:22] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -45.524072 | E_var:     0.2946 | E_err:   0.008480
[2025-10-06 12:39:28] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -45.522738 | E_var:     0.2172 | E_err:   0.007282
[2025-10-06 12:39:33] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -45.522074 | E_var:     0.2091 | E_err:   0.007145
[2025-10-06 12:39:38] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -45.517318 | E_var:     0.2410 | E_err:   0.007670
[2025-10-06 12:39:43] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -45.524509 | E_var:     0.2101 | E_err:   0.007162
[2025-10-06 12:39:48] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -45.522603 | E_var:     0.5534 | E_err:   0.011623
[2025-10-06 12:39:53] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -45.506625 | E_var:     0.2232 | E_err:   0.007381
[2025-10-06 12:39:58] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -45.514971 | E_var:     0.2300 | E_err:   0.007494
[2025-10-06 12:40:04] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -45.532828 | E_var:     0.2174 | E_err:   0.007286
[2025-10-06 12:40:09] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -45.514549 | E_var:     0.1821 | E_err:   0.006668
[2025-10-06 12:40:14] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -45.522361 | E_var:     0.1897 | E_err:   0.006805
[2025-10-06 12:40:19] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -45.527409 | E_var:     0.1833 | E_err:   0.006690
[2025-10-06 12:40:24] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -45.532358 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 12:40:29] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -45.519529 | E_var:     0.2400 | E_err:   0.007655
[2025-10-06 12:40:35] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -45.523641 | E_var:     0.2245 | E_err:   0.007403
[2025-10-06 12:40:40] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -45.510486 | E_var:     0.2260 | E_err:   0.007428
[2025-10-06 12:40:45] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -45.522002 | E_var:     0.1767 | E_err:   0.006568
[2025-10-06 12:40:50] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -45.522138 | E_var:     0.2269 | E_err:   0.007443
[2025-10-06 12:40:55] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -45.515289 | E_var:     0.3320 | E_err:   0.009002
[2025-10-06 12:41:00] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -45.526381 | E_var:     0.2263 | E_err:   0.007433
[2025-10-06 12:41:05] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -45.530346 | E_var:     0.1856 | E_err:   0.006732
[2025-10-06 12:41:11] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -45.518546 | E_var:     0.2062 | E_err:   0.007095
[2025-10-06 12:41:16] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -45.541838 | E_var:     0.2994 | E_err:   0.008550
[2025-10-06 12:41:21] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -45.528438 | E_var:     0.1849 | E_err:   0.006718
[2025-10-06 12:41:26] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -45.527046 | E_var:     0.1689 | E_err:   0.006422
[2025-10-06 12:41:31] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -45.539067 | E_var:     0.1936 | E_err:   0.006875
[2025-10-06 12:41:36] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -45.526249 | E_var:     0.2487 | E_err:   0.007793
[2025-10-06 12:41:42] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -45.525361 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 12:41:47] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -45.519083 | E_var:     0.1838 | E_err:   0.006698
[2025-10-06 12:41:52] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -45.519542 | E_var:     0.1846 | E_err:   0.006713
[2025-10-06 12:41:57] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -45.529642 | E_var:     0.1977 | E_err:   0.006948
[2025-10-06 12:42:02] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -45.522744 | E_var:     0.1719 | E_err:   0.006478
[2025-10-06 12:42:07] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -45.531730 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 12:42:12] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -45.526447 | E_var:     0.2141 | E_err:   0.007230
[2025-10-06 12:42:18] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -45.527208 | E_var:     0.1834 | E_err:   0.006691
[2025-10-06 12:42:23] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -45.526024 | E_var:     0.2204 | E_err:   0.007335
[2025-10-06 12:42:28] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -45.522448 | E_var:     0.2663 | E_err:   0.008063
[2025-10-06 12:42:33] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -45.524042 | E_var:     0.2635 | E_err:   0.008020
[2025-10-06 12:42:38] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -45.529823 | E_var:     0.7055 | E_err:   0.013124
[2025-10-06 12:42:43] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -45.521032 | E_var:     0.2363 | E_err:   0.007596
[2025-10-06 12:42:49] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -45.515443 | E_var:     0.2136 | E_err:   0.007221
[2025-10-06 12:42:54] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -45.527311 | E_var:     0.2495 | E_err:   0.007804
[2025-10-06 12:42:59] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -45.515803 | E_var:     0.2362 | E_err:   0.007594
[2025-10-06 12:43:04] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -45.525450 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 12:43:09] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -45.528292 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 12:43:14] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -45.529063 | E_var:     0.2847 | E_err:   0.008338
[2025-10-06 12:43:19] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -45.524512 | E_var:     0.2348 | E_err:   0.007571
[2025-10-06 12:43:25] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -45.516889 | E_var:     0.2187 | E_err:   0.007308
[2025-10-06 12:43:30] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -45.528873 | E_var:     0.1877 | E_err:   0.006770
[2025-10-06 12:43:35] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -45.516510 | E_var:     0.2484 | E_err:   0.007787
[2025-10-06 12:43:40] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -45.520483 | E_var:     0.1881 | E_err:   0.006777
[2025-10-06 12:43:45] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -45.512691 | E_var:     0.2613 | E_err:   0.007988
[2025-10-06 12:43:50] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -45.530060 | E_var:     0.2169 | E_err:   0.007277
[2025-10-06 12:43:56] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -45.525011 | E_var:     0.2023 | E_err:   0.007027
[2025-10-06 12:44:01] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -45.516893 | E_var:     0.2623 | E_err:   0.008003
[2025-10-06 12:44:06] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -45.522263 | E_var:     0.3081 | E_err:   0.008674
[2025-10-06 12:44:11] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -45.527272 | E_var:     0.2656 | E_err:   0.008052
[2025-10-06 12:44:16] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -45.522571 | E_var:     0.2598 | E_err:   0.007965
[2025-10-06 12:44:21] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -45.524368 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 12:44:27] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -45.515504 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 12:44:32] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -45.527501 | E_var:     0.1841 | E_err:   0.006704
[2025-10-06 12:44:37] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -45.513194 | E_var:     0.2181 | E_err:   0.007297
[2025-10-06 12:44:42] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -45.537465 | E_var:     0.2120 | E_err:   0.007195
[2025-10-06 12:44:47] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -45.529645 | E_var:     0.2192 | E_err:   0.007315
[2025-10-06 12:44:52] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -45.523841 | E_var:     0.2405 | E_err:   0.007662
[2025-10-06 12:44:57] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -45.522550 | E_var:     0.2235 | E_err:   0.007387
[2025-10-06 12:45:03] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -45.526100 | E_var:     0.2048 | E_err:   0.007072
[2025-10-06 12:45:08] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -45.521658 | E_var:     0.2544 | E_err:   0.007881
[2025-10-06 12:45:13] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -45.527040 | E_var:     0.2133 | E_err:   0.007217
[2025-10-06 12:45:18] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -45.515195 | E_var:     0.1811 | E_err:   0.006650
[2025-10-06 12:45:23] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -45.518255 | E_var:     0.1981 | E_err:   0.006955
[2025-10-06 12:45:28] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -45.532727 | E_var:     0.2141 | E_err:   0.007229
[2025-10-06 12:45:33] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -45.517230 | E_var:     0.2073 | E_err:   0.007113
[2025-10-06 12:45:39] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -45.528503 | E_var:     0.2229 | E_err:   0.007378
[2025-10-06 12:45:44] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -45.519313 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 12:45:49] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -45.521133 | E_var:     0.2273 | E_err:   0.007449
[2025-10-06 12:45:54] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -45.522635 | E_var:     0.2089 | E_err:   0.007142
[2025-10-06 12:45:59] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -45.521073 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 12:46:04] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -45.535475 | E_var:     0.2612 | E_err:   0.007986
[2025-10-06 12:46:10] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -45.528696 | E_var:     0.2217 | E_err:   0.007358
[2025-10-06 12:46:15] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -45.517283 | E_var:     0.1928 | E_err:   0.006860
[2025-10-06 12:46:20] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -45.517744 | E_var:     0.2080 | E_err:   0.007125
[2025-10-06 12:46:25] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -45.522687 | E_var:     0.3033 | E_err:   0.008606
[2025-10-06 12:46:30] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -45.522297 | E_var:     0.1751 | E_err:   0.006539
[2025-10-06 12:46:35] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -45.524355 | E_var:     0.2094 | E_err:   0.007150
[2025-10-06 12:46:35] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 12:46:41] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -45.516641 | E_var:     0.3494 | E_err:   0.009236
[2025-10-06 12:46:46] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -45.523720 | E_var:     0.1902 | E_err:   0.006814
[2025-10-06 12:46:51] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -45.525987 | E_var:     0.2229 | E_err:   0.007377
[2025-10-06 12:46:56] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -45.526438 | E_var:     0.1843 | E_err:   0.006708
[2025-10-06 12:47:01] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -45.520076 | E_var:     0.2267 | E_err:   0.007440
[2025-10-06 12:47:06] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -45.527930 | E_var:     0.2078 | E_err:   0.007122
[2025-10-06 12:47:12] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -45.533841 | E_var:     0.1830 | E_err:   0.006684
[2025-10-06 12:47:17] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -45.538453 | E_var:     0.8555 | E_err:   0.014452
[2025-10-06 12:47:22] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -45.520270 | E_var:     0.3385 | E_err:   0.009091
[2025-10-06 12:47:27] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -45.521955 | E_var:     0.1781 | E_err:   0.006594
[2025-10-06 12:47:32] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -45.526905 | E_var:     0.4714 | E_err:   0.010728
[2025-10-06 12:47:37] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -45.518280 | E_var:     0.2221 | E_err:   0.007364
[2025-10-06 12:47:42] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -45.518641 | E_var:     0.2356 | E_err:   0.007584
[2025-10-06 12:47:48] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -45.535411 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 12:47:53] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -45.521067 | E_var:     0.1711 | E_err:   0.006462
[2025-10-06 12:47:58] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -45.527518 | E_var:     0.1834 | E_err:   0.006692
[2025-10-06 12:48:03] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -45.540640 | E_var:     0.4845 | E_err:   0.010876
[2025-10-06 12:48:08] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -45.521523 | E_var:     0.1944 | E_err:   0.006890
[2025-10-06 12:48:13] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -45.521282 | E_var:     0.2329 | E_err:   0.007541
[2025-10-06 12:48:19] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -45.529541 | E_var:     0.2316 | E_err:   0.007520
[2025-10-06 12:48:24] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -45.516103 | E_var:     0.1848 | E_err:   0.006717
[2025-10-06 12:48:29] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -45.527327 | E_var:     0.2272 | E_err:   0.007447
[2025-10-06 12:48:34] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -45.530055 | E_var:     0.2125 | E_err:   0.007203
[2025-10-06 12:48:39] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -45.526907 | E_var:     0.2462 | E_err:   0.007753
[2025-10-06 12:48:44] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -45.538732 | E_var:     0.1894 | E_err:   0.006799
[2025-10-06 12:48:49] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -45.520242 | E_var:     0.3164 | E_err:   0.008788
[2025-10-06 12:48:55] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -45.533084 | E_var:     0.1865 | E_err:   0.006747
[2025-10-06 12:49:00] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -45.539253 | E_var:     0.2229 | E_err:   0.007376
[2025-10-06 12:49:05] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -45.523509 | E_var:     0.1996 | E_err:   0.006980
[2025-10-06 12:49:10] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -45.528066 | E_var:     0.1966 | E_err:   0.006929
[2025-10-06 12:49:15] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -45.527565 | E_var:     0.1642 | E_err:   0.006332
[2025-10-06 12:49:20] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -45.525155 | E_var:     0.2130 | E_err:   0.007211
[2025-10-06 12:49:26] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -45.513518 | E_var:     0.2229 | E_err:   0.007377
[2025-10-06 12:49:31] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -45.519945 | E_var:     0.3043 | E_err:   0.008619
[2025-10-06 12:49:36] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -45.520593 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 12:49:41] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -45.520633 | E_var:     0.1954 | E_err:   0.006908
[2025-10-06 12:49:46] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -45.524578 | E_var:     0.2051 | E_err:   0.007076
[2025-10-06 12:49:51] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -45.512902 | E_var:     0.1888 | E_err:   0.006790
[2025-10-06 12:49:56] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -45.523133 | E_var:     0.2469 | E_err:   0.007764
[2025-10-06 12:50:02] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -45.532303 | E_var:     0.1785 | E_err:   0.006601
[2025-10-06 12:50:07] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -45.516293 | E_var:     0.3430 | E_err:   0.009151
[2025-10-06 12:50:12] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -45.515537 | E_var:     0.2177 | E_err:   0.007290
[2025-10-06 12:50:17] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -45.516623 | E_var:     0.2153 | E_err:   0.007251
[2025-10-06 12:50:22] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -45.527724 | E_var:     0.1939 | E_err:   0.006880
[2025-10-06 12:50:28] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -45.521695 | E_var:     0.2261 | E_err:   0.007429
[2025-10-06 12:50:33] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -45.527870 | E_var:     0.2631 | E_err:   0.008015
[2025-10-06 12:50:38] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -45.529156 | E_var:     0.2336 | E_err:   0.007552
[2025-10-06 12:50:43] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -45.530237 | E_var:     0.2531 | E_err:   0.007861
[2025-10-06 12:50:48] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -45.537482 | E_var:     0.2642 | E_err:   0.008031
[2025-10-06 12:50:53] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -45.521245 | E_var:     0.1915 | E_err:   0.006838
[2025-10-06 12:50:53] 🔄 RESTART #2 | Period: 600
[2025-10-06 12:50:58] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -45.526318 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 12:51:04] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -45.522500 | E_var:     0.3041 | E_err:   0.008616
[2025-10-06 12:51:09] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -45.522386 | E_var:     0.2056 | E_err:   0.007086
[2025-10-06 12:51:14] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -45.512092 | E_var:     0.1897 | E_err:   0.006805
[2025-10-06 12:51:19] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -45.523117 | E_var:     0.1947 | E_err:   0.006895
[2025-10-06 12:51:24] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -45.540584 | E_var:     0.2151 | E_err:   0.007247
[2025-10-06 12:51:29] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -45.530345 | E_var:     0.1600 | E_err:   0.006250
[2025-10-06 12:51:35] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -45.517137 | E_var:     0.3065 | E_err:   0.008650
[2025-10-06 12:51:40] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -45.518999 | E_var:     0.1962 | E_err:   0.006921
[2025-10-06 12:51:45] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -45.530724 | E_var:     0.1927 | E_err:   0.006858
[2025-10-06 12:51:50] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -45.515557 | E_var:     0.1901 | E_err:   0.006813
[2025-10-06 12:51:55] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -45.529178 | E_var:     0.1670 | E_err:   0.006386
[2025-10-06 12:52:00] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -45.528554 | E_var:     0.2506 | E_err:   0.007822
[2025-10-06 12:52:05] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -45.527887 | E_var:     0.2087 | E_err:   0.007138
[2025-10-06 12:52:11] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -45.525660 | E_var:     0.1590 | E_err:   0.006230
[2025-10-06 12:52:16] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -45.524298 | E_var:     0.2197 | E_err:   0.007323
[2025-10-06 12:52:21] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -45.529474 | E_var:     0.1906 | E_err:   0.006821
[2025-10-06 12:52:26] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -45.518560 | E_var:     0.2640 | E_err:   0.008029
[2025-10-06 12:52:31] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -45.523242 | E_var:     0.2135 | E_err:   0.007220
[2025-10-06 12:52:36] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -45.539930 | E_var:     0.3754 | E_err:   0.009573
[2025-10-06 12:52:41] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -45.533735 | E_var:     0.2405 | E_err:   0.007662
[2025-10-06 12:52:47] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -45.518989 | E_var:     0.1882 | E_err:   0.006778
[2025-10-06 12:52:52] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -45.513787 | E_var:     0.2158 | E_err:   0.007259
[2025-10-06 12:52:57] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -45.518500 | E_var:     0.2059 | E_err:   0.007090
[2025-10-06 12:53:02] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -45.524239 | E_var:     0.2904 | E_err:   0.008420
[2025-10-06 12:53:07] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -45.516035 | E_var:     0.1885 | E_err:   0.006784
[2025-10-06 12:53:12] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -45.524321 | E_var:     0.2040 | E_err:   0.007058
[2025-10-06 12:53:18] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -45.531965 | E_var:     0.1672 | E_err:   0.006389
[2025-10-06 12:53:23] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -45.525414 | E_var:     0.2414 | E_err:   0.007676
[2025-10-06 12:53:28] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -45.522931 | E_var:     0.2559 | E_err:   0.007904
[2025-10-06 12:53:33] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -45.524361 | E_var:     0.2445 | E_err:   0.007726
[2025-10-06 12:53:38] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -45.518630 | E_var:     0.2132 | E_err:   0.007215
[2025-10-06 12:53:43] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -45.518384 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 12:53:48] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -45.532343 | E_var:     0.1896 | E_err:   0.006803
[2025-10-06 12:53:54] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -45.535279 | E_var:     0.4611 | E_err:   0.010610
[2025-10-06 12:53:59] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -45.525310 | E_var:     0.2005 | E_err:   0.006997
[2025-10-06 12:54:04] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -45.507421 | E_var:     0.2120 | E_err:   0.007194
[2025-10-06 12:54:09] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -45.522860 | E_var:     0.1984 | E_err:   0.006959
[2025-10-06 12:54:14] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -45.525455 | E_var:     0.1762 | E_err:   0.006558
[2025-10-06 12:54:19] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -45.527268 | E_var:     0.2319 | E_err:   0.007524
[2025-10-06 12:54:25] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -45.520725 | E_var:     0.2137 | E_err:   0.007223
[2025-10-06 12:54:30] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -45.511266 | E_var:     0.2154 | E_err:   0.007251
[2025-10-06 12:54:35] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -45.519996 | E_var:     0.2221 | E_err:   0.007363
[2025-10-06 12:54:40] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -45.505661 | E_var:     0.5019 | E_err:   0.011069
[2025-10-06 12:54:45] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -45.519163 | E_var:     0.2978 | E_err:   0.008526
[2025-10-06 12:54:50] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -45.532128 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 12:54:55] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -45.524453 | E_var:     0.2259 | E_err:   0.007426
[2025-10-06 12:55:01] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -45.517058 | E_var:     0.2198 | E_err:   0.007326
[2025-10-06 12:55:06] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -45.535494 | E_var:     0.2015 | E_err:   0.007015
[2025-10-06 12:55:11] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -45.527605 | E_var:     0.2162 | E_err:   0.007265
[2025-10-06 12:55:11] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 12:55:16] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -45.533591 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 12:55:21] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -45.518169 | E_var:     0.2297 | E_err:   0.007489
[2025-10-06 12:55:26] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -45.523713 | E_var:     0.2234 | E_err:   0.007386
[2025-10-06 12:55:32] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -45.525286 | E_var:     0.1968 | E_err:   0.006931
[2025-10-06 12:55:37] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -45.533550 | E_var:     0.2179 | E_err:   0.007294
[2025-10-06 12:55:42] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -45.520777 | E_var:     0.2307 | E_err:   0.007505
[2025-10-06 12:55:47] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -45.526660 | E_var:     0.2556 | E_err:   0.007899
[2025-10-06 12:55:52] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -45.522331 | E_var:     0.1902 | E_err:   0.006815
[2025-10-06 12:55:57] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -45.536234 | E_var:     0.2577 | E_err:   0.007932
[2025-10-06 12:56:03] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -45.522721 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 12:56:08] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -45.513506 | E_var:     0.2089 | E_err:   0.007141
[2025-10-06 12:56:13] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -45.528455 | E_var:     0.2272 | E_err:   0.007447
[2025-10-06 12:56:18] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -45.531692 | E_var:     0.4618 | E_err:   0.010619
[2025-10-06 12:56:23] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -45.513414 | E_var:     0.2431 | E_err:   0.007703
[2025-10-06 12:56:28] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -45.529400 | E_var:     0.1910 | E_err:   0.006828
[2025-10-06 12:56:33] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -45.540008 | E_var:     0.2124 | E_err:   0.007202
[2025-10-06 12:56:39] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -45.532913 | E_var:     0.1965 | E_err:   0.006925
[2025-10-06 12:56:44] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -45.505068 | E_var:     0.3075 | E_err:   0.008664
[2025-10-06 12:56:49] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -45.530372 | E_var:     0.1625 | E_err:   0.006298
[2025-10-06 12:56:54] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -45.511524 | E_var:     0.2406 | E_err:   0.007665
[2025-10-06 12:56:59] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -45.530331 | E_var:     0.2064 | E_err:   0.007098
[2025-10-06 12:57:04] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -45.527847 | E_var:     0.2321 | E_err:   0.007527
[2025-10-06 12:57:10] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -45.533596 | E_var:     0.3287 | E_err:   0.008958
[2025-10-06 12:57:15] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -45.521886 | E_var:     0.3219 | E_err:   0.008865
[2025-10-06 12:57:20] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -45.511518 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 12:57:25] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -45.528406 | E_var:     0.1657 | E_err:   0.006360
[2025-10-06 12:57:30] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -45.515248 | E_var:     0.2249 | E_err:   0.007409
[2025-10-06 12:57:35] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -45.529086 | E_var:     0.1979 | E_err:   0.006951
[2025-10-06 12:57:41] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -45.536721 | E_var:     0.2063 | E_err:   0.007097
[2025-10-06 12:57:46] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -45.519986 | E_var:     0.2037 | E_err:   0.007052
[2025-10-06 12:57:51] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -45.527658 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 12:57:56] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -45.529878 | E_var:     0.2038 | E_err:   0.007054
[2025-10-06 12:58:01] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -45.519035 | E_var:     0.2293 | E_err:   0.007482
[2025-10-06 12:58:06] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -45.508085 | E_var:     0.6201 | E_err:   0.012305
[2025-10-06 12:58:12] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -45.525831 | E_var:     0.1833 | E_err:   0.006691
[2025-10-06 12:58:17] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -45.523582 | E_var:     0.1793 | E_err:   0.006617
[2025-10-06 12:58:22] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -45.512814 | E_var:     0.2389 | E_err:   0.007637
[2025-10-06 12:58:27] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -45.523690 | E_var:     0.2384 | E_err:   0.007628
[2025-10-06 12:58:32] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -45.515727 | E_var:     0.2585 | E_err:   0.007944
[2025-10-06 12:58:37] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -45.521673 | E_var:     0.1683 | E_err:   0.006411
[2025-10-06 12:58:42] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -45.529481 | E_var:     0.1953 | E_err:   0.006905
[2025-10-06 12:58:48] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -45.527697 | E_var:     0.2055 | E_err:   0.007084
[2025-10-06 12:58:53] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -45.521416 | E_var:     0.1901 | E_err:   0.006813
[2025-10-06 12:58:58] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -45.524948 | E_var:     0.1917 | E_err:   0.006842
[2025-10-06 12:59:03] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -45.531345 | E_var:     0.1838 | E_err:   0.006699
[2025-10-06 12:59:08] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -45.534910 | E_var:     0.2080 | E_err:   0.007126
[2025-10-06 12:59:13] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -45.532410 | E_var:     0.3035 | E_err:   0.008609
[2025-10-06 12:59:18] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -45.521008 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 12:59:24] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -45.525692 | E_var:     0.1926 | E_err:   0.006856
[2025-10-06 12:59:29] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -45.528313 | E_var:     0.2147 | E_err:   0.007240
[2025-10-06 12:59:34] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -45.509825 | E_var:     0.2025 | E_err:   0.007030
[2025-10-06 12:59:39] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -45.520042 | E_var:     0.2216 | E_err:   0.007355
[2025-10-06 12:59:44] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -45.531877 | E_var:     0.2053 | E_err:   0.007079
[2025-10-06 12:59:49] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -45.527687 | E_var:     0.2699 | E_err:   0.008118
[2025-10-06 12:59:55] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -45.525157 | E_var:     0.1868 | E_err:   0.006753
[2025-10-06 13:00:00] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -45.522084 | E_var:     0.2903 | E_err:   0.008418
[2025-10-06 13:00:05] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -45.524026 | E_var:     0.2102 | E_err:   0.007165
[2025-10-06 13:00:10] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -45.524137 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 13:00:15] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -45.518570 | E_var:     0.2655 | E_err:   0.008050
[2025-10-06 13:00:20] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -45.525276 | E_var:     0.1942 | E_err:   0.006886
[2025-10-06 13:00:26] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -45.529722 | E_var:     0.1849 | E_err:   0.006718
[2025-10-06 13:00:31] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -45.541383 | E_var:     0.1918 | E_err:   0.006844
[2025-10-06 13:00:36] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -45.534596 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 13:00:41] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -45.522577 | E_var:     0.2124 | E_err:   0.007201
[2025-10-06 13:00:46] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -45.514541 | E_var:     0.2127 | E_err:   0.007206
[2025-10-06 13:00:51] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -45.519926 | E_var:     0.2188 | E_err:   0.007308
[2025-10-06 13:00:56] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -45.523503 | E_var:     0.1948 | E_err:   0.006896
[2025-10-06 13:01:02] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -45.518460 | E_var:     0.1868 | E_err:   0.006752
[2025-10-06 13:01:07] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -45.539053 | E_var:     0.1717 | E_err:   0.006474
[2025-10-06 13:01:12] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -45.527728 | E_var:     0.1966 | E_err:   0.006928
[2025-10-06 13:01:17] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -45.518915 | E_var:     0.4811 | E_err:   0.010838
[2025-10-06 13:01:22] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -45.517707 | E_var:     0.2077 | E_err:   0.007121
[2025-10-06 13:01:27] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -45.517780 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 13:01:33] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -45.531308 | E_var:     0.2225 | E_err:   0.007371
[2025-10-06 13:01:38] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -45.527245 | E_var:     0.2442 | E_err:   0.007721
[2025-10-06 13:01:43] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -45.535011 | E_var:     0.4092 | E_err:   0.009995
[2025-10-06 13:01:48] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -45.528836 | E_var:     0.2319 | E_err:   0.007524
[2025-10-06 13:01:53] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -45.515012 | E_var:     0.1903 | E_err:   0.006815
[2025-10-06 13:01:58] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -45.531599 | E_var:     0.1693 | E_err:   0.006428
[2025-10-06 13:02:03] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -45.514236 | E_var:     0.2095 | E_err:   0.007152
[2025-10-06 13:02:09] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -45.518920 | E_var:     0.2038 | E_err:   0.007055
[2025-10-06 13:02:14] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -45.519778 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 13:02:19] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -45.528282 | E_var:     0.2178 | E_err:   0.007292
[2025-10-06 13:02:24] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -45.526956 | E_var:     0.1879 | E_err:   0.006774
[2025-10-06 13:02:29] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -45.540145 | E_var:     0.1889 | E_err:   0.006791
[2025-10-06 13:02:34] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -45.508505 | E_var:     0.2338 | E_err:   0.007555
[2025-10-06 13:02:40] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -45.513517 | E_var:     0.2452 | E_err:   0.007736
[2025-10-06 13:02:45] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -45.532409 | E_var:     0.1792 | E_err:   0.006615
[2025-10-06 13:02:50] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -45.521856 | E_var:     0.2118 | E_err:   0.007191
[2025-10-06 13:02:55] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -45.522727 | E_var:     0.1832 | E_err:   0.006687
[2025-10-06 13:03:00] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -45.516807 | E_var:     0.3121 | E_err:   0.008729
[2025-10-06 13:03:05] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -45.511765 | E_var:     0.1457 | E_err:   0.005965
[2025-10-06 13:03:10] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -45.524498 | E_var:     0.1718 | E_err:   0.006477
[2025-10-06 13:03:16] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -45.532010 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 13:03:21] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -45.518580 | E_var:     0.2164 | E_err:   0.007268
[2025-10-06 13:03:26] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -45.526289 | E_var:     0.2541 | E_err:   0.007876
[2025-10-06 13:03:31] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -45.536249 | E_var:     0.2918 | E_err:   0.008440
[2025-10-06 13:03:36] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -45.521737 | E_var:     0.2753 | E_err:   0.008198
[2025-10-06 13:03:41] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -45.527875 | E_var:     0.2876 | E_err:   0.008379
[2025-10-06 13:03:47] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -45.519744 | E_var:     0.4667 | E_err:   0.010675
[2025-10-06 13:03:47] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 13:03:52] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -45.517244 | E_var:     0.1884 | E_err:   0.006782
[2025-10-06 13:03:57] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -45.521691 | E_var:     0.1921 | E_err:   0.006849
[2025-10-06 13:04:02] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -45.513538 | E_var:     0.1979 | E_err:   0.006952
[2025-10-06 13:04:07] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -45.521958 | E_var:     0.2201 | E_err:   0.007330
[2025-10-06 13:04:12] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -45.521378 | E_var:     0.2303 | E_err:   0.007498
[2025-10-06 13:04:18] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -45.514086 | E_var:     0.1974 | E_err:   0.006943
[2025-10-06 13:04:23] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -45.537181 | E_var:     0.2001 | E_err:   0.006990
[2025-10-06 13:04:28] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -45.522965 | E_var:     0.2470 | E_err:   0.007766
[2025-10-06 13:04:33] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -45.526036 | E_var:     0.2024 | E_err:   0.007030
[2025-10-06 13:04:38] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -45.542887 | E_var:     0.1818 | E_err:   0.006662
[2025-10-06 13:04:43] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -45.528237 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 13:04:48] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -45.521545 | E_var:     0.2248 | E_err:   0.007408
[2025-10-06 13:04:54] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -45.533223 | E_var:     0.1769 | E_err:   0.006572
[2025-10-06 13:04:59] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -45.525209 | E_var:     0.1851 | E_err:   0.006722
[2025-10-06 13:05:04] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -45.529478 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 13:05:09] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -45.526519 | E_var:     0.1905 | E_err:   0.006821
[2025-10-06 13:05:14] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -45.523695 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 13:05:19] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -45.524937 | E_var:     0.2495 | E_err:   0.007805
[2025-10-06 13:05:25] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -45.514607 | E_var:     0.2136 | E_err:   0.007221
[2025-10-06 13:05:30] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -45.536659 | E_var:     0.2981 | E_err:   0.008531
[2025-10-06 13:05:35] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -45.527587 | E_var:     0.2176 | E_err:   0.007289
[2025-10-06 13:05:40] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -45.529548 | E_var:     0.1952 | E_err:   0.006903
[2025-10-06 13:05:45] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -45.515606 | E_var:     0.2033 | E_err:   0.007046
[2025-10-06 13:05:50] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -45.524305 | E_var:     0.1827 | E_err:   0.006678
[2025-10-06 13:05:55] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -45.526951 | E_var:     0.2169 | E_err:   0.007276
[2025-10-06 13:06:01] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -45.538197 | E_var:     0.1914 | E_err:   0.006835
[2025-10-06 13:06:06] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -45.517694 | E_var:     0.2167 | E_err:   0.007274
[2025-10-06 13:06:11] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -45.511388 | E_var:     0.2380 | E_err:   0.007622
[2025-10-06 13:06:16] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -45.527865 | E_var:     0.1975 | E_err:   0.006944
[2025-10-06 13:06:21] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -45.517473 | E_var:     0.2276 | E_err:   0.007455
[2025-10-06 13:06:26] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -45.531723 | E_var:     0.2122 | E_err:   0.007197
[2025-10-06 13:06:32] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -45.520624 | E_var:     0.1688 | E_err:   0.006419
[2025-10-06 13:06:37] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -45.521598 | E_var:     0.2506 | E_err:   0.007822
[2025-10-06 13:06:42] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -45.521294 | E_var:     0.1960 | E_err:   0.006917
[2025-10-06 13:06:47] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -45.532441 | E_var:     0.1873 | E_err:   0.006761
[2025-10-06 13:06:52] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -45.510946 | E_var:     0.1831 | E_err:   0.006686
[2025-10-06 13:06:57] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -45.524387 | E_var:     0.1893 | E_err:   0.006799
[2025-10-06 13:07:02] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -45.528775 | E_var:     0.2474 | E_err:   0.007772
[2025-10-06 13:07:08] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -45.520375 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 13:07:13] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -45.522634 | E_var:     0.2336 | E_err:   0.007551
[2025-10-06 13:07:18] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -45.528140 | E_var:     0.1663 | E_err:   0.006372
[2025-10-06 13:07:23] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -45.530168 | E_var:     0.2517 | E_err:   0.007840
[2025-10-06 13:07:28] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -45.523817 | E_var:     0.1737 | E_err:   0.006512
[2025-10-06 13:07:33] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -45.519508 | E_var:     0.2160 | E_err:   0.007262
[2025-10-06 13:07:38] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -45.507594 | E_var:     0.4901 | E_err:   0.010939
[2025-10-06 13:07:44] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -45.525112 | E_var:     0.8774 | E_err:   0.014636
[2025-10-06 13:07:49] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -45.521082 | E_var:     0.2168 | E_err:   0.007275
[2025-10-06 13:07:54] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -45.536075 | E_var:     0.3285 | E_err:   0.008955
[2025-10-06 13:07:59] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -45.521706 | E_var:     0.5215 | E_err:   0.011284
[2025-10-06 13:08:04] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -45.503122 | E_var:     0.4283 | E_err:   0.010225
[2025-10-06 13:08:09] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -45.528030 | E_var:     0.1740 | E_err:   0.006518
[2025-10-06 13:08:15] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -45.517821 | E_var:     0.2095 | E_err:   0.007151
[2025-10-06 13:08:20] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -45.524026 | E_var:     0.1893 | E_err:   0.006798
[2025-10-06 13:08:25] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -45.519883 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 13:08:30] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -45.527884 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 13:08:35] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -45.513077 | E_var:     0.2141 | E_err:   0.007229
[2025-10-06 13:08:40] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -45.523488 | E_var:     0.4799 | E_err:   0.010824
[2025-10-06 13:08:45] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -45.524704 | E_var:     0.2883 | E_err:   0.008390
[2025-10-06 13:08:51] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -45.533538 | E_var:     0.1710 | E_err:   0.006461
[2025-10-06 13:08:56] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -45.525757 | E_var:     0.1661 | E_err:   0.006369
[2025-10-06 13:09:01] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -45.523690 | E_var:     0.1991 | E_err:   0.006973
[2025-10-06 13:09:06] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -45.531626 | E_var:     0.2187 | E_err:   0.007307
[2025-10-06 13:09:11] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -45.520731 | E_var:     0.2006 | E_err:   0.006998
[2025-10-06 13:09:16] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -45.525506 | E_var:     0.1926 | E_err:   0.006857
[2025-10-06 13:09:22] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -45.513890 | E_var:     0.1826 | E_err:   0.006677
[2025-10-06 13:09:27] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -45.519827 | E_var:     0.2472 | E_err:   0.007769
[2025-10-06 13:09:32] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -45.520571 | E_var:     0.2053 | E_err:   0.007080
[2025-10-06 13:09:37] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -45.526906 | E_var:     0.2098 | E_err:   0.007157
[2025-10-06 13:09:42] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -45.513860 | E_var:     0.1711 | E_err:   0.006462
[2025-10-06 13:09:47] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -45.504703 | E_var:     0.3760 | E_err:   0.009581
[2025-10-06 13:09:52] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -45.519050 | E_var:     0.2193 | E_err:   0.007318
[2025-10-06 13:09:58] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -45.525537 | E_var:     0.1849 | E_err:   0.006719
[2025-10-06 13:10:03] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -45.539609 | E_var:     0.1949 | E_err:   0.006897
[2025-10-06 13:10:08] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -45.526656 | E_var:     0.2023 | E_err:   0.007028
[2025-10-06 13:10:13] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -45.528572 | E_var:     0.2434 | E_err:   0.007709
[2025-10-06 13:10:18] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -45.530005 | E_var:     0.2233 | E_err:   0.007384
[2025-10-06 13:10:23] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -45.531594 | E_var:     0.2178 | E_err:   0.007292
[2025-10-06 13:10:28] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -45.524947 | E_var:     0.2299 | E_err:   0.007492
[2025-10-06 13:10:34] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -45.533552 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 13:10:39] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -45.513027 | E_var:     0.2067 | E_err:   0.007103
[2025-10-06 13:10:44] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -45.520566 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 13:10:49] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -45.522529 | E_var:     0.1880 | E_err:   0.006775
[2025-10-06 13:10:54] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -45.512782 | E_var:     0.2027 | E_err:   0.007034
[2025-10-06 13:10:59] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -45.530317 | E_var:     0.2091 | E_err:   0.007144
[2025-10-06 13:11:05] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -45.501469 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 13:11:10] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -45.544478 | E_var:     0.2551 | E_err:   0.007891
[2025-10-06 13:11:15] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -45.535160 | E_var:     0.1841 | E_err:   0.006704
[2025-10-06 13:11:20] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -45.527736 | E_var:     0.2080 | E_err:   0.007126
[2025-10-06 13:11:25] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -45.503185 | E_var:     1.0472 | E_err:   0.015990
[2025-10-06 13:11:30] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -45.530915 | E_var:     0.2442 | E_err:   0.007721
[2025-10-06 13:11:35] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -45.519622 | E_var:     0.1914 | E_err:   0.006836
[2025-10-06 13:11:41] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -45.525841 | E_var:     0.3113 | E_err:   0.008718
[2025-10-06 13:11:46] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -45.521029 | E_var:     0.2492 | E_err:   0.007801
[2025-10-06 13:11:51] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -45.534440 | E_var:     0.2110 | E_err:   0.007178
[2025-10-06 13:11:56] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -45.523172 | E_var:     0.2008 | E_err:   0.007002
[2025-10-06 13:12:01] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -45.537728 | E_var:     0.1856 | E_err:   0.006732
[2025-10-06 13:12:06] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -45.526731 | E_var:     0.2020 | E_err:   0.007023
[2025-10-06 13:12:12] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -45.532872 | E_var:     0.3725 | E_err:   0.009537
[2025-10-06 13:12:17] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -45.531092 | E_var:     0.1810 | E_err:   0.006648
[2025-10-06 13:12:22] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -45.524912 | E_var:     0.2965 | E_err:   0.008508
[2025-10-06 13:12:22] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 13:12:27] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -45.538373 | E_var:     0.2155 | E_err:   0.007254
[2025-10-06 13:12:32] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -45.509968 | E_var:     0.2322 | E_err:   0.007530
[2025-10-06 13:12:37] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -45.524952 | E_var:     0.2341 | E_err:   0.007560
[2025-10-06 13:12:43] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -45.534876 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 13:12:48] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -45.523857 | E_var:     0.1771 | E_err:   0.006575
[2025-10-06 13:12:53] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -45.525755 | E_var:     0.2452 | E_err:   0.007737
[2025-10-06 13:12:58] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -45.516093 | E_var:     0.1887 | E_err:   0.006788
[2025-10-06 13:13:03] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -45.512509 | E_var:     0.2398 | E_err:   0.007651
[2025-10-06 13:13:08] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -45.522782 | E_var:     0.1935 | E_err:   0.006874
[2025-10-06 13:13:13] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -45.525315 | E_var:     0.2260 | E_err:   0.007429
[2025-10-06 13:13:19] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -45.518413 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 13:13:24] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -45.518791 | E_var:     0.2076 | E_err:   0.007120
[2025-10-06 13:13:29] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -45.517039 | E_var:     0.3449 | E_err:   0.009176
[2025-10-06 13:13:34] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -45.525900 | E_var:     0.1984 | E_err:   0.006959
[2025-10-06 13:13:39] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -45.536977 | E_var:     0.1735 | E_err:   0.006508
[2025-10-06 13:13:44] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -45.532654 | E_var:     0.3437 | E_err:   0.009160
[2025-10-06 13:13:50] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -45.526521 | E_var:     0.1785 | E_err:   0.006601
[2025-10-06 13:13:55] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -45.531369 | E_var:     0.2215 | E_err:   0.007354
[2025-10-06 13:14:00] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -45.513127 | E_var:     0.2129 | E_err:   0.007209
[2025-10-06 13:14:05] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -45.526832 | E_var:     0.2808 | E_err:   0.008280
[2025-10-06 13:14:10] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -45.533659 | E_var:     0.2457 | E_err:   0.007745
[2025-10-06 13:14:15] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -45.533048 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 13:14:20] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -45.509175 | E_var:     0.1969 | E_err:   0.006933
[2025-10-06 13:14:26] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -45.523340 | E_var:     0.2337 | E_err:   0.007553
[2025-10-06 13:14:31] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -45.531087 | E_var:     0.1599 | E_err:   0.006249
[2025-10-06 13:14:36] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -45.536282 | E_var:     0.2865 | E_err:   0.008363
[2025-10-06 13:14:41] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -45.529213 | E_var:     0.2391 | E_err:   0.007641
[2025-10-06 13:14:46] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -45.534935 | E_var:     0.2215 | E_err:   0.007354
[2025-10-06 13:14:51] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -45.526870 | E_var:     0.1924 | E_err:   0.006853
[2025-10-06 13:14:57] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -45.521389 | E_var:     0.1962 | E_err:   0.006921
[2025-10-06 13:15:02] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -45.525729 | E_var:     0.2147 | E_err:   0.007241
[2025-10-06 13:15:07] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -45.518245 | E_var:     0.2095 | E_err:   0.007152
[2025-10-06 13:15:12] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -45.514826 | E_var:     0.1961 | E_err:   0.006920
[2025-10-06 13:15:17] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -45.543330 | E_var:     0.3038 | E_err:   0.008612
[2025-10-06 13:15:22] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -45.520083 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 13:15:27] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -45.514226 | E_var:     0.2165 | E_err:   0.007271
[2025-10-06 13:15:33] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -45.523353 | E_var:     0.1551 | E_err:   0.006153
[2025-10-06 13:15:38] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -45.517905 | E_var:     0.1735 | E_err:   0.006509
[2025-10-06 13:15:43] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -45.532592 | E_var:     0.3746 | E_err:   0.009564
[2025-10-06 13:15:48] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -45.531681 | E_var:     0.1710 | E_err:   0.006462
[2025-10-06 13:15:53] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -45.537214 | E_var:     0.1862 | E_err:   0.006743
[2025-10-06 13:15:58] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -45.529847 | E_var:     0.2249 | E_err:   0.007410
[2025-10-06 13:16:04] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -45.519162 | E_var:     0.2540 | E_err:   0.007875
[2025-10-06 13:16:09] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -45.535142 | E_var:     0.3118 | E_err:   0.008725
[2025-10-06 13:16:14] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -45.527038 | E_var:     0.2039 | E_err:   0.007056
[2025-10-06 13:16:19] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -45.533162 | E_var:     0.3945 | E_err:   0.009814
[2025-10-06 13:16:24] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -45.528825 | E_var:     0.2292 | E_err:   0.007481
[2025-10-06 13:16:29] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -45.530405 | E_var:     0.1804 | E_err:   0.006637
[2025-10-06 13:16:34] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -45.531414 | E_var:     0.1664 | E_err:   0.006374
[2025-10-06 13:16:40] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -45.536556 | E_var:     0.2020 | E_err:   0.007023
[2025-10-06 13:16:45] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -45.533936 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 13:16:50] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -45.515495 | E_var:     0.2435 | E_err:   0.007711
[2025-10-06 13:16:55] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -45.516595 | E_var:     0.1788 | E_err:   0.006608
[2025-10-06 13:17:00] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -45.512678 | E_var:     0.2274 | E_err:   0.007452
[2025-10-06 13:17:05] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -45.527804 | E_var:     0.2039 | E_err:   0.007056
[2025-10-06 13:17:11] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -45.520089 | E_var:     0.2385 | E_err:   0.007631
[2025-10-06 13:17:16] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -45.511170 | E_var:     0.2373 | E_err:   0.007611
[2025-10-06 13:17:21] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -45.528191 | E_var:     0.1912 | E_err:   0.006832
[2025-10-06 13:17:26] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -45.522145 | E_var:     0.1879 | E_err:   0.006774
[2025-10-06 13:17:31] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -45.530287 | E_var:     0.1862 | E_err:   0.006741
[2025-10-06 13:17:36] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -45.539440 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 13:17:41] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -45.520657 | E_var:     0.2293 | E_err:   0.007482
[2025-10-06 13:17:47] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -45.523073 | E_var:     0.2148 | E_err:   0.007241
[2025-10-06 13:17:52] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -45.518308 | E_var:     0.1595 | E_err:   0.006241
[2025-10-06 13:17:57] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -45.515341 | E_var:     0.1847 | E_err:   0.006716
[2025-10-06 13:18:02] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -45.530538 | E_var:     0.2074 | E_err:   0.007115
[2025-10-06 13:18:07] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -45.530236 | E_var:     0.1978 | E_err:   0.006950
[2025-10-06 13:18:12] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -45.531899 | E_var:     0.1987 | E_err:   0.006966
[2025-10-06 13:18:18] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -45.527676 | E_var:     0.2019 | E_err:   0.007021
[2025-10-06 13:18:23] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -45.524265 | E_var:     0.1683 | E_err:   0.006411
[2025-10-06 13:18:28] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -45.529641 | E_var:     0.1778 | E_err:   0.006589
[2025-10-06 13:18:33] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -45.526943 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 13:18:38] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -45.532807 | E_var:     0.3282 | E_err:   0.008951
[2025-10-06 13:18:43] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -45.530234 | E_var:     0.1941 | E_err:   0.006883
[2025-10-06 13:18:48] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -45.527533 | E_var:     0.1862 | E_err:   0.006743
[2025-10-06 13:18:54] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -45.539688 | E_var:     0.2098 | E_err:   0.007157
[2025-10-06 13:18:59] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -45.519505 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 13:19:04] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -45.533401 | E_var:     0.2948 | E_err:   0.008484
[2025-10-06 13:19:09] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -45.517211 | E_var:     0.2257 | E_err:   0.007423
[2025-10-06 13:19:14] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -45.523598 | E_var:     0.2300 | E_err:   0.007494
[2025-10-06 13:19:19] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -45.537947 | E_var:     0.2394 | E_err:   0.007646
[2025-10-06 13:19:25] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -45.517131 | E_var:     0.1867 | E_err:   0.006751
[2025-10-06 13:19:30] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -45.531577 | E_var:     0.1761 | E_err:   0.006557
[2025-10-06 13:19:35] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -45.522138 | E_var:     0.2116 | E_err:   0.007187
[2025-10-06 13:19:40] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -45.523781 | E_var:     0.2111 | E_err:   0.007179
[2025-10-06 13:19:45] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -45.527451 | E_var:     0.2162 | E_err:   0.007265
[2025-10-06 13:19:50] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -45.512029 | E_var:     0.2027 | E_err:   0.007035
[2025-10-06 13:19:55] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -45.513312 | E_var:     0.1964 | E_err:   0.006924
[2025-10-06 13:20:01] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -45.522275 | E_var:     0.1799 | E_err:   0.006627
[2025-10-06 13:20:06] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -45.521513 | E_var:     0.1883 | E_err:   0.006781
[2025-10-06 13:20:11] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -45.527811 | E_var:     0.1760 | E_err:   0.006554
[2025-10-06 13:20:16] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -45.515918 | E_var:     0.2136 | E_err:   0.007222
[2025-10-06 13:20:21] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -45.531341 | E_var:     0.1924 | E_err:   0.006853
[2025-10-06 13:20:26] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -45.516813 | E_var:     0.1974 | E_err:   0.006943
[2025-10-06 13:20:32] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -45.521531 | E_var:     0.2181 | E_err:   0.007297
[2025-10-06 13:20:37] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -45.532361 | E_var:     0.2262 | E_err:   0.007432
[2025-10-06 13:20:42] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -45.527926 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 13:20:47] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -45.527694 | E_var:     0.1835 | E_err:   0.006693
[2025-10-06 13:20:52] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -45.510509 | E_var:     0.2519 | E_err:   0.007843
[2025-10-06 13:20:57] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -45.522443 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 13:20:57] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 13:21:03] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -45.517883 | E_var:     0.1858 | E_err:   0.006736
[2025-10-06 13:21:08] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -45.530137 | E_var:     0.2319 | E_err:   0.007524
[2025-10-06 13:21:13] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -45.527913 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 13:21:18] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -45.520665 | E_var:     0.1895 | E_err:   0.006802
[2025-10-06 13:21:23] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -45.508214 | E_var:     0.1817 | E_err:   0.006660
[2025-10-06 13:21:28] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -45.512669 | E_var:     0.2439 | E_err:   0.007716
[2025-10-06 13:21:33] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -45.526026 | E_var:     0.2655 | E_err:   0.008052
[2025-10-06 13:21:39] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -45.526219 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 13:21:44] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -45.524732 | E_var:     0.2260 | E_err:   0.007428
[2025-10-06 13:21:49] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -45.536083 | E_var:     0.1888 | E_err:   0.006789
[2025-10-06 13:21:54] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -45.530483 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 13:21:59] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -45.536135 | E_var:     0.2221 | E_err:   0.007363
[2025-10-06 13:22:04] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -45.525747 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 13:22:10] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -45.534024 | E_var:     0.2134 | E_err:   0.007217
[2025-10-06 13:22:15] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -45.524588 | E_var:     0.2469 | E_err:   0.007764
[2025-10-06 13:22:20] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -45.525733 | E_var:     0.2140 | E_err:   0.007228
[2025-10-06 13:22:25] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -45.525147 | E_var:     0.1649 | E_err:   0.006345
[2025-10-06 13:22:30] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -45.547043 | E_var:     0.2812 | E_err:   0.008286
[2025-10-06 13:22:35] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -45.512201 | E_var:     0.2136 | E_err:   0.007222
[2025-10-06 13:22:40] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -45.526391 | E_var:     0.2276 | E_err:   0.007455
[2025-10-06 13:22:46] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -45.517248 | E_var:     0.2264 | E_err:   0.007435
[2025-10-06 13:22:51] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -45.522527 | E_var:     0.1966 | E_err:   0.006929
[2025-10-06 13:22:56] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -45.534128 | E_var:     0.1864 | E_err:   0.006746
[2025-10-06 13:23:01] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -45.526530 | E_var:     0.2130 | E_err:   0.007210
[2025-10-06 13:23:06] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -45.524617 | E_var:     0.1957 | E_err:   0.006912
[2025-10-06 13:23:11] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -45.511949 | E_var:     0.2204 | E_err:   0.007335
[2025-10-06 13:23:17] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -45.520828 | E_var:     0.1960 | E_err:   0.006917
[2025-10-06 13:23:22] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -45.530609 | E_var:     0.2956 | E_err:   0.008495
[2025-10-06 13:23:27] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -45.528286 | E_var:     0.2046 | E_err:   0.007068
[2025-10-06 13:23:32] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -45.525158 | E_var:     0.2218 | E_err:   0.007359
[2025-10-06 13:23:37] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -45.528820 | E_var:     0.1589 | E_err:   0.006228
[2025-10-06 13:23:42] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -45.535544 | E_var:     0.1800 | E_err:   0.006629
[2025-10-06 13:23:48] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -45.529600 | E_var:     0.2253 | E_err:   0.007416
[2025-10-06 13:23:53] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -45.535958 | E_var:     0.1996 | E_err:   0.006981
[2025-10-06 13:23:58] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -45.527551 | E_var:     0.1883 | E_err:   0.006781
[2025-10-06 13:24:03] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -45.520591 | E_var:     0.2160 | E_err:   0.007262
[2025-10-06 13:24:08] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -45.533657 | E_var:     0.1939 | E_err:   0.006880
[2025-10-06 13:24:13] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -45.521793 | E_var:     0.3968 | E_err:   0.009842
[2025-10-06 13:24:18] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -45.527690 | E_var:     0.1930 | E_err:   0.006864
[2025-10-06 13:24:24] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -45.518920 | E_var:     0.9314 | E_err:   0.015079
[2025-10-06 13:24:29] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -45.528838 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 13:24:34] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -45.520720 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 13:24:39] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -45.530899 | E_var:     0.3574 | E_err:   0.009341
[2025-10-06 13:24:44] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -45.526320 | E_var:     0.2314 | E_err:   0.007516
[2025-10-06 13:24:49] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -45.525816 | E_var:     0.2338 | E_err:   0.007554
[2025-10-06 13:24:55] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -45.523475 | E_var:     0.2362 | E_err:   0.007594
[2025-10-06 13:25:00] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -45.529734 | E_var:     0.1903 | E_err:   0.006816
[2025-10-06 13:25:05] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -45.525563 | E_var:     0.2204 | E_err:   0.007335
[2025-10-06 13:25:10] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -45.520562 | E_var:     0.1656 | E_err:   0.006359
[2025-10-06 13:25:15] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -45.524102 | E_var:     0.1899 | E_err:   0.006809
[2025-10-06 13:25:20] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -45.525440 | E_var:     0.2381 | E_err:   0.007624
[2025-10-06 13:25:25] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -45.517002 | E_var:     0.2406 | E_err:   0.007663
[2025-10-06 13:25:31] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -45.527680 | E_var:     0.2860 | E_err:   0.008356
[2025-10-06 13:25:36] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -45.530768 | E_var:     0.1786 | E_err:   0.006603
[2025-10-06 13:25:41] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -45.523952 | E_var:     0.1948 | E_err:   0.006896
[2025-10-06 13:25:46] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -45.534151 | E_var:     0.2448 | E_err:   0.007731
[2025-10-06 13:25:51] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -45.524589 | E_var:     0.2170 | E_err:   0.007278
[2025-10-06 13:25:56] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -45.530931 | E_var:     0.1690 | E_err:   0.006423
[2025-10-06 13:26:02] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -45.538127 | E_var:     0.2086 | E_err:   0.007137
[2025-10-06 13:26:07] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -45.520511 | E_var:     0.2456 | E_err:   0.007743
[2025-10-06 13:26:12] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -45.525158 | E_var:     0.2416 | E_err:   0.007680
[2025-10-06 13:26:17] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -45.522251 | E_var:     0.2566 | E_err:   0.007915
[2025-10-06 13:26:22] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -45.515781 | E_var:     0.2300 | E_err:   0.007493
[2025-10-06 13:26:27] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -45.529293 | E_var:     0.2879 | E_err:   0.008384
[2025-10-06 13:26:32] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -45.529669 | E_var:     0.2121 | E_err:   0.007195
[2025-10-06 13:26:38] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -45.532688 | E_var:     0.2094 | E_err:   0.007150
[2025-10-06 13:26:43] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -45.525732 | E_var:     0.2216 | E_err:   0.007355
[2025-10-06 13:26:48] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -45.520365 | E_var:     0.2365 | E_err:   0.007598
[2025-10-06 13:26:53] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -45.534095 | E_var:     0.1860 | E_err:   0.006739
[2025-10-06 13:26:58] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -45.521145 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 13:27:03] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -45.522418 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 13:27:09] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -45.528041 | E_var:     0.1992 | E_err:   0.006974
[2025-10-06 13:27:14] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -45.518163 | E_var:     0.2413 | E_err:   0.007676
[2025-10-06 13:27:19] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -45.517258 | E_var:     0.1834 | E_err:   0.006691
[2025-10-06 13:27:24] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -45.525816 | E_var:     0.3080 | E_err:   0.008671
[2025-10-06 13:27:29] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -45.523589 | E_var:     0.2117 | E_err:   0.007189
[2025-10-06 13:27:34] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -45.533407 | E_var:     0.2004 | E_err:   0.006995
[2025-10-06 13:27:40] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -45.527974 | E_var:     0.1938 | E_err:   0.006879
[2025-10-06 13:27:45] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -45.513633 | E_var:     0.2297 | E_err:   0.007488
[2025-10-06 13:27:50] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -45.538101 | E_var:     0.2108 | E_err:   0.007173
[2025-10-06 13:27:55] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -45.529969 | E_var:     0.1974 | E_err:   0.006941
[2025-10-06 13:28:00] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -45.518142 | E_var:     0.2026 | E_err:   0.007033
[2025-10-06 13:28:05] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -45.517619 | E_var:     0.2415 | E_err:   0.007678
[2025-10-06 13:28:10] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -45.535464 | E_var:     0.2080 | E_err:   0.007126
[2025-10-06 13:28:16] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -45.530003 | E_var:     0.1924 | E_err:   0.006854
[2025-10-06 13:28:21] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -45.532462 | E_var:     0.1931 | E_err:   0.006867
[2025-10-06 13:28:26] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -45.527228 | E_var:     0.2075 | E_err:   0.007117
[2025-10-06 13:28:31] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -45.508887 | E_var:     1.0729 | E_err:   0.016184
[2025-10-06 13:28:36] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -45.527918 | E_var:     0.1865 | E_err:   0.006747
[2025-10-06 13:28:41] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -45.520823 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 13:28:47] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -45.519698 | E_var:     0.2482 | E_err:   0.007784
[2025-10-06 13:28:52] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -45.520987 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 13:28:57] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -45.534039 | E_var:     0.4238 | E_err:   0.010172
[2025-10-06 13:29:02] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -45.529103 | E_var:     0.2371 | E_err:   0.007607
[2025-10-06 13:29:07] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -45.517831 | E_var:     0.1897 | E_err:   0.006806
[2025-10-06 13:29:12] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -45.525801 | E_var:     0.2167 | E_err:   0.007274
[2025-10-06 13:29:17] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -45.528373 | E_var:     0.1632 | E_err:   0.006312
[2025-10-06 13:29:23] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -45.521010 | E_var:     0.2179 | E_err:   0.007293
[2025-10-06 13:29:28] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -45.531630 | E_var:     0.2218 | E_err:   0.007359
[2025-10-06 13:29:33] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -45.522716 | E_var:     0.1910 | E_err:   0.006828
[2025-10-06 13:29:33] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 13:29:38] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -45.517172 | E_var:     0.1930 | E_err:   0.006864
[2025-10-06 13:29:43] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -45.517116 | E_var:     0.2121 | E_err:   0.007195
[2025-10-06 13:29:48] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -45.529559 | E_var:     0.1932 | E_err:   0.006868
[2025-10-06 13:29:54] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -45.524089 | E_var:     0.1912 | E_err:   0.006832
[2025-10-06 13:29:59] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -45.531950 | E_var:     0.2387 | E_err:   0.007634
[2025-10-06 13:30:04] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -45.528345 | E_var:     0.2055 | E_err:   0.007084
[2025-10-06 13:30:09] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -45.512895 | E_var:     0.1846 | E_err:   0.006714
[2025-10-06 13:30:14] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -45.528061 | E_var:     0.2164 | E_err:   0.007268
[2025-10-06 13:30:19] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -45.529686 | E_var:     0.2207 | E_err:   0.007340
[2025-10-06 13:30:24] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -45.519735 | E_var:     0.2404 | E_err:   0.007662
[2025-10-06 13:30:30] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -45.526757 | E_var:     0.2071 | E_err:   0.007111
[2025-10-06 13:30:35] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -45.513776 | E_var:     0.2452 | E_err:   0.007737
[2025-10-06 13:30:40] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -45.533253 | E_var:     0.1901 | E_err:   0.006813
[2025-10-06 13:30:45] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -45.524953 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 13:30:50] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -45.516454 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 13:30:55] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -45.527250 | E_var:     0.2413 | E_err:   0.007676
[2025-10-06 13:31:01] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -45.521503 | E_var:     0.2053 | E_err:   0.007080
[2025-10-06 13:31:06] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -45.526120 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 13:31:11] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -45.526003 | E_var:     0.1953 | E_err:   0.006904
[2025-10-06 13:31:16] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -45.529111 | E_var:     0.1899 | E_err:   0.006810
[2025-10-06 13:31:21] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -45.518885 | E_var:     0.2181 | E_err:   0.007297
[2025-10-06 13:31:26] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -45.526645 | E_var:     0.2329 | E_err:   0.007541
[2025-10-06 13:31:31] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -45.529070 | E_var:     0.2175 | E_err:   0.007286
[2025-10-06 13:31:37] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -45.532323 | E_var:     0.3454 | E_err:   0.009183
[2025-10-06 13:31:42] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -45.521115 | E_var:     0.2413 | E_err:   0.007675
[2025-10-06 13:31:47] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -45.533104 | E_var:     0.2177 | E_err:   0.007291
[2025-10-06 13:31:52] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -45.516496 | E_var:     0.2324 | E_err:   0.007532
[2025-10-06 13:31:57] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -45.531448 | E_var:     0.1850 | E_err:   0.006721
[2025-10-06 13:32:02] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -45.505060 | E_var:     0.1868 | E_err:   0.006754
[2025-10-06 13:32:08] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -45.535744 | E_var:     0.1940 | E_err:   0.006881
[2025-10-06 13:32:13] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -45.522041 | E_var:     0.2065 | E_err:   0.007101
[2025-10-06 13:32:18] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -45.524867 | E_var:     0.2411 | E_err:   0.007671
[2025-10-06 13:32:23] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -45.533231 | E_var:     0.2079 | E_err:   0.007124
[2025-10-06 13:32:28] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -45.530968 | E_var:     0.1996 | E_err:   0.006981
[2025-10-06 13:32:33] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -45.516181 | E_var:     0.2306 | E_err:   0.007504
[2025-10-06 13:32:38] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -45.538717 | E_var:     0.2268 | E_err:   0.007440
[2025-10-06 13:32:44] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -45.519843 | E_var:     0.1645 | E_err:   0.006338
[2025-10-06 13:32:49] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -45.525954 | E_var:     0.1944 | E_err:   0.006890
[2025-10-06 13:32:54] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -45.519267 | E_var:     0.1635 | E_err:   0.006317
[2025-10-06 13:32:59] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -45.516418 | E_var:     0.2450 | E_err:   0.007734
[2025-10-06 13:33:04] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -45.521609 | E_var:     0.1992 | E_err:   0.006974
[2025-10-06 13:33:09] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -45.528837 | E_var:     0.3449 | E_err:   0.009176
[2025-10-06 13:33:15] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -45.527130 | E_var:     0.2640 | E_err:   0.008028
[2025-10-06 13:33:20] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -45.526337 | E_var:     0.1705 | E_err:   0.006451
[2025-10-06 13:33:25] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -45.518398 | E_var:     0.2646 | E_err:   0.008037
[2025-10-06 13:33:30] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -45.526452 | E_var:     0.1711 | E_err:   0.006464
[2025-10-06 13:33:35] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -45.526099 | E_var:     0.2375 | E_err:   0.007614
[2025-10-06 13:33:40] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -45.533553 | E_var:     0.2745 | E_err:   0.008186
[2025-10-06 13:33:46] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -45.524778 | E_var:     0.2290 | E_err:   0.007478
[2025-10-06 13:33:51] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -45.528522 | E_var:     0.2619 | E_err:   0.007997
[2025-10-06 13:33:56] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -45.521097 | E_var:     0.2173 | E_err:   0.007284
[2025-10-06 13:34:01] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -45.522444 | E_var:     0.2266 | E_err:   0.007438
[2025-10-06 13:34:06] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -45.530128 | E_var:     0.2477 | E_err:   0.007777
[2025-10-06 13:34:11] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -45.534365 | E_var:     0.1863 | E_err:   0.006745
[2025-10-06 13:34:16] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -45.531559 | E_var:     0.1960 | E_err:   0.006917
[2025-10-06 13:34:22] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -45.520478 | E_var:     0.1922 | E_err:   0.006850
[2025-10-06 13:34:27] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -45.522370 | E_var:     0.1945 | E_err:   0.006890
[2025-10-06 13:34:32] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -45.532659 | E_var:     0.1960 | E_err:   0.006918
[2025-10-06 13:34:37] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -45.519992 | E_var:     0.2375 | E_err:   0.007615
[2025-10-06 13:34:42] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -45.544171 | E_var:     0.4436 | E_err:   0.010407
[2025-10-06 13:34:47] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -45.527572 | E_var:     0.1795 | E_err:   0.006620
[2025-10-06 13:34:53] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -45.528922 | E_var:     0.2252 | E_err:   0.007416
[2025-10-06 13:34:58] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -45.527281 | E_var:     0.1889 | E_err:   0.006791
[2025-10-06 13:35:03] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -45.523541 | E_var:     0.2431 | E_err:   0.007703
[2025-10-06 13:35:08] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -45.537510 | E_var:     0.1845 | E_err:   0.006712
[2025-10-06 13:35:13] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -45.523954 | E_var:     0.2163 | E_err:   0.007266
[2025-10-06 13:35:18] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -45.527202 | E_var:     0.2234 | E_err:   0.007386
[2025-10-06 13:35:23] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -45.523602 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 13:35:29] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -45.511858 | E_var:     0.1800 | E_err:   0.006629
[2025-10-06 13:35:34] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -45.531180 | E_var:     0.1942 | E_err:   0.006886
[2025-10-06 13:35:39] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -45.521726 | E_var:     0.2016 | E_err:   0.007016
[2025-10-06 13:35:44] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -45.531088 | E_var:     0.1850 | E_err:   0.006720
[2025-10-06 13:35:49] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -45.525072 | E_var:     0.3093 | E_err:   0.008690
[2025-10-06 13:35:54] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -45.530530 | E_var:     0.1962 | E_err:   0.006921
[2025-10-06 13:36:00] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -45.521762 | E_var:     0.1763 | E_err:   0.006561
[2025-10-06 13:36:05] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -45.516328 | E_var:     0.2298 | E_err:   0.007490
[2025-10-06 13:36:10] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -45.520262 | E_var:     0.2210 | E_err:   0.007345
[2025-10-06 13:36:15] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -45.538134 | E_var:     0.2566 | E_err:   0.007915
[2025-10-06 13:36:20] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -45.527631 | E_var:     0.2161 | E_err:   0.007263
[2025-10-06 13:36:25] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -45.526495 | E_var:     0.2242 | E_err:   0.007398
[2025-10-06 13:36:30] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -45.533556 | E_var:     0.3265 | E_err:   0.008928
[2025-10-06 13:36:36] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -45.532043 | E_var:     0.2323 | E_err:   0.007530
[2025-10-06 13:36:41] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -45.521783 | E_var:     0.1763 | E_err:   0.006561
[2025-10-06 13:36:46] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -45.520459 | E_var:     0.1901 | E_err:   0.006813
[2025-10-06 13:36:51] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -45.528375 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 13:36:56] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -45.523432 | E_var:     0.1970 | E_err:   0.006934
[2025-10-06 13:37:01] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -45.530787 | E_var:     0.2161 | E_err:   0.007264
[2025-10-06 13:37:07] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -45.544506 | E_var:     0.1963 | E_err:   0.006923
[2025-10-06 13:37:12] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -45.516434 | E_var:     0.2047 | E_err:   0.007070
[2025-10-06 13:37:17] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -45.527795 | E_var:     0.1978 | E_err:   0.006949
[2025-10-06 13:37:22] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -45.531580 | E_var:     0.1870 | E_err:   0.006757
[2025-10-06 13:37:27] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -45.510301 | E_var:     0.2070 | E_err:   0.007109
[2025-10-06 13:37:32] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -45.530593 | E_var:     0.2265 | E_err:   0.007435
[2025-10-06 13:37:37] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -45.532990 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 13:37:43] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -45.524927 | E_var:     0.2474 | E_err:   0.007772
[2025-10-06 13:37:48] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -45.532377 | E_var:     0.1892 | E_err:   0.006797
[2025-10-06 13:37:53] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -45.529319 | E_var:     0.2213 | E_err:   0.007350
[2025-10-06 13:37:58] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -45.515214 | E_var:     0.2308 | E_err:   0.007507
[2025-10-06 13:38:03] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -45.516283 | E_var:     0.2155 | E_err:   0.007253
[2025-10-06 13:38:08] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -45.521304 | E_var:     0.1725 | E_err:   0.006490
[2025-10-06 13:38:08] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 13:38:14] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -45.524705 | E_var:     0.2139 | E_err:   0.007226
[2025-10-06 13:38:19] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -45.523938 | E_var:     0.2533 | E_err:   0.007864
[2025-10-06 13:38:24] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -45.518447 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 13:38:29] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -45.519905 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 13:38:34] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -45.519885 | E_var:     0.1919 | E_err:   0.006846
[2025-10-06 13:38:39] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -45.531951 | E_var:     0.1879 | E_err:   0.006774
[2025-10-06 13:38:44] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -45.520235 | E_var:     0.1777 | E_err:   0.006586
[2025-10-06 13:38:50] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -45.517990 | E_var:     0.2201 | E_err:   0.007330
[2025-10-06 13:38:55] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -45.523573 | E_var:     0.2089 | E_err:   0.007141
[2025-10-06 13:39:00] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -45.520904 | E_var:     0.2115 | E_err:   0.007185
[2025-10-06 13:39:05] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -45.525820 | E_var:     0.2392 | E_err:   0.007643
[2025-10-06 13:39:10] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -45.515058 | E_var:     0.3040 | E_err:   0.008615
[2025-10-06 13:39:15] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -45.537825 | E_var:     0.2798 | E_err:   0.008266
[2025-10-06 13:39:21] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -45.533917 | E_var:     0.2209 | E_err:   0.007344
[2025-10-06 13:39:26] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -45.518488 | E_var:     0.1855 | E_err:   0.006730
[2025-10-06 13:39:31] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -45.517702 | E_var:     0.2367 | E_err:   0.007602
[2025-10-06 13:39:36] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -45.526795 | E_var:     0.1709 | E_err:   0.006460
[2025-10-06 13:39:41] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -45.507392 | E_var:     0.2855 | E_err:   0.008349
[2025-10-06 13:39:46] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -45.519775 | E_var:     0.2200 | E_err:   0.007328
[2025-10-06 13:39:52] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -45.517436 | E_var:     0.2304 | E_err:   0.007501
[2025-10-06 13:39:57] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -45.525444 | E_var:     0.2062 | E_err:   0.007095
[2025-10-06 13:40:02] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -45.531743 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 13:40:07] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -45.519865 | E_var:     0.1652 | E_err:   0.006352
[2025-10-06 13:40:12] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -45.535091 | E_var:     0.1742 | E_err:   0.006521
[2025-10-06 13:40:17] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -45.532870 | E_var:     0.1696 | E_err:   0.006436
[2025-10-06 13:40:23] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -45.516749 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 13:40:28] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -45.524484 | E_var:     0.1745 | E_err:   0.006526
[2025-10-06 13:40:33] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -45.514542 | E_var:     0.2041 | E_err:   0.007059
[2025-10-06 13:40:38] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -45.534603 | E_var:     0.2240 | E_err:   0.007396
[2025-10-06 13:40:43] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -45.526741 | E_var:     0.1739 | E_err:   0.006515
[2025-10-06 13:40:48] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -45.534314 | E_var:     0.2160 | E_err:   0.007261
[2025-10-06 13:40:53] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -45.523447 | E_var:     0.1964 | E_err:   0.006924
[2025-10-06 13:40:59] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -45.523121 | E_var:     0.1934 | E_err:   0.006871
[2025-10-06 13:41:04] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -45.512951 | E_var:     0.3591 | E_err:   0.009363
[2025-10-06 13:41:09] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -45.525949 | E_var:     0.1913 | E_err:   0.006834
[2025-10-06 13:41:14] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -45.516316 | E_var:     0.1947 | E_err:   0.006894
[2025-10-06 13:41:19] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -45.516203 | E_var:     0.2260 | E_err:   0.007427
[2025-10-06 13:41:24] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -45.514210 | E_var:     0.2131 | E_err:   0.007213
[2025-10-06 13:41:29] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -45.525026 | E_var:     0.2210 | E_err:   0.007346
[2025-10-06 13:41:35] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -45.518081 | E_var:     0.1858 | E_err:   0.006736
[2025-10-06 13:41:40] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -45.530226 | E_var:     0.2035 | E_err:   0.007049
[2025-10-06 13:41:45] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -45.531815 | E_var:     0.1632 | E_err:   0.006311
[2025-10-06 13:41:50] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -45.524689 | E_var:     0.1871 | E_err:   0.006759
[2025-10-06 13:41:55] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -45.530351 | E_var:     0.2337 | E_err:   0.007554
[2025-10-06 13:42:00] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -45.520751 | E_var:     0.2145 | E_err:   0.007236
[2025-10-06 13:42:06] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -45.526566 | E_var:     0.1935 | E_err:   0.006874
[2025-10-06 13:42:11] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -45.531052 | E_var:     0.2063 | E_err:   0.007098
[2025-10-06 13:42:16] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -45.528208 | E_var:     0.1933 | E_err:   0.006870
[2025-10-06 13:42:21] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -45.534569 | E_var:     0.2195 | E_err:   0.007320
[2025-10-06 13:42:26] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -45.513471 | E_var:     0.2091 | E_err:   0.007145
[2025-10-06 13:42:26] ======================================================================================================
[2025-10-06 13:42:26] ✅ Training completed successfully
[2025-10-06 13:42:26] Total restarts: 2
[2025-10-06 13:42:28] Final Energy: -45.51347095 ± 0.00714477
[2025-10-06 13:42:28] Final Variance: 0.209091
[2025-10-06 13:42:28] ======================================================================================================
[2025-10-06 13:42:28] ======================================================================================================
[2025-10-06 13:42:28] Training completed | Runtime: 5457.2s
[2025-10-06 13:42:30] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 13:42:30] ======================================================================================================
