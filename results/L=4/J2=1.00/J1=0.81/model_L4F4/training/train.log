[2025-10-06 03:10:52] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.80/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 03:10:52]   - 迭代次数: final
[2025-10-06 03:10:52]   - 能量: -28.768129-0.003554j ± 0.006820, Var: 0.190538
[2025-10-06 03:10:52]   - 时间戳: 2025-10-06T03:10:39.964964+08:00
[2025-10-06 03:11:07] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 03:11:07] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 03:11:07] ======================================================================================================
[2025-10-06 03:11:07] GCNN for Shastry-Sutherland Model
[2025-10-06 03:11:07] ======================================================================================================
[2025-10-06 03:11:07] System parameters:
[2025-10-06 03:11:07]   - System size: L=4, N=64
[2025-10-06 03:11:07]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-10-06 03:11:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 03:11:07] Model parameters:
[2025-10-06 03:11:07]   - Number of layers = 4
[2025-10-06 03:11:07]   - Number of features = 4
[2025-10-06 03:11:07]   - Total parameters = 12572
[2025-10-06 03:11:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 03:11:07] Training parameters:
[2025-10-06 03:11:07]   - Total iterations: 1050
[2025-10-06 03:11:07]   - Annealing cycles: 3
[2025-10-06 03:11:07]   - Initial period: 150
[2025-10-06 03:11:07]   - Period multiplier: 2.0
[2025-10-06 03:11:07]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 03:11:07]   - Samples: 4096
[2025-10-06 03:11:07]   - Discarded samples: 0
[2025-10-06 03:11:07]   - Chunk size: 4096
[2025-10-06 03:11:07]   - Diagonal shift: 0.15
[2025-10-06 03:11:07]   - Gradient clipping: 1.0
[2025-10-06 03:11:07]   - Checkpoint enabled: interval=100
[2025-10-06 03:11:07]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.81/model_L4F4/training/checkpoints
[2025-10-06 03:11:07] ------------------------------------------------------------------------------------------------------
[2025-10-06 03:11:07] Device status:
[2025-10-06 03:11:07]   - Devices model: NVIDIA H200 NVL
[2025-10-06 03:11:07]   - Number of devices: 1
[2025-10-06 03:11:07]   - Sharding: True
[2025-10-06 03:11:08] ======================================================================================================
[2025-10-06 03:11:42] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -29.191590 | E_var:     0.2946 | E_err:   0.008481
[2025-10-06 03:12:01] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -29.178880 | E_var:     0.1930 | E_err:   0.006865
[2025-10-06 03:12:04] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -29.172483 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 03:12:06] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -29.180104 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 03:12:09] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -29.172551 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 03:12:11] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -29.184486 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 03:12:14] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -29.182910 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 03:12:16] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -29.178648 | E_var:     0.1128 | E_err:   0.005249
[2025-10-06 03:12:18] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -29.176628 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 03:12:21] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -29.185276 | E_var:     0.0944 | E_err:   0.004802
[2025-10-06 03:12:23] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -29.188617 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 03:12:26] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -29.180378 | E_var:     0.0970 | E_err:   0.004866
[2025-10-06 03:12:28] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -29.187057 | E_var:     0.1013 | E_err:   0.004972
[2025-10-06 03:12:30] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -29.183175 | E_var:     0.0901 | E_err:   0.004689
[2025-10-06 03:12:33] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -29.185298 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 03:12:35] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -29.180910 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 03:12:38] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -29.177513 | E_var:     0.1413 | E_err:   0.005874
[2025-10-06 03:12:40] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -29.181707 | E_var:     0.1744 | E_err:   0.006525
[2025-10-06 03:12:43] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -29.173360 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 03:12:45] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -29.166833 | E_var:     0.1339 | E_err:   0.005718
[2025-10-06 03:12:48] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -29.184470 | E_var:     0.0957 | E_err:   0.004834
[2025-10-06 03:12:50] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -29.191018 | E_var:     0.1010 | E_err:   0.004967
[2025-10-06 03:12:52] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -29.182457 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 03:12:55] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -29.187307 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 03:12:57] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -29.188107 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 03:13:00] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -29.178687 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 03:13:02] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -29.192439 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 03:13:05] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -29.178807 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 03:13:07] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -29.184025 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 03:13:09] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -29.176164 | E_var:     0.1479 | E_err:   0.006009
[2025-10-06 03:13:12] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -29.181541 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 03:13:14] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -29.173286 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 03:13:17] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -29.190193 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 03:13:19] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -29.181267 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 03:13:22] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -29.185719 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 03:13:24] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -29.182090 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 03:13:26] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -29.175575 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 03:13:29] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -29.186085 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 03:13:31] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -29.182361 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 03:13:34] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -29.178639 | E_var:     0.0963 | E_err:   0.004850
[2025-10-06 03:13:36] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -29.181573 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 03:13:39] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -29.177110 | E_var:     0.1165 | E_err:   0.005334
[2025-10-06 03:13:41] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -29.179191 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 03:13:43] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -29.183374 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 03:13:46] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -29.183672 | E_var:     0.0900 | E_err:   0.004687
[2025-10-06 03:13:48] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -29.185445 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 03:13:51] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -29.183795 | E_var:     0.1094 | E_err:   0.005168
[2025-10-06 03:13:53] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -29.183860 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 03:13:55] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -29.181185 | E_var:     0.0882 | E_err:   0.004639
[2025-10-06 03:13:58] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -29.183620 | E_var:     0.1492 | E_err:   0.006034
[2025-10-06 03:14:00] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -29.171235 | E_var:     0.1464 | E_err:   0.005978
[2025-10-06 03:14:03] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -29.183091 | E_var:     0.0985 | E_err:   0.004903
[2025-10-06 03:14:05] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -29.180283 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 03:14:08] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -29.183114 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 03:14:10] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -29.187525 | E_var:     0.0844 | E_err:   0.004539
[2025-10-06 03:14:13] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -29.182785 | E_var:     0.1189 | E_err:   0.005387
[2025-10-06 03:14:15] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -29.179938 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 03:14:17] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -29.190721 | E_var:     0.1002 | E_err:   0.004945
[2025-10-06 03:14:20] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -29.182081 | E_var:     0.0983 | E_err:   0.004900
[2025-10-06 03:14:22] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -29.181479 | E_var:     0.1002 | E_err:   0.004945
[2025-10-06 03:14:25] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -29.182003 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 03:14:27] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -29.183765 | E_var:     0.0927 | E_err:   0.004756
[2025-10-06 03:14:30] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -29.180076 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 03:14:32] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -29.181476 | E_var:     0.1006 | E_err:   0.004955
[2025-10-06 03:14:34] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -29.178860 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 03:14:37] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -29.191335 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 03:14:39] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -29.181275 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 03:14:42] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -29.188774 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 03:14:44] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -29.183867 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 03:14:47] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -29.195739 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 03:14:49] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -29.182597 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 03:14:51] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -29.190811 | E_var:     0.1122 | E_err:   0.005233
[2025-10-06 03:14:54] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -29.178236 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 03:14:56] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -29.190591 | E_var:     0.0959 | E_err:   0.004838
[2025-10-06 03:14:59] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -29.173094 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 03:15:01] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -29.187445 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 03:15:04] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -29.180767 | E_var:     0.1096 | E_err:   0.005174
[2025-10-06 03:15:06] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -29.183815 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 03:15:08] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -29.185716 | E_var:     0.0876 | E_err:   0.004624
[2025-10-06 03:15:11] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -29.184733 | E_var:     0.0933 | E_err:   0.004773
[2025-10-06 03:15:13] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -29.176337 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 03:15:16] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -29.190247 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 03:15:18] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -29.188915 | E_var:     0.0955 | E_err:   0.004830
[2025-10-06 03:15:21] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -29.179032 | E_var:     0.0951 | E_err:   0.004819
[2025-10-06 03:15:23] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -29.187724 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 03:15:25] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -29.179097 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 03:15:28] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -29.178482 | E_var:     0.2392 | E_err:   0.007642
[2025-10-06 03:15:30] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -29.180364 | E_var:     0.1336 | E_err:   0.005712
[2025-10-06 03:15:33] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -29.181893 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 03:15:35] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -29.188039 | E_var:     0.1413 | E_err:   0.005873
[2025-10-06 03:15:38] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -29.178265 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 03:15:40] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -29.186875 | E_var:     0.0857 | E_err:   0.004573
[2025-10-06 03:15:42] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -29.172110 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 03:15:45] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -29.173536 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 03:15:47] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -29.190014 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 03:15:50] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -29.189132 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 03:15:52] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -29.182777 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 03:15:55] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -29.181067 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 03:15:57] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -29.175850 | E_var:     0.0823 | E_err:   0.004484
[2025-10-06 03:15:59] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -29.186209 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 03:15:59] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 03:16:02] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -29.177247 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 03:16:04] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -29.183529 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 03:16:07] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -29.193736 | E_var:     0.1246 | E_err:   0.005515
[2025-10-06 03:16:09] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -29.180904 | E_var:     0.0930 | E_err:   0.004766
[2025-10-06 03:16:12] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -29.187057 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 03:16:14] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -29.181745 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 03:16:16] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -29.181377 | E_var:     0.1139 | E_err:   0.005273
[2025-10-06 03:16:19] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -29.187867 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 03:16:21] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -29.181107 | E_var:     0.1644 | E_err:   0.006336
[2025-10-06 03:16:24] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -29.187932 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 03:16:26] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -29.175509 | E_var:     0.1727 | E_err:   0.006493
[2025-10-06 03:16:29] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -29.175313 | E_var:     0.1546 | E_err:   0.006144
[2025-10-06 03:16:31] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -29.186952 | E_var:     0.1226 | E_err:   0.005472
[2025-10-06 03:16:33] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -29.185047 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 03:16:36] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -29.169548 | E_var:     0.1395 | E_err:   0.005837
[2025-10-06 03:16:38] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -29.178523 | E_var:     0.1054 | E_err:   0.005072
[2025-10-06 03:16:41] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -29.177293 | E_var:     0.1358 | E_err:   0.005757
[2025-10-06 03:16:43] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -29.188752 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 03:16:46] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -29.178356 | E_var:     0.0795 | E_err:   0.004405
[2025-10-06 03:16:48] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -29.170430 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 03:16:50] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -29.169748 | E_var:     0.1205 | E_err:   0.005424
[2025-10-06 03:16:53] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -29.183518 | E_var:     0.0982 | E_err:   0.004897
[2025-10-06 03:16:55] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -29.175515 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 03:16:58] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -29.181123 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 03:17:00] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -29.178991 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 03:17:03] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -29.185049 | E_var:     0.1684 | E_err:   0.006412
[2025-10-06 03:17:05] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -29.179687 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 03:17:07] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -29.179773 | E_var:     0.1017 | E_err:   0.004982
[2025-10-06 03:17:10] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -29.175846 | E_var:     0.1139 | E_err:   0.005273
[2025-10-06 03:17:12] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -29.177984 | E_var:     0.1345 | E_err:   0.005731
[2025-10-06 03:17:15] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -29.182887 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 03:17:17] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -29.185446 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 03:17:20] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -29.174145 | E_var:     0.1185 | E_err:   0.005379
[2025-10-06 03:17:22] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -29.183734 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 03:17:24] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -29.179171 | E_var:     0.2120 | E_err:   0.007195
[2025-10-06 03:17:27] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -29.186222 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 03:17:29] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -29.181535 | E_var:     0.1217 | E_err:   0.005452
[2025-10-06 03:17:32] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -29.178249 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 03:17:34] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -29.186791 | E_var:     0.1059 | E_err:   0.005085
[2025-10-06 03:17:37] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -29.183609 | E_var:     0.1388 | E_err:   0.005822
[2025-10-06 03:17:39] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -29.177941 | E_var:     0.1051 | E_err:   0.005066
[2025-10-06 03:17:41] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -29.175436 | E_var:     0.1273 | E_err:   0.005574
[2025-10-06 03:17:44] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -29.183037 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 03:17:46] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -29.181116 | E_var:     0.0925 | E_err:   0.004751
[2025-10-06 03:17:49] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -29.176007 | E_var:     0.1127 | E_err:   0.005244
[2025-10-06 03:17:51] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -29.187321 | E_var:     0.0907 | E_err:   0.004706
[2025-10-06 03:17:54] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -29.181971 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 03:17:56] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -29.180120 | E_var:     0.0911 | E_err:   0.004716
[2025-10-06 03:17:58] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -29.187168 | E_var:     0.0877 | E_err:   0.004627
[2025-10-06 03:18:01] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -29.188333 | E_var:     0.1214 | E_err:   0.005444
[2025-10-06 03:18:01] 🔄 RESTART #1 | Period: 300
[2025-10-06 03:18:03] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -29.185648 | E_var:     0.1038 | E_err:   0.005034
[2025-10-06 03:18:06] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -29.179042 | E_var:     0.1335 | E_err:   0.005708
[2025-10-06 03:18:08] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -29.182438 | E_var:     0.0992 | E_err:   0.004920
[2025-10-06 03:18:11] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -29.187940 | E_var:     0.1209 | E_err:   0.005432
[2025-10-06 03:18:13] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -29.179876 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 03:18:16] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -29.190526 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 03:18:18] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -29.177105 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 03:18:20] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -29.185369 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 03:18:23] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -29.184667 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 03:18:25] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -29.177704 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 03:18:28] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -29.169019 | E_var:     0.1402 | E_err:   0.005851
[2025-10-06 03:18:30] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -29.181088 | E_var:     0.0904 | E_err:   0.004697
[2025-10-06 03:18:33] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -29.182421 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 03:18:35] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -29.176391 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 03:18:37] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -29.180107 | E_var:     0.0717 | E_err:   0.004183
[2025-10-06 03:18:40] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -29.189375 | E_var:     0.0972 | E_err:   0.004870
[2025-10-06 03:18:42] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -29.186056 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 03:18:45] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -29.184950 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 03:18:47] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -29.179749 | E_var:     0.0909 | E_err:   0.004712
[2025-10-06 03:18:50] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -29.179592 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 03:18:52] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -29.184586 | E_var:     0.0836 | E_err:   0.004517
[2025-10-06 03:18:54] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -29.178909 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 03:18:57] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -29.177962 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 03:18:59] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -29.181555 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 03:19:02] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -29.184119 | E_var:     0.0920 | E_err:   0.004740
[2025-10-06 03:19:04] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -29.189963 | E_var:     0.1652 | E_err:   0.006351
[2025-10-06 03:19:07] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -29.181860 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 03:19:09] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -29.184081 | E_var:     0.0796 | E_err:   0.004409
[2025-10-06 03:19:11] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -29.186007 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 03:19:14] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -29.179572 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 03:19:16] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -29.177588 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 03:19:19] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -29.188957 | E_var:     0.1209 | E_err:   0.005433
[2025-10-06 03:19:21] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -29.193179 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 03:19:24] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -29.186946 | E_var:     0.1350 | E_err:   0.005742
[2025-10-06 03:19:26] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -29.185907 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 03:19:29] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -29.185902 | E_var:     0.0926 | E_err:   0.004754
[2025-10-06 03:19:31] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -29.183570 | E_var:     0.0768 | E_err:   0.004330
[2025-10-06 03:19:33] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -29.172947 | E_var:     0.1354 | E_err:   0.005750
[2025-10-06 03:19:36] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -29.175437 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 03:19:38] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -29.181033 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 03:19:41] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -29.186362 | E_var:     0.0773 | E_err:   0.004343
[2025-10-06 03:19:43] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -29.172177 | E_var:     0.0798 | E_err:   0.004413
[2025-10-06 03:19:46] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -29.181724 | E_var:     0.1137 | E_err:   0.005268
[2025-10-06 03:19:48] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -29.180017 | E_var:     0.1368 | E_err:   0.005779
[2025-10-06 03:19:50] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -29.176172 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 03:19:53] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -29.180502 | E_var:     0.0818 | E_err:   0.004470
[2025-10-06 03:19:55] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -29.179527 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 03:19:58] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -29.186519 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 03:20:00] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -29.182045 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 03:20:03] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -29.185887 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 03:20:03] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 03:20:05] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -29.179317 | E_var:     0.0916 | E_err:   0.004728
[2025-10-06 03:20:07] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -29.185621 | E_var:     0.1063 | E_err:   0.005093
[2025-10-06 03:20:10] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -29.182145 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 03:20:12] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -29.187654 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 03:20:15] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -29.180998 | E_var:     0.0853 | E_err:   0.004563
[2025-10-06 03:20:17] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -29.184940 | E_var:     0.0966 | E_err:   0.004855
[2025-10-06 03:20:20] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -29.192616 | E_var:     0.0919 | E_err:   0.004736
[2025-10-06 03:20:22] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -29.180252 | E_var:     0.1025 | E_err:   0.005002
[2025-10-06 03:20:24] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -29.182096 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 03:20:27] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -29.185752 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 03:20:29] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -29.185023 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 03:20:32] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -29.183398 | E_var:     0.1288 | E_err:   0.005608
[2025-10-06 03:20:34] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -29.182460 | E_var:     0.1160 | E_err:   0.005323
[2025-10-06 03:20:37] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -29.186094 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 03:20:39] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -29.183935 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 03:20:41] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -29.176742 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 03:20:44] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -29.183350 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 03:20:46] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -29.180781 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 03:20:49] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -29.182999 | E_var:     0.0764 | E_err:   0.004320
[2025-10-06 03:20:51] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -29.180020 | E_var:     0.0907 | E_err:   0.004707
[2025-10-06 03:20:54] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -29.186409 | E_var:     0.1341 | E_err:   0.005722
[2025-10-06 03:20:56] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -29.179018 | E_var:     0.0951 | E_err:   0.004818
[2025-10-06 03:20:58] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -29.172935 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 03:21:01] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -29.183699 | E_var:     0.1221 | E_err:   0.005459
[2025-10-06 03:21:03] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -29.179185 | E_var:     0.0949 | E_err:   0.004814
[2025-10-06 03:21:06] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -29.181411 | E_var:     0.0923 | E_err:   0.004748
[2025-10-06 03:21:08] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -29.176989 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 03:21:11] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -29.178556 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 03:21:13] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -29.185087 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 03:21:15] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -29.179802 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 03:21:18] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -29.190601 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 03:21:20] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -29.176223 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 03:21:23] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -29.180776 | E_var:     0.1106 | E_err:   0.005197
[2025-10-06 03:21:25] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -29.176094 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 03:21:28] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -29.182715 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 03:21:30] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -29.182753 | E_var:     0.1227 | E_err:   0.005474
[2025-10-06 03:21:32] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -29.191542 | E_var:     0.5061 | E_err:   0.011116
[2025-10-06 03:21:35] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -29.184010 | E_var:     0.1109 | E_err:   0.005203
[2025-10-06 03:21:37] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -29.189573 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 03:21:40] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -29.178873 | E_var:     0.1077 | E_err:   0.005127
[2025-10-06 03:21:42] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -29.174345 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 03:21:45] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -29.184488 | E_var:     0.0937 | E_err:   0.004784
[2025-10-06 03:21:47] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -29.173650 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 03:21:49] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -29.179815 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 03:21:52] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -29.184106 | E_var:     0.1607 | E_err:   0.006264
[2025-10-06 03:21:54] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -29.175194 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 03:21:57] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -29.182996 | E_var:     0.1022 | E_err:   0.004996
[2025-10-06 03:21:59] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -29.182057 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 03:22:02] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -29.184888 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 03:22:04] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -29.167981 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 03:22:06] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -29.176670 | E_var:     0.1129 | E_err:   0.005249
[2025-10-06 03:22:09] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -29.176637 | E_var:     0.1290 | E_err:   0.005612
[2025-10-06 03:22:11] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -29.180215 | E_var:     0.1279 | E_err:   0.005588
[2025-10-06 03:22:14] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -29.188984 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 03:22:16] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -29.186256 | E_var:     0.2203 | E_err:   0.007334
[2025-10-06 03:22:19] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -29.172950 | E_var:     0.1413 | E_err:   0.005873
[2025-10-06 03:22:21] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -29.179742 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 03:22:23] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -29.186437 | E_var:     0.0979 | E_err:   0.004888
[2025-10-06 03:22:26] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -29.180333 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 03:22:28] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -29.182654 | E_var:     0.0897 | E_err:   0.004678
[2025-10-06 03:22:31] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -29.178215 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 03:22:33] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -29.180113 | E_var:     0.0895 | E_err:   0.004674
[2025-10-06 03:22:36] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -29.179760 | E_var:     0.0933 | E_err:   0.004772
[2025-10-06 03:22:38] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -29.186328 | E_var:     0.1113 | E_err:   0.005212
[2025-10-06 03:22:40] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -29.184280 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 03:22:43] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -29.175057 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 03:22:45] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -29.181046 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 03:22:48] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -29.181560 | E_var:     0.1281 | E_err:   0.005593
[2025-10-06 03:22:50] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -29.177972 | E_var:     0.1505 | E_err:   0.006061
[2025-10-06 03:22:53] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -29.174012 | E_var:     0.1037 | E_err:   0.005032
[2025-10-06 03:22:55] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -29.176983 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 03:22:57] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -29.183086 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 03:23:00] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -29.186169 | E_var:     0.0863 | E_err:   0.004591
[2025-10-06 03:23:02] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -29.187510 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 03:23:05] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -29.184667 | E_var:     0.0961 | E_err:   0.004843
[2025-10-06 03:23:07] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -29.178165 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 03:23:10] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -29.178481 | E_var:     0.1288 | E_err:   0.005607
[2025-10-06 03:23:12] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -29.179755 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 03:23:14] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -29.188198 | E_var:     0.0966 | E_err:   0.004856
[2025-10-06 03:23:17] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -29.178021 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 03:23:19] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -29.190255 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 03:23:22] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -29.177410 | E_var:     0.1406 | E_err:   0.005858
[2025-10-06 03:23:24] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -29.179006 | E_var:     0.0752 | E_err:   0.004286
[2025-10-06 03:23:27] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -29.176071 | E_var:     0.1041 | E_err:   0.005041
[2025-10-06 03:23:29] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -29.180991 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 03:23:31] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -29.177513 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 03:23:34] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -29.180373 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 03:23:36] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -29.177536 | E_var:     0.1357 | E_err:   0.005755
[2025-10-06 03:23:39] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -29.186121 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 03:23:41] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -29.178074 | E_var:     0.1038 | E_err:   0.005034
[2025-10-06 03:23:44] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -29.181528 | E_var:     0.0636 | E_err:   0.003941
[2025-10-06 03:23:46] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -29.175461 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 03:23:49] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -29.180868 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 03:23:51] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -29.175896 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 03:23:53] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -29.187854 | E_var:     0.0799 | E_err:   0.004415
[2025-10-06 03:23:56] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -29.184947 | E_var:     0.0758 | E_err:   0.004300
[2025-10-06 03:23:58] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -29.176080 | E_var:     0.1035 | E_err:   0.005027
[2025-10-06 03:24:01] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -29.177812 | E_var:     0.0847 | E_err:   0.004548
[2025-10-06 03:24:03] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -29.186300 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 03:24:06] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -29.188327 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 03:24:06] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 03:24:08] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -29.180607 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 03:24:10] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -29.182358 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 03:24:13] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -29.188672 | E_var:     0.1429 | E_err:   0.005906
[2025-10-06 03:24:15] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -29.176152 | E_var:     0.1119 | E_err:   0.005228
[2025-10-06 03:24:18] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -29.172748 | E_var:     0.3099 | E_err:   0.008698
[2025-10-06 03:24:20] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -29.172609 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 03:24:23] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -29.188307 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 03:24:25] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -29.166829 | E_var:     0.1391 | E_err:   0.005827
[2025-10-06 03:24:27] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -29.184454 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 03:24:30] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -29.181886 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 03:24:32] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -29.192882 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 03:24:35] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -29.178079 | E_var:     0.0863 | E_err:   0.004589
[2025-10-06 03:24:37] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -29.177238 | E_var:     0.0858 | E_err:   0.004578
[2025-10-06 03:24:40] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -29.188479 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 03:24:42] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -29.178335 | E_var:     0.1108 | E_err:   0.005200
[2025-10-06 03:24:44] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -29.172491 | E_var:     0.1304 | E_err:   0.005643
[2025-10-06 03:24:47] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -29.178468 | E_var:     0.1044 | E_err:   0.005048
[2025-10-06 03:24:49] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -29.184127 | E_var:     0.1308 | E_err:   0.005650
[2025-10-06 03:24:52] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -29.188020 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 03:24:54] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -29.186188 | E_var:     0.0835 | E_err:   0.004514
[2025-10-06 03:24:57] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -29.184053 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 03:24:59] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -29.184616 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 03:25:01] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -29.181853 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 03:25:04] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -29.184904 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 03:25:06] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -29.189682 | E_var:     0.1230 | E_err:   0.005480
[2025-10-06 03:25:09] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -29.185775 | E_var:     0.0900 | E_err:   0.004686
[2025-10-06 03:25:11] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -29.179404 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 03:25:14] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -29.181277 | E_var:     0.2271 | E_err:   0.007446
[2025-10-06 03:25:16] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -29.174762 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 03:25:18] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -29.180483 | E_var:     0.0806 | E_err:   0.004436
[2025-10-06 03:25:21] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -29.180118 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 03:25:23] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -29.184367 | E_var:     0.1286 | E_err:   0.005604
[2025-10-06 03:25:26] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -29.182329 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 03:25:28] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -29.188380 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 03:25:31] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -29.188925 | E_var:     0.1048 | E_err:   0.005057
[2025-10-06 03:25:33] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -29.173382 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 03:25:35] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -29.186144 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 03:25:38] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -29.169045 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 03:25:40] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -29.181925 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 03:25:43] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -29.184239 | E_var:     0.0818 | E_err:   0.004468
[2025-10-06 03:25:45] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -29.184284 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 03:25:48] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -29.173827 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 03:25:50] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -29.186307 | E_var:     0.1077 | E_err:   0.005129
[2025-10-06 03:25:52] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -29.178755 | E_var:     0.0990 | E_err:   0.004917
[2025-10-06 03:25:55] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -29.181079 | E_var:     0.0950 | E_err:   0.004816
[2025-10-06 03:25:57] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -29.181299 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 03:26:00] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -29.184094 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 03:26:02] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -29.179888 | E_var:     0.1241 | E_err:   0.005505
[2025-10-06 03:26:05] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -29.182442 | E_var:     0.0820 | E_err:   0.004475
[2025-10-06 03:26:07] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -29.180807 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 03:26:09] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -29.185564 | E_var:     0.0863 | E_err:   0.004589
[2025-10-06 03:26:12] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -29.178089 | E_var:     0.0929 | E_err:   0.004763
[2025-10-06 03:26:14] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -29.183925 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 03:26:17] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -29.185271 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 03:26:19] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -29.176654 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 03:26:22] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -29.188288 | E_var:     0.1187 | E_err:   0.005382
[2025-10-06 03:26:24] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -29.191091 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 03:26:26] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -29.183008 | E_var:     0.0906 | E_err:   0.004703
[2025-10-06 03:26:29] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -29.189059 | E_var:     0.1056 | E_err:   0.005078
[2025-10-06 03:26:31] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -29.177532 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 03:26:34] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -29.186218 | E_var:     0.0958 | E_err:   0.004836
[2025-10-06 03:26:36] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -29.188120 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 03:26:39] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -29.184237 | E_var:     0.0892 | E_err:   0.004665
[2025-10-06 03:26:41] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -29.187744 | E_var:     0.0783 | E_err:   0.004372
[2025-10-06 03:26:43] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -29.174948 | E_var:     0.1000 | E_err:   0.004942
[2025-10-06 03:26:46] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -29.183300 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 03:26:48] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -29.176080 | E_var:     0.0888 | E_err:   0.004656
[2025-10-06 03:26:51] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -29.179733 | E_var:     0.1085 | E_err:   0.005147
[2025-10-06 03:26:53] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -29.180071 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 03:26:56] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -29.181645 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 03:26:58] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -29.177643 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 03:27:00] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -29.174112 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 03:27:03] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -29.182854 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 03:27:05] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -29.179438 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 03:27:08] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -29.182002 | E_var:     0.0811 | E_err:   0.004451
[2025-10-06 03:27:10] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -29.186795 | E_var:     0.0808 | E_err:   0.004441
[2025-10-06 03:27:13] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -29.175722 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 03:27:15] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -29.177581 | E_var:     0.1056 | E_err:   0.005079
[2025-10-06 03:27:17] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -29.190833 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 03:27:20] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -29.186062 | E_var:     0.0792 | E_err:   0.004399
[2025-10-06 03:27:22] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -29.186880 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 03:27:25] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -29.181269 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 03:27:27] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -29.190189 | E_var:     0.0947 | E_err:   0.004807
[2025-10-06 03:27:30] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -29.184963 | E_var:     0.0813 | E_err:   0.004455
[2025-10-06 03:27:32] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -29.177862 | E_var:     0.0925 | E_err:   0.004753
[2025-10-06 03:27:34] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -29.187036 | E_var:     0.0815 | E_err:   0.004462
[2025-10-06 03:27:37] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -29.177439 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 03:27:39] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -29.181197 | E_var:     0.1128 | E_err:   0.005249
[2025-10-06 03:27:42] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -29.188536 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 03:27:44] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -29.184316 | E_var:     0.1629 | E_err:   0.006307
[2025-10-06 03:27:47] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -29.185058 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 03:27:49] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -29.182943 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 03:27:52] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -29.177893 | E_var:     0.1013 | E_err:   0.004972
[2025-10-06 03:27:54] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -29.181981 | E_var:     0.0800 | E_err:   0.004418
[2025-10-06 03:27:56] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -29.177422 | E_var:     0.1078 | E_err:   0.005131
[2025-10-06 03:27:59] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -29.174991 | E_var:     0.0966 | E_err:   0.004856
[2025-10-06 03:28:01] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -29.187297 | E_var:     0.0740 | E_err:   0.004251
[2025-10-06 03:28:04] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -29.183660 | E_var:     0.0989 | E_err:   0.004914
[2025-10-06 03:28:06] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -29.177922 | E_var:     0.1124 | E_err:   0.005240
[2025-10-06 03:28:09] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -29.179737 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 03:28:09] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 03:28:11] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -29.184764 | E_var:     0.0809 | E_err:   0.004443
[2025-10-06 03:28:13] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -29.183073 | E_var:     0.1177 | E_err:   0.005361
[2025-10-06 03:28:16] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -29.178885 | E_var:     0.0780 | E_err:   0.004365
[2025-10-06 03:28:18] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -29.184938 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 03:28:21] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -29.186610 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 03:28:23] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -29.180474 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 03:28:26] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -29.187297 | E_var:     0.0909 | E_err:   0.004711
[2025-10-06 03:28:28] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -29.179183 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 03:28:30] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -29.181366 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 03:28:33] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -29.171822 | E_var:     0.3307 | E_err:   0.008985
[2025-10-06 03:28:35] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -29.189469 | E_var:     0.0935 | E_err:   0.004778
[2025-10-06 03:28:38] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -29.182389 | E_var:     0.0964 | E_err:   0.004850
[2025-10-06 03:28:40] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -29.182152 | E_var:     0.0903 | E_err:   0.004696
[2025-10-06 03:28:43] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -29.189289 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 03:28:45] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -29.175300 | E_var:     0.1043 | E_err:   0.005045
[2025-10-06 03:28:47] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -29.178411 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 03:28:50] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -29.175120 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 03:28:52] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -29.174857 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 03:28:55] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -29.184427 | E_var:     0.1076 | E_err:   0.005127
[2025-10-06 03:28:57] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -29.185239 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 03:29:00] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -29.183239 | E_var:     0.0769 | E_err:   0.004334
[2025-10-06 03:29:02] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -29.190858 | E_var:     0.1342 | E_err:   0.005724
[2025-10-06 03:29:04] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -29.182037 | E_var:     0.0831 | E_err:   0.004504
[2025-10-06 03:29:07] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -29.193411 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 03:29:09] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -29.184733 | E_var:     0.0740 | E_err:   0.004250
[2025-10-06 03:29:12] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -29.182518 | E_var:     0.1062 | E_err:   0.005092
[2025-10-06 03:29:14] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -29.179878 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 03:29:17] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -29.184268 | E_var:     0.1034 | E_err:   0.005023
[2025-10-06 03:29:19] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -29.183469 | E_var:     0.0770 | E_err:   0.004334
[2025-10-06 03:29:21] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -29.174246 | E_var:     0.1200 | E_err:   0.005414
[2025-10-06 03:29:24] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -29.174286 | E_var:     0.0956 | E_err:   0.004831
[2025-10-06 03:29:26] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -29.188418 | E_var:     0.1190 | E_err:   0.005389
[2025-10-06 03:29:29] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -29.175712 | E_var:     0.0876 | E_err:   0.004624
[2025-10-06 03:29:31] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -29.189362 | E_var:     0.1162 | E_err:   0.005327
[2025-10-06 03:29:34] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -29.182966 | E_var:     0.0847 | E_err:   0.004546
[2025-10-06 03:29:36] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -29.187004 | E_var:     0.0906 | E_err:   0.004704
[2025-10-06 03:29:38] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -29.187402 | E_var:     0.1108 | E_err:   0.005201
[2025-10-06 03:29:41] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -29.178403 | E_var:     0.0878 | E_err:   0.004631
[2025-10-06 03:29:43] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -29.178355 | E_var:     0.0754 | E_err:   0.004289
[2025-10-06 03:29:46] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -29.189078 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 03:29:48] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -29.183146 | E_var:     0.0900 | E_err:   0.004689
[2025-10-06 03:29:51] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -29.174950 | E_var:     0.0888 | E_err:   0.004656
[2025-10-06 03:29:53] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -29.184103 | E_var:     0.1457 | E_err:   0.005964
[2025-10-06 03:29:55] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -29.176027 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 03:29:58] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -29.177895 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 03:30:00] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -29.182100 | E_var:     0.1119 | E_err:   0.005226
[2025-10-06 03:30:03] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -29.183556 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 03:30:05] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -29.186257 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 03:30:08] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -29.193192 | E_var:     0.0833 | E_err:   0.004511
[2025-10-06 03:30:10] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -29.179778 | E_var:     0.1487 | E_err:   0.006026
[2025-10-06 03:30:10] 🔄 RESTART #2 | Period: 600
[2025-10-06 03:30:12] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -29.182631 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 03:30:15] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -29.175583 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 03:30:17] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -29.180902 | E_var:     0.1622 | E_err:   0.006293
[2025-10-06 03:30:20] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -29.185682 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 03:30:22] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -29.186331 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 03:30:25] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -29.177727 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 03:30:27] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -29.186232 | E_var:     0.0854 | E_err:   0.004566
[2025-10-06 03:30:29] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -29.178445 | E_var:     0.0877 | E_err:   0.004626
[2025-10-06 03:30:32] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -29.182499 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 03:30:34] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -29.185826 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 03:30:37] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -29.185999 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 03:30:39] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -29.183790 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 03:30:42] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -29.184628 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 03:30:44] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -29.183794 | E_var:     0.1560 | E_err:   0.006171
[2025-10-06 03:30:46] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -29.176801 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 03:30:49] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -29.179185 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 03:30:51] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -29.175261 | E_var:     0.1147 | E_err:   0.005293
[2025-10-06 03:30:54] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -29.174459 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 03:30:56] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -29.181154 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 03:30:59] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -29.181169 | E_var:     0.1298 | E_err:   0.005629
[2025-10-06 03:31:01] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -29.182789 | E_var:     0.1152 | E_err:   0.005302
[2025-10-06 03:31:03] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -29.181667 | E_var:     0.0957 | E_err:   0.004834
[2025-10-06 03:31:06] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -29.173714 | E_var:     0.0759 | E_err:   0.004303
[2025-10-06 03:31:08] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -29.177180 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 03:31:11] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -29.178824 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 03:31:13] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -29.180374 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 03:31:16] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -29.179759 | E_var:     0.1043 | E_err:   0.005045
[2025-10-06 03:31:18] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -29.189338 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 03:31:20] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -29.179853 | E_var:     0.1023 | E_err:   0.004997
[2025-10-06 03:31:23] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -29.179078 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 03:31:25] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -29.181067 | E_var:     0.1410 | E_err:   0.005868
[2025-10-06 03:31:28] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -29.180921 | E_var:     0.0889 | E_err:   0.004660
[2025-10-06 03:31:30] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -29.176896 | E_var:     0.0980 | E_err:   0.004891
[2025-10-06 03:31:33] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -29.186359 | E_var:     0.0925 | E_err:   0.004751
[2025-10-06 03:31:35] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -29.183964 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 03:31:37] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -29.186021 | E_var:     0.1165 | E_err:   0.005332
[2025-10-06 03:31:40] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -29.186046 | E_var:     0.1063 | E_err:   0.005093
[2025-10-06 03:31:42] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -29.177595 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 03:31:45] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -29.181331 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 03:31:47] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -29.188808 | E_var:     0.0777 | E_err:   0.004357
[2025-10-06 03:31:50] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -29.179699 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 03:31:52] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -29.183012 | E_var:     0.0883 | E_err:   0.004642
[2025-10-06 03:31:54] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -29.183261 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 03:31:57] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -29.178621 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 03:31:59] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -29.180706 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 03:32:02] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -29.180226 | E_var:     0.0847 | E_err:   0.004547
[2025-10-06 03:32:04] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -29.188264 | E_var:     0.1122 | E_err:   0.005233
[2025-10-06 03:32:07] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -29.185544 | E_var:     0.0884 | E_err:   0.004647
[2025-10-06 03:32:09] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -29.178094 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 03:32:11] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -29.167957 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 03:32:11] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 03:32:14] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -29.186362 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 03:32:16] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -29.183504 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 03:32:19] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -29.181950 | E_var:     0.1261 | E_err:   0.005549
[2025-10-06 03:32:21] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -29.185518 | E_var:     0.0910 | E_err:   0.004712
[2025-10-06 03:32:24] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -29.183973 | E_var:     0.1101 | E_err:   0.005185
[2025-10-06 03:32:26] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -29.184035 | E_var:     0.1115 | E_err:   0.005217
[2025-10-06 03:32:28] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -29.175278 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 03:32:31] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -29.188316 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 03:32:33] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -29.184084 | E_var:     0.1377 | E_err:   0.005797
[2025-10-06 03:32:36] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -29.172814 | E_var:     0.0983 | E_err:   0.004900
[2025-10-06 03:32:38] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -29.180132 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 03:32:41] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -29.178998 | E_var:     0.0760 | E_err:   0.004308
[2025-10-06 03:32:43] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -29.182079 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 03:32:45] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -29.179930 | E_var:     0.1090 | E_err:   0.005159
[2025-10-06 03:32:48] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -29.177324 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 03:32:50] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -29.181577 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 03:32:53] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -29.180668 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 03:32:55] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -29.183041 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 03:32:58] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -29.187756 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 03:33:00] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -29.180959 | E_var:     0.1033 | E_err:   0.005021
[2025-10-06 03:33:02] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -29.187798 | E_var:     0.0958 | E_err:   0.004835
[2025-10-06 03:33:05] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -29.179484 | E_var:     0.1585 | E_err:   0.006221
[2025-10-06 03:33:07] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -29.177267 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 03:33:10] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -29.177722 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 03:33:12] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -29.184314 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 03:33:15] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -29.176082 | E_var:     0.1005 | E_err:   0.004955
[2025-10-06 03:33:17] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -29.179946 | E_var:     0.0888 | E_err:   0.004656
[2025-10-06 03:33:20] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -29.184692 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 03:33:22] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -29.179361 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 03:33:24] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -29.185496 | E_var:     0.1105 | E_err:   0.005193
[2025-10-06 03:33:27] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -29.188757 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 03:33:29] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -29.177391 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 03:33:32] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -29.184354 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 03:33:34] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -29.171577 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 03:33:37] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -29.188013 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 03:33:39] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -29.189564 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 03:33:41] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -29.183478 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 03:33:44] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -29.184004 | E_var:     0.0803 | E_err:   0.004428
[2025-10-06 03:33:46] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -29.175831 | E_var:     0.1195 | E_err:   0.005401
[2025-10-06 03:33:49] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -29.180994 | E_var:     0.0976 | E_err:   0.004882
[2025-10-06 03:33:51] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -29.180748 | E_var:     0.0911 | E_err:   0.004716
[2025-10-06 03:33:54] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -29.173249 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 03:33:56] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -29.180811 | E_var:     0.1129 | E_err:   0.005250
[2025-10-06 03:33:58] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -29.175854 | E_var:     0.0991 | E_err:   0.004919
[2025-10-06 03:34:01] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -29.183613 | E_var:     0.0939 | E_err:   0.004789
[2025-10-06 03:34:03] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -29.175192 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 03:34:06] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -29.182793 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 03:34:08] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -29.186879 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 03:34:11] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -29.180037 | E_var:     0.1007 | E_err:   0.004959
[2025-10-06 03:34:13] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -29.183484 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 03:34:15] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -29.180851 | E_var:     0.0763 | E_err:   0.004316
[2025-10-06 03:34:18] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -29.176805 | E_var:     0.1071 | E_err:   0.005112
[2025-10-06 03:34:20] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -29.176014 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 03:34:23] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -29.182365 | E_var:     0.1446 | E_err:   0.005942
[2025-10-06 03:34:25] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -29.185313 | E_var:     0.1344 | E_err:   0.005729
[2025-10-06 03:34:28] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -29.182507 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 03:34:30] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -29.176728 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 03:34:32] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -29.180007 | E_var:     0.0960 | E_err:   0.004841
[2025-10-06 03:34:35] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -29.183886 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 03:34:37] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -29.174330 | E_var:     0.1046 | E_err:   0.005052
[2025-10-06 03:34:40] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -29.169314 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 03:34:42] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -29.182710 | E_var:     0.0882 | E_err:   0.004639
[2025-10-06 03:34:45] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -29.189510 | E_var:     0.1086 | E_err:   0.005148
[2025-10-06 03:34:47] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -29.184105 | E_var:     0.0838 | E_err:   0.004523
[2025-10-06 03:34:49] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -29.188131 | E_var:     0.1086 | E_err:   0.005150
[2025-10-06 03:34:52] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -29.179849 | E_var:     0.1235 | E_err:   0.005492
[2025-10-06 03:34:54] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -29.178628 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 03:34:57] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -29.175027 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 03:34:59] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -29.180257 | E_var:     0.1063 | E_err:   0.005093
[2025-10-06 03:35:02] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -29.171486 | E_var:     0.1197 | E_err:   0.005407
[2025-10-06 03:35:04] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -29.180535 | E_var:     0.1541 | E_err:   0.006133
[2025-10-06 03:35:06] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -29.182960 | E_var:     0.0939 | E_err:   0.004789
[2025-10-06 03:35:09] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -29.182032 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 03:35:11] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -29.171960 | E_var:     0.1323 | E_err:   0.005682
[2025-10-06 03:35:14] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -29.189112 | E_var:     0.0966 | E_err:   0.004856
[2025-10-06 03:35:16] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -29.178957 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 03:35:19] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -29.181030 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 03:35:21] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -29.181777 | E_var:     0.0822 | E_err:   0.004479
[2025-10-06 03:35:23] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -29.171543 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 03:35:26] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -29.177444 | E_var:     0.1032 | E_err:   0.005019
[2025-10-06 03:35:28] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -29.177542 | E_var:     0.1047 | E_err:   0.005057
[2025-10-06 03:35:31] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -29.193749 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 03:35:33] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -29.178395 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 03:35:36] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -29.182330 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 03:35:38] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -29.179350 | E_var:     0.1034 | E_err:   0.005023
[2025-10-06 03:35:40] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -29.182513 | E_var:     0.0743 | E_err:   0.004260
[2025-10-06 03:35:43] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -29.183331 | E_var:     0.1875 | E_err:   0.006765
[2025-10-06 03:35:45] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -29.184060 | E_var:     0.0852 | E_err:   0.004562
[2025-10-06 03:35:48] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -29.178849 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 03:35:50] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -29.171843 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 03:35:53] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -29.179958 | E_var:     0.0981 | E_err:   0.004895
[2025-10-06 03:35:55] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -29.180538 | E_var:     0.1085 | E_err:   0.005148
[2025-10-06 03:35:57] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -29.180678 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 03:36:00] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -29.178379 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 03:36:02] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -29.179162 | E_var:     0.0853 | E_err:   0.004563
[2025-10-06 03:36:05] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -29.176140 | E_var:     0.1119 | E_err:   0.005226
[2025-10-06 03:36:07] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -29.178869 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 03:36:10] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -29.187465 | E_var:     0.0897 | E_err:   0.004681
[2025-10-06 03:36:12] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -29.190106 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 03:36:14] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -29.186006 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 03:36:14] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 03:36:17] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -29.177587 | E_var:     0.0822 | E_err:   0.004481
[2025-10-06 03:36:19] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -29.188614 | E_var:     0.1015 | E_err:   0.004977
[2025-10-06 03:36:22] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -29.176907 | E_var:     0.0900 | E_err:   0.004687
[2025-10-06 03:36:24] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -29.181798 | E_var:     0.1275 | E_err:   0.005579
[2025-10-06 03:36:27] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -29.182191 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 03:36:29] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -29.179880 | E_var:     0.1064 | E_err:   0.005097
[2025-10-06 03:36:31] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -29.181977 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 03:36:34] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -29.190796 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 03:36:36] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -29.189766 | E_var:     0.1163 | E_err:   0.005329
[2025-10-06 03:36:39] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -29.186205 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 03:36:41] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -29.188067 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 03:36:44] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -29.175223 | E_var:     0.1044 | E_err:   0.005050
[2025-10-06 03:36:46] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -29.185817 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 03:36:48] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -29.185412 | E_var:     0.0779 | E_err:   0.004360
[2025-10-06 03:36:51] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -29.187005 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 03:36:53] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -29.179447 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 03:36:56] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -29.181544 | E_var:     0.0999 | E_err:   0.004937
[2025-10-06 03:36:58] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -29.190128 | E_var:     0.0995 | E_err:   0.004930
[2025-10-06 03:37:01] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -29.182732 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 03:37:03] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -29.185781 | E_var:     0.1005 | E_err:   0.004955
[2025-10-06 03:37:05] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -29.179061 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 03:37:08] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -29.176552 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 03:37:10] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -29.183439 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 03:37:13] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -29.177786 | E_var:     0.0970 | E_err:   0.004866
[2025-10-06 03:37:15] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -29.189636 | E_var:     0.1162 | E_err:   0.005325
[2025-10-06 03:37:18] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -29.187535 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 03:37:20] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -29.191538 | E_var:     0.1595 | E_err:   0.006240
[2025-10-06 03:37:22] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -29.180828 | E_var:     0.0949 | E_err:   0.004812
[2025-10-06 03:37:25] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -29.182278 | E_var:     0.1039 | E_err:   0.005038
[2025-10-06 03:37:27] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -29.178163 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 03:37:30] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -29.178647 | E_var:     0.1713 | E_err:   0.006467
[2025-10-06 03:37:32] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -29.185409 | E_var:     0.0830 | E_err:   0.004500
[2025-10-06 03:37:35] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -29.178730 | E_var:     0.1062 | E_err:   0.005091
[2025-10-06 03:37:37] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -29.176334 | E_var:     0.0795 | E_err:   0.004407
[2025-10-06 03:37:40] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -29.183407 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 03:37:42] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -29.179625 | E_var:     0.0836 | E_err:   0.004519
[2025-10-06 03:37:44] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -29.184976 | E_var:     0.0894 | E_err:   0.004673
[2025-10-06 03:37:47] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -29.184909 | E_var:     0.1511 | E_err:   0.006074
[2025-10-06 03:37:49] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -29.183517 | E_var:     0.0956 | E_err:   0.004831
[2025-10-06 03:37:52] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -29.181439 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 03:37:54] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -29.177311 | E_var:     0.1088 | E_err:   0.005153
[2025-10-06 03:37:57] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -29.179345 | E_var:     0.0711 | E_err:   0.004168
[2025-10-06 03:37:59] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -29.168727 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 03:38:01] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -29.184919 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 03:38:04] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -29.183600 | E_var:     0.0870 | E_err:   0.004608
[2025-10-06 03:38:06] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -29.186772 | E_var:     0.0802 | E_err:   0.004425
[2025-10-06 03:38:09] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -29.180187 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 03:38:11] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -29.182926 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 03:38:13] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -29.187778 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 03:38:16] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -29.167749 | E_var:     0.1077 | E_err:   0.005127
[2025-10-06 03:38:18] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -29.181045 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 03:38:21] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -29.180612 | E_var:     0.1166 | E_err:   0.005334
[2025-10-06 03:38:23] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -29.186294 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 03:38:26] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -29.175705 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 03:38:28] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -29.183026 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 03:38:30] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -29.179066 | E_var:     0.0807 | E_err:   0.004439
[2025-10-06 03:38:33] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -29.182053 | E_var:     0.0997 | E_err:   0.004935
[2025-10-06 03:38:35] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -29.181440 | E_var:     0.0907 | E_err:   0.004706
[2025-10-06 03:38:38] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -29.181764 | E_var:     0.0991 | E_err:   0.004919
[2025-10-06 03:38:40] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -29.192373 | E_var:     0.0990 | E_err:   0.004917
[2025-10-06 03:38:43] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -29.179790 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 03:38:45] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -29.181811 | E_var:     0.0838 | E_err:   0.004523
[2025-10-06 03:38:48] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -29.180731 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 03:38:50] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -29.179733 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 03:38:52] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -29.184182 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 03:38:55] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -29.180095 | E_var:     0.0857 | E_err:   0.004573
[2025-10-06 03:38:57] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -29.181144 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 03:39:00] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -29.186449 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 03:39:02] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -29.183596 | E_var:     0.1085 | E_err:   0.005148
[2025-10-06 03:39:05] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -29.184834 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 03:39:07] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -29.187939 | E_var:     0.0970 | E_err:   0.004866
[2025-10-06 03:39:09] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -29.191706 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 03:39:12] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -29.183908 | E_var:     0.0891 | E_err:   0.004663
[2025-10-06 03:39:14] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -29.185562 | E_var:     0.0906 | E_err:   0.004704
[2025-10-06 03:39:17] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -29.182654 | E_var:     0.0992 | E_err:   0.004920
[2025-10-06 03:39:19] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -29.181529 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 03:39:21] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -29.183428 | E_var:     0.0834 | E_err:   0.004512
[2025-10-06 03:39:24] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -29.188554 | E_var:     0.0957 | E_err:   0.004834
[2025-10-06 03:39:26] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -29.181223 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 03:39:29] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -29.182100 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 03:39:31] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -29.188392 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 03:39:34] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -29.186301 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 03:39:36] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -29.179875 | E_var:     0.0929 | E_err:   0.004761
[2025-10-06 03:39:38] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -29.181262 | E_var:     0.1179 | E_err:   0.005364
[2025-10-06 03:39:41] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -29.184480 | E_var:     0.0804 | E_err:   0.004432
[2025-10-06 03:39:43] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -29.184288 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 03:39:46] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -29.190237 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 03:39:48] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -29.171918 | E_var:     0.1047 | E_err:   0.005057
[2025-10-06 03:39:51] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -29.180205 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 03:39:53] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -29.185453 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 03:39:55] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -29.183367 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 03:39:58] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -29.176843 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 03:40:00] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -29.178657 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 03:40:03] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -29.182204 | E_var:     0.1168 | E_err:   0.005339
[2025-10-06 03:40:05] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -29.173558 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 03:40:08] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -29.192770 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 03:40:10] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -29.183231 | E_var:     0.0979 | E_err:   0.004890
[2025-10-06 03:40:13] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -29.173728 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 03:40:15] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -29.182663 | E_var:     0.1151 | E_err:   0.005302
[2025-10-06 03:40:17] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -29.182815 | E_var:     0.0905 | E_err:   0.004700
[2025-10-06 03:40:17] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 03:40:20] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -29.189027 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 03:40:22] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -29.173997 | E_var:     0.1120 | E_err:   0.005229
[2025-10-06 03:40:25] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -29.179312 | E_var:     0.0975 | E_err:   0.004878
[2025-10-06 03:40:27] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -29.176161 | E_var:     0.0958 | E_err:   0.004836
[2025-10-06 03:40:30] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -29.202850 | E_var:     1.9663 | E_err:   0.021910
[2025-10-06 03:40:32] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -29.175147 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 03:40:34] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -29.184983 | E_var:     0.0982 | E_err:   0.004898
[2025-10-06 03:40:37] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -29.187631 | E_var:     0.1180 | E_err:   0.005366
[2025-10-06 03:40:39] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -29.190022 | E_var:     0.0950 | E_err:   0.004816
[2025-10-06 03:40:42] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -29.177239 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 03:40:44] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -29.189116 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 03:40:47] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -29.176113 | E_var:     0.1527 | E_err:   0.006105
[2025-10-06 03:40:49] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -29.177296 | E_var:     0.1263 | E_err:   0.005554
[2025-10-06 03:40:51] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -29.190358 | E_var:     0.1325 | E_err:   0.005687
[2025-10-06 03:40:54] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -29.180443 | E_var:     0.1701 | E_err:   0.006444
[2025-10-06 03:40:56] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -29.184485 | E_var:     0.0820 | E_err:   0.004474
[2025-10-06 03:40:59] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -29.185873 | E_var:     0.0980 | E_err:   0.004892
[2025-10-06 03:41:01] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -29.178319 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 03:41:03] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -29.186788 | E_var:     0.3374 | E_err:   0.009076
[2025-10-06 03:41:06] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -29.191347 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 03:41:08] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -29.180390 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 03:41:11] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -29.182522 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 03:41:13] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -29.175994 | E_var:     0.1104 | E_err:   0.005191
[2025-10-06 03:41:16] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -29.182493 | E_var:     0.1311 | E_err:   0.005657
[2025-10-06 03:41:18] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -29.190040 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 03:41:20] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -29.183271 | E_var:     0.1227 | E_err:   0.005472
[2025-10-06 03:41:23] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -29.182753 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 03:41:25] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -29.181675 | E_var:     0.0773 | E_err:   0.004343
[2025-10-06 03:41:28] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -29.180065 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 03:41:30] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -29.183714 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 03:41:33] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -29.176390 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 03:41:35] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -29.185646 | E_var:     0.1044 | E_err:   0.005050
[2025-10-06 03:41:37] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -29.179117 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 03:41:40] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -29.185996 | E_var:     0.2045 | E_err:   0.007066
[2025-10-06 03:41:42] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -29.179908 | E_var:     0.0853 | E_err:   0.004565
[2025-10-06 03:41:45] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -29.179981 | E_var:     0.1279 | E_err:   0.005588
[2025-10-06 03:41:47] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -29.187107 | E_var:     0.1038 | E_err:   0.005034
[2025-10-06 03:41:50] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -29.192059 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 03:41:52] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -29.181851 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 03:41:54] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -29.179630 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 03:41:57] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -29.195509 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 03:41:59] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -29.183590 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 03:42:02] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -29.185627 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 03:42:04] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -29.177329 | E_var:     0.1026 | E_err:   0.005004
[2025-10-06 03:42:07] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -29.189429 | E_var:     0.0933 | E_err:   0.004772
[2025-10-06 03:42:09] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -29.175306 | E_var:     0.1027 | E_err:   0.005007
[2025-10-06 03:42:11] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -29.180647 | E_var:     0.1011 | E_err:   0.004968
[2025-10-06 03:42:14] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -29.179785 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 03:42:16] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -29.185227 | E_var:     0.0957 | E_err:   0.004834
[2025-10-06 03:42:19] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -29.179646 | E_var:     0.0715 | E_err:   0.004179
[2025-10-06 03:42:21] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -29.183698 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 03:42:24] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -29.182965 | E_var:     0.1024 | E_err:   0.005001
[2025-10-06 03:42:26] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -29.183600 | E_var:     0.0782 | E_err:   0.004368
[2025-10-06 03:42:29] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -29.177338 | E_var:     0.0953 | E_err:   0.004822
[2025-10-06 03:42:31] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -29.183391 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 03:42:33] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -29.171049 | E_var:     0.1020 | E_err:   0.004989
[2025-10-06 03:42:36] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -29.181830 | E_var:     0.2204 | E_err:   0.007336
[2025-10-06 03:42:38] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -29.179362 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 03:42:41] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -29.179531 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 03:42:43] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -29.175515 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 03:42:45] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -29.188802 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 03:42:48] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -29.190788 | E_var:     0.0838 | E_err:   0.004522
[2025-10-06 03:42:50] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -29.182349 | E_var:     0.1153 | E_err:   0.005307
[2025-10-06 03:42:53] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -29.178722 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 03:42:55] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -29.181605 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 03:42:58] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -29.178924 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 03:43:00] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -29.185999 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 03:43:02] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -29.185359 | E_var:     0.1004 | E_err:   0.004950
[2025-10-06 03:43:05] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -29.182478 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 03:43:07] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -29.184109 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 03:43:10] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -29.188979 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 03:43:12] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -29.183610 | E_var:     0.0853 | E_err:   0.004562
[2025-10-06 03:43:15] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -29.189236 | E_var:     0.0827 | E_err:   0.004493
[2025-10-06 03:43:17] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -29.179452 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 03:43:19] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -29.194014 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 03:43:22] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -29.180921 | E_var:     0.0959 | E_err:   0.004839
[2025-10-06 03:43:24] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -29.181553 | E_var:     0.1441 | E_err:   0.005931
[2025-10-06 03:43:27] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -29.186486 | E_var:     0.1572 | E_err:   0.006196
[2025-10-06 03:43:29] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -29.175461 | E_var:     0.4569 | E_err:   0.010561
[2025-10-06 03:43:32] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -29.178617 | E_var:     0.1046 | E_err:   0.005054
[2025-10-06 03:43:34] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -29.183569 | E_var:     0.1441 | E_err:   0.005932
[2025-10-06 03:43:36] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -29.179058 | E_var:     0.1227 | E_err:   0.005474
[2025-10-06 03:43:39] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -29.183159 | E_var:     0.1022 | E_err:   0.004995
[2025-10-06 03:43:41] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -29.182122 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 03:43:44] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -29.183506 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 03:43:46] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -29.186135 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 03:43:49] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -29.194287 | E_var:     0.1075 | E_err:   0.005124
[2025-10-06 03:43:51] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -29.186015 | E_var:     0.0794 | E_err:   0.004403
[2025-10-06 03:43:53] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -29.181247 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 03:43:56] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -29.178280 | E_var:     0.1032 | E_err:   0.005019
[2025-10-06 03:43:58] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -29.189683 | E_var:     0.1650 | E_err:   0.006346
[2025-10-06 03:44:01] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -29.184143 | E_var:     0.1148 | E_err:   0.005294
[2025-10-06 03:44:03] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -29.177205 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 03:44:06] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -29.182479 | E_var:     0.0967 | E_err:   0.004858
[2025-10-06 03:44:08] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -29.190340 | E_var:     0.0943 | E_err:   0.004797
[2025-10-06 03:44:10] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -29.175854 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 03:44:13] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -29.193380 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 03:44:15] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -29.174693 | E_var:     0.0859 | E_err:   0.004581
[2025-10-06 03:44:18] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -29.191103 | E_var:     0.0896 | E_err:   0.004676
[2025-10-06 03:44:20] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -29.181670 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 03:44:20] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 03:44:23] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -29.185204 | E_var:     0.1000 | E_err:   0.004940
[2025-10-06 03:44:25] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -29.186718 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 03:44:27] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -29.186111 | E_var:     0.0811 | E_err:   0.004450
[2025-10-06 03:44:30] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -29.180635 | E_var:     0.1079 | E_err:   0.005132
[2025-10-06 03:44:32] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -29.179696 | E_var:     0.0862 | E_err:   0.004588
[2025-10-06 03:44:35] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -29.175771 | E_var:     0.1165 | E_err:   0.005332
[2025-10-06 03:44:37] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -29.177827 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 03:44:40] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -29.189044 | E_var:     0.0847 | E_err:   0.004549
[2025-10-06 03:44:42] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -29.177151 | E_var:     0.1035 | E_err:   0.005027
[2025-10-06 03:44:44] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -29.179838 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 03:44:47] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -29.181606 | E_var:     0.0790 | E_err:   0.004391
[2025-10-06 03:44:49] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -29.180468 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 03:44:52] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -29.188753 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 03:44:54] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -29.177147 | E_var:     0.0780 | E_err:   0.004362
[2025-10-06 03:44:57] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -29.183569 | E_var:     0.0989 | E_err:   0.004914
[2025-10-06 03:44:59] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -29.186699 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 03:45:01] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -29.181771 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 03:45:04] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -29.186620 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 03:45:06] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -29.181237 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 03:45:09] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -29.191553 | E_var:     0.1256 | E_err:   0.005537
[2025-10-06 03:45:11] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -29.185851 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 03:45:14] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -29.186630 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 03:45:16] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -29.178909 | E_var:     0.1055 | E_err:   0.005074
[2025-10-06 03:45:18] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -29.183420 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 03:45:21] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -29.185288 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 03:45:23] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -29.178243 | E_var:     0.0954 | E_err:   0.004827
[2025-10-06 03:45:26] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -29.180747 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 03:45:28] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -29.191927 | E_var:     0.1195 | E_err:   0.005402
[2025-10-06 03:45:31] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -29.180817 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 03:45:33] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -29.175566 | E_var:     0.1021 | E_err:   0.004993
[2025-10-06 03:45:36] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -29.189509 | E_var:     0.1136 | E_err:   0.005267
[2025-10-06 03:45:38] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -29.187616 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 03:45:40] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -29.184792 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 03:45:43] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -29.186590 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 03:45:45] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -29.179882 | E_var:     0.1040 | E_err:   0.005040
[2025-10-06 03:45:48] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -29.177534 | E_var:     0.1701 | E_err:   0.006444
[2025-10-06 03:45:50] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -29.176213 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 03:45:53] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -29.187431 | E_var:     0.1089 | E_err:   0.005156
[2025-10-06 03:45:55] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -29.187570 | E_var:     0.1309 | E_err:   0.005653
[2025-10-06 03:45:57] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -29.183466 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 03:46:00] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -29.187266 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 03:46:02] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -29.185803 | E_var:     0.0982 | E_err:   0.004897
[2025-10-06 03:46:05] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -29.177592 | E_var:     0.0994 | E_err:   0.004925
[2025-10-06 03:46:07] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -29.195273 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 03:46:10] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -29.189254 | E_var:     0.1326 | E_err:   0.005690
[2025-10-06 03:46:12] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -29.177605 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 03:46:14] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -29.185387 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 03:46:17] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -29.177061 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 03:46:19] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -29.178627 | E_var:     0.0782 | E_err:   0.004369
[2025-10-06 03:46:22] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -29.180837 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 03:46:24] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -29.181917 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 03:46:27] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -29.189304 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 03:46:29] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -29.177995 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 03:46:31] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -29.187885 | E_var:     0.0850 | E_err:   0.004554
[2025-10-06 03:46:34] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -29.183433 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 03:46:36] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -29.179381 | E_var:     0.1175 | E_err:   0.005355
[2025-10-06 03:46:39] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -29.180032 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 03:46:41] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -29.182542 | E_var:     0.0728 | E_err:   0.004215
[2025-10-06 03:46:44] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -29.181136 | E_var:     0.0801 | E_err:   0.004423
[2025-10-06 03:46:46] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -29.183421 | E_var:     0.0835 | E_err:   0.004516
[2025-10-06 03:46:48] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -29.185065 | E_var:     0.0765 | E_err:   0.004322
[2025-10-06 03:46:51] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -29.174348 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 03:46:53] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -29.189136 | E_var:     0.1005 | E_err:   0.004952
[2025-10-06 03:46:56] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -29.186845 | E_var:     0.0976 | E_err:   0.004882
[2025-10-06 03:46:58] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -29.176430 | E_var:     0.0835 | E_err:   0.004514
[2025-10-06 03:47:01] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -29.174596 | E_var:     0.0891 | E_err:   0.004663
[2025-10-06 03:47:03] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -29.179704 | E_var:     0.0929 | E_err:   0.004761
[2025-10-06 03:47:05] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -29.185566 | E_var:     0.0830 | E_err:   0.004503
[2025-10-06 03:47:08] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -29.194878 | E_var:     0.1111 | E_err:   0.005209
[2025-10-06 03:47:10] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -29.179126 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 03:47:13] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -29.178504 | E_var:     0.0675 | E_err:   0.004059
[2025-10-06 03:47:15] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -29.187009 | E_var:     0.0923 | E_err:   0.004748
[2025-10-06 03:47:18] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -29.190707 | E_var:     0.0976 | E_err:   0.004881
[2025-10-06 03:47:20] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -29.180560 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 03:47:22] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -29.178842 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 03:47:25] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -29.179568 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 03:47:27] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -29.185541 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 03:47:30] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -29.182837 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 03:47:32] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -29.183710 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 03:47:34] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -29.191522 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 03:47:37] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -29.188741 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 03:47:39] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -29.183830 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 03:47:42] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -29.183606 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 03:47:44] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -29.186753 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 03:47:47] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -29.179463 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 03:47:49] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -29.181823 | E_var:     0.1066 | E_err:   0.005101
[2025-10-06 03:47:51] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -29.192497 | E_var:     0.0817 | E_err:   0.004467
[2025-10-06 03:47:54] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -29.188555 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 03:47:56] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -29.181232 | E_var:     0.0911 | E_err:   0.004716
[2025-10-06 03:47:59] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -29.168088 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 03:48:01] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -29.185050 | E_var:     0.1001 | E_err:   0.004944
[2025-10-06 03:48:04] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -29.186596 | E_var:     0.1065 | E_err:   0.005098
[2025-10-06 03:48:06] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -29.190382 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 03:48:08] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -29.173091 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 03:48:11] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -29.184354 | E_var:     0.0757 | E_err:   0.004298
[2025-10-06 03:48:13] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -29.184074 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 03:48:16] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -29.178781 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 03:48:18] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -29.184187 | E_var:     0.0779 | E_err:   0.004362
[2025-10-06 03:48:21] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -29.186805 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 03:48:23] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -29.180950 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 03:48:23] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 03:48:26] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -29.177523 | E_var:     0.0819 | E_err:   0.004473
[2025-10-06 03:48:28] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -29.178458 | E_var:     0.0984 | E_err:   0.004900
[2025-10-06 03:48:30] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -29.183537 | E_var:     0.0802 | E_err:   0.004425
[2025-10-06 03:48:33] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -29.179361 | E_var:     0.0977 | E_err:   0.004884
[2025-10-06 03:48:35] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -29.182278 | E_var:     0.0741 | E_err:   0.004252
[2025-10-06 03:48:38] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -29.183208 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 03:48:40] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -29.179478 | E_var:     0.1000 | E_err:   0.004942
[2025-10-06 03:48:43] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -29.179701 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 03:48:45] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -29.186803 | E_var:     0.1007 | E_err:   0.004957
[2025-10-06 03:48:47] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -29.178076 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 03:48:50] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -29.188224 | E_var:     0.0732 | E_err:   0.004226
[2025-10-06 03:48:52] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -29.194444 | E_var:     0.1635 | E_err:   0.006317
[2025-10-06 03:48:55] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -29.180620 | E_var:     0.0715 | E_err:   0.004177
[2025-10-06 03:48:57] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -29.186227 | E_var:     0.1429 | E_err:   0.005907
[2025-10-06 03:49:00] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -29.183189 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 03:49:02] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -29.178860 | E_var:     0.0633 | E_err:   0.003931
[2025-10-06 03:49:04] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -29.182867 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 03:49:07] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -29.185716 | E_var:     0.0950 | E_err:   0.004816
[2025-10-06 03:49:09] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -29.180797 | E_var:     0.0967 | E_err:   0.004858
[2025-10-06 03:49:12] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -29.171534 | E_var:     0.0739 | E_err:   0.004248
[2025-10-06 03:49:14] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -29.183911 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 03:49:17] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -29.174060 | E_var:     0.1045 | E_err:   0.005052
[2025-10-06 03:49:19] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -29.185522 | E_var:     0.1351 | E_err:   0.005743
[2025-10-06 03:49:21] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -29.178087 | E_var:     0.0839 | E_err:   0.004527
[2025-10-06 03:49:24] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -29.187253 | E_var:     0.0757 | E_err:   0.004299
[2025-10-06 03:49:26] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -29.183837 | E_var:     0.0666 | E_err:   0.004033
[2025-10-06 03:49:29] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -29.176822 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 03:49:31] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -29.185134 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 03:49:34] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -29.195693 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 03:49:36] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -29.179708 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 03:49:38] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -29.189178 | E_var:     0.0790 | E_err:   0.004392
[2025-10-06 03:49:41] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -29.184747 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 03:49:43] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -29.185790 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 03:49:46] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -29.177557 | E_var:     0.2335 | E_err:   0.007550
[2025-10-06 03:49:48] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -29.173326 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 03:49:50] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -29.186620 | E_var:     0.0788 | E_err:   0.004385
[2025-10-06 03:49:53] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -29.181651 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 03:49:55] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -29.180436 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 03:49:58] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -29.177159 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 03:50:00] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -29.185813 | E_var:     0.0870 | E_err:   0.004608
[2025-10-06 03:50:03] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -29.186435 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 03:50:05] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -29.179067 | E_var:     0.0983 | E_err:   0.004898
[2025-10-06 03:50:07] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -29.170479 | E_var:     0.1079 | E_err:   0.005133
[2025-10-06 03:50:10] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -29.198370 | E_var:     0.3534 | E_err:   0.009288
[2025-10-06 03:50:12] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -29.182122 | E_var:     0.1187 | E_err:   0.005382
[2025-10-06 03:50:15] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -29.186840 | E_var:     0.0994 | E_err:   0.004926
[2025-10-06 03:50:17] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -29.177391 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 03:50:20] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -29.191568 | E_var:     0.1291 | E_err:   0.005614
[2025-10-06 03:50:22] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -29.180191 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 03:50:24] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -29.177514 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 03:50:27] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -29.190571 | E_var:     0.0950 | E_err:   0.004816
[2025-10-06 03:50:29] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -29.186558 | E_var:     0.0992 | E_err:   0.004920
[2025-10-06 03:50:32] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -29.171356 | E_var:     0.1470 | E_err:   0.005990
[2025-10-06 03:50:34] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -29.180218 | E_var:     0.1425 | E_err:   0.005898
[2025-10-06 03:50:37] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -29.175839 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 03:50:39] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -29.181353 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 03:50:41] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -29.187552 | E_var:     0.1307 | E_err:   0.005649
[2025-10-06 03:50:44] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -29.186962 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 03:50:46] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -29.182443 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 03:50:49] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -29.185653 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 03:50:51] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -29.180390 | E_var:     0.1982 | E_err:   0.006957
[2025-10-06 03:50:54] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -29.183746 | E_var:     0.1261 | E_err:   0.005549
[2025-10-06 03:50:56] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -29.184994 | E_var:     0.1048 | E_err:   0.005058
[2025-10-06 03:50:58] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -29.186950 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 03:51:01] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -29.184958 | E_var:     0.1005 | E_err:   0.004955
[2025-10-06 03:51:03] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -29.181963 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 03:51:06] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -29.176296 | E_var:     0.1132 | E_err:   0.005256
[2025-10-06 03:51:08] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -29.181676 | E_var:     0.1050 | E_err:   0.005064
[2025-10-06 03:51:11] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -29.185208 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 03:51:13] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -29.184901 | E_var:     0.0871 | E_err:   0.004611
[2025-10-06 03:51:15] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -29.181430 | E_var:     0.0717 | E_err:   0.004184
[2025-10-06 03:51:18] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -29.185367 | E_var:     0.0840 | E_err:   0.004529
[2025-10-06 03:51:20] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -29.180596 | E_var:     0.1114 | E_err:   0.005214
[2025-10-06 03:51:23] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -29.177330 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 03:51:25] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -29.182150 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 03:51:28] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -29.173197 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 03:51:30] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -29.189015 | E_var:     0.1339 | E_err:   0.005718
[2025-10-06 03:51:32] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -29.176992 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 03:51:35] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -29.182829 | E_var:     0.0993 | E_err:   0.004924
[2025-10-06 03:51:37] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -29.172781 | E_var:     0.1160 | E_err:   0.005321
[2025-10-06 03:51:40] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -29.186866 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 03:51:42] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -29.189059 | E_var:     0.0915 | E_err:   0.004725
[2025-10-06 03:51:45] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -29.187996 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 03:51:47] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -29.188759 | E_var:     0.1586 | E_err:   0.006222
[2025-10-06 03:51:49] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -29.192252 | E_var:     0.0813 | E_err:   0.004455
[2025-10-06 03:51:52] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -29.188217 | E_var:     0.0781 | E_err:   0.004366
[2025-10-06 03:51:54] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -29.178996 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 03:51:57] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -29.187894 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 03:51:59] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -29.179576 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 03:52:02] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -29.184967 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 03:52:04] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -29.174708 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 03:52:06] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -29.189591 | E_var:     0.0851 | E_err:   0.004558
[2025-10-06 03:52:09] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -29.184050 | E_var:     0.1070 | E_err:   0.005110
[2025-10-06 03:52:11] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -29.186358 | E_var:     0.0702 | E_err:   0.004138
[2025-10-06 03:52:14] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -29.185542 | E_var:     0.1167 | E_err:   0.005338
[2025-10-06 03:52:16] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -29.184432 | E_var:     0.0928 | E_err:   0.004761
[2025-10-06 03:52:19] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -29.188743 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 03:52:21] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -29.177355 | E_var:     0.1644 | E_err:   0.006336
[2025-10-06 03:52:23] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -29.176057 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 03:52:26] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -29.187331 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 03:52:26] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 03:52:28] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -29.181891 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 03:52:31] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -29.189992 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 03:52:33] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -29.183755 | E_var:     0.1325 | E_err:   0.005688
[2025-10-06 03:52:36] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -29.182555 | E_var:     0.0789 | E_err:   0.004390
[2025-10-06 03:52:38] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -29.183659 | E_var:     0.1479 | E_err:   0.006010
[2025-10-06 03:52:40] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -29.178779 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 03:52:43] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -29.181946 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 03:52:45] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -29.177301 | E_var:     0.2129 | E_err:   0.007209
[2025-10-06 03:52:48] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -29.183389 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 03:52:50] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -29.171768 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 03:52:53] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -29.179792 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 03:52:55] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -29.189763 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 03:52:57] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -29.188059 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 03:53:00] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -29.186608 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 03:53:02] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -29.179529 | E_var:     0.1206 | E_err:   0.005426
[2025-10-06 03:53:05] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -29.180036 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 03:53:07] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -29.181079 | E_var:     0.0702 | E_err:   0.004141
[2025-10-06 03:53:10] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -29.195758 | E_var:     0.1126 | E_err:   0.005243
[2025-10-06 03:53:12] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -29.184849 | E_var:     0.1072 | E_err:   0.005116
[2025-10-06 03:53:14] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -29.179560 | E_var:     0.0727 | E_err:   0.004214
[2025-10-06 03:53:17] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -29.185645 | E_var:     0.0870 | E_err:   0.004610
[2025-10-06 03:53:19] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -29.179282 | E_var:     0.0982 | E_err:   0.004897
[2025-10-06 03:53:22] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -29.183221 | E_var:     0.0944 | E_err:   0.004802
[2025-10-06 03:53:24] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -29.177877 | E_var:     0.0793 | E_err:   0.004400
[2025-10-06 03:53:27] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -29.183720 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 03:53:29] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -29.182777 | E_var:     0.1475 | E_err:   0.006000
[2025-10-06 03:53:31] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -29.175937 | E_var:     0.1018 | E_err:   0.004985
[2025-10-06 03:53:34] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -29.190882 | E_var:     0.1145 | E_err:   0.005287
[2025-10-06 03:53:36] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -29.187481 | E_var:     0.0851 | E_err:   0.004558
[2025-10-06 03:53:39] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -29.178013 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 03:53:41] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -29.180605 | E_var:     0.0792 | E_err:   0.004398
[2025-10-06 03:53:44] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -29.178942 | E_var:     0.1041 | E_err:   0.005040
[2025-10-06 03:53:46] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -29.187246 | E_var:     0.1109 | E_err:   0.005202
[2025-10-06 03:53:48] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -29.180024 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 03:53:51] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -29.175237 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 03:53:53] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -29.173738 | E_var:     0.1358 | E_err:   0.005758
[2025-10-06 03:53:56] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -29.189620 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 03:53:58] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -29.175897 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 03:54:01] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -29.179761 | E_var:     0.1513 | E_err:   0.006077
[2025-10-06 03:54:03] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -29.188984 | E_var:     0.0919 | E_err:   0.004736
[2025-10-06 03:54:05] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -29.182423 | E_var:     0.0732 | E_err:   0.004228
[2025-10-06 03:54:08] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -29.172803 | E_var:     0.1949 | E_err:   0.006899
[2025-10-06 03:54:10] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -29.183128 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 03:54:13] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -29.182510 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 03:54:15] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -29.180060 | E_var:     0.0904 | E_err:   0.004697
[2025-10-06 03:54:18] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -29.183632 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 03:54:20] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -29.177909 | E_var:     0.0859 | E_err:   0.004579
[2025-10-06 03:54:22] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -29.191120 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 03:54:25] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -29.187656 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 03:54:27] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -29.186881 | E_var:     0.1000 | E_err:   0.004940
[2025-10-06 03:54:27] ======================================================================================================
[2025-10-06 03:54:27] ✅ Training completed successfully
[2025-10-06 03:54:27] Total restarts: 2
[2025-10-06 03:54:28] Final Energy: -29.18688118 ± 0.00494003
[2025-10-06 03:54:28] Final Variance: 0.099958
[2025-10-06 03:54:28] ======================================================================================================
[2025-10-06 03:54:28] ======================================================================================================
[2025-10-06 03:54:28] Training completed | Runtime: 2600.8s
[2025-10-06 03:54:29] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 03:54:29] ======================================================================================================
