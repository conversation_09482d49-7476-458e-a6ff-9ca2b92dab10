[2025-10-03 02:54:06] ==================================================
[2025-10-03 02:54:06] GCNN for Shastry-Sutherland Model
[2025-10-03 02:54:06] ==================================================
[2025-10-03 02:54:06] System parameters:
[2025-10-03 02:54:06]   - System size: L=4, N=64
[2025-10-03 02:54:06]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-03 02:54:06] --------------------------------------------------
[2025-10-03 02:54:06] Model parameters:
[2025-10-03 02:54:06]   - Number of layers = 8
[2025-10-03 02:54:06]   - Number of features = 4
[2025-10-03 02:54:06]   - Total parameters = 28988
[2025-10-03 02:54:06] --------------------------------------------------
[2025-10-03 02:54:06] Training parameters:
[2025-10-03 02:54:06]   - Total iterations: 2250
[2025-10-03 02:54:06]   - Annealing cycles: 4
[2025-10-03 02:54:06]   - Initial period: 150
[2025-10-03 02:54:06]   - Period multiplier: 2.0
[2025-10-03 02:54:06]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-03 02:54:06]   - Samples: 4096
[2025-10-03 02:54:06]   - Discarded samples: 0
[2025-10-03 02:54:06]   - Chunk size: 4096
[2025-10-03 02:54:06]   - Diagonal shift: 0.15
[2025-10-03 02:54:06]   - Gradient clipping: 1.0
[2025-10-03 02:54:06]   - Checkpoint enabled: interval=200
[2025-10-03 02:54:06]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.76/model_L8F4/training/checkpoints
[2025-10-03 02:54:06] --------------------------------------------------
[2025-10-03 02:54:06] Device status:
[2025-10-03 02:54:06]   - Devices model: NVIDIA H200 NVL
[2025-10-03 02:54:06]   - Number of devices: 1
[2025-10-03 02:54:06]   - Sharding: True
[2025-10-03 02:54:58] [Iter    1/2250] R0[0/150]    | LR: 0.030000 | E:  32.304339 | E_var:     0.1021 | E_err:   0.004992
[2025-10-03 02:55:03] [Iter    2/2250] R0[2/150]    | LR: 0.029989 | E:  32.301505 | E_var:     0.1542 | E_err:   0.006137
[2025-10-03 02:55:08] [Iter    3/2250] R0[4/150]    | LR: 0.029956 | E:  32.284894 | E_var:     0.2419 | E_err:   0.007684
[2025-10-03 02:55:13] [Iter    4/2250] R0[6/150]    | LR: 0.029901 | E:  32.254887 | E_var:     0.4013 | E_err:   0.009899
[2025-10-03 02:55:18] [Iter    5/2250] R0[8/150]    | LR: 0.029825 | E:  32.226896 | E_var:     0.7720 | E_err:   0.013729
[2025-10-03 02:55:23] [Iter    6/2250] R0[10/150]   | LR: 0.029727 | E:  32.143658 | E_var:     1.5161 | E_err:   0.019239
[2025-10-03 02:55:28] [Iter    7/2250] R0[12/150]   | LR: 0.029607 | E:  31.877410 | E_var:     4.1173 | E_err:   0.031705
[2025-10-03 02:55:33] [Iter    8/2250] R0[14/150]   | LR: 0.029466 | E:  31.093161 | E_var:    11.9177 | E_err:   0.053941
[2025-10-03 02:55:38] [Iter    9/2250] R0[16/150]   | LR: 0.029305 | E:  28.575508 | E_var:    40.8255 | E_err:   0.099836
[2025-10-03 02:55:43] [Iter   10/2250] R0[18/150]   | LR: 0.029122 | E:  21.880501 | E_var:    76.4140 | E_err:   0.136586
[2025-10-03 02:55:48] [Iter   11/2250] R0[20/150]   | LR: 0.028919 | E:  13.813237 | E_var:    61.3129 | E_err:   0.122348
[2025-10-03 02:55:53] [Iter   12/2250] R0[22/150]   | LR: 0.028696 | E:   8.987681 | E_var:    48.1904 | E_err:   0.108468
[2025-10-03 02:55:57] [Iter   13/2250] R0[24/150]   | LR: 0.028454 | E:   5.524425 | E_var:    41.0130 | E_err:   0.100065
[2025-10-03 02:56:02] [Iter   14/2250] R0[26/150]   | LR: 0.028192 | E:   2.653037 | E_var:    33.9622 | E_err:   0.091058
[2025-10-03 02:56:07] [Iter   15/2250] R0[28/150]   | LR: 0.027912 | E:   0.678073 | E_var:    33.2877 | E_err:   0.090149
[2025-10-03 02:56:12] [Iter   16/2250] R0[30/150]   | LR: 0.027613 | E:  -0.978701 | E_var:    26.5138 | E_err:   0.080456
[2025-10-03 02:56:17] [Iter   17/2250] R0[32/150]   | LR: 0.027296 | E:  -2.205667 | E_var:    24.1014 | E_err:   0.076708
[2025-10-03 02:56:22] [Iter   18/2250] R0[34/150]   | LR: 0.026962 | E:  -3.231558 | E_var:    25.7792 | E_err:   0.079333
[2025-10-03 02:56:27] [Iter   19/2250] R0[36/150]   | LR: 0.026612 | E:  -4.067707 | E_var:    24.1171 | E_err:   0.076733
[2025-10-03 02:56:32] [Iter   20/2250] R0[38/150]   | LR: 0.026246 | E:  -5.058857 | E_var:    21.1561 | E_err:   0.071868
[2025-10-03 02:56:37] [Iter   21/2250] R0[40/150]   | LR: 0.025864 | E:  -5.732902 | E_var:    19.7015 | E_err:   0.069354
[2025-10-03 02:56:42] [Iter   22/2250] R0[42/150]   | LR: 0.025468 | E:  -6.182666 | E_var:    20.2828 | E_err:   0.070369
[2025-10-03 02:56:47] [Iter   23/2250] R0[44/150]   | LR: 0.025057 | E:  -6.868237 | E_var:    20.4717 | E_err:   0.070696
[2025-10-03 02:56:52] [Iter   24/2250] R0[46/150]   | LR: 0.024634 | E:  -7.378424 | E_var:    19.1171 | E_err:   0.068317
[2025-10-03 02:56:57] [Iter   25/2250] R0[48/150]   | LR: 0.024198 | E:  -7.861548 | E_var:    17.7573 | E_err:   0.065843
[2025-10-03 02:57:02] [Iter   26/2250] R0[50/150]   | LR: 0.023750 | E:  -8.417299 | E_var:    20.2550 | E_err:   0.070321
[2025-10-03 02:57:07] [Iter   27/2250] R0[52/150]   | LR: 0.023291 | E:  -8.760115 | E_var:    18.6227 | E_err:   0.067428
[2025-10-03 02:57:12] [Iter   28/2250] R0[54/150]   | LR: 0.022822 | E:  -9.160120 | E_var:    18.7528 | E_err:   0.067663
[2025-10-03 02:57:16] [Iter   29/2250] R0[56/150]   | LR: 0.022344 | E:  -9.663247 | E_var:    19.7958 | E_err:   0.069520
[2025-10-03 02:57:21] [Iter   30/2250] R0[58/150]   | LR: 0.021857 | E:  -9.952710 | E_var:    17.9124 | E_err:   0.066130
[2025-10-03 02:57:26] [Iter   31/2250] R0[60/150]   | LR: 0.021363 | E: -10.310615 | E_var:    16.4772 | E_err:   0.063425
[2025-10-03 02:57:31] [Iter   32/2250] R0[62/150]   | LR: 0.020861 | E: -10.649703 | E_var:    16.5868 | E_err:   0.063636
[2025-10-03 02:57:36] [Iter   33/2250] R0[64/150]   | LR: 0.020354 | E: -11.050207 | E_var:    17.1269 | E_err:   0.064663
[2025-10-03 02:57:41] [Iter   34/2250] R0[66/150]   | LR: 0.019842 | E: -11.437654 | E_var:    17.3222 | E_err:   0.065031
[2025-10-03 02:57:46] [Iter   35/2250] R0[68/150]   | LR: 0.019326 | E: -11.750319 | E_var:    16.3380 | E_err:   0.063157
[2025-10-03 02:57:51] [Iter   36/2250] R0[70/150]   | LR: 0.018807 | E: -12.052606 | E_var:    16.5930 | E_err:   0.063648
[2025-10-03 02:57:56] [Iter   37/2250] R0[72/150]   | LR: 0.018285 | E: -12.279085 | E_var:    17.2231 | E_err:   0.064845
[2025-10-03 02:58:01] [Iter   38/2250] R0[74/150]   | LR: 0.017762 | E: -12.691130 | E_var:    16.0414 | E_err:   0.062581
[2025-10-03 02:58:06] [Iter   39/2250] R0[76/150]   | LR: 0.017238 | E: -13.010372 | E_var:    14.8366 | E_err:   0.060185
[2025-10-03 02:58:11] [Iter   40/2250] R0[78/150]   | LR: 0.016715 | E: -13.338902 | E_var:    16.5102 | E_err:   0.063489
[2025-10-03 02:58:16] [Iter   41/2250] R0[80/150]   | LR: 0.016193 | E: -13.563763 | E_var:    15.4169 | E_err:   0.061351
[2025-10-03 02:58:21] [Iter   42/2250] R0[82/150]   | LR: 0.015674 | E: -13.801016 | E_var:    13.7213 | E_err:   0.057879
[2025-10-03 02:58:26] [Iter   43/2250] R0[84/150]   | LR: 0.015158 | E: -14.109620 | E_var:    15.7310 | E_err:   0.061972
[2025-10-03 02:58:31] [Iter   44/2250] R0[86/150]   | LR: 0.014646 | E: -14.367455 | E_var:    14.8856 | E_err:   0.060284
[2025-10-03 02:58:36] [Iter   45/2250] R0[88/150]   | LR: 0.014139 | E: -14.584631 | E_var:    13.1650 | E_err:   0.056693
[2025-10-03 02:58:40] [Iter   46/2250] R0[90/150]   | LR: 0.013637 | E: -14.875647 | E_var:    13.4888 | E_err:   0.057386
[2025-10-03 02:58:45] [Iter   47/2250] R0[92/150]   | LR: 0.013143 | E: -15.005809 | E_var:    18.5054 | E_err:   0.067216
[2025-10-03 02:58:50] [Iter   48/2250] R0[94/150]   | LR: 0.012656 | E: -15.338020 | E_var:    14.2990 | E_err:   0.059084
[2025-10-03 02:58:55] [Iter   49/2250] R0[96/150]   | LR: 0.012178 | E: -15.562835 | E_var:    13.3330 | E_err:   0.057054
[2025-10-03 02:59:00] [Iter   50/2250] R0[98/150]   | LR: 0.011709 | E: -15.749337 | E_var:    12.8384 | E_err:   0.055985
[2025-10-03 02:59:05] [Iter   51/2250] R0[100/150]  | LR: 0.011250 | E: -15.918289 | E_var:    12.7448 | E_err:   0.055781
[2025-10-03 02:59:10] [Iter   52/2250] R0[102/150]  | LR: 0.010802 | E: -16.106095 | E_var:    12.5425 | E_err:   0.055337
[2025-10-03 02:59:15] [Iter   53/2250] R0[104/150]  | LR: 0.010366 | E: -16.178447 | E_var:    12.1986 | E_err:   0.054573
[2025-10-03 02:59:20] [Iter   54/2250] R0[106/150]  | LR: 0.009943 | E: -16.489665 | E_var:    12.4312 | E_err:   0.055091
[2025-10-03 02:59:25] [Iter   55/2250] R0[108/150]  | LR: 0.009532 | E: -16.666367 | E_var:    12.6085 | E_err:   0.055482
[2025-10-03 02:59:30] [Iter   56/2250] R0[110/150]  | LR: 0.009136 | E: -16.860957 | E_var:    12.2112 | E_err:   0.054601
[2025-10-03 02:59:35] [Iter   57/2250] R0[112/150]  | LR: 0.008754 | E: -17.035521 | E_var:    12.3856 | E_err:   0.054989
[2025-10-03 02:59:40] [Iter   58/2250] R0[114/150]  | LR: 0.008388 | E: -17.215430 | E_var:    11.2087 | E_err:   0.052311
[2025-10-03 02:59:45] [Iter   59/2250] R0[116/150]  | LR: 0.008038 | E: -17.399357 | E_var:    11.0404 | E_err:   0.051917
[2025-10-03 02:59:50] [Iter   60/2250] R0[118/150]  | LR: 0.007704 | E: -17.534372 | E_var:    11.0494 | E_err:   0.051939
[2025-10-03 02:59:55] [Iter   61/2250] R0[120/150]  | LR: 0.007387 | E: -17.636426 | E_var:    11.7584 | E_err:   0.053579
[2025-10-03 03:00:00] [Iter   62/2250] R0[122/150]  | LR: 0.007088 | E: -17.828529 | E_var:    11.7720 | E_err:   0.053610
[2025-10-03 03:00:05] [Iter   63/2250] R0[124/150]  | LR: 0.006808 | E: -17.953782 | E_var:    12.4900 | E_err:   0.055221
[2025-10-03 03:00:09] [Iter   64/2250] R0[126/150]  | LR: 0.006546 | E: -18.109263 | E_var:    10.7214 | E_err:   0.051162
[2025-10-03 03:00:14] [Iter   65/2250] R0[128/150]  | LR: 0.006304 | E: -18.238050 | E_var:    11.3327 | E_err:   0.052600
[2025-10-03 03:00:19] [Iter   66/2250] R0[130/150]  | LR: 0.006081 | E: -18.382080 | E_var:    11.8833 | E_err:   0.053863
[2025-10-03 03:00:24] [Iter   67/2250] R0[132/150]  | LR: 0.005878 | E: -18.535953 | E_var:    10.6890 | E_err:   0.051084
[2025-10-03 03:00:29] [Iter   68/2250] R0[134/150]  | LR: 0.005695 | E: -18.641127 | E_var:    10.4613 | E_err:   0.050537
[2025-10-03 03:00:34] [Iter   69/2250] R0[136/150]  | LR: 0.005534 | E: -18.702693 | E_var:    10.7279 | E_err:   0.051177
[2025-10-03 03:00:39] [Iter   70/2250] R0[138/150]  | LR: 0.005393 | E: -18.848694 | E_var:    10.2482 | E_err:   0.050020
[2025-10-03 03:00:44] [Iter   71/2250] R0[140/150]  | LR: 0.005273 | E: -18.974564 | E_var:     9.7169 | E_err:   0.048706
[2025-10-03 03:00:49] [Iter   72/2250] R0[142/150]  | LR: 0.005175 | E: -19.051642 | E_var:     9.3460 | E_err:   0.047768
[2025-10-03 03:00:54] [Iter   73/2250] R0[144/150]  | LR: 0.005099 | E: -19.166460 | E_var:    10.1869 | E_err:   0.049870
[2025-10-03 03:00:59] [Iter   74/2250] R0[146/150]  | LR: 0.005044 | E: -19.351124 | E_var:     9.4006 | E_err:   0.047907
[2025-10-03 03:01:04] [Iter   75/2250] R0[148/150]  | LR: 0.005011 | E: -19.348091 | E_var:     9.5033 | E_err:   0.048168
[2025-10-03 03:01:04] 🔄 RESTART #1 | Period: 300
[2025-10-03 03:01:09] [Iter   76/2250] R1[0/300]    | LR: 0.030000 | E: -19.481043 | E_var:    10.0361 | E_err:   0.049500
[2025-10-03 03:01:14] [Iter   77/2250] R1[2/300]    | LR: 0.029997 | E: -19.589206 | E_var:     9.0882 | E_err:   0.047104
[2025-10-03 03:01:19] [Iter   78/2250] R1[4/300]    | LR: 0.029989 | E: -19.646129 | E_var:     9.4128 | E_err:   0.047938
[2025-10-03 03:01:24] [Iter   79/2250] R1[6/300]    | LR: 0.029975 | E: -19.825438 | E_var:     9.0621 | E_err:   0.047037
[2025-10-03 03:01:28] [Iter   80/2250] R1[8/300]    | LR: 0.029956 | E: -19.886767 | E_var:     9.1730 | E_err:   0.047323
[2025-10-03 03:01:33] [Iter   81/2250] R1[10/300]   | LR: 0.029932 | E: -19.953089 | E_var:     8.6603 | E_err:   0.045982
[2025-10-03 03:01:38] [Iter   82/2250] R1[12/300]   | LR: 0.029901 | E: -20.011471 | E_var:     8.7673 | E_err:   0.046265
[2025-10-03 03:01:43] [Iter   83/2250] R1[14/300]   | LR: 0.029866 | E: -20.060118 | E_var:     8.5360 | E_err:   0.045651
[2025-10-03 03:01:48] [Iter   84/2250] R1[16/300]   | LR: 0.029825 | E: -20.177983 | E_var:     8.2993 | E_err:   0.045013
[2025-10-03 03:01:53] [Iter   85/2250] R1[18/300]   | LR: 0.029779 | E: -20.249923 | E_var:     8.6667 | E_err:   0.045999
[2025-10-03 03:01:58] [Iter   86/2250] R1[20/300]   | LR: 0.029727 | E: -20.320379 | E_var:     7.9171 | E_err:   0.043965
[2025-10-03 03:02:03] [Iter   87/2250] R1[22/300]   | LR: 0.029670 | E: -20.373135 | E_var:     8.2724 | E_err:   0.044940
[2025-10-03 03:02:08] [Iter   88/2250] R1[24/300]   | LR: 0.029607 | E: -20.466262 | E_var:     8.7113 | E_err:   0.046117
[2025-10-03 03:02:13] [Iter   89/2250] R1[26/300]   | LR: 0.029540 | E: -20.535700 | E_var:     7.7499 | E_err:   0.043498
[2025-10-03 03:02:18] [Iter   90/2250] R1[28/300]   | LR: 0.029466 | E: -20.684601 | E_var:     7.6918 | E_err:   0.043334
[2025-10-03 03:02:23] [Iter   91/2250] R1[30/300]   | LR: 0.029388 | E: -20.658302 | E_var:     7.9175 | E_err:   0.043966
[2025-10-03 03:02:28] [Iter   92/2250] R1[32/300]   | LR: 0.029305 | E: -20.809908 | E_var:     7.6367 | E_err:   0.043179
[2025-10-03 03:02:33] [Iter   93/2250] R1[34/300]   | LR: 0.029216 | E: -20.833360 | E_var:     7.8344 | E_err:   0.043734
[2025-10-03 03:02:38] [Iter   94/2250] R1[36/300]   | LR: 0.029122 | E: -20.870534 | E_var:     6.8798 | E_err:   0.040983
[2025-10-03 03:02:43] [Iter   95/2250] R1[38/300]   | LR: 0.029023 | E: -20.985324 | E_var:     7.3232 | E_err:   0.042284
[2025-10-03 03:02:47] [Iter   96/2250] R1[40/300]   | LR: 0.028919 | E: -21.029471 | E_var:     7.0512 | E_err:   0.041491
[2025-10-03 03:02:52] [Iter   97/2250] R1[42/300]   | LR: 0.028810 | E: -21.131499 | E_var:     6.9491 | E_err:   0.041189
[2025-10-03 03:02:57] [Iter   98/2250] R1[44/300]   | LR: 0.028696 | E: -21.136564 | E_var:     6.5731 | E_err:   0.040059
[2025-10-03 03:03:02] [Iter   99/2250] R1[46/300]   | LR: 0.028578 | E: -21.227291 | E_var:     7.2256 | E_err:   0.042001
[2025-10-03 03:03:07] [Iter  100/2250] R1[48/300]   | LR: 0.028454 | E: -21.261881 | E_var:     6.7991 | E_err:   0.040742
[2025-10-03 03:03:12] [Iter  101/2250] R1[50/300]   | LR: 0.028325 | E: -21.360125 | E_var:     6.8927 | E_err:   0.041022
[2025-10-03 03:03:17] [Iter  102/2250] R1[52/300]   | LR: 0.028192 | E: -21.416706 | E_var:     6.2972 | E_err:   0.039210
[2025-10-03 03:03:22] [Iter  103/2250] R1[54/300]   | LR: 0.028054 | E: -21.426235 | E_var:     6.1063 | E_err:   0.038611
[2025-10-03 03:03:27] [Iter  104/2250] R1[56/300]   | LR: 0.027912 | E: -21.551911 | E_var:     6.5028 | E_err:   0.039845
[2025-10-03 03:03:32] [Iter  105/2250] R1[58/300]   | LR: 0.027764 | E: -21.639260 | E_var:     6.4166 | E_err:   0.039580
[2025-10-03 03:03:37] [Iter  106/2250] R1[60/300]   | LR: 0.027613 | E: -21.656492 | E_var:     6.0889 | E_err:   0.038556
[2025-10-03 03:03:42] [Iter  107/2250] R1[62/300]   | LR: 0.027457 | E: -21.766012 | E_var:     6.1551 | E_err:   0.038765
[2025-10-03 03:03:47] [Iter  108/2250] R1[64/300]   | LR: 0.027296 | E: -21.743144 | E_var:     5.7909 | E_err:   0.037601
[2025-10-03 03:03:52] [Iter  109/2250] R1[66/300]   | LR: 0.027131 | E: -21.817201 | E_var:     6.0266 | E_err:   0.038358
[2025-10-03 03:03:57] [Iter  110/2250] R1[68/300]   | LR: 0.026962 | E: -21.930789 | E_var:     5.5674 | E_err:   0.036868
[2025-10-03 03:04:02] [Iter  111/2250] R1[70/300]   | LR: 0.026789 | E: -21.920760 | E_var:     6.2925 | E_err:   0.039195
[2025-10-03 03:04:06] [Iter  112/2250] R1[72/300]   | LR: 0.026612 | E: -22.000492 | E_var:     5.9795 | E_err:   0.038208
[2025-10-03 03:04:11] [Iter  113/2250] R1[74/300]   | LR: 0.026431 | E: -22.061538 | E_var:     5.6491 | E_err:   0.037137
[2025-10-03 03:04:16] [Iter  114/2250] R1[76/300]   | LR: 0.026246 | E: -22.075095 | E_var:     6.0985 | E_err:   0.038586
[2025-10-03 03:04:21] [Iter  115/2250] R1[78/300]   | LR: 0.026057 | E: -22.197763 | E_var:     5.5514 | E_err:   0.036815
[2025-10-03 03:04:26] [Iter  116/2250] R1[80/300]   | LR: 0.025864 | E: -22.194269 | E_var:     5.2044 | E_err:   0.035646
[2025-10-03 03:04:31] [Iter  117/2250] R1[82/300]   | LR: 0.025668 | E: -22.235278 | E_var:     5.4616 | E_err:   0.036516
[2025-10-03 03:04:36] [Iter  118/2250] R1[84/300]   | LR: 0.025468 | E: -22.311936 | E_var:     5.5009 | E_err:   0.036647
[2025-10-03 03:04:41] [Iter  119/2250] R1[86/300]   | LR: 0.025264 | E: -22.399783 | E_var:     5.2083 | E_err:   0.035659
[2025-10-03 03:04:46] [Iter  120/2250] R1[88/300]   | LR: 0.025057 | E: -22.413096 | E_var:     4.7706 | E_err:   0.034128
[2025-10-03 03:04:51] [Iter  121/2250] R1[90/300]   | LR: 0.024847 | E: -22.499728 | E_var:     4.7607 | E_err:   0.034092
[2025-10-03 03:04:56] [Iter  122/2250] R1[92/300]   | LR: 0.024634 | E: -22.505736 | E_var:     4.5338 | E_err:   0.033270
[2025-10-03 03:05:01] [Iter  123/2250] R1[94/300]   | LR: 0.024417 | E: -22.574386 | E_var:     4.9504 | E_err:   0.034765
[2025-10-03 03:05:06] [Iter  124/2250] R1[96/300]   | LR: 0.024198 | E: -22.637023 | E_var:     4.8952 | E_err:   0.034571
[2025-10-03 03:05:11] [Iter  125/2250] R1[98/300]   | LR: 0.023975 | E: -22.663423 | E_var:     4.6352 | E_err:   0.033640
[2025-10-03 03:05:16] [Iter  126/2250] R1[100/300]  | LR: 0.023750 | E: -22.695114 | E_var:     5.0449 | E_err:   0.035095
[2025-10-03 03:05:21] [Iter  127/2250] R1[102/300]  | LR: 0.023522 | E: -22.766362 | E_var:     4.3199 | E_err:   0.032476
[2025-10-03 03:05:25] [Iter  128/2250] R1[104/300]  | LR: 0.023291 | E: -22.795542 | E_var:     4.3360 | E_err:   0.032536
[2025-10-03 03:05:30] [Iter  129/2250] R1[106/300]  | LR: 0.023058 | E: -22.843067 | E_var:     4.4274 | E_err:   0.032877
[2025-10-03 03:05:35] [Iter  130/2250] R1[108/300]  | LR: 0.022822 | E: -22.882487 | E_var:     4.2807 | E_err:   0.032328
[2025-10-03 03:05:40] [Iter  131/2250] R1[110/300]  | LR: 0.022584 | E: -22.880421 | E_var:     4.3474 | E_err:   0.032579
[2025-10-03 03:05:45] [Iter  132/2250] R1[112/300]  | LR: 0.022344 | E: -22.973101 | E_var:     4.2629 | E_err:   0.032261
[2025-10-03 03:05:50] [Iter  133/2250] R1[114/300]  | LR: 0.022102 | E: -23.001851 | E_var:     4.0753 | E_err:   0.031543
[2025-10-03 03:05:55] [Iter  134/2250] R1[116/300]  | LR: 0.021857 | E: -23.023943 | E_var:     4.0677 | E_err:   0.031513
[2025-10-03 03:06:00] [Iter  135/2250] R1[118/300]  | LR: 0.021611 | E: -23.061964 | E_var:     4.0997 | E_err:   0.031637
[2025-10-03 03:06:05] [Iter  136/2250] R1[120/300]  | LR: 0.021363 | E: -23.139282 | E_var:     3.6190 | E_err:   0.029725
[2025-10-03 03:06:10] [Iter  137/2250] R1[122/300]  | LR: 0.021113 | E: -23.175660 | E_var:     3.7884 | E_err:   0.030412
[2025-10-03 03:06:15] [Iter  138/2250] R1[124/300]  | LR: 0.020861 | E: -23.196292 | E_var:     3.8093 | E_err:   0.030496
[2025-10-03 03:06:20] [Iter  139/2250] R1[126/300]  | LR: 0.020609 | E: -23.257321 | E_var:     3.5060 | E_err:   0.029257
[2025-10-03 03:06:25] [Iter  140/2250] R1[128/300]  | LR: 0.020354 | E: -23.296328 | E_var:     4.0392 | E_err:   0.031403
[2025-10-03 03:06:30] [Iter  141/2250] R1[130/300]  | LR: 0.020099 | E: -23.304510 | E_var:     3.6883 | E_err:   0.030008
[2025-10-03 03:06:35] [Iter  142/2250] R1[132/300]  | LR: 0.019842 | E: -23.303089 | E_var:     3.6854 | E_err:   0.029996
[2025-10-03 03:06:39] [Iter  143/2250] R1[134/300]  | LR: 0.019585 | E: -23.375436 | E_var:     3.6007 | E_err:   0.029649
[2025-10-03 03:06:44] [Iter  144/2250] R1[136/300]  | LR: 0.019326 | E: -23.454255 | E_var:     3.7039 | E_err:   0.030071
[2025-10-03 03:06:49] [Iter  145/2250] R1[138/300]  | LR: 0.019067 | E: -23.400180 | E_var:     3.5850 | E_err:   0.029584
[2025-10-03 03:06:54] [Iter  146/2250] R1[140/300]  | LR: 0.018807 | E: -23.489871 | E_var:     3.9277 | E_err:   0.030966
[2025-10-03 03:06:59] [Iter  147/2250] R1[142/300]  | LR: 0.018546 | E: -23.503360 | E_var:     3.6759 | E_err:   0.029957
[2025-10-03 03:07:04] [Iter  148/2250] R1[144/300]  | LR: 0.018285 | E: -23.506714 | E_var:     3.5788 | E_err:   0.029559
[2025-10-03 03:07:09] [Iter  149/2250] R1[146/300]  | LR: 0.018023 | E: -23.597485 | E_var:     3.2421 | E_err:   0.028134
[2025-10-03 03:07:14] [Iter  150/2250] R1[148/300]  | LR: 0.017762 | E: -23.577402 | E_var:     3.5296 | E_err:   0.029355
[2025-10-03 03:07:19] [Iter  151/2250] R1[150/300]  | LR: 0.017500 | E: -23.618161 | E_var:     3.7955 | E_err:   0.030441
[2025-10-03 03:07:24] [Iter  152/2250] R1[152/300]  | LR: 0.017238 | E: -23.666342 | E_var:     3.3429 | E_err:   0.028568
[2025-10-03 03:07:29] [Iter  153/2250] R1[154/300]  | LR: 0.016977 | E: -23.668931 | E_var:     3.3762 | E_err:   0.028710
[2025-10-03 03:07:34] [Iter  154/2250] R1[156/300]  | LR: 0.016715 | E: -23.694982 | E_var:     3.2179 | E_err:   0.028029
[2025-10-03 03:07:39] [Iter  155/2250] R1[158/300]  | LR: 0.016454 | E: -23.722714 | E_var:     3.5379 | E_err:   0.029390
[2025-10-03 03:07:44] [Iter  156/2250] R1[160/300]  | LR: 0.016193 | E: -23.767236 | E_var:     3.0784 | E_err:   0.027414
[2025-10-03 03:07:49] [Iter  157/2250] R1[162/300]  | LR: 0.015933 | E: -23.768840 | E_var:     3.1165 | E_err:   0.027584
[2025-10-03 03:07:54] [Iter  158/2250] R1[164/300]  | LR: 0.015674 | E: -23.781124 | E_var:     3.1193 | E_err:   0.027596
[2025-10-03 03:07:58] [Iter  159/2250] R1[166/300]  | LR: 0.015415 | E: -23.824036 | E_var:     3.0846 | E_err:   0.027442
[2025-10-03 03:08:03] [Iter  160/2250] R1[168/300]  | LR: 0.015158 | E: -23.807989 | E_var:     3.0804 | E_err:   0.027424
[2025-10-03 03:08:08] [Iter  161/2250] R1[170/300]  | LR: 0.014901 | E: -23.854177 | E_var:     3.0087 | E_err:   0.027102
[2025-10-03 03:08:13] [Iter  162/2250] R1[172/300]  | LR: 0.014646 | E: -23.902584 | E_var:     3.1686 | E_err:   0.027813
[2025-10-03 03:08:18] [Iter  163/2250] R1[174/300]  | LR: 0.014391 | E: -23.894592 | E_var:     3.0694 | E_err:   0.027375
[2025-10-03 03:08:23] [Iter  164/2250] R1[176/300]  | LR: 0.014139 | E: -23.952199 | E_var:     2.9473 | E_err:   0.026825
[2025-10-03 03:08:28] [Iter  165/2250] R1[178/300]  | LR: 0.013887 | E: -23.958293 | E_var:     3.4847 | E_err:   0.029168
[2025-10-03 03:08:33] [Iter  166/2250] R1[180/300]  | LR: 0.013637 | E: -24.023249 | E_var:     2.9915 | E_err:   0.027025
[2025-10-03 03:08:38] [Iter  167/2250] R1[182/300]  | LR: 0.013389 | E: -24.019553 | E_var:     3.1173 | E_err:   0.027587
[2025-10-03 03:08:43] [Iter  168/2250] R1[184/300]  | LR: 0.013143 | E: -24.027379 | E_var:     3.0229 | E_err:   0.027166
[2025-10-03 03:08:48] [Iter  169/2250] R1[186/300]  | LR: 0.012898 | E: -24.074936 | E_var:     2.9555 | E_err:   0.026862
[2025-10-03 03:08:53] [Iter  170/2250] R1[188/300]  | LR: 0.012656 | E: -24.101886 | E_var:     2.9174 | E_err:   0.026688
[2025-10-03 03:08:58] [Iter  171/2250] R1[190/300]  | LR: 0.012416 | E: -24.091201 | E_var:     3.0014 | E_err:   0.027070
[2025-10-03 03:09:03] [Iter  172/2250] R1[192/300]  | LR: 0.012178 | E: -24.112826 | E_var:     2.8130 | E_err:   0.026206
[2025-10-03 03:09:08] [Iter  173/2250] R1[194/300]  | LR: 0.011942 | E: -24.144015 | E_var:     2.7962 | E_err:   0.026128
[2025-10-03 03:09:12] [Iter  174/2250] R1[196/300]  | LR: 0.011709 | E: -24.182120 | E_var:     2.8317 | E_err:   0.026293
[2025-10-03 03:09:17] [Iter  175/2250] R1[198/300]  | LR: 0.011478 | E: -24.220178 | E_var:     3.1720 | E_err:   0.027828
[2025-10-03 03:09:22] [Iter  176/2250] R1[200/300]  | LR: 0.011250 | E: -24.262456 | E_var:     2.9127 | E_err:   0.026667
[2025-10-03 03:09:27] [Iter  177/2250] R1[202/300]  | LR: 0.011025 | E: -24.245526 | E_var:     2.9197 | E_err:   0.026699
[2025-10-03 03:09:32] [Iter  178/2250] R1[204/300]  | LR: 0.010802 | E: -24.281874 | E_var:     2.9084 | E_err:   0.026647
[2025-10-03 03:09:37] [Iter  179/2250] R1[206/300]  | LR: 0.010583 | E: -24.294910 | E_var:     2.8916 | E_err:   0.026570
[2025-10-03 03:09:42] [Iter  180/2250] R1[208/300]  | LR: 0.010366 | E: -24.345680 | E_var:     2.7107 | E_err:   0.025726
[2025-10-03 03:09:47] [Iter  181/2250] R1[210/300]  | LR: 0.010153 | E: -24.392290 | E_var:     3.1522 | E_err:   0.027741
[2025-10-03 03:09:52] [Iter  182/2250] R1[212/300]  | LR: 0.009943 | E: -24.381801 | E_var:     2.7775 | E_err:   0.026040
[2025-10-03 03:09:57] [Iter  183/2250] R1[214/300]  | LR: 0.009736 | E: -24.417463 | E_var:     2.6216 | E_err:   0.025299
[2025-10-03 03:10:02] [Iter  184/2250] R1[216/300]  | LR: 0.009532 | E: -24.412707 | E_var:     2.8295 | E_err:   0.026283
[2025-10-03 03:10:07] [Iter  185/2250] R1[218/300]  | LR: 0.009332 | E: -24.471686 | E_var:     2.6124 | E_err:   0.025255
[2025-10-03 03:10:12] [Iter  186/2250] R1[220/300]  | LR: 0.009136 | E: -24.492891 | E_var:     2.7221 | E_err:   0.025780
[2025-10-03 03:10:17] [Iter  187/2250] R1[222/300]  | LR: 0.008943 | E: -24.508170 | E_var:     2.7680 | E_err:   0.025996
[2025-10-03 03:10:22] [Iter  188/2250] R1[224/300]  | LR: 0.008754 | E: -24.527179 | E_var:     2.6571 | E_err:   0.025470
[2025-10-03 03:10:26] [Iter  189/2250] R1[226/300]  | LR: 0.008569 | E: -24.540113 | E_var:     2.8022 | E_err:   0.026156
[2025-10-03 03:10:31] [Iter  190/2250] R1[228/300]  | LR: 0.008388 | E: -24.604716 | E_var:     2.5044 | E_err:   0.024727
[2025-10-03 03:10:36] [Iter  191/2250] R1[230/300]  | LR: 0.008211 | E: -24.590074 | E_var:     3.7501 | E_err:   0.030258
[2025-10-03 03:10:41] [Iter  192/2250] R1[232/300]  | LR: 0.008038 | E: -24.666522 | E_var:     3.0645 | E_err:   0.027353
[2025-10-03 03:10:46] [Iter  193/2250] R1[234/300]  | LR: 0.007869 | E: -24.694729 | E_var:     2.8766 | E_err:   0.026501
[2025-10-03 03:10:51] [Iter  194/2250] R1[236/300]  | LR: 0.007704 | E: -24.708189 | E_var:     2.7377 | E_err:   0.025853
[2025-10-03 03:10:56] [Iter  195/2250] R1[238/300]  | LR: 0.007543 | E: -24.723625 | E_var:     2.6773 | E_err:   0.025567
[2025-10-03 03:11:01] [Iter  196/2250] R1[240/300]  | LR: 0.007387 | E: -24.748130 | E_var:     2.6733 | E_err:   0.025547
[2025-10-03 03:11:06] [Iter  197/2250] R1[242/300]  | LR: 0.007236 | E: -24.833738 | E_var:     2.6194 | E_err:   0.025288
[2025-10-03 03:11:11] [Iter  198/2250] R1[244/300]  | LR: 0.007088 | E: -24.881557 | E_var:     2.7223 | E_err:   0.025781
[2025-10-03 03:11:16] [Iter  199/2250] R1[246/300]  | LR: 0.006946 | E: -24.912862 | E_var:     2.5964 | E_err:   0.025177
[2025-10-03 03:11:21] [Iter  200/2250] R1[248/300]  | LR: 0.006808 | E: -24.946146 | E_var:     2.6106 | E_err:   0.025246
[2025-10-03 03:11:21] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-03 03:11:26] [Iter  201/2250] R1[250/300]  | LR: 0.006675 | E: -24.956341 | E_var:     2.7564 | E_err:   0.025941
[2025-10-03 03:11:31] [Iter  202/2250] R1[252/300]  | LR: 0.006546 | E: -24.998653 | E_var:     2.6716 | E_err:   0.025539
[2025-10-03 03:11:36] [Iter  203/2250] R1[254/300]  | LR: 0.006422 | E: -25.046810 | E_var:     2.6325 | E_err:   0.025352
[2025-10-03 03:11:41] [Iter  204/2250] R1[256/300]  | LR: 0.006304 | E: -25.066235 | E_var:     2.5421 | E_err:   0.024913
[2025-10-03 03:11:45] [Iter  205/2250] R1[258/300]  | LR: 0.006190 | E: -25.134177 | E_var:     2.7502 | E_err:   0.025912
[2025-10-03 03:11:50] [Iter  206/2250] R1[260/300]  | LR: 0.006081 | E: -25.173960 | E_var:     2.5087 | E_err:   0.024748
[2025-10-03 03:11:55] [Iter  207/2250] R1[262/300]  | LR: 0.005977 | E: -25.235394 | E_var:     2.6432 | E_err:   0.025403
[2025-10-03 03:12:00] [Iter  208/2250] R1[264/300]  | LR: 0.005878 | E: -25.281632 | E_var:     2.8704 | E_err:   0.026472
[2025-10-03 03:12:05] [Iter  209/2250] R1[266/300]  | LR: 0.005784 | E: -25.324231 | E_var:     2.5287 | E_err:   0.024847
[2025-10-03 03:12:10] [Iter  210/2250] R1[268/300]  | LR: 0.005695 | E: -25.388381 | E_var:     2.6498 | E_err:   0.025435
[2025-10-03 03:12:15] [Iter  211/2250] R1[270/300]  | LR: 0.005612 | E: -25.431505 | E_var:     2.4162 | E_err:   0.024288
[2025-10-03 03:12:20] [Iter  212/2250] R1[272/300]  | LR: 0.005534 | E: -25.499008 | E_var:     2.4387 | E_err:   0.024401
[2025-10-03 03:12:25] [Iter  213/2250] R1[274/300]  | LR: 0.005460 | E: -25.528806 | E_var:     2.7797 | E_err:   0.026051
[2025-10-03 03:12:30] [Iter  214/2250] R1[276/300]  | LR: 0.005393 | E: -25.593973 | E_var:     2.4186 | E_err:   0.024300
[2025-10-03 03:12:35] [Iter  215/2250] R1[278/300]  | LR: 0.005330 | E: -25.686156 | E_var:     2.4677 | E_err:   0.024545
[2025-10-03 03:12:40] [Iter  216/2250] R1[280/300]  | LR: 0.005273 | E: -25.772725 | E_var:     2.4048 | E_err:   0.024230
[2025-10-03 03:12:45] [Iter  217/2250] R1[282/300]  | LR: 0.005221 | E: -25.793171 | E_var:     2.2840 | E_err:   0.023614
[2025-10-03 03:12:50] [Iter  218/2250] R1[284/300]  | LR: 0.005175 | E: -25.873781 | E_var:     2.3678 | E_err:   0.024043
[2025-10-03 03:12:55] [Iter  219/2250] R1[286/300]  | LR: 0.005134 | E: -25.936561 | E_var:     1.9960 | E_err:   0.022075
[2025-10-03 03:13:00] [Iter  220/2250] R1[288/300]  | LR: 0.005099 | E: -25.985135 | E_var:     2.2552 | E_err:   0.023465
[2025-10-03 03:13:04] [Iter  221/2250] R1[290/300]  | LR: 0.005068 | E: -26.085436 | E_var:     2.0582 | E_err:   0.022416
[2025-10-03 03:13:09] [Iter  222/2250] R1[292/300]  | LR: 0.005044 | E: -26.136315 | E_var:     2.0548 | E_err:   0.022398
[2025-10-03 03:13:14] [Iter  223/2250] R1[294/300]  | LR: 0.005025 | E: -26.236661 | E_var:     1.8433 | E_err:   0.021214
[2025-10-03 03:13:19] [Iter  224/2250] R1[296/300]  | LR: 0.005011 | E: -26.303952 | E_var:     1.9502 | E_err:   0.021820
[2025-10-03 03:13:24] [Iter  225/2250] R1[298/300]  | LR: 0.005003 | E: -26.363625 | E_var:     1.7050 | E_err:   0.020403
[2025-10-03 03:13:24] 🔄 RESTART #2 | Period: 600
[2025-10-03 03:13:29] [Iter  226/2250] R2[0/600]    | LR: 0.030000 | E: -26.415275 | E_var:     1.9591 | E_err:   0.021870
[2025-10-03 03:13:34] [Iter  227/2250] R2[2/600]    | LR: 0.029999 | E: -26.453977 | E_var:     1.6196 | E_err:   0.019885
[2025-10-03 03:13:39] [Iter  228/2250] R2[4/600]    | LR: 0.029997 | E: -26.517271 | E_var:     1.4121 | E_err:   0.018567
[2025-10-03 03:13:44] [Iter  229/2250] R2[6/600]    | LR: 0.029994 | E: -26.559934 | E_var:     1.3901 | E_err:   0.018422
[2025-10-03 03:13:49] [Iter  230/2250] R2[8/600]    | LR: 0.029989 | E: -26.611409 | E_var:     1.3815 | E_err:   0.018365
[2025-10-03 03:13:54] [Iter  231/2250] R2[10/600]   | LR: 0.029983 | E: -26.666212 | E_var:     1.1769 | E_err:   0.016951
[2025-10-03 03:13:59] [Iter  232/2250] R2[12/600]   | LR: 0.029975 | E: -26.648218 | E_var:     1.1475 | E_err:   0.016738
[2025-10-03 03:14:04] [Iter  233/2250] R2[14/600]   | LR: 0.029966 | E: -26.712527 | E_var:     1.0538 | E_err:   0.016040
[2025-10-03 03:14:09] [Iter  234/2250] R2[16/600]   | LR: 0.029956 | E: -26.733783 | E_var:     1.2071 | E_err:   0.017167
[2025-10-03 03:14:14] [Iter  235/2250] R2[18/600]   | LR: 0.029945 | E: -26.747022 | E_var:     1.0668 | E_err:   0.016139
[2025-10-03 03:14:18] [Iter  236/2250] R2[20/600]   | LR: 0.029932 | E: -26.787827 | E_var:     0.9462 | E_err:   0.015199
[2025-10-03 03:14:23] [Iter  237/2250] R2[22/600]   | LR: 0.029917 | E: -26.807032 | E_var:     0.9248 | E_err:   0.015026
[2025-10-03 03:14:28] [Iter  238/2250] R2[24/600]   | LR: 0.029901 | E: -26.826247 | E_var:     0.8326 | E_err:   0.014258
[2025-10-03 03:14:33] [Iter  239/2250] R2[26/600]   | LR: 0.029884 | E: -26.840740 | E_var:     0.9064 | E_err:   0.014876
[2025-10-03 03:14:38] [Iter  240/2250] R2[28/600]   | LR: 0.029866 | E: -26.860022 | E_var:     0.7705 | E_err:   0.013715
[2025-10-03 03:14:43] [Iter  241/2250] R2[30/600]   | LR: 0.029846 | E: -26.886494 | E_var:     0.7638 | E_err:   0.013656
[2025-10-03 03:14:48] [Iter  242/2250] R2[32/600]   | LR: 0.029825 | E: -26.891029 | E_var:     0.7181 | E_err:   0.013241
[2025-10-03 03:14:53] [Iter  243/2250] R2[34/600]   | LR: 0.029802 | E: -26.897952 | E_var:     0.7205 | E_err:   0.013263
[2025-10-03 03:14:58] [Iter  244/2250] R2[36/600]   | LR: 0.029779 | E: -26.909486 | E_var:     0.7512 | E_err:   0.013543
[2025-10-03 03:15:03] [Iter  245/2250] R2[38/600]   | LR: 0.029753 | E: -26.914846 | E_var:     0.7931 | E_err:   0.013915
[2025-10-03 03:15:08] [Iter  246/2250] R2[40/600]   | LR: 0.029727 | E: -26.928853 | E_var:     0.7585 | E_err:   0.013608
[2025-10-03 03:15:13] [Iter  247/2250] R2[42/600]   | LR: 0.029699 | E: -26.934131 | E_var:     0.6244 | E_err:   0.012347
[2025-10-03 03:15:18] [Iter  248/2250] R2[44/600]   | LR: 0.029670 | E: -26.959290 | E_var:     0.7364 | E_err:   0.013408
[2025-10-03 03:15:23] [Iter  249/2250] R2[46/600]   | LR: 0.029639 | E: -26.959177 | E_var:     0.5911 | E_err:   0.012013
[2025-10-03 03:15:28] [Iter  250/2250] R2[48/600]   | LR: 0.029607 | E: -26.961719 | E_var:     0.5891 | E_err:   0.011993
[2025-10-03 03:15:32] [Iter  251/2250] R2[50/600]   | LR: 0.029574 | E: -26.986121 | E_var:     0.5722 | E_err:   0.011820
[2025-10-03 03:15:37] [Iter  252/2250] R2[52/600]   | LR: 0.029540 | E: -26.979262 | E_var:     0.5697 | E_err:   0.011794
[2025-10-03 03:15:42] [Iter  253/2250] R2[54/600]   | LR: 0.029504 | E: -26.970418 | E_var:     0.4779 | E_err:   0.010801
[2025-10-03 03:15:47] [Iter  254/2250] R2[56/600]   | LR: 0.029466 | E: -26.991781 | E_var:     0.4129 | E_err:   0.010040
[2025-10-03 03:15:52] [Iter  255/2250] R2[58/600]   | LR: 0.029428 | E: -26.990423 | E_var:     0.5576 | E_err:   0.011667
[2025-10-03 03:15:57] [Iter  256/2250] R2[60/600]   | LR: 0.029388 | E: -26.991739 | E_var:     0.4972 | E_err:   0.011018
[2025-10-03 03:16:02] [Iter  257/2250] R2[62/600]   | LR: 0.029347 | E: -26.982732 | E_var:     0.4746 | E_err:   0.010765
[2025-10-03 03:16:07] [Iter  258/2250] R2[64/600]   | LR: 0.029305 | E: -26.996981 | E_var:     0.5054 | E_err:   0.011108
[2025-10-03 03:16:12] [Iter  259/2250] R2[66/600]   | LR: 0.029261 | E: -26.998992 | E_var:     0.5025 | E_err:   0.011076
[2025-10-03 03:16:17] [Iter  260/2250] R2[68/600]   | LR: 0.029216 | E: -27.000511 | E_var:     0.4488 | E_err:   0.010468
[2025-10-03 03:16:22] [Iter  261/2250] R2[70/600]   | LR: 0.029170 | E: -26.999555 | E_var:     0.4521 | E_err:   0.010506
[2025-10-03 03:16:27] [Iter  262/2250] R2[72/600]   | LR: 0.029122 | E: -27.004951 | E_var:     0.4306 | E_err:   0.010253
[2025-10-03 03:16:32] [Iter  263/2250] R2[74/600]   | LR: 0.029073 | E: -27.012672 | E_var:     0.6239 | E_err:   0.012341
[2025-10-03 03:16:37] [Iter  264/2250] R2[76/600]   | LR: 0.029023 | E: -27.026329 | E_var:     0.5235 | E_err:   0.011305
[2025-10-03 03:16:42] [Iter  265/2250] R2[78/600]   | LR: 0.028972 | E: -27.021353 | E_var:     0.4434 | E_err:   0.010405
[2025-10-03 03:16:46] [Iter  266/2250] R2[80/600]   | LR: 0.028919 | E: -27.025533 | E_var:     0.4859 | E_err:   0.010892
[2025-10-03 03:16:51] [Iter  267/2250] R2[82/600]   | LR: 0.028865 | E: -27.030280 | E_var:     0.4041 | E_err:   0.009933
[2025-10-03 03:16:56] [Iter  268/2250] R2[84/600]   | LR: 0.028810 | E: -27.024563 | E_var:     0.4209 | E_err:   0.010137
[2025-10-03 03:17:01] [Iter  269/2250] R2[86/600]   | LR: 0.028754 | E: -27.020332 | E_var:     0.5062 | E_err:   0.011117
[2025-10-03 03:17:06] [Iter  270/2250] R2[88/600]   | LR: 0.028696 | E: -27.030155 | E_var:     0.5320 | E_err:   0.011397
[2025-10-03 03:17:11] [Iter  271/2250] R2[90/600]   | LR: 0.028638 | E: -27.026086 | E_var:     0.4174 | E_err:   0.010095
[2025-10-03 03:17:16] [Iter  272/2250] R2[92/600]   | LR: 0.028578 | E: -27.021572 | E_var:     0.4284 | E_err:   0.010227
[2025-10-03 03:17:21] [Iter  273/2250] R2[94/600]   | LR: 0.028516 | E: -27.030939 | E_var:     0.4939 | E_err:   0.010981
[2025-10-03 03:17:26] [Iter  274/2250] R2[96/600]   | LR: 0.028454 | E: -27.031935 | E_var:     0.4270 | E_err:   0.010210
[2025-10-03 03:17:31] [Iter  275/2250] R2[98/600]   | LR: 0.028390 | E: -27.040201 | E_var:     0.4489 | E_err:   0.010469
[2025-10-03 03:17:36] [Iter  276/2250] R2[100/600]  | LR: 0.028325 | E: -27.034706 | E_var:     0.4060 | E_err:   0.009956
[2025-10-03 03:17:41] [Iter  277/2250] R2[102/600]  | LR: 0.028259 | E: -27.029498 | E_var:     0.4319 | E_err:   0.010268
[2025-10-03 03:17:46] [Iter  278/2250] R2[104/600]  | LR: 0.028192 | E: -27.038004 | E_var:     0.4831 | E_err:   0.010860
[2025-10-03 03:17:51] [Iter  279/2250] R2[106/600]  | LR: 0.028124 | E: -27.045764 | E_var:     0.4415 | E_err:   0.010382
[2025-10-03 03:17:56] [Iter  280/2250] R2[108/600]  | LR: 0.028054 | E: -27.047911 | E_var:     0.4432 | E_err:   0.010403
[2025-10-03 03:18:00] [Iter  281/2250] R2[110/600]  | LR: 0.027983 | E: -27.048770 | E_var:     0.4725 | E_err:   0.010740
[2025-10-03 03:18:05] [Iter  282/2250] R2[112/600]  | LR: 0.027912 | E: -27.040584 | E_var:     0.3818 | E_err:   0.009655
[2025-10-03 03:18:10] [Iter  283/2250] R2[114/600]  | LR: 0.027839 | E: -27.058700 | E_var:     0.4328 | E_err:   0.010279
[2025-10-03 03:18:15] [Iter  284/2250] R2[116/600]  | LR: 0.027764 | E: -27.058176 | E_var:     0.3998 | E_err:   0.009879
[2025-10-03 03:18:20] [Iter  285/2250] R2[118/600]  | LR: 0.027689 | E: -27.049972 | E_var:     0.4111 | E_err:   0.010019
[2025-10-03 03:18:25] [Iter  286/2250] R2[120/600]  | LR: 0.027613 | E: -27.061773 | E_var:     0.4108 | E_err:   0.010014
[2025-10-03 03:18:30] [Iter  287/2250] R2[122/600]  | LR: 0.027535 | E: -27.056169 | E_var:     0.3709 | E_err:   0.009516
[2025-10-03 03:18:35] [Iter  288/2250] R2[124/600]  | LR: 0.027457 | E: -27.046948 | E_var:     0.4767 | E_err:   0.010789
[2025-10-03 03:18:40] [Iter  289/2250] R2[126/600]  | LR: 0.027377 | E: -27.043066 | E_var:     0.3656 | E_err:   0.009448
[2025-10-03 03:18:45] [Iter  290/2250] R2[128/600]  | LR: 0.027296 | E: -27.065780 | E_var:     0.7230 | E_err:   0.013286
[2025-10-03 03:18:50] [Iter  291/2250] R2[130/600]  | LR: 0.027214 | E: -27.048960 | E_var:     0.3653 | E_err:   0.009444
[2025-10-03 03:18:55] [Iter  292/2250] R2[132/600]  | LR: 0.027131 | E: -27.056189 | E_var:     0.4440 | E_err:   0.010412
[2025-10-03 03:19:00] [Iter  293/2250] R2[134/600]  | LR: 0.027047 | E: -27.054407 | E_var:     0.4380 | E_err:   0.010341
[2025-10-03 03:19:05] [Iter  294/2250] R2[136/600]  | LR: 0.026962 | E: -27.049187 | E_var:     0.4671 | E_err:   0.010679
[2025-10-03 03:19:10] [Iter  295/2250] R2[138/600]  | LR: 0.026876 | E: -27.054868 | E_var:     0.5771 | E_err:   0.011870
[2025-10-03 03:19:14] [Iter  296/2250] R2[140/600]  | LR: 0.026789 | E: -27.069590 | E_var:     0.3860 | E_err:   0.009708
[2025-10-03 03:19:19] [Iter  297/2250] R2[142/600]  | LR: 0.026701 | E: -27.059491 | E_var:     0.3719 | E_err:   0.009529
[2025-10-03 03:19:24] [Iter  298/2250] R2[144/600]  | LR: 0.026612 | E: -27.058875 | E_var:     0.4655 | E_err:   0.010661
[2025-10-03 03:19:29] [Iter  299/2250] R2[146/600]  | LR: 0.026522 | E: -27.069698 | E_var:     0.3972 | E_err:   0.009848
[2025-10-03 03:19:34] [Iter  300/2250] R2[148/600]  | LR: 0.026431 | E: -27.068996 | E_var:     0.3873 | E_err:   0.009724
[2025-10-03 03:19:39] [Iter  301/2250] R2[150/600]  | LR: 0.026339 | E: -27.051057 | E_var:     0.4054 | E_err:   0.009949
[2025-10-03 03:19:44] [Iter  302/2250] R2[152/600]  | LR: 0.026246 | E: -27.060657 | E_var:     0.3641 | E_err:   0.009429
[2025-10-03 03:19:49] [Iter  303/2250] R2[154/600]  | LR: 0.026152 | E: -27.053878 | E_var:     0.4136 | E_err:   0.010049
[2025-10-03 03:19:54] [Iter  304/2250] R2[156/600]  | LR: 0.026057 | E: -27.079677 | E_var:     0.4171 | E_err:   0.010091
[2025-10-03 03:19:59] [Iter  305/2250] R2[158/600]  | LR: 0.025961 | E: -27.060329 | E_var:     0.3823 | E_err:   0.009661
[2025-10-03 03:20:04] [Iter  306/2250] R2[160/600]  | LR: 0.025864 | E: -27.070501 | E_var:     0.3684 | E_err:   0.009484
[2025-10-03 03:20:09] [Iter  307/2250] R2[162/600]  | LR: 0.025766 | E: -27.066798 | E_var:     0.3548 | E_err:   0.009307
[2025-10-03 03:20:14] [Iter  308/2250] R2[164/600]  | LR: 0.025668 | E: -27.074165 | E_var:     0.4233 | E_err:   0.010166
[2025-10-03 03:20:19] [Iter  309/2250] R2[166/600]  | LR: 0.025568 | E: -27.070806 | E_var:     0.3486 | E_err:   0.009226
[2025-10-03 03:20:24] [Iter  310/2250] R2[168/600]  | LR: 0.025468 | E: -27.066226 | E_var:     0.4010 | E_err:   0.009895
[2025-10-03 03:20:28] [Iter  311/2250] R2[170/600]  | LR: 0.025367 | E: -27.066327 | E_var:     0.3717 | E_err:   0.009525
[2025-10-03 03:20:33] [Iter  312/2250] R2[172/600]  | LR: 0.025264 | E: -27.057615 | E_var:     0.3941 | E_err:   0.009809
[2025-10-03 03:20:38] [Iter  313/2250] R2[174/600]  | LR: 0.025161 | E: -27.064560 | E_var:     0.3357 | E_err:   0.009053
[2025-10-03 03:20:43] [Iter  314/2250] R2[176/600]  | LR: 0.025057 | E: -27.069099 | E_var:     0.3973 | E_err:   0.009849
[2025-10-03 03:20:48] [Iter  315/2250] R2[178/600]  | LR: 0.024953 | E: -27.067639 | E_var:     0.3980 | E_err:   0.009858
[2025-10-03 03:20:53] [Iter  316/2250] R2[180/600]  | LR: 0.024847 | E: -27.061489 | E_var:     0.4379 | E_err:   0.010340
[2025-10-03 03:20:58] [Iter  317/2250] R2[182/600]  | LR: 0.024741 | E: -27.065969 | E_var:     0.3301 | E_err:   0.008977
[2025-10-03 03:21:03] [Iter  318/2250] R2[184/600]  | LR: 0.024634 | E: -27.073334 | E_var:     0.3759 | E_err:   0.009580
[2025-10-03 03:21:08] [Iter  319/2250] R2[186/600]  | LR: 0.024526 | E: -27.068159 | E_var:     0.3523 | E_err:   0.009274
[2025-10-03 03:21:13] [Iter  320/2250] R2[188/600]  | LR: 0.024417 | E: -27.068100 | E_var:     0.3838 | E_err:   0.009681
[2025-10-03 03:21:18] [Iter  321/2250] R2[190/600]  | LR: 0.024308 | E: -27.070870 | E_var:     0.4263 | E_err:   0.010201
[2025-10-03 03:21:23] [Iter  322/2250] R2[192/600]  | LR: 0.024198 | E: -27.067011 | E_var:     0.3303 | E_err:   0.008980
[2025-10-03 03:21:28] [Iter  323/2250] R2[194/600]  | LR: 0.024087 | E: -27.082524 | E_var:     0.3269 | E_err:   0.008934
[2025-10-03 03:21:33] [Iter  324/2250] R2[196/600]  | LR: 0.023975 | E: -27.083983 | E_var:     0.4645 | E_err:   0.010649
[2025-10-03 03:21:38] [Iter  325/2250] R2[198/600]  | LR: 0.023863 | E: -27.069835 | E_var:     0.4000 | E_err:   0.009882
[2025-10-03 03:21:43] [Iter  326/2250] R2[200/600]  | LR: 0.023750 | E: -27.071131 | E_var:     0.3510 | E_err:   0.009257
[2025-10-03 03:21:47] [Iter  327/2250] R2[202/600]  | LR: 0.023636 | E: -27.084066 | E_var:     0.3929 | E_err:   0.009794
[2025-10-03 03:21:52] [Iter  328/2250] R2[204/600]  | LR: 0.023522 | E: -27.081585 | E_var:     0.3526 | E_err:   0.009278
[2025-10-03 03:21:57] [Iter  329/2250] R2[206/600]  | LR: 0.023407 | E: -27.083115 | E_var:     0.3570 | E_err:   0.009335
[2025-10-03 03:22:02] [Iter  330/2250] R2[208/600]  | LR: 0.023291 | E: -27.080777 | E_var:     0.3042 | E_err:   0.008618
[2025-10-03 03:22:07] [Iter  331/2250] R2[210/600]  | LR: 0.023175 | E: -27.069766 | E_var:     0.3199 | E_err:   0.008838
[2025-10-03 03:22:12] [Iter  332/2250] R2[212/600]  | LR: 0.023058 | E: -27.060550 | E_var:     0.3210 | E_err:   0.008852
[2025-10-03 03:22:17] [Iter  333/2250] R2[214/600]  | LR: 0.022940 | E: -27.090804 | E_var:     0.3732 | E_err:   0.009546
[2025-10-03 03:22:22] [Iter  334/2250] R2[216/600]  | LR: 0.022822 | E: -27.085270 | E_var:     0.3551 | E_err:   0.009310
[2025-10-03 03:22:27] [Iter  335/2250] R2[218/600]  | LR: 0.022704 | E: -27.077883 | E_var:     0.3518 | E_err:   0.009267
[2025-10-03 03:22:32] [Iter  336/2250] R2[220/600]  | LR: 0.022584 | E: -27.080403 | E_var:     0.3685 | E_err:   0.009485
[2025-10-03 03:22:37] [Iter  337/2250] R2[222/600]  | LR: 0.022464 | E: -27.095267 | E_var:     0.3502 | E_err:   0.009247
[2025-10-03 03:22:42] [Iter  338/2250] R2[224/600]  | LR: 0.022344 | E: -27.074614 | E_var:     0.3196 | E_err:   0.008833
[2025-10-03 03:22:47] [Iter  339/2250] R2[226/600]  | LR: 0.022223 | E: -27.079930 | E_var:     0.3578 | E_err:   0.009347
[2025-10-03 03:22:52] [Iter  340/2250] R2[228/600]  | LR: 0.022102 | E: -27.080182 | E_var:     0.3111 | E_err:   0.008715
[2025-10-03 03:22:56] [Iter  341/2250] R2[230/600]  | LR: 0.021980 | E: -27.079537 | E_var:     0.3767 | E_err:   0.009590
[2025-10-03 03:23:01] [Iter  342/2250] R2[232/600]  | LR: 0.021857 | E: -27.080567 | E_var:     0.3132 | E_err:   0.008745
[2025-10-03 03:23:06] [Iter  343/2250] R2[234/600]  | LR: 0.021734 | E: -27.086170 | E_var:     0.3353 | E_err:   0.009047
[2025-10-03 03:23:11] [Iter  344/2250] R2[236/600]  | LR: 0.021611 | E: -27.076588 | E_var:     0.3618 | E_err:   0.009399
[2025-10-03 03:23:16] [Iter  345/2250] R2[238/600]  | LR: 0.021487 | E: -27.078757 | E_var:     0.3664 | E_err:   0.009458
[2025-10-03 03:23:21] [Iter  346/2250] R2[240/600]  | LR: 0.021363 | E: -27.081044 | E_var:     0.3256 | E_err:   0.008916
[2025-10-03 03:23:26] [Iter  347/2250] R2[242/600]  | LR: 0.021238 | E: -27.093359 | E_var:     0.3540 | E_err:   0.009296
[2025-10-03 03:23:31] [Iter  348/2250] R2[244/600]  | LR: 0.021113 | E: -27.083104 | E_var:     0.3513 | E_err:   0.009261
[2025-10-03 03:23:36] [Iter  349/2250] R2[246/600]  | LR: 0.020987 | E: -27.083519 | E_var:     0.2901 | E_err:   0.008415
[2025-10-03 03:23:41] [Iter  350/2250] R2[248/600]  | LR: 0.020861 | E: -27.077058 | E_var:     0.3040 | E_err:   0.008614
[2025-10-03 03:23:46] [Iter  351/2250] R2[250/600]  | LR: 0.020735 | E: -27.090555 | E_var:     0.2935 | E_err:   0.008465
[2025-10-03 03:23:51] [Iter  352/2250] R2[252/600]  | LR: 0.020609 | E: -27.082516 | E_var:     0.3536 | E_err:   0.009291
[2025-10-03 03:23:56] [Iter  353/2250] R2[254/600]  | LR: 0.020482 | E: -27.083404 | E_var:     0.2949 | E_err:   0.008486
[2025-10-03 03:24:01] [Iter  354/2250] R2[256/600]  | LR: 0.020354 | E: -27.086722 | E_var:     0.3173 | E_err:   0.008801
[2025-10-03 03:24:06] [Iter  355/2250] R2[258/600]  | LR: 0.020227 | E: -27.076337 | E_var:     0.4000 | E_err:   0.009882
[2025-10-03 03:24:11] [Iter  356/2250] R2[260/600]  | LR: 0.020099 | E: -27.082713 | E_var:     0.2728 | E_err:   0.008161
[2025-10-03 03:24:15] [Iter  357/2250] R2[262/600]  | LR: 0.019971 | E: -27.090154 | E_var:     0.3001 | E_err:   0.008560
[2025-10-03 03:24:20] [Iter  358/2250] R2[264/600]  | LR: 0.019842 | E: -27.074286 | E_var:     0.3304 | E_err:   0.008981
[2025-10-03 03:24:25] [Iter  359/2250] R2[266/600]  | LR: 0.019714 | E: -27.095181 | E_var:     0.3673 | E_err:   0.009470
[2025-10-03 03:24:30] [Iter  360/2250] R2[268/600]  | LR: 0.019585 | E: -27.078268 | E_var:     0.2602 | E_err:   0.007970
[2025-10-03 03:24:35] [Iter  361/2250] R2[270/600]  | LR: 0.019455 | E: -27.078950 | E_var:     0.3097 | E_err:   0.008696
[2025-10-03 03:24:40] [Iter  362/2250] R2[272/600]  | LR: 0.019326 | E: -27.078859 | E_var:     0.3009 | E_err:   0.008570
[2025-10-03 03:24:45] [Iter  363/2250] R2[274/600]  | LR: 0.019196 | E: -27.084968 | E_var:     0.3104 | E_err:   0.008705
[2025-10-03 03:24:50] [Iter  364/2250] R2[276/600]  | LR: 0.019067 | E: -27.079913 | E_var:     0.4948 | E_err:   0.010991
[2025-10-03 03:24:55] [Iter  365/2250] R2[278/600]  | LR: 0.018937 | E: -27.095643 | E_var:     0.3204 | E_err:   0.008845
[2025-10-03 03:25:00] [Iter  366/2250] R2[280/600]  | LR: 0.018807 | E: -27.093685 | E_var:     0.2724 | E_err:   0.008154
[2025-10-03 03:25:05] [Iter  367/2250] R2[282/600]  | LR: 0.018676 | E: -27.083794 | E_var:     0.3329 | E_err:   0.009016
[2025-10-03 03:25:10] [Iter  368/2250] R2[284/600]  | LR: 0.018546 | E: -27.090410 | E_var:     0.2954 | E_err:   0.008492
[2025-10-03 03:25:15] [Iter  369/2250] R2[286/600]  | LR: 0.018415 | E: -27.071860 | E_var:     0.2692 | E_err:   0.008107
[2025-10-03 03:25:20] [Iter  370/2250] R2[288/600]  | LR: 0.018285 | E: -27.092287 | E_var:     0.3115 | E_err:   0.008720
[2025-10-03 03:25:25] [Iter  371/2250] R2[290/600]  | LR: 0.018154 | E: -27.085809 | E_var:     0.3283 | E_err:   0.008953
[2025-10-03 03:25:29] [Iter  372/2250] R2[292/600]  | LR: 0.018023 | E: -27.093028 | E_var:     0.2777 | E_err:   0.008235
[2025-10-03 03:25:34] [Iter  373/2250] R2[294/600]  | LR: 0.017893 | E: -27.090775 | E_var:     0.2960 | E_err:   0.008500
[2025-10-03 03:25:39] [Iter  374/2250] R2[296/600]  | LR: 0.017762 | E: -27.092241 | E_var:     0.3729 | E_err:   0.009541
[2025-10-03 03:25:44] [Iter  375/2250] R2[298/600]  | LR: 0.017631 | E: -27.097502 | E_var:     0.3060 | E_err:   0.008644
[2025-10-03 03:25:49] [Iter  376/2250] R2[300/600]  | LR: 0.017500 | E: -27.085009 | E_var:     1.1574 | E_err:   0.016810
[2025-10-03 03:25:54] [Iter  377/2250] R2[302/600]  | LR: 0.017369 | E: -27.090194 | E_var:     0.4492 | E_err:   0.010472
[2025-10-03 03:25:59] [Iter  378/2250] R2[304/600]  | LR: 0.017238 | E: -27.080312 | E_var:     0.3095 | E_err:   0.008692
[2025-10-03 03:26:04] [Iter  379/2250] R2[306/600]  | LR: 0.017107 | E: -27.097223 | E_var:     0.3755 | E_err:   0.009575
[2025-10-03 03:26:09] [Iter  380/2250] R2[308/600]  | LR: 0.016977 | E: -27.091067 | E_var:     0.2617 | E_err:   0.007994
[2025-10-03 03:26:14] [Iter  381/2250] R2[310/600]  | LR: 0.016846 | E: -27.093762 | E_var:     0.3452 | E_err:   0.009180
[2025-10-03 03:26:19] [Iter  382/2250] R2[312/600]  | LR: 0.016715 | E: -27.096827 | E_var:     0.3084 | E_err:   0.008678
[2025-10-03 03:26:24] [Iter  383/2250] R2[314/600]  | LR: 0.016585 | E: -27.086970 | E_var:     0.3284 | E_err:   0.008954
[2025-10-03 03:26:29] [Iter  384/2250] R2[316/600]  | LR: 0.016454 | E: -27.096382 | E_var:     0.3139 | E_err:   0.008755
[2025-10-03 03:26:34] [Iter  385/2250] R2[318/600]  | LR: 0.016324 | E: -27.094455 | E_var:     0.2987 | E_err:   0.008539
[2025-10-03 03:26:39] [Iter  386/2250] R2[320/600]  | LR: 0.016193 | E: -27.089712 | E_var:     0.2888 | E_err:   0.008397
[2025-10-03 03:26:44] [Iter  387/2250] R2[322/600]  | LR: 0.016063 | E: -27.085699 | E_var:     0.3126 | E_err:   0.008736
[2025-10-03 03:26:48] [Iter  388/2250] R2[324/600]  | LR: 0.015933 | E: -27.082360 | E_var:     0.3347 | E_err:   0.009040
[2025-10-03 03:26:53] [Iter  389/2250] R2[326/600]  | LR: 0.015804 | E: -27.075090 | E_var:     0.2910 | E_err:   0.008429
[2025-10-03 03:26:58] [Iter  390/2250] R2[328/600]  | LR: 0.015674 | E: -27.085616 | E_var:     0.3206 | E_err:   0.008848
[2025-10-03 03:27:03] [Iter  391/2250] R2[330/600]  | LR: 0.015545 | E: -27.086844 | E_var:     0.2649 | E_err:   0.008041
[2025-10-03 03:27:08] [Iter  392/2250] R2[332/600]  | LR: 0.015415 | E: -27.088763 | E_var:     0.2786 | E_err:   0.008247
[2025-10-03 03:27:13] [Iter  393/2250] R2[334/600]  | LR: 0.015286 | E: -27.100281 | E_var:     0.3189 | E_err:   0.008824
[2025-10-03 03:27:18] [Iter  394/2250] R2[336/600]  | LR: 0.015158 | E: -27.103467 | E_var:     0.2785 | E_err:   0.008246
[2025-10-03 03:27:23] [Iter  395/2250] R2[338/600]  | LR: 0.015029 | E: -27.091660 | E_var:     0.2910 | E_err:   0.008429
[2025-10-03 03:27:28] [Iter  396/2250] R2[340/600]  | LR: 0.014901 | E: -27.094716 | E_var:     0.2557 | E_err:   0.007901
[2025-10-03 03:27:33] [Iter  397/2250] R2[342/600]  | LR: 0.014773 | E: -27.098696 | E_var:     0.2735 | E_err:   0.008172
[2025-10-03 03:27:38] [Iter  398/2250] R2[344/600]  | LR: 0.014646 | E: -27.102088 | E_var:     0.3096 | E_err:   0.008693
[2025-10-03 03:27:43] [Iter  399/2250] R2[346/600]  | LR: 0.014518 | E: -27.094673 | E_var:     0.2926 | E_err:   0.008451
[2025-10-03 03:27:48] [Iter  400/2250] R2[348/600]  | LR: 0.014391 | E: -27.090809 | E_var:     0.3165 | E_err:   0.008791
[2025-10-03 03:27:48] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-03 03:27:53] [Iter  401/2250] R2[350/600]  | LR: 0.014265 | E: -27.092698 | E_var:     0.3128 | E_err:   0.008738
[2025-10-03 03:27:58] [Iter  402/2250] R2[352/600]  | LR: 0.014139 | E: -27.096729 | E_var:     0.2781 | E_err:   0.008240
[2025-10-03 03:28:03] [Iter  403/2250] R2[354/600]  | LR: 0.014013 | E: -27.094897 | E_var:     0.2463 | E_err:   0.007754
[2025-10-03 03:28:07] [Iter  404/2250] R2[356/600]  | LR: 0.013887 | E: -27.091621 | E_var:     0.2919 | E_err:   0.008442
[2025-10-03 03:28:12] [Iter  405/2250] R2[358/600]  | LR: 0.013762 | E: -27.095530 | E_var:     0.2504 | E_err:   0.007819
[2025-10-03 03:28:17] [Iter  406/2250] R2[360/600]  | LR: 0.013637 | E: -27.084382 | E_var:     0.2840 | E_err:   0.008327
[2025-10-03 03:28:22] [Iter  407/2250] R2[362/600]  | LR: 0.013513 | E: -27.086849 | E_var:     0.3981 | E_err:   0.009858
[2025-10-03 03:28:27] [Iter  408/2250] R2[364/600]  | LR: 0.013389 | E: -27.087451 | E_var:     0.2997 | E_err:   0.008554
[2025-10-03 03:28:32] [Iter  409/2250] R2[366/600]  | LR: 0.013266 | E: -27.097418 | E_var:     0.2806 | E_err:   0.008277
[2025-10-03 03:28:37] [Iter  410/2250] R2[368/600]  | LR: 0.013143 | E: -27.101435 | E_var:     0.2668 | E_err:   0.008071
[2025-10-03 03:28:42] [Iter  411/2250] R2[370/600]  | LR: 0.013020 | E: -27.098564 | E_var:     0.3005 | E_err:   0.008565
[2025-10-03 03:28:47] [Iter  412/2250] R2[372/600]  | LR: 0.012898 | E: -27.102066 | E_var:     0.2576 | E_err:   0.007931
[2025-10-03 03:28:52] [Iter  413/2250] R2[374/600]  | LR: 0.012777 | E: -27.103697 | E_var:     0.2718 | E_err:   0.008147
[2025-10-03 03:28:57] [Iter  414/2250] R2[376/600]  | LR: 0.012656 | E: -27.110436 | E_var:     0.2721 | E_err:   0.008151
[2025-10-03 03:29:02] [Iter  415/2250] R2[378/600]  | LR: 0.012536 | E: -27.079242 | E_var:     0.2898 | E_err:   0.008411
[2025-10-03 03:29:07] [Iter  416/2250] R2[380/600]  | LR: 0.012416 | E: -27.091383 | E_var:     0.3257 | E_err:   0.008918
[2025-10-03 03:29:12] [Iter  417/2250] R2[382/600]  | LR: 0.012296 | E: -27.101651 | E_var:     0.2488 | E_err:   0.007794
[2025-10-03 03:29:17] [Iter  418/2250] R2[384/600]  | LR: 0.012178 | E: -27.088266 | E_var:     0.2713 | E_err:   0.008138
[2025-10-03 03:29:21] [Iter  419/2250] R2[386/600]  | LR: 0.012060 | E: -27.090663 | E_var:     0.2506 | E_err:   0.007822
[2025-10-03 03:29:26] [Iter  420/2250] R2[388/600]  | LR: 0.011942 | E: -27.092991 | E_var:     0.2782 | E_err:   0.008241
[2025-10-03 03:29:31] [Iter  421/2250] R2[390/600]  | LR: 0.011825 | E: -27.093464 | E_var:     0.2753 | E_err:   0.008199
[2025-10-03 03:29:36] [Iter  422/2250] R2[392/600]  | LR: 0.011709 | E: -27.091890 | E_var:     0.2826 | E_err:   0.008306
[2025-10-03 03:29:41] [Iter  423/2250] R2[394/600]  | LR: 0.011593 | E: -27.092893 | E_var:     0.7060 | E_err:   0.013129
[2025-10-03 03:29:46] [Iter  424/2250] R2[396/600]  | LR: 0.011478 | E: -27.100929 | E_var:     0.5245 | E_err:   0.011316
[2025-10-03 03:29:51] [Iter  425/2250] R2[398/600]  | LR: 0.011364 | E: -27.093821 | E_var:     0.2713 | E_err:   0.008138
[2025-10-03 03:29:56] [Iter  426/2250] R2[400/600]  | LR: 0.011250 | E: -27.090415 | E_var:     0.3325 | E_err:   0.009010
[2025-10-03 03:30:01] [Iter  427/2250] R2[402/600]  | LR: 0.011137 | E: -27.099187 | E_var:     0.3109 | E_err:   0.008712
[2025-10-03 03:30:06] [Iter  428/2250] R2[404/600]  | LR: 0.011025 | E: -27.097014 | E_var:     0.2553 | E_err:   0.007894
[2025-10-03 03:30:11] [Iter  429/2250] R2[406/600]  | LR: 0.010913 | E: -27.091899 | E_var:     0.3116 | E_err:   0.008722
[2025-10-03 03:30:16] [Iter  430/2250] R2[408/600]  | LR: 0.010802 | E: -27.093309 | E_var:     0.3766 | E_err:   0.009589
[2025-10-03 03:30:21] [Iter  431/2250] R2[410/600]  | LR: 0.010692 | E: -27.091725 | E_var:     0.3906 | E_err:   0.009765
[2025-10-03 03:30:26] [Iter  432/2250] R2[412/600]  | LR: 0.010583 | E: -27.092619 | E_var:     0.2932 | E_err:   0.008461
[2025-10-03 03:30:31] [Iter  433/2250] R2[414/600]  | LR: 0.010474 | E: -27.099545 | E_var:     0.2416 | E_err:   0.007680
[2025-10-03 03:30:36] [Iter  434/2250] R2[416/600]  | LR: 0.010366 | E: -27.104145 | E_var:     0.3191 | E_err:   0.008826
[2025-10-03 03:30:40] [Iter  435/2250] R2[418/600]  | LR: 0.010259 | E: -27.093999 | E_var:     0.2670 | E_err:   0.008074
[2025-10-03 03:30:45] [Iter  436/2250] R2[420/600]  | LR: 0.010153 | E: -27.096545 | E_var:     0.2872 | E_err:   0.008374
[2025-10-03 03:30:50] [Iter  437/2250] R2[422/600]  | LR: 0.010047 | E: -27.090691 | E_var:     0.2773 | E_err:   0.008228
[2025-10-03 03:30:55] [Iter  438/2250] R2[424/600]  | LR: 0.009943 | E: -27.096332 | E_var:     0.2421 | E_err:   0.007688
[2025-10-03 03:31:00] [Iter  439/2250] R2[426/600]  | LR: 0.009839 | E: -27.107578 | E_var:     0.3486 | E_err:   0.009226
[2025-10-03 03:31:05] [Iter  440/2250] R2[428/600]  | LR: 0.009736 | E: -27.114109 | E_var:     0.2539 | E_err:   0.007873
[2025-10-03 03:31:10] [Iter  441/2250] R2[430/600]  | LR: 0.009633 | E: -27.110796 | E_var:     0.2994 | E_err:   0.008550
[2025-10-03 03:31:15] [Iter  442/2250] R2[432/600]  | LR: 0.009532 | E: -27.113524 | E_var:     0.2142 | E_err:   0.007231
[2025-10-03 03:31:20] [Iter  443/2250] R2[434/600]  | LR: 0.009432 | E: -27.106702 | E_var:     0.3113 | E_err:   0.008717
[2025-10-03 03:31:25] [Iter  444/2250] R2[436/600]  | LR: 0.009332 | E: -27.080300 | E_var:     0.4878 | E_err:   0.010913
[2025-10-03 03:31:30] [Iter  445/2250] R2[438/600]  | LR: 0.009234 | E: -27.120567 | E_var:     0.4067 | E_err:   0.009965
[2025-10-03 03:31:35] [Iter  446/2250] R2[440/600]  | LR: 0.009136 | E: -27.093763 | E_var:     0.2490 | E_err:   0.007797
[2025-10-03 03:31:40] [Iter  447/2250] R2[442/600]  | LR: 0.009039 | E: -27.103612 | E_var:     0.2723 | E_err:   0.008154
[2025-10-03 03:31:45] [Iter  448/2250] R2[444/600]  | LR: 0.008943 | E: -27.100070 | E_var:     0.2550 | E_err:   0.007891
[2025-10-03 03:31:50] [Iter  449/2250] R2[446/600]  | LR: 0.008848 | E: -27.094359 | E_var:     0.3105 | E_err:   0.008707
[2025-10-03 03:31:54] [Iter  450/2250] R2[448/600]  | LR: 0.008754 | E: -27.110820 | E_var:     0.2400 | E_err:   0.007654
[2025-10-03 03:31:59] [Iter  451/2250] R2[450/600]  | LR: 0.008661 | E: -27.107875 | E_var:     0.2822 | E_err:   0.008300
[2025-10-03 03:32:04] [Iter  452/2250] R2[452/600]  | LR: 0.008569 | E: -27.091440 | E_var:     0.2767 | E_err:   0.008220
[2025-10-03 03:32:09] [Iter  453/2250] R2[454/600]  | LR: 0.008478 | E: -27.087328 | E_var:     0.2314 | E_err:   0.007517
[2025-10-03 03:32:14] [Iter  454/2250] R2[456/600]  | LR: 0.008388 | E: -27.107871 | E_var:     0.2705 | E_err:   0.008127
[2025-10-03 03:32:19] [Iter  455/2250] R2[458/600]  | LR: 0.008299 | E: -27.105389 | E_var:     0.2385 | E_err:   0.007631
[2025-10-03 03:32:24] [Iter  456/2250] R2[460/600]  | LR: 0.008211 | E: -27.116826 | E_var:     0.2313 | E_err:   0.007515
[2025-10-03 03:32:29] [Iter  457/2250] R2[462/600]  | LR: 0.008124 | E: -27.105879 | E_var:     0.2288 | E_err:   0.007474
[2025-10-03 03:32:34] [Iter  458/2250] R2[464/600]  | LR: 0.008038 | E: -27.097294 | E_var:     0.5000 | E_err:   0.011049
[2025-10-03 03:32:39] [Iter  459/2250] R2[466/600]  | LR: 0.007953 | E: -27.100657 | E_var:     0.2259 | E_err:   0.007427
[2025-10-03 03:32:44] [Iter  460/2250] R2[468/600]  | LR: 0.007869 | E: -27.096134 | E_var:     0.4376 | E_err:   0.010336
[2025-10-03 03:32:49] [Iter  461/2250] R2[470/600]  | LR: 0.007786 | E: -27.103154 | E_var:     0.2442 | E_err:   0.007722
[2025-10-03 03:32:54] [Iter  462/2250] R2[472/600]  | LR: 0.007704 | E: -27.104623 | E_var:     0.2443 | E_err:   0.007723
[2025-10-03 03:32:59] [Iter  463/2250] R2[474/600]  | LR: 0.007623 | E: -27.114921 | E_var:     0.2574 | E_err:   0.007927
[2025-10-03 03:33:04] [Iter  464/2250] R2[476/600]  | LR: 0.007543 | E: -27.096280 | E_var:     0.5994 | E_err:   0.012097
[2025-10-03 03:33:08] [Iter  465/2250] R2[478/600]  | LR: 0.007465 | E: -27.118013 | E_var:     0.3387 | E_err:   0.009094
[2025-10-03 03:33:13] [Iter  466/2250] R2[480/600]  | LR: 0.007387 | E: -27.107296 | E_var:     0.2783 | E_err:   0.008243
[2025-10-03 03:33:18] [Iter  467/2250] R2[482/600]  | LR: 0.007311 | E: -27.110659 | E_var:     0.2395 | E_err:   0.007647
[2025-10-03 03:33:23] [Iter  468/2250] R2[484/600]  | LR: 0.007236 | E: -27.104220 | E_var:     0.2935 | E_err:   0.008465
[2025-10-03 03:33:28] [Iter  469/2250] R2[486/600]  | LR: 0.007161 | E: -27.087751 | E_var:     0.2455 | E_err:   0.007742
[2025-10-03 03:33:33] [Iter  470/2250] R2[488/600]  | LR: 0.007088 | E: -27.104804 | E_var:     0.2779 | E_err:   0.008236
[2025-10-03 03:33:38] [Iter  471/2250] R2[490/600]  | LR: 0.007017 | E: -27.103935 | E_var:     0.3188 | E_err:   0.008822
[2025-10-03 03:33:43] [Iter  472/2250] R2[492/600]  | LR: 0.006946 | E: -27.103520 | E_var:     0.2452 | E_err:   0.007737
[2025-10-03 03:33:48] [Iter  473/2250] R2[494/600]  | LR: 0.006876 | E: -27.099464 | E_var:     0.2765 | E_err:   0.008216
[2025-10-03 03:33:53] [Iter  474/2250] R2[496/600]  | LR: 0.006808 | E: -27.107223 | E_var:     0.2759 | E_err:   0.008207
[2025-10-03 03:33:58] [Iter  475/2250] R2[498/600]  | LR: 0.006741 | E: -27.103081 | E_var:     0.3152 | E_err:   0.008773
[2025-10-03 03:34:03] [Iter  476/2250] R2[500/600]  | LR: 0.006675 | E: -27.100470 | E_var:     0.3108 | E_err:   0.008711
[2025-10-03 03:34:08] [Iter  477/2250] R2[502/600]  | LR: 0.006610 | E: -27.099126 | E_var:     0.2521 | E_err:   0.007845
[2025-10-03 03:34:13] [Iter  478/2250] R2[504/600]  | LR: 0.006546 | E: -27.097964 | E_var:     0.3152 | E_err:   0.008772
[2025-10-03 03:34:18] [Iter  479/2250] R2[506/600]  | LR: 0.006484 | E: -27.099691 | E_var:     0.2726 | E_err:   0.008158
[2025-10-03 03:34:22] [Iter  480/2250] R2[508/600]  | LR: 0.006422 | E: -27.112145 | E_var:     0.2880 | E_err:   0.008385
[2025-10-03 03:34:27] [Iter  481/2250] R2[510/600]  | LR: 0.006362 | E: -27.094635 | E_var:     0.2610 | E_err:   0.007983
[2025-10-03 03:34:32] [Iter  482/2250] R2[512/600]  | LR: 0.006304 | E: -27.094935 | E_var:     0.3095 | E_err:   0.008693
[2025-10-03 03:34:37] [Iter  483/2250] R2[514/600]  | LR: 0.006246 | E: -27.113743 | E_var:     0.2233 | E_err:   0.007384
[2025-10-03 03:34:42] [Iter  484/2250] R2[516/600]  | LR: 0.006190 | E: -27.096656 | E_var:     0.2398 | E_err:   0.007652
[2025-10-03 03:34:47] [Iter  485/2250] R2[518/600]  | LR: 0.006135 | E: -27.106854 | E_var:     0.2382 | E_err:   0.007626
[2025-10-03 03:34:52] [Iter  486/2250] R2[520/600]  | LR: 0.006081 | E: -27.096457 | E_var:     0.2598 | E_err:   0.007964
[2025-10-03 03:34:57] [Iter  487/2250] R2[522/600]  | LR: 0.006028 | E: -27.097087 | E_var:     0.2407 | E_err:   0.007667
[2025-10-03 03:35:02] [Iter  488/2250] R2[524/600]  | LR: 0.005977 | E: -27.117125 | E_var:     0.3046 | E_err:   0.008623
[2025-10-03 03:35:07] [Iter  489/2250] R2[526/600]  | LR: 0.005927 | E: -27.109629 | E_var:     0.2224 | E_err:   0.007369
[2025-10-03 03:35:12] [Iter  490/2250] R2[528/600]  | LR: 0.005878 | E: -27.105248 | E_var:     0.2600 | E_err:   0.007967
[2025-10-03 03:35:17] [Iter  491/2250] R2[530/600]  | LR: 0.005830 | E: -27.110724 | E_var:     0.2413 | E_err:   0.007675
[2025-10-03 03:35:22] [Iter  492/2250] R2[532/600]  | LR: 0.005784 | E: -27.106279 | E_var:     0.2753 | E_err:   0.008198
[2025-10-03 03:35:27] [Iter  493/2250] R2[534/600]  | LR: 0.005739 | E: -27.112904 | E_var:     0.2300 | E_err:   0.007493
[2025-10-03 03:35:32] [Iter  494/2250] R2[536/600]  | LR: 0.005695 | E: -27.114542 | E_var:     0.2299 | E_err:   0.007491
[2025-10-03 03:35:36] [Iter  495/2250] R2[538/600]  | LR: 0.005653 | E: -27.103905 | E_var:     0.2648 | E_err:   0.008040
[2025-10-03 03:35:41] [Iter  496/2250] R2[540/600]  | LR: 0.005612 | E: -27.096946 | E_var:     0.2448 | E_err:   0.007732
[2025-10-03 03:35:46] [Iter  497/2250] R2[542/600]  | LR: 0.005572 | E: -27.113762 | E_var:     0.2337 | E_err:   0.007554
[2025-10-03 03:35:51] [Iter  498/2250] R2[544/600]  | LR: 0.005534 | E: -27.101956 | E_var:     0.2311 | E_err:   0.007511
[2025-10-03 03:35:56] [Iter  499/2250] R2[546/600]  | LR: 0.005496 | E: -27.098699 | E_var:     0.2934 | E_err:   0.008464
[2025-10-03 03:36:01] [Iter  500/2250] R2[548/600]  | LR: 0.005460 | E: -27.114119 | E_var:     0.2709 | E_err:   0.008133
[2025-10-03 03:36:06] [Iter  501/2250] R2[550/600]  | LR: 0.005426 | E: -27.091059 | E_var:     0.2608 | E_err:   0.007980
[2025-10-03 03:36:11] [Iter  502/2250] R2[552/600]  | LR: 0.005393 | E: -27.093591 | E_var:     0.2149 | E_err:   0.007244
[2025-10-03 03:36:16] [Iter  503/2250] R2[554/600]  | LR: 0.005361 | E: -27.107786 | E_var:     0.2287 | E_err:   0.007473
[2025-10-03 03:36:21] [Iter  504/2250] R2[556/600]  | LR: 0.005330 | E: -27.090451 | E_var:     0.3783 | E_err:   0.009610
[2025-10-03 03:36:26] [Iter  505/2250] R2[558/600]  | LR: 0.005301 | E: -27.104411 | E_var:     0.2271 | E_err:   0.007445
[2025-10-03 03:36:31] [Iter  506/2250] R2[560/600]  | LR: 0.005273 | E: -27.116556 | E_var:     0.2566 | E_err:   0.007915
[2025-10-03 03:36:36] [Iter  507/2250] R2[562/600]  | LR: 0.005247 | E: -27.102248 | E_var:     0.2810 | E_err:   0.008282
[2025-10-03 03:36:41] [Iter  508/2250] R2[564/600]  | LR: 0.005221 | E: -27.112749 | E_var:     0.2577 | E_err:   0.007932
[2025-10-03 03:36:46] [Iter  509/2250] R2[566/600]  | LR: 0.005198 | E: -27.106740 | E_var:     0.2597 | E_err:   0.007963
[2025-10-03 03:36:51] [Iter  510/2250] R2[568/600]  | LR: 0.005175 | E: -27.118051 | E_var:     0.3309 | E_err:   0.008988
[2025-10-03 03:36:55] [Iter  511/2250] R2[570/600]  | LR: 0.005154 | E: -27.094300 | E_var:     0.2489 | E_err:   0.007795
[2025-10-03 03:37:00] [Iter  512/2250] R2[572/600]  | LR: 0.005134 | E: -27.115265 | E_var:     0.2666 | E_err:   0.008068
[2025-10-03 03:37:05] [Iter  513/2250] R2[574/600]  | LR: 0.005116 | E: -27.105009 | E_var:     0.2608 | E_err:   0.007980
[2025-10-03 03:37:10] [Iter  514/2250] R2[576/600]  | LR: 0.005099 | E: -27.114724 | E_var:     0.2505 | E_err:   0.007820
[2025-10-03 03:37:15] [Iter  515/2250] R2[578/600]  | LR: 0.005083 | E: -27.123791 | E_var:     0.2659 | E_err:   0.008057
[2025-10-03 03:37:20] [Iter  516/2250] R2[580/600]  | LR: 0.005068 | E: -27.104771 | E_var:     0.2783 | E_err:   0.008243
[2025-10-03 03:37:25] [Iter  517/2250] R2[582/600]  | LR: 0.005055 | E: -27.110914 | E_var:     0.2419 | E_err:   0.007685
[2025-10-03 03:37:30] [Iter  518/2250] R2[584/600]  | LR: 0.005044 | E: -27.124189 | E_var:     0.2447 | E_err:   0.007730
[2025-10-03 03:37:35] [Iter  519/2250] R2[586/600]  | LR: 0.005034 | E: -27.105430 | E_var:     0.2249 | E_err:   0.007410
[2025-10-03 03:37:40] [Iter  520/2250] R2[588/600]  | LR: 0.005025 | E: -27.115596 | E_var:     0.1797 | E_err:   0.006623
[2025-10-03 03:37:45] [Iter  521/2250] R2[590/600]  | LR: 0.005017 | E: -27.111203 | E_var:     0.2441 | E_err:   0.007720
[2025-10-03 03:37:50] [Iter  522/2250] R2[592/600]  | LR: 0.005011 | E: -27.110262 | E_var:     0.2992 | E_err:   0.008546
[2025-10-03 03:37:55] [Iter  523/2250] R2[594/600]  | LR: 0.005006 | E: -27.114647 | E_var:     0.2922 | E_err:   0.008446
[2025-10-03 03:38:00] [Iter  524/2250] R2[596/600]  | LR: 0.005003 | E: -27.109740 | E_var:     0.2625 | E_err:   0.008005
[2025-10-03 03:38:05] [Iter  525/2250] R2[598/600]  | LR: 0.005001 | E: -27.103378 | E_var:     0.2390 | E_err:   0.007638
[2025-10-03 03:38:05] 🔄 RESTART #3 | Period: 1200
[2025-10-03 03:38:09] [Iter  526/2250] R3[0/1200]   | LR: 0.030000 | E: -27.115613 | E_var:     0.2212 | E_err:   0.007348
[2025-10-03 03:38:14] [Iter  527/2250] R3[2/1200]   | LR: 0.030000 | E: -27.112248 | E_var:     0.2638 | E_err:   0.008025
[2025-10-03 03:38:19] [Iter  528/2250] R3[4/1200]   | LR: 0.029999 | E: -27.117365 | E_var:     0.2891 | E_err:   0.008401
[2025-10-03 03:38:24] [Iter  529/2250] R3[6/1200]   | LR: 0.029998 | E: -27.101551 | E_var:     0.2536 | E_err:   0.007869
[2025-10-03 03:38:29] [Iter  530/2250] R3[8/1200]   | LR: 0.029997 | E: -27.118983 | E_var:     0.2718 | E_err:   0.008146
[2025-10-03 03:38:34] [Iter  531/2250] R3[10/1200]  | LR: 0.029996 | E: -27.115675 | E_var:     0.1823 | E_err:   0.006672
[2025-10-03 03:38:39] [Iter  532/2250] R3[12/1200]  | LR: 0.029994 | E: -27.110048 | E_var:     0.2410 | E_err:   0.007670
[2025-10-03 03:38:44] [Iter  533/2250] R3[14/1200]  | LR: 0.029992 | E: -27.099534 | E_var:     0.2443 | E_err:   0.007723
[2025-10-03 03:38:49] [Iter  534/2250] R3[16/1200]  | LR: 0.029989 | E: -27.100681 | E_var:     0.2347 | E_err:   0.007570
[2025-10-03 03:38:54] [Iter  535/2250] R3[18/1200]  | LR: 0.029986 | E: -27.106611 | E_var:     0.2263 | E_err:   0.007433
[2025-10-03 03:38:59] [Iter  536/2250] R3[20/1200]  | LR: 0.029983 | E: -27.121302 | E_var:     0.2233 | E_err:   0.007383
[2025-10-03 03:39:04] [Iter  537/2250] R3[22/1200]  | LR: 0.029979 | E: -27.112363 | E_var:     0.2317 | E_err:   0.007520
[2025-10-03 03:39:09] [Iter  538/2250] R3[24/1200]  | LR: 0.029975 | E: -27.124577 | E_var:     0.3563 | E_err:   0.009327
[2025-10-03 03:39:14] [Iter  539/2250] R3[26/1200]  | LR: 0.029971 | E: -27.121522 | E_var:     0.2695 | E_err:   0.008111
[2025-10-03 03:39:19] [Iter  540/2250] R3[28/1200]  | LR: 0.029966 | E: -27.106657 | E_var:     0.2222 | E_err:   0.007365
[2025-10-03 03:39:24] [Iter  541/2250] R3[30/1200]  | LR: 0.029961 | E: -27.108599 | E_var:     0.2349 | E_err:   0.007573
[2025-10-03 03:39:28] [Iter  542/2250] R3[32/1200]  | LR: 0.029956 | E: -27.106691 | E_var:     0.2816 | E_err:   0.008291
[2025-10-03 03:39:33] [Iter  543/2250] R3[34/1200]  | LR: 0.029951 | E: -27.113757 | E_var:     0.2330 | E_err:   0.007542
[2025-10-03 03:39:38] [Iter  544/2250] R3[36/1200]  | LR: 0.029945 | E: -27.100118 | E_var:     0.2113 | E_err:   0.007182
[2025-10-03 03:39:43] [Iter  545/2250] R3[38/1200]  | LR: 0.029938 | E: -27.100117 | E_var:     0.2603 | E_err:   0.007972
[2025-10-03 03:39:48] [Iter  546/2250] R3[40/1200]  | LR: 0.029932 | E: -27.113137 | E_var:     0.2272 | E_err:   0.007449
[2025-10-03 03:39:53] [Iter  547/2250] R3[42/1200]  | LR: 0.029925 | E: -27.094761 | E_var:     0.2331 | E_err:   0.007544
[2025-10-03 03:39:58] [Iter  548/2250] R3[44/1200]  | LR: 0.029917 | E: -27.114343 | E_var:     0.2513 | E_err:   0.007833
[2025-10-03 03:40:03] [Iter  549/2250] R3[46/1200]  | LR: 0.029909 | E: -27.104288 | E_var:     0.2378 | E_err:   0.007620
[2025-10-03 03:40:08] [Iter  550/2250] R3[48/1200]  | LR: 0.029901 | E: -27.107652 | E_var:     0.2442 | E_err:   0.007722
[2025-10-03 03:40:13] [Iter  551/2250] R3[50/1200]  | LR: 0.029893 | E: -27.113103 | E_var:     0.2542 | E_err:   0.007878
[2025-10-03 03:40:18] [Iter  552/2250] R3[52/1200]  | LR: 0.029884 | E: -27.116341 | E_var:     0.2613 | E_err:   0.007988
[2025-10-03 03:40:23] [Iter  553/2250] R3[54/1200]  | LR: 0.029875 | E: -27.101512 | E_var:     0.2149 | E_err:   0.007243
[2025-10-03 03:40:28] [Iter  554/2250] R3[56/1200]  | LR: 0.029866 | E: -27.104977 | E_var:     0.2352 | E_err:   0.007577
[2025-10-03 03:40:33] [Iter  555/2250] R3[58/1200]  | LR: 0.029856 | E: -27.108910 | E_var:     0.2630 | E_err:   0.008013
[2025-10-03 03:40:38] [Iter  556/2250] R3[60/1200]  | LR: 0.029846 | E: -27.111634 | E_var:     0.2307 | E_err:   0.007505
[2025-10-03 03:40:42] [Iter  557/2250] R3[62/1200]  | LR: 0.029836 | E: -27.097495 | E_var:     0.2437 | E_err:   0.007714
[2025-10-03 03:40:47] [Iter  558/2250] R3[64/1200]  | LR: 0.029825 | E: -27.104361 | E_var:     0.2434 | E_err:   0.007709
[2025-10-03 03:40:52] [Iter  559/2250] R3[66/1200]  | LR: 0.029814 | E: -27.110292 | E_var:     0.2235 | E_err:   0.007387
[2025-10-03 03:40:57] [Iter  560/2250] R3[68/1200]  | LR: 0.029802 | E: -27.109593 | E_var:     0.2343 | E_err:   0.007563
[2025-10-03 03:41:02] [Iter  561/2250] R3[70/1200]  | LR: 0.029791 | E: -27.129372 | E_var:     0.2478 | E_err:   0.007779
[2025-10-03 03:41:07] [Iter  562/2250] R3[72/1200]  | LR: 0.029779 | E: -27.112311 | E_var:     0.2286 | E_err:   0.007470
[2025-10-03 03:41:12] [Iter  563/2250] R3[74/1200]  | LR: 0.029766 | E: -27.095184 | E_var:     0.2637 | E_err:   0.008024
[2025-10-03 03:41:17] [Iter  564/2250] R3[76/1200]  | LR: 0.029753 | E: -27.107419 | E_var:     0.2260 | E_err:   0.007428
[2025-10-03 03:41:22] [Iter  565/2250] R3[78/1200]  | LR: 0.029740 | E: -27.099044 | E_var:     0.2085 | E_err:   0.007134
[2025-10-03 03:41:27] [Iter  566/2250] R3[80/1200]  | LR: 0.029727 | E: -27.111165 | E_var:     0.2101 | E_err:   0.007161
[2025-10-03 03:41:32] [Iter  567/2250] R3[82/1200]  | LR: 0.029713 | E: -27.105497 | E_var:     0.2650 | E_err:   0.008043
[2025-10-03 03:41:37] [Iter  568/2250] R3[84/1200]  | LR: 0.029699 | E: -27.116806 | E_var:     0.2067 | E_err:   0.007103
[2025-10-03 03:41:42] [Iter  569/2250] R3[86/1200]  | LR: 0.029685 | E: -27.099934 | E_var:     0.2351 | E_err:   0.007575
[2025-10-03 03:41:47] [Iter  570/2250] R3[88/1200]  | LR: 0.029670 | E: -27.115088 | E_var:     0.2239 | E_err:   0.007393
[2025-10-03 03:41:52] [Iter  571/2250] R3[90/1200]  | LR: 0.029655 | E: -27.095576 | E_var:     0.4333 | E_err:   0.010285
[2025-10-03 03:41:57] [Iter  572/2250] R3[92/1200]  | LR: 0.029639 | E: -27.115582 | E_var:     0.2552 | E_err:   0.007893
[2025-10-03 03:42:01] [Iter  573/2250] R3[94/1200]  | LR: 0.029623 | E: -27.105618 | E_var:     0.2625 | E_err:   0.008006
[2025-10-03 03:42:06] [Iter  574/2250] R3[96/1200]  | LR: 0.029607 | E: -27.104550 | E_var:     0.2796 | E_err:   0.008263
[2025-10-03 03:42:11] [Iter  575/2250] R3[98/1200]  | LR: 0.029591 | E: -27.113600 | E_var:     0.2757 | E_err:   0.008204
[2025-10-03 03:42:16] [Iter  576/2250] R3[100/1200] | LR: 0.029574 | E: -27.110532 | E_var:     0.2432 | E_err:   0.007705
[2025-10-03 03:42:21] [Iter  577/2250] R3[102/1200] | LR: 0.029557 | E: -27.108988 | E_var:     0.7946 | E_err:   0.013928
[2025-10-03 03:42:26] [Iter  578/2250] R3[104/1200] | LR: 0.029540 | E: -27.103810 | E_var:     0.3056 | E_err:   0.008638
[2025-10-03 03:42:31] [Iter  579/2250] R3[106/1200] | LR: 0.029522 | E: -27.120407 | E_var:     0.2203 | E_err:   0.007334
[2025-10-03 03:42:36] [Iter  580/2250] R3[108/1200] | LR: 0.029504 | E: -27.110557 | E_var:     0.2616 | E_err:   0.007991
[2025-10-03 03:42:41] [Iter  581/2250] R3[110/1200] | LR: 0.029485 | E: -27.112737 | E_var:     0.1981 | E_err:   0.006955
[2025-10-03 03:42:46] [Iter  582/2250] R3[112/1200] | LR: 0.029466 | E: -27.115165 | E_var:     0.2215 | E_err:   0.007354
[2025-10-03 03:42:51] [Iter  583/2250] R3[114/1200] | LR: 0.029447 | E: -27.109002 | E_var:     0.2269 | E_err:   0.007442
[2025-10-03 03:42:56] [Iter  584/2250] R3[116/1200] | LR: 0.029428 | E: -27.122094 | E_var:     0.2119 | E_err:   0.007192
[2025-10-03 03:43:01] [Iter  585/2250] R3[118/1200] | LR: 0.029408 | E: -27.117955 | E_var:     0.3207 | E_err:   0.008848
[2025-10-03 03:43:06] [Iter  586/2250] R3[120/1200] | LR: 0.029388 | E: -27.117561 | E_var:     0.2512 | E_err:   0.007831
[2025-10-03 03:43:11] [Iter  587/2250] R3[122/1200] | LR: 0.029368 | E: -27.119666 | E_var:     0.2963 | E_err:   0.008505
[2025-10-03 03:43:16] [Iter  588/2250] R3[124/1200] | LR: 0.029347 | E: -27.116210 | E_var:     0.2875 | E_err:   0.008378
[2025-10-03 03:43:20] [Iter  589/2250] R3[126/1200] | LR: 0.029326 | E: -27.111798 | E_var:     0.3061 | E_err:   0.008645
[2025-10-03 03:43:25] [Iter  590/2250] R3[128/1200] | LR: 0.029305 | E: -27.116275 | E_var:     0.2322 | E_err:   0.007530
[2025-10-03 03:43:30] [Iter  591/2250] R3[130/1200] | LR: 0.029283 | E: -27.120795 | E_var:     0.3285 | E_err:   0.008955
[2025-10-03 03:43:35] [Iter  592/2250] R3[132/1200] | LR: 0.029261 | E: -27.116649 | E_var:     0.2517 | E_err:   0.007839
[2025-10-03 03:43:40] [Iter  593/2250] R3[134/1200] | LR: 0.029239 | E: -27.118304 | E_var:     0.2356 | E_err:   0.007584
[2025-10-03 03:43:45] [Iter  594/2250] R3[136/1200] | LR: 0.029216 | E: -27.115542 | E_var:     0.2242 | E_err:   0.007398
[2025-10-03 03:43:50] [Iter  595/2250] R3[138/1200] | LR: 0.029193 | E: -27.112684 | E_var:     0.2504 | E_err:   0.007819
[2025-10-03 03:43:55] [Iter  596/2250] R3[140/1200] | LR: 0.029170 | E: -27.110007 | E_var:     0.2024 | E_err:   0.007029
[2025-10-03 03:44:00] [Iter  597/2250] R3[142/1200] | LR: 0.029146 | E: -27.120651 | E_var:     0.2012 | E_err:   0.007009
[2025-10-03 03:44:05] [Iter  598/2250] R3[144/1200] | LR: 0.029122 | E: -27.125230 | E_var:     0.2109 | E_err:   0.007176
[2025-10-03 03:44:10] [Iter  599/2250] R3[146/1200] | LR: 0.029098 | E: -27.119540 | E_var:     0.1988 | E_err:   0.006966
[2025-10-03 03:44:15] [Iter  600/2250] R3[148/1200] | LR: 0.029073 | E: -27.110724 | E_var:     0.2454 | E_err:   0.007741
[2025-10-03 03:44:15] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-03 03:44:20] [Iter  601/2250] R3[150/1200] | LR: 0.029048 | E: -27.118083 | E_var:     0.3309 | E_err:   0.008988
[2025-10-03 03:44:25] [Iter  602/2250] R3[152/1200] | LR: 0.029023 | E: -27.123644 | E_var:     0.2716 | E_err:   0.008144
[2025-10-03 03:44:30] [Iter  603/2250] R3[154/1200] | LR: 0.028998 | E: -27.108439 | E_var:     0.2654 | E_err:   0.008049
[2025-10-03 03:44:35] [Iter  604/2250] R3[156/1200] | LR: 0.028972 | E: -27.115130 | E_var:     0.2292 | E_err:   0.007480
[2025-10-03 03:44:40] [Iter  605/2250] R3[158/1200] | LR: 0.028946 | E: -27.106021 | E_var:     0.2274 | E_err:   0.007450
[2025-10-03 03:44:44] [Iter  606/2250] R3[160/1200] | LR: 0.028919 | E: -27.109214 | E_var:     0.2677 | E_err:   0.008085
[2025-10-03 03:44:49] [Iter  607/2250] R3[162/1200] | LR: 0.028893 | E: -27.106991 | E_var:     0.4696 | E_err:   0.010708
[2025-10-03 03:44:54] [Iter  608/2250] R3[164/1200] | LR: 0.028865 | E: -27.114085 | E_var:     0.1923 | E_err:   0.006852
[2025-10-03 03:44:59] [Iter  609/2250] R3[166/1200] | LR: 0.028838 | E: -27.123277 | E_var:     0.1893 | E_err:   0.006798
[2025-10-03 03:45:04] [Iter  610/2250] R3[168/1200] | LR: 0.028810 | E: -27.118311 | E_var:     0.2284 | E_err:   0.007467
[2025-10-03 03:45:09] [Iter  611/2250] R3[170/1200] | LR: 0.028782 | E: -27.134102 | E_var:     0.2547 | E_err:   0.007885
[2025-10-03 03:45:14] [Iter  612/2250] R3[172/1200] | LR: 0.028754 | E: -27.111959 | E_var:     0.1945 | E_err:   0.006891
[2025-10-03 03:45:19] [Iter  613/2250] R3[174/1200] | LR: 0.028725 | E: -27.119238 | E_var:     0.2341 | E_err:   0.007560
[2025-10-03 03:45:24] [Iter  614/2250] R3[176/1200] | LR: 0.028696 | E: -27.118752 | E_var:     0.2103 | E_err:   0.007166
[2025-10-03 03:45:29] [Iter  615/2250] R3[178/1200] | LR: 0.028667 | E: -27.111061 | E_var:     0.2135 | E_err:   0.007220
[2025-10-03 03:45:34] [Iter  616/2250] R3[180/1200] | LR: 0.028638 | E: -27.106489 | E_var:     0.2049 | E_err:   0.007072
[2025-10-03 03:45:39] [Iter  617/2250] R3[182/1200] | LR: 0.028608 | E: -27.121643 | E_var:     0.2058 | E_err:   0.007089
[2025-10-03 03:45:44] [Iter  618/2250] R3[184/1200] | LR: 0.028578 | E: -27.109400 | E_var:     0.3028 | E_err:   0.008597
[2025-10-03 03:45:49] [Iter  619/2250] R3[186/1200] | LR: 0.028547 | E: -27.114547 | E_var:     0.2079 | E_err:   0.007124
[2025-10-03 03:45:54] [Iter  620/2250] R3[188/1200] | LR: 0.028516 | E: -27.116575 | E_var:     0.2068 | E_err:   0.007105
[2025-10-03 03:45:59] [Iter  621/2250] R3[190/1200] | LR: 0.028485 | E: -27.104027 | E_var:     0.2116 | E_err:   0.007188
[2025-10-03 03:46:03] [Iter  622/2250] R3[192/1200] | LR: 0.028454 | E: -27.091241 | E_var:     0.3029 | E_err:   0.008599
[2025-10-03 03:46:08] [Iter  623/2250] R3[194/1200] | LR: 0.028422 | E: -27.131785 | E_var:     0.2254 | E_err:   0.007418
[2025-10-03 03:46:13] [Iter  624/2250] R3[196/1200] | LR: 0.028390 | E: -27.120745 | E_var:     0.2529 | E_err:   0.007858
[2025-10-03 03:46:18] [Iter  625/2250] R3[198/1200] | LR: 0.028358 | E: -27.126862 | E_var:     0.2160 | E_err:   0.007262
[2025-10-03 03:46:23] [Iter  626/2250] R3[200/1200] | LR: 0.028325 | E: -27.105089 | E_var:     0.2737 | E_err:   0.008174
[2025-10-03 03:46:28] [Iter  627/2250] R3[202/1200] | LR: 0.028292 | E: -27.126469 | E_var:     0.2183 | E_err:   0.007300
[2025-10-03 03:46:33] [Iter  628/2250] R3[204/1200] | LR: 0.028259 | E: -27.118461 | E_var:     0.2108 | E_err:   0.007173
[2025-10-03 03:46:38] [Iter  629/2250] R3[206/1200] | LR: 0.028226 | E: -27.118638 | E_var:     0.2267 | E_err:   0.007440
[2025-10-03 03:46:43] [Iter  630/2250] R3[208/1200] | LR: 0.028192 | E: -27.123623 | E_var:     0.2894 | E_err:   0.008405
[2025-10-03 03:46:48] [Iter  631/2250] R3[210/1200] | LR: 0.028158 | E: -27.105496 | E_var:     0.2371 | E_err:   0.007608
[2025-10-03 03:46:53] [Iter  632/2250] R3[212/1200] | LR: 0.028124 | E: -27.117651 | E_var:     0.1971 | E_err:   0.006937
[2025-10-03 03:46:58] [Iter  633/2250] R3[214/1200] | LR: 0.028089 | E: -27.112472 | E_var:     0.1749 | E_err:   0.006534
[2025-10-03 03:47:03] [Iter  634/2250] R3[216/1200] | LR: 0.028054 | E: -27.127617 | E_var:     0.2446 | E_err:   0.007728
[2025-10-03 03:47:08] [Iter  635/2250] R3[218/1200] | LR: 0.028019 | E: -27.109092 | E_var:     0.3307 | E_err:   0.008985
[2025-10-03 03:47:13] [Iter  636/2250] R3[220/1200] | LR: 0.027983 | E: -27.113933 | E_var:     0.2783 | E_err:   0.008243
[2025-10-03 03:47:17] [Iter  637/2250] R3[222/1200] | LR: 0.027948 | E: -27.114639 | E_var:     0.8512 | E_err:   0.014415
[2025-10-03 03:47:22] [Iter  638/2250] R3[224/1200] | LR: 0.027912 | E: -27.114809 | E_var:     0.2740 | E_err:   0.008180
[2025-10-03 03:47:27] [Iter  639/2250] R3[226/1200] | LR: 0.027875 | E: -27.121856 | E_var:     0.2066 | E_err:   0.007102
[2025-10-03 03:47:32] [Iter  640/2250] R3[228/1200] | LR: 0.027839 | E: -27.118141 | E_var:     0.2236 | E_err:   0.007389
[2025-10-03 03:47:37] [Iter  641/2250] R3[230/1200] | LR: 0.027802 | E: -27.119943 | E_var:     0.2193 | E_err:   0.007318
[2025-10-03 03:47:42] [Iter  642/2250] R3[232/1200] | LR: 0.027764 | E: -27.118557 | E_var:     0.1808 | E_err:   0.006643
[2025-10-03 03:47:47] [Iter  643/2250] R3[234/1200] | LR: 0.027727 | E: -27.105148 | E_var:     0.2858 | E_err:   0.008354
[2025-10-03 03:47:52] [Iter  644/2250] R3[236/1200] | LR: 0.027689 | E: -27.113766 | E_var:     0.2043 | E_err:   0.007062
[2025-10-03 03:47:57] [Iter  645/2250] R3[238/1200] | LR: 0.027651 | E: -27.104502 | E_var:     0.2296 | E_err:   0.007486
[2025-10-03 03:48:02] [Iter  646/2250] R3[240/1200] | LR: 0.027613 | E: -27.114170 | E_var:     0.1918 | E_err:   0.006843
[2025-10-03 03:48:07] [Iter  647/2250] R3[242/1200] | LR: 0.027574 | E: -27.118837 | E_var:     0.2137 | E_err:   0.007223
[2025-10-03 03:48:12] [Iter  648/2250] R3[244/1200] | LR: 0.027535 | E: -27.111455 | E_var:     0.2343 | E_err:   0.007562
[2025-10-03 03:48:17] [Iter  649/2250] R3[246/1200] | LR: 0.027496 | E: -27.121736 | E_var:     0.1924 | E_err:   0.006854
[2025-10-03 03:48:22] [Iter  650/2250] R3[248/1200] | LR: 0.027457 | E: -27.126492 | E_var:     0.2208 | E_err:   0.007341
[2025-10-03 03:48:27] [Iter  651/2250] R3[250/1200] | LR: 0.027417 | E: -27.127852 | E_var:     0.2092 | E_err:   0.007146
[2025-10-03 03:48:32] [Iter  652/2250] R3[252/1200] | LR: 0.027377 | E: -27.104863 | E_var:     0.2647 | E_err:   0.008039
[2025-10-03 03:48:36] [Iter  653/2250] R3[254/1200] | LR: 0.027337 | E: -27.118015 | E_var:     0.1961 | E_err:   0.006920
[2025-10-03 03:48:41] [Iter  654/2250] R3[256/1200] | LR: 0.027296 | E: -27.120846 | E_var:     0.2256 | E_err:   0.007422
[2025-10-03 03:48:46] [Iter  655/2250] R3[258/1200] | LR: 0.027255 | E: -27.112639 | E_var:     0.2531 | E_err:   0.007861
[2025-10-03 03:48:51] [Iter  656/2250] R3[260/1200] | LR: 0.027214 | E: -27.120755 | E_var:     0.2327 | E_err:   0.007538
[2025-10-03 03:48:56] [Iter  657/2250] R3[262/1200] | LR: 0.027173 | E: -27.113244 | E_var:     0.2986 | E_err:   0.008539
[2025-10-03 03:49:01] [Iter  658/2250] R3[264/1200] | LR: 0.027131 | E: -27.122488 | E_var:     0.2039 | E_err:   0.007056
[2025-10-03 03:49:06] [Iter  659/2250] R3[266/1200] | LR: 0.027090 | E: -27.115146 | E_var:     0.2070 | E_err:   0.007109
[2025-10-03 03:49:11] [Iter  660/2250] R3[268/1200] | LR: 0.027047 | E: -27.116650 | E_var:     0.2727 | E_err:   0.008160
[2025-10-03 03:49:16] [Iter  661/2250] R3[270/1200] | LR: 0.027005 | E: -27.114264 | E_var:     0.2410 | E_err:   0.007671
[2025-10-03 03:49:21] [Iter  662/2250] R3[272/1200] | LR: 0.026962 | E: -27.107069 | E_var:     0.2648 | E_err:   0.008040
[2025-10-03 03:49:26] [Iter  663/2250] R3[274/1200] | LR: 0.026920 | E: -27.111716 | E_var:     0.2217 | E_err:   0.007357
[2025-10-03 03:49:31] [Iter  664/2250] R3[276/1200] | LR: 0.026876 | E: -27.120563 | E_var:     0.2115 | E_err:   0.007185
[2025-10-03 03:49:36] [Iter  665/2250] R3[278/1200] | LR: 0.026833 | E: -27.105714 | E_var:     0.2459 | E_err:   0.007748
[2025-10-03 03:49:41] [Iter  666/2250] R3[280/1200] | LR: 0.026789 | E: -27.126125 | E_var:     0.2232 | E_err:   0.007382
[2025-10-03 03:49:46] [Iter  667/2250] R3[282/1200] | LR: 0.026745 | E: -27.125451 | E_var:     0.2072 | E_err:   0.007112
[2025-10-03 03:49:51] [Iter  668/2250] R3[284/1200] | LR: 0.026701 | E: -27.105770 | E_var:     0.2462 | E_err:   0.007752
[2025-10-03 03:49:55] [Iter  669/2250] R3[286/1200] | LR: 0.026657 | E: -27.098269 | E_var:     0.2621 | E_err:   0.008000
[2025-10-03 03:50:00] [Iter  670/2250] R3[288/1200] | LR: 0.026612 | E: -27.130201 | E_var:     0.1843 | E_err:   0.006708
[2025-10-03 03:50:05] [Iter  671/2250] R3[290/1200] | LR: 0.026567 | E: -27.119265 | E_var:     0.2057 | E_err:   0.007086
[2025-10-03 03:50:10] [Iter  672/2250] R3[292/1200] | LR: 0.026522 | E: -27.121353 | E_var:     0.2151 | E_err:   0.007246
[2025-10-03 03:50:15] [Iter  673/2250] R3[294/1200] | LR: 0.026477 | E: -27.116504 | E_var:     0.1866 | E_err:   0.006749
[2025-10-03 03:50:20] [Iter  674/2250] R3[296/1200] | LR: 0.026431 | E: -27.127528 | E_var:     0.2365 | E_err:   0.007598
[2025-10-03 03:50:25] [Iter  675/2250] R3[298/1200] | LR: 0.026385 | E: -27.117666 | E_var:     0.2337 | E_err:   0.007554
[2025-10-03 03:50:30] [Iter  676/2250] R3[300/1200] | LR: 0.026339 | E: -27.106379 | E_var:     0.2024 | E_err:   0.007030
[2025-10-03 03:50:35] [Iter  677/2250] R3[302/1200] | LR: 0.026292 | E: -27.116526 | E_var:     0.2013 | E_err:   0.007010
[2025-10-03 03:50:40] [Iter  678/2250] R3[304/1200] | LR: 0.026246 | E: -27.123133 | E_var:     0.2321 | E_err:   0.007527
[2025-10-03 03:50:45] [Iter  679/2250] R3[306/1200] | LR: 0.026199 | E: -27.126809 | E_var:     0.1900 | E_err:   0.006810
[2025-10-03 03:50:50] [Iter  680/2250] R3[308/1200] | LR: 0.026152 | E: -27.129132 | E_var:     0.2200 | E_err:   0.007329
[2025-10-03 03:50:55] [Iter  681/2250] R3[310/1200] | LR: 0.026104 | E: -27.123750 | E_var:     0.3296 | E_err:   0.008971
[2025-10-03 03:51:00] [Iter  682/2250] R3[312/1200] | LR: 0.026057 | E: -27.116840 | E_var:     0.2583 | E_err:   0.007941
[2025-10-03 03:51:05] [Iter  683/2250] R3[314/1200] | LR: 0.026009 | E: -27.122532 | E_var:     0.2391 | E_err:   0.007640
[2025-10-03 03:51:10] [Iter  684/2250] R3[316/1200] | LR: 0.025961 | E: -27.124506 | E_var:     0.2180 | E_err:   0.007295
[2025-10-03 03:51:14] [Iter  685/2250] R3[318/1200] | LR: 0.025913 | E: -27.118197 | E_var:     0.2699 | E_err:   0.008117
[2025-10-03 03:51:19] [Iter  686/2250] R3[320/1200] | LR: 0.025864 | E: -27.128192 | E_var:     0.1941 | E_err:   0.006884
[2025-10-03 03:51:24] [Iter  687/2250] R3[322/1200] | LR: 0.025815 | E: -27.124737 | E_var:     0.2189 | E_err:   0.007310
[2025-10-03 03:51:29] [Iter  688/2250] R3[324/1200] | LR: 0.025766 | E: -27.123419 | E_var:     0.2368 | E_err:   0.007603
[2025-10-03 03:51:34] [Iter  689/2250] R3[326/1200] | LR: 0.025717 | E: -27.111646 | E_var:     0.2354 | E_err:   0.007580
[2025-10-03 03:51:39] [Iter  690/2250] R3[328/1200] | LR: 0.025668 | E: -27.122757 | E_var:     0.2147 | E_err:   0.007239
[2025-10-03 03:51:44] [Iter  691/2250] R3[330/1200] | LR: 0.025618 | E: -27.125979 | E_var:     0.1908 | E_err:   0.006824
[2025-10-03 03:51:49] [Iter  692/2250] R3[332/1200] | LR: 0.025568 | E: -27.116257 | E_var:     0.3229 | E_err:   0.008879
[2025-10-03 03:51:54] [Iter  693/2250] R3[334/1200] | LR: 0.025518 | E: -27.111369 | E_var:     0.2042 | E_err:   0.007061
[2025-10-03 03:51:59] [Iter  694/2250] R3[336/1200] | LR: 0.025468 | E: -27.107352 | E_var:     0.2063 | E_err:   0.007096
[2025-10-03 03:52:04] [Iter  695/2250] R3[338/1200] | LR: 0.025417 | E: -27.120884 | E_var:     0.3768 | E_err:   0.009591
[2025-10-03 03:52:09] [Iter  696/2250] R3[340/1200] | LR: 0.025367 | E: -27.124741 | E_var:     0.1853 | E_err:   0.006726
[2025-10-03 03:52:14] [Iter  697/2250] R3[342/1200] | LR: 0.025316 | E: -27.117319 | E_var:     0.1916 | E_err:   0.006839
[2025-10-03 03:52:19] [Iter  698/2250] R3[344/1200] | LR: 0.025264 | E: -27.115924 | E_var:     0.2384 | E_err:   0.007630
[2025-10-03 03:52:24] [Iter  699/2250] R3[346/1200] | LR: 0.025213 | E: -27.117787 | E_var:     0.2055 | E_err:   0.007084
[2025-10-03 03:52:28] [Iter  700/2250] R3[348/1200] | LR: 0.025161 | E: -27.115300 | E_var:     0.3822 | E_err:   0.009660
[2025-10-03 03:52:33] [Iter  701/2250] R3[350/1200] | LR: 0.025110 | E: -27.121326 | E_var:     0.1978 | E_err:   0.006949
[2025-10-03 03:52:38] [Iter  702/2250] R3[352/1200] | LR: 0.025057 | E: -27.111676 | E_var:     0.2381 | E_err:   0.007625
[2025-10-03 03:52:43] [Iter  703/2250] R3[354/1200] | LR: 0.025005 | E: -27.127076 | E_var:     0.3158 | E_err:   0.008780
[2025-10-03 03:52:48] [Iter  704/2250] R3[356/1200] | LR: 0.024953 | E: -27.109477 | E_var:     0.2977 | E_err:   0.008526
[2025-10-03 03:52:53] [Iter  705/2250] R3[358/1200] | LR: 0.024900 | E: -27.118314 | E_var:     0.2299 | E_err:   0.007492
[2025-10-03 03:52:58] [Iter  706/2250] R3[360/1200] | LR: 0.024847 | E: -27.124105 | E_var:     0.1984 | E_err:   0.006960
[2025-10-03 03:53:03] [Iter  707/2250] R3[362/1200] | LR: 0.024794 | E: -27.120320 | E_var:     0.2195 | E_err:   0.007320
[2025-10-03 03:53:08] [Iter  708/2250] R3[364/1200] | LR: 0.024741 | E: -27.113332 | E_var:     0.2724 | E_err:   0.008156
[2025-10-03 03:53:13] [Iter  709/2250] R3[366/1200] | LR: 0.024688 | E: -27.114122 | E_var:     0.2132 | E_err:   0.007215
[2025-10-03 03:53:18] [Iter  710/2250] R3[368/1200] | LR: 0.024634 | E: -27.120695 | E_var:     0.2378 | E_err:   0.007620
[2025-10-03 03:53:23] [Iter  711/2250] R3[370/1200] | LR: 0.024580 | E: -27.116602 | E_var:     0.2860 | E_err:   0.008356
[2025-10-03 03:53:28] [Iter  712/2250] R3[372/1200] | LR: 0.024526 | E: -27.121231 | E_var:     0.2004 | E_err:   0.006995
[2025-10-03 03:53:33] [Iter  713/2250] R3[374/1200] | LR: 0.024472 | E: -27.113601 | E_var:     0.1900 | E_err:   0.006810
[2025-10-03 03:53:38] [Iter  714/2250] R3[376/1200] | LR: 0.024417 | E: -27.121344 | E_var:     0.2268 | E_err:   0.007442
[2025-10-03 03:53:42] [Iter  715/2250] R3[378/1200] | LR: 0.024363 | E: -27.123060 | E_var:     0.1983 | E_err:   0.006958
[2025-10-03 03:53:47] [Iter  716/2250] R3[380/1200] | LR: 0.024308 | E: -27.122073 | E_var:     0.1951 | E_err:   0.006902
[2025-10-03 03:53:52] [Iter  717/2250] R3[382/1200] | LR: 0.024253 | E: -27.131884 | E_var:     0.1873 | E_err:   0.006762
[2025-10-03 03:53:57] [Iter  718/2250] R3[384/1200] | LR: 0.024198 | E: -27.118213 | E_var:     0.1775 | E_err:   0.006582
[2025-10-03 03:54:02] [Iter  719/2250] R3[386/1200] | LR: 0.024142 | E: -27.122668 | E_var:     0.4165 | E_err:   0.010084
[2025-10-03 03:54:07] [Iter  720/2250] R3[388/1200] | LR: 0.024087 | E: -27.116755 | E_var:     0.6651 | E_err:   0.012742
[2025-10-03 03:54:12] [Iter  721/2250] R3[390/1200] | LR: 0.024031 | E: -27.115254 | E_var:     0.5098 | E_err:   0.011156
[2025-10-03 03:54:17] [Iter  722/2250] R3[392/1200] | LR: 0.023975 | E: -27.104299 | E_var:     0.5139 | E_err:   0.011201
[2025-10-03 03:54:22] [Iter  723/2250] R3[394/1200] | LR: 0.023919 | E: -27.124043 | E_var:     0.1754 | E_err:   0.006545
[2025-10-03 03:54:27] [Iter  724/2250] R3[396/1200] | LR: 0.023863 | E: -27.118806 | E_var:     0.2120 | E_err:   0.007194
[2025-10-03 03:54:32] [Iter  725/2250] R3[398/1200] | LR: 0.023807 | E: -27.123768 | E_var:     0.1861 | E_err:   0.006740
[2025-10-03 03:54:37] [Iter  726/2250] R3[400/1200] | LR: 0.023750 | E: -27.113136 | E_var:     0.1778 | E_err:   0.006588
[2025-10-03 03:54:42] [Iter  727/2250] R3[402/1200] | LR: 0.023693 | E: -27.109751 | E_var:     0.3370 | E_err:   0.009071
[2025-10-03 03:54:47] [Iter  728/2250] R3[404/1200] | LR: 0.023636 | E: -27.125256 | E_var:     0.1824 | E_err:   0.006672
[2025-10-03 03:54:52] [Iter  729/2250] R3[406/1200] | LR: 0.023579 | E: -27.115026 | E_var:     0.1985 | E_err:   0.006961
[2025-10-03 03:54:57] [Iter  730/2250] R3[408/1200] | LR: 0.023522 | E: -27.109285 | E_var:     0.2093 | E_err:   0.007149
[2025-10-03 03:55:01] [Iter  731/2250] R3[410/1200] | LR: 0.023464 | E: -27.116810 | E_var:     0.2185 | E_err:   0.007304
[2025-10-03 03:55:06] [Iter  732/2250] R3[412/1200] | LR: 0.023407 | E: -27.123901 | E_var:     0.1828 | E_err:   0.006680
[2025-10-03 03:55:11] [Iter  733/2250] R3[414/1200] | LR: 0.023349 | E: -27.135510 | E_var:     0.1769 | E_err:   0.006572
[2025-10-03 03:55:16] [Iter  734/2250] R3[416/1200] | LR: 0.023291 | E: -27.123253 | E_var:     0.2183 | E_err:   0.007300
[2025-10-03 03:55:21] [Iter  735/2250] R3[418/1200] | LR: 0.023233 | E: -27.124409 | E_var:     0.1959 | E_err:   0.006916
[2025-10-03 03:55:26] [Iter  736/2250] R3[420/1200] | LR: 0.023175 | E: -27.115119 | E_var:     0.2319 | E_err:   0.007524
[2025-10-03 03:55:31] [Iter  737/2250] R3[422/1200] | LR: 0.023116 | E: -27.115473 | E_var:     0.3397 | E_err:   0.009106
[2025-10-03 03:55:36] [Iter  738/2250] R3[424/1200] | LR: 0.023058 | E: -27.114568 | E_var:     0.1939 | E_err:   0.006880
[2025-10-03 03:55:41] [Iter  739/2250] R3[426/1200] | LR: 0.022999 | E: -27.125180 | E_var:     0.2031 | E_err:   0.007042
[2025-10-03 03:55:46] [Iter  740/2250] R3[428/1200] | LR: 0.022940 | E: -27.119421 | E_var:     0.1889 | E_err:   0.006791
[2025-10-03 03:55:51] [Iter  741/2250] R3[430/1200] | LR: 0.022881 | E: -27.122447 | E_var:     0.1887 | E_err:   0.006787
[2025-10-03 03:55:56] [Iter  742/2250] R3[432/1200] | LR: 0.022822 | E: -27.110746 | E_var:     0.2365 | E_err:   0.007598
[2025-10-03 03:56:01] [Iter  743/2250] R3[434/1200] | LR: 0.022763 | E: -27.116729 | E_var:     0.1866 | E_err:   0.006750
[2025-10-03 03:56:06] [Iter  744/2250] R3[436/1200] | LR: 0.022704 | E: -27.122634 | E_var:     0.2286 | E_err:   0.007471
[2025-10-03 03:56:11] [Iter  745/2250] R3[438/1200] | LR: 0.022644 | E: -27.120880 | E_var:     0.2780 | E_err:   0.008238
[2025-10-03 03:56:16] [Iter  746/2250] R3[440/1200] | LR: 0.022584 | E: -27.129389 | E_var:     0.2132 | E_err:   0.007214
[2025-10-03 03:56:20] [Iter  747/2250] R3[442/1200] | LR: 0.022524 | E: -27.105342 | E_var:     0.2296 | E_err:   0.007487
[2025-10-03 03:56:25] [Iter  748/2250] R3[444/1200] | LR: 0.022464 | E: -27.105432 | E_var:     0.3762 | E_err:   0.009583
[2025-10-03 03:56:30] [Iter  749/2250] R3[446/1200] | LR: 0.022404 | E: -27.115124 | E_var:     0.2025 | E_err:   0.007032
[2025-10-03 03:56:35] [Iter  750/2250] R3[448/1200] | LR: 0.022344 | E: -27.108016 | E_var:     0.2541 | E_err:   0.007877
[2025-10-03 03:56:40] [Iter  751/2250] R3[450/1200] | LR: 0.022284 | E: -27.117583 | E_var:     0.1989 | E_err:   0.006969
[2025-10-03 03:56:45] [Iter  752/2250] R3[452/1200] | LR: 0.022223 | E: -27.117557 | E_var:     0.2023 | E_err:   0.007028
[2025-10-03 03:56:50] [Iter  753/2250] R3[454/1200] | LR: 0.022162 | E: -27.124128 | E_var:     0.2135 | E_err:   0.007220
[2025-10-03 03:56:55] [Iter  754/2250] R3[456/1200] | LR: 0.022102 | E: -27.123239 | E_var:     0.2330 | E_err:   0.007542
[2025-10-03 03:57:00] [Iter  755/2250] R3[458/1200] | LR: 0.022041 | E: -27.113142 | E_var:     0.3398 | E_err:   0.009108
[2025-10-03 03:57:05] [Iter  756/2250] R3[460/1200] | LR: 0.021980 | E: -27.128941 | E_var:     0.2733 | E_err:   0.008168
[2025-10-03 03:57:10] [Iter  757/2250] R3[462/1200] | LR: 0.021918 | E: -27.116347 | E_var:     0.2719 | E_err:   0.008147
[2025-10-03 03:57:15] [Iter  758/2250] R3[464/1200] | LR: 0.021857 | E: -27.124340 | E_var:     0.2252 | E_err:   0.007416
[2025-10-03 03:57:20] [Iter  759/2250] R3[466/1200] | LR: 0.021796 | E: -27.133749 | E_var:     0.2110 | E_err:   0.007178
[2025-10-03 03:57:25] [Iter  760/2250] R3[468/1200] | LR: 0.021734 | E: -27.120094 | E_var:     0.2248 | E_err:   0.007409
[2025-10-03 03:57:30] [Iter  761/2250] R3[470/1200] | LR: 0.021673 | E: -27.118414 | E_var:     0.1909 | E_err:   0.006827
[2025-10-03 03:57:34] [Iter  762/2250] R3[472/1200] | LR: 0.021611 | E: -27.110586 | E_var:     0.4723 | E_err:   0.010738
[2025-10-03 03:57:39] [Iter  763/2250] R3[474/1200] | LR: 0.021549 | E: -27.111901 | E_var:     0.2188 | E_err:   0.007310
[2025-10-03 03:57:44] [Iter  764/2250] R3[476/1200] | LR: 0.021487 | E: -27.114054 | E_var:     0.1943 | E_err:   0.006887
[2025-10-03 03:57:49] [Iter  765/2250] R3[478/1200] | LR: 0.021425 | E: -27.118710 | E_var:     0.2104 | E_err:   0.007167
[2025-10-03 03:57:54] [Iter  766/2250] R3[480/1200] | LR: 0.021363 | E: -27.121318 | E_var:     0.2836 | E_err:   0.008321
[2025-10-03 03:57:59] [Iter  767/2250] R3[482/1200] | LR: 0.021300 | E: -27.120529 | E_var:     0.3226 | E_err:   0.008875
[2025-10-03 03:58:04] [Iter  768/2250] R3[484/1200] | LR: 0.021238 | E: -27.125077 | E_var:     0.1831 | E_err:   0.006686
[2025-10-03 03:58:09] [Iter  769/2250] R3[486/1200] | LR: 0.021176 | E: -27.124984 | E_var:     0.1769 | E_err:   0.006572
[2025-10-03 03:58:14] [Iter  770/2250] R3[488/1200] | LR: 0.021113 | E: -27.118637 | E_var:     0.2489 | E_err:   0.007796
[2025-10-03 03:58:19] [Iter  771/2250] R3[490/1200] | LR: 0.021050 | E: -27.117219 | E_var:     0.4333 | E_err:   0.010285
[2025-10-03 03:58:24] [Iter  772/2250] R3[492/1200] | LR: 0.020987 | E: -27.131065 | E_var:     0.2967 | E_err:   0.008512
[2025-10-03 03:58:29] [Iter  773/2250] R3[494/1200] | LR: 0.020924 | E: -27.124608 | E_var:     0.2098 | E_err:   0.007157
[2025-10-03 03:58:34] [Iter  774/2250] R3[496/1200] | LR: 0.020861 | E: -27.131794 | E_var:     0.1774 | E_err:   0.006582
[2025-10-03 03:58:39] [Iter  775/2250] R3[498/1200] | LR: 0.020798 | E: -27.126816 | E_var:     0.2379 | E_err:   0.007620
[2025-10-03 03:58:44] [Iter  776/2250] R3[500/1200] | LR: 0.020735 | E: -27.119930 | E_var:     0.2552 | E_err:   0.007894
[2025-10-03 03:58:49] [Iter  777/2250] R3[502/1200] | LR: 0.020672 | E: -27.120583 | E_var:     0.2104 | E_err:   0.007167
[2025-10-03 03:58:53] [Iter  778/2250] R3[504/1200] | LR: 0.020609 | E: -27.117801 | E_var:     0.2460 | E_err:   0.007749
[2025-10-03 03:58:58] [Iter  779/2250] R3[506/1200] | LR: 0.020545 | E: -27.121468 | E_var:     0.1824 | E_err:   0.006673
[2025-10-03 03:59:03] [Iter  780/2250] R3[508/1200] | LR: 0.020482 | E: -27.127901 | E_var:     0.1756 | E_err:   0.006548
[2025-10-03 03:59:08] [Iter  781/2250] R3[510/1200] | LR: 0.020418 | E: -27.123235 | E_var:     0.1842 | E_err:   0.006706
[2025-10-03 03:59:13] [Iter  782/2250] R3[512/1200] | LR: 0.020354 | E: -27.112874 | E_var:     0.2493 | E_err:   0.007802
[2025-10-03 03:59:18] [Iter  783/2250] R3[514/1200] | LR: 0.020291 | E: -27.122796 | E_var:     0.1849 | E_err:   0.006718
[2025-10-03 03:59:23] [Iter  784/2250] R3[516/1200] | LR: 0.020227 | E: -27.122019 | E_var:     0.2225 | E_err:   0.007369
[2025-10-03 03:59:28] [Iter  785/2250] R3[518/1200] | LR: 0.020163 | E: -27.127979 | E_var:     0.3335 | E_err:   0.009023
[2025-10-03 03:59:33] [Iter  786/2250] R3[520/1200] | LR: 0.020099 | E: -27.120112 | E_var:     0.2177 | E_err:   0.007291
[2025-10-03 03:59:38] [Iter  787/2250] R3[522/1200] | LR: 0.020035 | E: -27.121912 | E_var:     0.1612 | E_err:   0.006273
[2025-10-03 03:59:43] [Iter  788/2250] R3[524/1200] | LR: 0.019971 | E: -27.118435 | E_var:     0.2157 | E_err:   0.007257
[2025-10-03 03:59:48] [Iter  789/2250] R3[526/1200] | LR: 0.019907 | E: -27.113810 | E_var:     0.3390 | E_err:   0.009098
[2025-10-03 03:59:53] [Iter  790/2250] R3[528/1200] | LR: 0.019842 | E: -27.127449 | E_var:     0.2342 | E_err:   0.007562
[2025-10-03 03:59:58] [Iter  791/2250] R3[530/1200] | LR: 0.019778 | E: -27.127662 | E_var:     0.1976 | E_err:   0.006946
[2025-10-03 04:00:03] [Iter  792/2250] R3[532/1200] | LR: 0.019714 | E: -27.122301 | E_var:     0.2092 | E_err:   0.007146
[2025-10-03 04:00:08] [Iter  793/2250] R3[534/1200] | LR: 0.019649 | E: -27.135002 | E_var:     0.2088 | E_err:   0.007140
[2025-10-03 04:00:13] [Iter  794/2250] R3[536/1200] | LR: 0.019585 | E: -27.119915 | E_var:     0.1935 | E_err:   0.006874
[2025-10-03 04:00:17] [Iter  795/2250] R3[538/1200] | LR: 0.019520 | E: -27.110980 | E_var:     0.2482 | E_err:   0.007784
[2025-10-03 04:00:22] [Iter  796/2250] R3[540/1200] | LR: 0.019455 | E: -27.114693 | E_var:     0.2151 | E_err:   0.007247
[2025-10-03 04:00:27] [Iter  797/2250] R3[542/1200] | LR: 0.019391 | E: -27.128864 | E_var:     0.1936 | E_err:   0.006875
[2025-10-03 04:00:32] [Iter  798/2250] R3[544/1200] | LR: 0.019326 | E: -27.131087 | E_var:     0.1931 | E_err:   0.006866
[2025-10-03 04:00:37] [Iter  799/2250] R3[546/1200] | LR: 0.019261 | E: -27.117198 | E_var:     0.2999 | E_err:   0.008556
[2025-10-03 04:00:42] [Iter  800/2250] R3[548/1200] | LR: 0.019196 | E: -27.115534 | E_var:     0.1672 | E_err:   0.006388
[2025-10-03 04:00:42] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-03 04:00:47] [Iter  801/2250] R3[550/1200] | LR: 0.019132 | E: -27.128842 | E_var:     0.1967 | E_err:   0.006930
[2025-10-03 04:00:52] [Iter  802/2250] R3[552/1200] | LR: 0.019067 | E: -27.127217 | E_var:     0.1705 | E_err:   0.006451
[2025-10-03 04:00:57] [Iter  803/2250] R3[554/1200] | LR: 0.019002 | E: -27.120129 | E_var:     0.1913 | E_err:   0.006834
[2025-10-03 04:01:02] [Iter  804/2250] R3[556/1200] | LR: 0.018937 | E: -27.116755 | E_var:     0.1633 | E_err:   0.006314
[2025-10-03 04:01:07] [Iter  805/2250] R3[558/1200] | LR: 0.018872 | E: -27.119354 | E_var:     0.1728 | E_err:   0.006495
[2025-10-03 04:01:12] [Iter  806/2250] R3[560/1200] | LR: 0.018807 | E: -27.119905 | E_var:     0.1757 | E_err:   0.006550
[2025-10-03 04:01:17] [Iter  807/2250] R3[562/1200] | LR: 0.018741 | E: -27.117097 | E_var:     0.2076 | E_err:   0.007119
[2025-10-03 04:01:22] [Iter  808/2250] R3[564/1200] | LR: 0.018676 | E: -27.125445 | E_var:     0.1703 | E_err:   0.006447
[2025-10-03 04:01:27] [Iter  809/2250] R3[566/1200] | LR: 0.018611 | E: -27.121897 | E_var:     0.3068 | E_err:   0.008655
[2025-10-03 04:01:32] [Iter  810/2250] R3[568/1200] | LR: 0.018546 | E: -27.121901 | E_var:     0.1783 | E_err:   0.006598
[2025-10-03 04:01:36] [Iter  811/2250] R3[570/1200] | LR: 0.018481 | E: -27.120104 | E_var:     0.1581 | E_err:   0.006213
[2025-10-03 04:01:41] [Iter  812/2250] R3[572/1200] | LR: 0.018415 | E: -27.118431 | E_var:     0.2021 | E_err:   0.007024
[2025-10-03 04:01:46] [Iter  813/2250] R3[574/1200] | LR: 0.018350 | E: -27.114694 | E_var:     0.2371 | E_err:   0.007608
[2025-10-03 04:01:51] [Iter  814/2250] R3[576/1200] | LR: 0.018285 | E: -27.124099 | E_var:     0.1805 | E_err:   0.006639
[2025-10-03 04:01:56] [Iter  815/2250] R3[578/1200] | LR: 0.018220 | E: -27.132279 | E_var:     0.2496 | E_err:   0.007806
[2025-10-03 04:02:01] [Iter  816/2250] R3[580/1200] | LR: 0.018154 | E: -27.111143 | E_var:     0.2494 | E_err:   0.007804
[2025-10-03 04:02:06] [Iter  817/2250] R3[582/1200] | LR: 0.018089 | E: -27.111076 | E_var:     0.4404 | E_err:   0.010369
[2025-10-03 04:02:11] [Iter  818/2250] R3[584/1200] | LR: 0.018023 | E: -27.137452 | E_var:     0.2032 | E_err:   0.007044
[2025-10-03 04:02:16] [Iter  819/2250] R3[586/1200] | LR: 0.017958 | E: -27.109663 | E_var:     0.1862 | E_err:   0.006742
[2025-10-03 04:02:21] [Iter  820/2250] R3[588/1200] | LR: 0.017893 | E: -27.123999 | E_var:     0.2560 | E_err:   0.007906
[2025-10-03 04:02:26] [Iter  821/2250] R3[590/1200] | LR: 0.017827 | E: -27.124340 | E_var:     0.1899 | E_err:   0.006810
[2025-10-03 04:02:31] [Iter  822/2250] R3[592/1200] | LR: 0.017762 | E: -27.120287 | E_var:     0.2605 | E_err:   0.007975
[2025-10-03 04:02:36] [Iter  823/2250] R3[594/1200] | LR: 0.017696 | E: -27.120177 | E_var:     0.2116 | E_err:   0.007187
[2025-10-03 04:02:41] [Iter  824/2250] R3[596/1200] | LR: 0.017631 | E: -27.143926 | E_var:     0.3146 | E_err:   0.008764
[2025-10-03 04:02:46] [Iter  825/2250] R3[598/1200] | LR: 0.017565 | E: -27.140252 | E_var:     0.1978 | E_err:   0.006949
[2025-10-03 04:02:51] [Iter  826/2250] R3[600/1200] | LR: 0.017500 | E: -27.131713 | E_var:     0.2362 | E_err:   0.007593
[2025-10-03 04:02:55] [Iter  827/2250] R3[602/1200] | LR: 0.017435 | E: -27.130294 | E_var:     0.1771 | E_err:   0.006575
[2025-10-03 04:03:00] [Iter  828/2250] R3[604/1200] | LR: 0.017369 | E: -27.121114 | E_var:     0.1753 | E_err:   0.006542
[2025-10-03 04:03:05] [Iter  829/2250] R3[606/1200] | LR: 0.017304 | E: -27.123334 | E_var:     0.2249 | E_err:   0.007411
[2025-10-03 04:03:10] [Iter  830/2250] R3[608/1200] | LR: 0.017238 | E: -27.128820 | E_var:     0.1777 | E_err:   0.006587
[2025-10-03 04:03:15] [Iter  831/2250] R3[610/1200] | LR: 0.017173 | E: -27.120075 | E_var:     0.2887 | E_err:   0.008395
[2025-10-03 04:03:20] [Iter  832/2250] R3[612/1200] | LR: 0.017107 | E: -27.123383 | E_var:     0.1885 | E_err:   0.006784
[2025-10-03 04:03:25] [Iter  833/2250] R3[614/1200] | LR: 0.017042 | E: -27.126114 | E_var:     0.1878 | E_err:   0.006771
[2025-10-03 04:03:30] [Iter  834/2250] R3[616/1200] | LR: 0.016977 | E: -27.117232 | E_var:     0.5137 | E_err:   0.011199
[2025-10-03 04:03:35] [Iter  835/2250] R3[618/1200] | LR: 0.016911 | E: -27.126193 | E_var:     0.1838 | E_err:   0.006699
[2025-10-03 04:03:40] [Iter  836/2250] R3[620/1200] | LR: 0.016846 | E: -27.137173 | E_var:     0.2061 | E_err:   0.007093
[2025-10-03 04:03:45] [Iter  837/2250] R3[622/1200] | LR: 0.016780 | E: -27.117596 | E_var:     0.1884 | E_err:   0.006782
[2025-10-03 04:03:50] [Iter  838/2250] R3[624/1200] | LR: 0.016715 | E: -27.124021 | E_var:     0.2006 | E_err:   0.006998
[2025-10-03 04:03:55] [Iter  839/2250] R3[626/1200] | LR: 0.016650 | E: -27.121298 | E_var:     0.1928 | E_err:   0.006860
[2025-10-03 04:04:00] [Iter  840/2250] R3[628/1200] | LR: 0.016585 | E: -27.113366 | E_var:     0.2330 | E_err:   0.007542
[2025-10-03 04:04:05] [Iter  841/2250] R3[630/1200] | LR: 0.016519 | E: -27.123781 | E_var:     0.1667 | E_err:   0.006380
[2025-10-03 04:04:09] [Iter  842/2250] R3[632/1200] | LR: 0.016454 | E: -27.121113 | E_var:     0.1894 | E_err:   0.006800
[2025-10-03 04:04:14] [Iter  843/2250] R3[634/1200] | LR: 0.016389 | E: -27.134211 | E_var:     0.1973 | E_err:   0.006940
[2025-10-03 04:04:19] [Iter  844/2250] R3[636/1200] | LR: 0.016324 | E: -27.144052 | E_var:     0.2317 | E_err:   0.007521
[2025-10-03 04:04:24] [Iter  845/2250] R3[638/1200] | LR: 0.016259 | E: -27.115386 | E_var:     0.3113 | E_err:   0.008718
[2025-10-03 04:04:29] [Iter  846/2250] R3[640/1200] | LR: 0.016193 | E: -27.124219 | E_var:     0.1860 | E_err:   0.006739
[2025-10-03 04:04:34] [Iter  847/2250] R3[642/1200] | LR: 0.016128 | E: -27.123619 | E_var:     0.1681 | E_err:   0.006406
[2025-10-03 04:04:39] [Iter  848/2250] R3[644/1200] | LR: 0.016063 | E: -27.126942 | E_var:     0.1735 | E_err:   0.006508
[2025-10-03 04:04:44] [Iter  849/2250] R3[646/1200] | LR: 0.015998 | E: -27.124131 | E_var:     0.2093 | E_err:   0.007149
[2025-10-03 04:04:49] [Iter  850/2250] R3[648/1200] | LR: 0.015933 | E: -27.123870 | E_var:     0.2009 | E_err:   0.007004
[2025-10-03 04:04:54] [Iter  851/2250] R3[650/1200] | LR: 0.015868 | E: -27.116775 | E_var:     0.2755 | E_err:   0.008201
[2025-10-03 04:04:59] [Iter  852/2250] R3[652/1200] | LR: 0.015804 | E: -27.129844 | E_var:     0.2127 | E_err:   0.007206
[2025-10-03 04:05:04] [Iter  853/2250] R3[654/1200] | LR: 0.015739 | E: -27.123914 | E_var:     0.1765 | E_err:   0.006564
[2025-10-03 04:05:09] [Iter  854/2250] R3[656/1200] | LR: 0.015674 | E: -27.113448 | E_var:     0.2353 | E_err:   0.007580
[2025-10-03 04:05:14] [Iter  855/2250] R3[658/1200] | LR: 0.015609 | E: -27.131367 | E_var:     0.1669 | E_err:   0.006384
[2025-10-03 04:05:19] [Iter  856/2250] R3[660/1200] | LR: 0.015545 | E: -27.127434 | E_var:     0.2056 | E_err:   0.007085
[2025-10-03 04:05:24] [Iter  857/2250] R3[662/1200] | LR: 0.015480 | E: -27.125824 | E_var:     0.3706 | E_err:   0.009511
[2025-10-03 04:05:28] [Iter  858/2250] R3[664/1200] | LR: 0.015415 | E: -27.132605 | E_var:     0.1884 | E_err:   0.006782
[2025-10-03 04:05:33] [Iter  859/2250] R3[666/1200] | LR: 0.015351 | E: -27.129898 | E_var:     0.1658 | E_err:   0.006363
[2025-10-03 04:05:38] [Iter  860/2250] R3[668/1200] | LR: 0.015286 | E: -27.123908 | E_var:     0.1654 | E_err:   0.006355
[2025-10-03 04:05:43] [Iter  861/2250] R3[670/1200] | LR: 0.015222 | E: -27.119676 | E_var:     0.3572 | E_err:   0.009338
[2025-10-03 04:05:48] [Iter  862/2250] R3[672/1200] | LR: 0.015158 | E: -27.129938 | E_var:     0.4430 | E_err:   0.010399
[2025-10-03 04:05:53] [Iter  863/2250] R3[674/1200] | LR: 0.015093 | E: -27.131848 | E_var:     0.1658 | E_err:   0.006362
[2025-10-03 04:05:58] [Iter  864/2250] R3[676/1200] | LR: 0.015029 | E: -27.125903 | E_var:     0.1701 | E_err:   0.006444
[2025-10-03 04:06:03] [Iter  865/2250] R3[678/1200] | LR: 0.014965 | E: -27.119270 | E_var:     0.1706 | E_err:   0.006453
[2025-10-03 04:06:08] [Iter  866/2250] R3[680/1200] | LR: 0.014901 | E: -27.130436 | E_var:     0.1556 | E_err:   0.006163
[2025-10-03 04:06:13] [Iter  867/2250] R3[682/1200] | LR: 0.014837 | E: -27.120897 | E_var:     0.1732 | E_err:   0.006503
[2025-10-03 04:06:18] [Iter  868/2250] R3[684/1200] | LR: 0.014773 | E: -27.124996 | E_var:     0.1709 | E_err:   0.006460
[2025-10-03 04:06:23] [Iter  869/2250] R3[686/1200] | LR: 0.014709 | E: -27.130442 | E_var:     0.2577 | E_err:   0.007931
[2025-10-03 04:06:28] [Iter  870/2250] R3[688/1200] | LR: 0.014646 | E: -27.130674 | E_var:     0.2121 | E_err:   0.007197
[2025-10-03 04:06:33] [Iter  871/2250] R3[690/1200] | LR: 0.014582 | E: -27.124433 | E_var:     0.1701 | E_err:   0.006444
[2025-10-03 04:06:38] [Iter  872/2250] R3[692/1200] | LR: 0.014518 | E: -27.118787 | E_var:     0.2064 | E_err:   0.007098
[2025-10-03 04:06:43] [Iter  873/2250] R3[694/1200] | LR: 0.014455 | E: -27.129996 | E_var:     0.2069 | E_err:   0.007108
[2025-10-03 04:06:47] [Iter  874/2250] R3[696/1200] | LR: 0.014391 | E: -27.114222 | E_var:     0.2252 | E_err:   0.007415
[2025-10-03 04:06:52] [Iter  875/2250] R3[698/1200] | LR: 0.014328 | E: -27.128561 | E_var:     0.2039 | E_err:   0.007056
[2025-10-03 04:06:57] [Iter  876/2250] R3[700/1200] | LR: 0.014265 | E: -27.117948 | E_var:     0.1840 | E_err:   0.006702
[2025-10-03 04:07:02] [Iter  877/2250] R3[702/1200] | LR: 0.014202 | E: -27.116070 | E_var:     0.2484 | E_err:   0.007787
[2025-10-03 04:07:07] [Iter  878/2250] R3[704/1200] | LR: 0.014139 | E: -27.116923 | E_var:     0.1855 | E_err:   0.006730
[2025-10-03 04:07:12] [Iter  879/2250] R3[706/1200] | LR: 0.014076 | E: -27.116811 | E_var:     0.2465 | E_err:   0.007757
[2025-10-03 04:07:17] [Iter  880/2250] R3[708/1200] | LR: 0.014013 | E: -27.129251 | E_var:     0.4535 | E_err:   0.010522
[2025-10-03 04:07:22] [Iter  881/2250] R3[710/1200] | LR: 0.013950 | E: -27.135484 | E_var:     0.1935 | E_err:   0.006873
[2025-10-03 04:07:27] [Iter  882/2250] R3[712/1200] | LR: 0.013887 | E: -27.123432 | E_var:     0.2956 | E_err:   0.008495
[2025-10-03 04:07:32] [Iter  883/2250] R3[714/1200] | LR: 0.013824 | E: -27.123608 | E_var:     0.2592 | E_err:   0.007955
[2025-10-03 04:07:37] [Iter  884/2250] R3[716/1200] | LR: 0.013762 | E: -27.125018 | E_var:     0.1803 | E_err:   0.006634
[2025-10-03 04:07:42] [Iter  885/2250] R3[718/1200] | LR: 0.013700 | E: -27.121746 | E_var:     0.1690 | E_err:   0.006424
[2025-10-03 04:07:47] [Iter  886/2250] R3[720/1200] | LR: 0.013637 | E: -27.135126 | E_var:     0.1717 | E_err:   0.006475
[2025-10-03 04:07:52] [Iter  887/2250] R3[722/1200] | LR: 0.013575 | E: -27.129453 | E_var:     0.2250 | E_err:   0.007412
[2025-10-03 04:07:57] [Iter  888/2250] R3[724/1200] | LR: 0.013513 | E: -27.129267 | E_var:     0.1957 | E_err:   0.006912
[2025-10-03 04:08:01] [Iter  889/2250] R3[726/1200] | LR: 0.013451 | E: -27.120098 | E_var:     0.1601 | E_err:   0.006252
[2025-10-03 04:08:06] [Iter  890/2250] R3[728/1200] | LR: 0.013389 | E: -27.128213 | E_var:     0.1734 | E_err:   0.006507
[2025-10-03 04:08:11] [Iter  891/2250] R3[730/1200] | LR: 0.013327 | E: -27.122650 | E_var:     0.1767 | E_err:   0.006569
[2025-10-03 04:08:16] [Iter  892/2250] R3[732/1200] | LR: 0.013266 | E: -27.124893 | E_var:     0.2120 | E_err:   0.007193
[2025-10-03 04:08:21] [Iter  893/2250] R3[734/1200] | LR: 0.013204 | E: -27.121472 | E_var:     0.1781 | E_err:   0.006594
[2025-10-03 04:08:26] [Iter  894/2250] R3[736/1200] | LR: 0.013143 | E: -27.121387 | E_var:     0.2044 | E_err:   0.007063
[2025-10-03 04:08:31] [Iter  895/2250] R3[738/1200] | LR: 0.013082 | E: -27.121319 | E_var:     0.2359 | E_err:   0.007589
[2025-10-03 04:08:36] [Iter  896/2250] R3[740/1200] | LR: 0.013020 | E: -27.128916 | E_var:     0.2288 | E_err:   0.007474
[2025-10-03 04:08:41] [Iter  897/2250] R3[742/1200] | LR: 0.012959 | E: -27.130846 | E_var:     0.2394 | E_err:   0.007645
[2025-10-03 04:08:46] [Iter  898/2250] R3[744/1200] | LR: 0.012898 | E: -27.125723 | E_var:     0.1782 | E_err:   0.006596
[2025-10-03 04:08:51] [Iter  899/2250] R3[746/1200] | LR: 0.012838 | E: -27.132406 | E_var:     0.1722 | E_err:   0.006483
[2025-10-03 04:08:56] [Iter  900/2250] R3[748/1200] | LR: 0.012777 | E: -27.121535 | E_var:     0.2716 | E_err:   0.008144
[2025-10-03 04:09:01] [Iter  901/2250] R3[750/1200] | LR: 0.012716 | E: -27.125634 | E_var:     0.1995 | E_err:   0.006979
[2025-10-03 04:09:06] [Iter  902/2250] R3[752/1200] | LR: 0.012656 | E: -27.132201 | E_var:     0.2364 | E_err:   0.007596
[2025-10-03 04:09:11] [Iter  903/2250] R3[754/1200] | LR: 0.012596 | E: -27.126328 | E_var:     0.1730 | E_err:   0.006499
[2025-10-03 04:09:16] [Iter  904/2250] R3[756/1200] | LR: 0.012536 | E: -27.131116 | E_var:     0.1697 | E_err:   0.006437
[2025-10-03 04:09:20] [Iter  905/2250] R3[758/1200] | LR: 0.012476 | E: -27.135143 | E_var:     0.2313 | E_err:   0.007515
[2025-10-03 04:09:25] [Iter  906/2250] R3[760/1200] | LR: 0.012416 | E: -27.134882 | E_var:     0.1585 | E_err:   0.006220
[2025-10-03 04:09:30] [Iter  907/2250] R3[762/1200] | LR: 0.012356 | E: -27.113331 | E_var:     0.3776 | E_err:   0.009601
[2025-10-03 04:09:35] [Iter  908/2250] R3[764/1200] | LR: 0.012296 | E: -27.133658 | E_var:     0.1765 | E_err:   0.006565
[2025-10-03 04:09:40] [Iter  909/2250] R3[766/1200] | LR: 0.012237 | E: -27.122996 | E_var:     0.1781 | E_err:   0.006594
[2025-10-03 04:09:45] [Iter  910/2250] R3[768/1200] | LR: 0.012178 | E: -27.123595 | E_var:     0.2182 | E_err:   0.007298
[2025-10-03 04:09:50] [Iter  911/2250] R3[770/1200] | LR: 0.012119 | E: -27.121052 | E_var:     0.6179 | E_err:   0.012282
[2025-10-03 04:09:55] [Iter  912/2250] R3[772/1200] | LR: 0.012060 | E: -27.114800 | E_var:     0.4805 | E_err:   0.010831
[2025-10-03 04:10:00] [Iter  913/2250] R3[774/1200] | LR: 0.012001 | E: -27.113285 | E_var:     0.6506 | E_err:   0.012603
[2025-10-03 04:10:05] [Iter  914/2250] R3[776/1200] | LR: 0.011942 | E: -27.123995 | E_var:     0.6675 | E_err:   0.012766
[2025-10-03 04:10:10] [Iter  915/2250] R3[778/1200] | LR: 0.011884 | E: -27.114261 | E_var:     0.4992 | E_err:   0.011040
[2025-10-03 04:10:15] [Iter  916/2250] R3[780/1200] | LR: 0.011825 | E: -27.119654 | E_var:     0.1667 | E_err:   0.006379
[2025-10-03 04:10:20] [Iter  917/2250] R3[782/1200] | LR: 0.011767 | E: -27.121603 | E_var:     0.1768 | E_err:   0.006570
[2025-10-03 04:10:25] [Iter  918/2250] R3[784/1200] | LR: 0.011709 | E: -27.128423 | E_var:     0.1547 | E_err:   0.006145
[2025-10-03 04:10:30] [Iter  919/2250] R3[786/1200] | LR: 0.011651 | E: -27.126389 | E_var:     0.1607 | E_err:   0.006263
[2025-10-03 04:10:34] [Iter  920/2250] R3[788/1200] | LR: 0.011593 | E: -27.123092 | E_var:     0.3013 | E_err:   0.008577
[2025-10-03 04:10:39] [Iter  921/2250] R3[790/1200] | LR: 0.011536 | E: -27.108305 | E_var:     0.2509 | E_err:   0.007826
[2025-10-03 04:10:44] [Iter  922/2250] R3[792/1200] | LR: 0.011478 | E: -27.127055 | E_var:     0.1572 | E_err:   0.006196
[2025-10-03 04:10:49] [Iter  923/2250] R3[794/1200] | LR: 0.011421 | E: -27.125762 | E_var:     0.2177 | E_err:   0.007290
[2025-10-03 04:10:54] [Iter  924/2250] R3[796/1200] | LR: 0.011364 | E: -27.120153 | E_var:     0.1445 | E_err:   0.005940
[2025-10-03 04:10:59] [Iter  925/2250] R3[798/1200] | LR: 0.011307 | E: -27.130026 | E_var:     0.2025 | E_err:   0.007031
[2025-10-03 04:11:04] [Iter  926/2250] R3[800/1200] | LR: 0.011250 | E: -27.128425 | E_var:     0.1608 | E_err:   0.006265
[2025-10-03 04:11:09] [Iter  927/2250] R3[802/1200] | LR: 0.011193 | E: -27.124605 | E_var:     0.1599 | E_err:   0.006247
[2025-10-03 04:11:14] [Iter  928/2250] R3[804/1200] | LR: 0.011137 | E: -27.130925 | E_var:     0.2144 | E_err:   0.007235
[2025-10-03 04:11:19] [Iter  929/2250] R3[806/1200] | LR: 0.011081 | E: -27.126860 | E_var:     0.1537 | E_err:   0.006125
[2025-10-03 04:11:24] [Iter  930/2250] R3[808/1200] | LR: 0.011025 | E: -27.131300 | E_var:     0.2322 | E_err:   0.007530
[2025-10-03 04:11:29] [Iter  931/2250] R3[810/1200] | LR: 0.010969 | E: -27.119219 | E_var:     0.1678 | E_err:   0.006400
[2025-10-03 04:11:34] [Iter  932/2250] R3[812/1200] | LR: 0.010913 | E: -27.134752 | E_var:     0.1961 | E_err:   0.006919
[2025-10-03 04:11:39] [Iter  933/2250] R3[814/1200] | LR: 0.010858 | E: -27.122212 | E_var:     0.1666 | E_err:   0.006377
[2025-10-03 04:11:44] [Iter  934/2250] R3[816/1200] | LR: 0.010802 | E: -27.134633 | E_var:     0.1958 | E_err:   0.006914
[2025-10-03 04:11:49] [Iter  935/2250] R3[818/1200] | LR: 0.010747 | E: -27.129710 | E_var:     0.2141 | E_err:   0.007230
[2025-10-03 04:11:53] [Iter  936/2250] R3[820/1200] | LR: 0.010692 | E: -27.135073 | E_var:     0.1791 | E_err:   0.006613
[2025-10-03 04:11:58] [Iter  937/2250] R3[822/1200] | LR: 0.010637 | E: -27.124823 | E_var:     0.1742 | E_err:   0.006521
[2025-10-03 04:12:03] [Iter  938/2250] R3[824/1200] | LR: 0.010583 | E: -27.125993 | E_var:     0.2670 | E_err:   0.008073
[2025-10-03 04:12:08] [Iter  939/2250] R3[826/1200] | LR: 0.010528 | E: -27.124686 | E_var:     0.1579 | E_err:   0.006209
[2025-10-03 04:12:13] [Iter  940/2250] R3[828/1200] | LR: 0.010474 | E: -27.131186 | E_var:     0.2130 | E_err:   0.007212
[2025-10-03 04:12:18] [Iter  941/2250] R3[830/1200] | LR: 0.010420 | E: -27.133932 | E_var:     0.1676 | E_err:   0.006396
[2025-10-03 04:12:23] [Iter  942/2250] R3[832/1200] | LR: 0.010366 | E: -27.134954 | E_var:     0.1918 | E_err:   0.006843
[2025-10-03 04:12:28] [Iter  943/2250] R3[834/1200] | LR: 0.010312 | E: -27.137185 | E_var:     0.2241 | E_err:   0.007396
[2025-10-03 04:12:33] [Iter  944/2250] R3[836/1200] | LR: 0.010259 | E: -27.127664 | E_var:     0.2060 | E_err:   0.007091
[2025-10-03 04:12:38] [Iter  945/2250] R3[838/1200] | LR: 0.010206 | E: -27.122811 | E_var:     0.2793 | E_err:   0.008258
[2025-10-03 04:12:43] [Iter  946/2250] R3[840/1200] | LR: 0.010153 | E: -27.118696 | E_var:     0.1911 | E_err:   0.006831
[2025-10-03 04:12:48] [Iter  947/2250] R3[842/1200] | LR: 0.010100 | E: -27.135208 | E_var:     0.1957 | E_err:   0.006912
[2025-10-03 04:12:53] [Iter  948/2250] R3[844/1200] | LR: 0.010047 | E: -27.128200 | E_var:     0.2270 | E_err:   0.007445
[2025-10-03 04:12:58] [Iter  949/2250] R3[846/1200] | LR: 0.009995 | E: -27.119304 | E_var:     0.1706 | E_err:   0.006454
[2025-10-03 04:13:03] [Iter  950/2250] R3[848/1200] | LR: 0.009943 | E: -27.128452 | E_var:     0.1670 | E_err:   0.006385
[2025-10-03 04:13:07] [Iter  951/2250] R3[850/1200] | LR: 0.009890 | E: -27.123147 | E_var:     0.1768 | E_err:   0.006569
[2025-10-03 04:13:12] [Iter  952/2250] R3[852/1200] | LR: 0.009839 | E: -27.125863 | E_var:     0.1705 | E_err:   0.006452
[2025-10-03 04:13:17] [Iter  953/2250] R3[854/1200] | LR: 0.009787 | E: -27.125855 | E_var:     0.1757 | E_err:   0.006550
[2025-10-03 04:13:22] [Iter  954/2250] R3[856/1200] | LR: 0.009736 | E: -27.115429 | E_var:     0.4253 | E_err:   0.010190
[2025-10-03 04:13:27] [Iter  955/2250] R3[858/1200] | LR: 0.009684 | E: -27.123543 | E_var:     0.1817 | E_err:   0.006660
[2025-10-03 04:13:32] [Iter  956/2250] R3[860/1200] | LR: 0.009633 | E: -27.132476 | E_var:     0.2068 | E_err:   0.007106
[2025-10-03 04:13:37] [Iter  957/2250] R3[862/1200] | LR: 0.009583 | E: -27.132288 | E_var:     0.2322 | E_err:   0.007529
[2025-10-03 04:13:42] [Iter  958/2250] R3[864/1200] | LR: 0.009532 | E: -27.127621 | E_var:     0.1517 | E_err:   0.006087
[2025-10-03 04:13:47] [Iter  959/2250] R3[866/1200] | LR: 0.009482 | E: -27.124261 | E_var:     0.3053 | E_err:   0.008634
[2025-10-03 04:13:52] [Iter  960/2250] R3[868/1200] | LR: 0.009432 | E: -27.132409 | E_var:     0.1982 | E_err:   0.006956
[2025-10-03 04:13:57] [Iter  961/2250] R3[870/1200] | LR: 0.009382 | E: -27.143583 | E_var:     0.2216 | E_err:   0.007356
[2025-10-03 04:14:02] [Iter  962/2250] R3[872/1200] | LR: 0.009332 | E: -27.125083 | E_var:     0.1749 | E_err:   0.006534
[2025-10-03 04:14:07] [Iter  963/2250] R3[874/1200] | LR: 0.009283 | E: -27.136091 | E_var:     0.2179 | E_err:   0.007295
[2025-10-03 04:14:12] [Iter  964/2250] R3[876/1200] | LR: 0.009234 | E: -27.128720 | E_var:     0.1532 | E_err:   0.006116
[2025-10-03 04:14:17] [Iter  965/2250] R3[878/1200] | LR: 0.009185 | E: -27.125377 | E_var:     0.2135 | E_err:   0.007220
[2025-10-03 04:14:22] [Iter  966/2250] R3[880/1200] | LR: 0.009136 | E: -27.114586 | E_var:     0.2249 | E_err:   0.007409
[2025-10-03 04:14:26] [Iter  967/2250] R3[882/1200] | LR: 0.009087 | E: -27.126309 | E_var:     0.3578 | E_err:   0.009347
[2025-10-03 04:14:31] [Iter  968/2250] R3[884/1200] | LR: 0.009039 | E: -27.120072 | E_var:     0.1554 | E_err:   0.006160
[2025-10-03 04:14:36] [Iter  969/2250] R3[886/1200] | LR: 0.008991 | E: -27.127437 | E_var:     0.2044 | E_err:   0.007064
[2025-10-03 04:14:41] [Iter  970/2250] R3[888/1200] | LR: 0.008943 | E: -27.128726 | E_var:     0.1388 | E_err:   0.005821
[2025-10-03 04:14:46] [Iter  971/2250] R3[890/1200] | LR: 0.008896 | E: -27.129294 | E_var:     0.2140 | E_err:   0.007228
[2025-10-03 04:14:51] [Iter  972/2250] R3[892/1200] | LR: 0.008848 | E: -27.134703 | E_var:     0.1875 | E_err:   0.006765
[2025-10-03 04:14:56] [Iter  973/2250] R3[894/1200] | LR: 0.008801 | E: -27.118652 | E_var:     0.1602 | E_err:   0.006253
[2025-10-03 04:15:01] [Iter  974/2250] R3[896/1200] | LR: 0.008754 | E: -27.117958 | E_var:     0.1872 | E_err:   0.006761
[2025-10-03 04:15:06] [Iter  975/2250] R3[898/1200] | LR: 0.008708 | E: -27.131238 | E_var:     0.1650 | E_err:   0.006347
[2025-10-03 04:15:11] [Iter  976/2250] R3[900/1200] | LR: 0.008661 | E: -27.128555 | E_var:     0.1925 | E_err:   0.006856
[2025-10-03 04:15:16] [Iter  977/2250] R3[902/1200] | LR: 0.008615 | E: -27.129738 | E_var:     0.1546 | E_err:   0.006144
[2025-10-03 04:15:21] [Iter  978/2250] R3[904/1200] | LR: 0.008569 | E: -27.129548 | E_var:     0.1957 | E_err:   0.006913
[2025-10-03 04:15:26] [Iter  979/2250] R3[906/1200] | LR: 0.008523 | E: -27.128192 | E_var:     0.1440 | E_err:   0.005928
[2025-10-03 04:15:31] [Iter  980/2250] R3[908/1200] | LR: 0.008478 | E: -27.134717 | E_var:     0.1641 | E_err:   0.006329
[2025-10-03 04:15:36] [Iter  981/2250] R3[910/1200] | LR: 0.008433 | E: -27.113389 | E_var:     0.2593 | E_err:   0.007956
[2025-10-03 04:15:40] [Iter  982/2250] R3[912/1200] | LR: 0.008388 | E: -27.140534 | E_var:     0.1617 | E_err:   0.006283
[2025-10-03 04:15:45] [Iter  983/2250] R3[914/1200] | LR: 0.008343 | E: -27.129490 | E_var:     0.1829 | E_err:   0.006682
[2025-10-03 04:15:50] [Iter  984/2250] R3[916/1200] | LR: 0.008299 | E: -27.127161 | E_var:     0.1360 | E_err:   0.005762
[2025-10-03 04:15:55] [Iter  985/2250] R3[918/1200] | LR: 0.008255 | E: -27.131298 | E_var:     0.1586 | E_err:   0.006222
[2025-10-03 04:16:00] [Iter  986/2250] R3[920/1200] | LR: 0.008211 | E: -27.125740 | E_var:     0.1820 | E_err:   0.006667
[2025-10-03 04:16:05] [Iter  987/2250] R3[922/1200] | LR: 0.008167 | E: -27.132602 | E_var:     0.1854 | E_err:   0.006727
[2025-10-03 04:16:10] [Iter  988/2250] R3[924/1200] | LR: 0.008124 | E: -27.121524 | E_var:     0.1602 | E_err:   0.006254
[2025-10-03 04:16:15] [Iter  989/2250] R3[926/1200] | LR: 0.008080 | E: -27.132876 | E_var:     0.1823 | E_err:   0.006672
[2025-10-03 04:16:20] [Iter  990/2250] R3[928/1200] | LR: 0.008038 | E: -27.132652 | E_var:     0.1796 | E_err:   0.006623
[2025-10-03 04:16:25] [Iter  991/2250] R3[930/1200] | LR: 0.007995 | E: -27.129718 | E_var:     0.1739 | E_err:   0.006516
[2025-10-03 04:16:30] [Iter  992/2250] R3[932/1200] | LR: 0.007953 | E: -27.135423 | E_var:     0.1776 | E_err:   0.006585
[2025-10-03 04:16:35] [Iter  993/2250] R3[934/1200] | LR: 0.007910 | E: -27.132267 | E_var:     0.1915 | E_err:   0.006837
[2025-10-03 04:16:40] [Iter  994/2250] R3[936/1200] | LR: 0.007869 | E: -27.124712 | E_var:     0.1857 | E_err:   0.006733
[2025-10-03 04:16:45] [Iter  995/2250] R3[938/1200] | LR: 0.007827 | E: -27.123478 | E_var:     0.1620 | E_err:   0.006289
[2025-10-03 04:16:50] [Iter  996/2250] R3[940/1200] | LR: 0.007786 | E: -27.130821 | E_var:     0.1750 | E_err:   0.006536
[2025-10-03 04:16:55] [Iter  997/2250] R3[942/1200] | LR: 0.007745 | E: -27.130259 | E_var:     0.2347 | E_err:   0.007570
[2025-10-03 04:16:59] [Iter  998/2250] R3[944/1200] | LR: 0.007704 | E: -27.130405 | E_var:     0.1573 | E_err:   0.006198
[2025-10-03 04:17:04] [Iter  999/2250] R3[946/1200] | LR: 0.007663 | E: -27.127813 | E_var:     0.1800 | E_err:   0.006630
[2025-10-03 04:17:09] [Iter 1000/2250] R3[948/1200] | LR: 0.007623 | E: -27.136161 | E_var:     0.1619 | E_err:   0.006286
[2025-10-03 04:17:09] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-03 04:17:14] [Iter 1001/2250] R3[950/1200] | LR: 0.007583 | E: -27.130731 | E_var:     0.1727 | E_err:   0.006492
[2025-10-03 04:17:19] [Iter 1002/2250] R3[952/1200] | LR: 0.007543 | E: -27.126252 | E_var:     0.1856 | E_err:   0.006732
[2025-10-03 04:17:24] [Iter 1003/2250] R3[954/1200] | LR: 0.007504 | E: -27.125830 | E_var:     0.2898 | E_err:   0.008411
[2025-10-03 04:17:29] [Iter 1004/2250] R3[956/1200] | LR: 0.007465 | E: -27.133717 | E_var:     0.2399 | E_err:   0.007654
[2025-10-03 04:17:34] [Iter 1005/2250] R3[958/1200] | LR: 0.007426 | E: -27.117761 | E_var:     0.2224 | E_err:   0.007368
[2025-10-03 04:17:39] [Iter 1006/2250] R3[960/1200] | LR: 0.007387 | E: -27.113264 | E_var:     0.2109 | E_err:   0.007175
[2025-10-03 04:17:44] [Iter 1007/2250] R3[962/1200] | LR: 0.007349 | E: -27.132046 | E_var:     0.2563 | E_err:   0.007911
[2025-10-03 04:17:49] [Iter 1008/2250] R3[964/1200] | LR: 0.007311 | E: -27.141147 | E_var:     0.4205 | E_err:   0.010132
[2025-10-03 04:17:54] [Iter 1009/2250] R3[966/1200] | LR: 0.007273 | E: -27.121483 | E_var:     0.2524 | E_err:   0.007849
[2025-10-03 04:17:59] [Iter 1010/2250] R3[968/1200] | LR: 0.007236 | E: -27.131293 | E_var:     0.2245 | E_err:   0.007404
[2025-10-03 04:18:04] [Iter 1011/2250] R3[970/1200] | LR: 0.007198 | E: -27.125973 | E_var:     0.1672 | E_err:   0.006389
[2025-10-03 04:18:09] [Iter 1012/2250] R3[972/1200] | LR: 0.007161 | E: -27.122297 | E_var:     0.1681 | E_err:   0.006406
[2025-10-03 04:18:14] [Iter 1013/2250] R3[974/1200] | LR: 0.007125 | E: -27.131029 | E_var:     0.1696 | E_err:   0.006436
[2025-10-03 04:18:18] [Iter 1014/2250] R3[976/1200] | LR: 0.007088 | E: -27.125069 | E_var:     0.1863 | E_err:   0.006744
[2025-10-03 04:18:23] [Iter 1015/2250] R3[978/1200] | LR: 0.007052 | E: -27.121752 | E_var:     0.2488 | E_err:   0.007794
[2025-10-03 04:18:28] [Iter 1016/2250] R3[980/1200] | LR: 0.007017 | E: -27.119224 | E_var:     0.2007 | E_err:   0.006999
[2025-10-03 04:18:33] [Iter 1017/2250] R3[982/1200] | LR: 0.006981 | E: -27.130782 | E_var:     0.2057 | E_err:   0.007086
[2025-10-03 04:18:38] [Iter 1018/2250] R3[984/1200] | LR: 0.006946 | E: -27.123960 | E_var:     0.2067 | E_err:   0.007103
[2025-10-03 04:18:43] [Iter 1019/2250] R3[986/1200] | LR: 0.006911 | E: -27.138959 | E_var:     0.2241 | E_err:   0.007397
[2025-10-03 04:18:48] [Iter 1020/2250] R3[988/1200] | LR: 0.006876 | E: -27.123568 | E_var:     0.1794 | E_err:   0.006618
[2025-10-03 04:18:53] [Iter 1021/2250] R3[990/1200] | LR: 0.006842 | E: -27.143892 | E_var:     0.2221 | E_err:   0.007364
[2025-10-03 04:18:58] [Iter 1022/2250] R3[992/1200] | LR: 0.006808 | E: -27.134172 | E_var:     0.1688 | E_err:   0.006419
[2025-10-03 04:19:03] [Iter 1023/2250] R3[994/1200] | LR: 0.006774 | E: -27.129405 | E_var:     0.1492 | E_err:   0.006035
[2025-10-03 04:19:08] [Iter 1024/2250] R3[996/1200] | LR: 0.006741 | E: -27.131563 | E_var:     0.1630 | E_err:   0.006308
[2025-10-03 04:19:13] [Iter 1025/2250] R3[998/1200] | LR: 0.006708 | E: -27.124898 | E_var:     0.1858 | E_err:   0.006736
[2025-10-03 04:19:18] [Iter 1026/2250] R3[1000/1200] | LR: 0.006675 | E: -27.129680 | E_var:     0.1739 | E_err:   0.006516
[2025-10-03 04:19:23] [Iter 1027/2250] R3[1002/1200] | LR: 0.006642 | E: -27.135325 | E_var:     0.2120 | E_err:   0.007194
[2025-10-03 04:19:28] [Iter 1028/2250] R3[1004/1200] | LR: 0.006610 | E: -27.124029 | E_var:     0.2369 | E_err:   0.007604
[2025-10-03 04:19:32] [Iter 1029/2250] R3[1006/1200] | LR: 0.006578 | E: -27.123272 | E_var:     0.1939 | E_err:   0.006880
[2025-10-03 04:19:37] [Iter 1030/2250] R3[1008/1200] | LR: 0.006546 | E: -27.125828 | E_var:     0.2008 | E_err:   0.007002
[2025-10-03 04:19:42] [Iter 1031/2250] R3[1010/1200] | LR: 0.006515 | E: -27.135901 | E_var:     0.1866 | E_err:   0.006749
[2025-10-03 04:19:47] [Iter 1032/2250] R3[1012/1200] | LR: 0.006484 | E: -27.130575 | E_var:     0.1524 | E_err:   0.006099
[2025-10-03 04:19:52] [Iter 1033/2250] R3[1014/1200] | LR: 0.006453 | E: -27.116672 | E_var:     0.1754 | E_err:   0.006544
[2025-10-03 04:19:57] [Iter 1034/2250] R3[1016/1200] | LR: 0.006422 | E: -27.132340 | E_var:     0.1711 | E_err:   0.006464
[2025-10-03 04:20:02] [Iter 1035/2250] R3[1018/1200] | LR: 0.006392 | E: -27.126968 | E_var:     0.1842 | E_err:   0.006706
[2025-10-03 04:20:07] [Iter 1036/2250] R3[1020/1200] | LR: 0.006362 | E: -27.142488 | E_var:     0.1661 | E_err:   0.006369
[2025-10-03 04:20:12] [Iter 1037/2250] R3[1022/1200] | LR: 0.006333 | E: -27.129617 | E_var:     0.1899 | E_err:   0.006809
[2025-10-03 04:20:17] [Iter 1038/2250] R3[1024/1200] | LR: 0.006304 | E: -27.130745 | E_var:     0.1549 | E_err:   0.006150
[2025-10-03 04:20:22] [Iter 1039/2250] R3[1026/1200] | LR: 0.006275 | E: -27.131847 | E_var:     0.1789 | E_err:   0.006609
[2025-10-03 04:20:27] [Iter 1040/2250] R3[1028/1200] | LR: 0.006246 | E: -27.123073 | E_var:     0.1533 | E_err:   0.006118
[2025-10-03 04:20:32] [Iter 1041/2250] R3[1030/1200] | LR: 0.006218 | E: -27.133469 | E_var:     0.1479 | E_err:   0.006009
[2025-10-03 04:20:37] [Iter 1042/2250] R3[1032/1200] | LR: 0.006190 | E: -27.121225 | E_var:     0.1996 | E_err:   0.006981
[2025-10-03 04:20:42] [Iter 1043/2250] R3[1034/1200] | LR: 0.006162 | E: -27.125518 | E_var:     0.3137 | E_err:   0.008751
[2025-10-03 04:20:47] [Iter 1044/2250] R3[1036/1200] | LR: 0.006135 | E: -27.125346 | E_var:     0.1739 | E_err:   0.006516
[2025-10-03 04:20:51] [Iter 1045/2250] R3[1038/1200] | LR: 0.006107 | E: -27.117240 | E_var:     0.5583 | E_err:   0.011675
[2025-10-03 04:20:56] [Iter 1046/2250] R3[1040/1200] | LR: 0.006081 | E: -27.106284 | E_var:     0.6556 | E_err:   0.012652
[2025-10-03 04:21:01] [Iter 1047/2250] R3[1042/1200] | LR: 0.006054 | E: -27.125196 | E_var:     0.6556 | E_err:   0.012652
[2025-10-03 04:21:06] [Iter 1048/2250] R3[1044/1200] | LR: 0.006028 | E: -27.126919 | E_var:     0.5252 | E_err:   0.011324
[2025-10-03 04:21:11] [Iter 1049/2250] R3[1046/1200] | LR: 0.006002 | E: -27.122669 | E_var:     0.5114 | E_err:   0.011174
[2025-10-03 04:21:16] [Iter 1050/2250] R3[1048/1200] | LR: 0.005977 | E: -27.127413 | E_var:     0.1761 | E_err:   0.006556
[2025-10-03 04:21:21] [Iter 1051/2250] R3[1050/1200] | LR: 0.005952 | E: -27.124184 | E_var:     0.2055 | E_err:   0.007083
[2025-10-03 04:21:26] [Iter 1052/2250] R3[1052/1200] | LR: 0.005927 | E: -27.124177 | E_var:     0.1775 | E_err:   0.006583
[2025-10-03 04:21:31] [Iter 1053/2250] R3[1054/1200] | LR: 0.005902 | E: -27.135061 | E_var:     0.4105 | E_err:   0.010011
[2025-10-03 04:21:36] [Iter 1054/2250] R3[1056/1200] | LR: 0.005878 | E: -27.127147 | E_var:     0.2634 | E_err:   0.008020
[2025-10-03 04:21:41] [Iter 1055/2250] R3[1058/1200] | LR: 0.005854 | E: -27.133630 | E_var:     0.1831 | E_err:   0.006686
[2025-10-03 04:21:46] [Iter 1056/2250] R3[1060/1200] | LR: 0.005830 | E: -27.124797 | E_var:     0.1611 | E_err:   0.006271
[2025-10-03 04:21:51] [Iter 1057/2250] R3[1062/1200] | LR: 0.005807 | E: -27.135075 | E_var:     0.1710 | E_err:   0.006462
[2025-10-03 04:21:56] [Iter 1058/2250] R3[1064/1200] | LR: 0.005784 | E: -27.135194 | E_var:     0.1889 | E_err:   0.006792
[2025-10-03 04:22:01] [Iter 1059/2250] R3[1066/1200] | LR: 0.005761 | E: -27.123890 | E_var:     0.2871 | E_err:   0.008372
[2025-10-03 04:22:06] [Iter 1060/2250] R3[1068/1200] | LR: 0.005739 | E: -27.137668 | E_var:     0.1581 | E_err:   0.006212
[2025-10-03 04:22:10] [Iter 1061/2250] R3[1070/1200] | LR: 0.005717 | E: -27.130063 | E_var:     0.1988 | E_err:   0.006966
[2025-10-03 04:22:15] [Iter 1062/2250] R3[1072/1200] | LR: 0.005695 | E: -27.126339 | E_var:     0.1935 | E_err:   0.006874
[2025-10-03 04:22:20] [Iter 1063/2250] R3[1074/1200] | LR: 0.005674 | E: -27.116746 | E_var:     0.1558 | E_err:   0.006168
[2025-10-03 04:22:25] [Iter 1064/2250] R3[1076/1200] | LR: 0.005653 | E: -27.127501 | E_var:     0.2387 | E_err:   0.007635
[2025-10-03 04:22:30] [Iter 1065/2250] R3[1078/1200] | LR: 0.005632 | E: -27.129495 | E_var:     0.1813 | E_err:   0.006653
[2025-10-03 04:22:35] [Iter 1066/2250] R3[1080/1200] | LR: 0.005612 | E: -27.120059 | E_var:     0.2096 | E_err:   0.007154
[2025-10-03 04:22:40] [Iter 1067/2250] R3[1082/1200] | LR: 0.005592 | E: -27.122321 | E_var:     0.1844 | E_err:   0.006710
[2025-10-03 04:22:45] [Iter 1068/2250] R3[1084/1200] | LR: 0.005572 | E: -27.133777 | E_var:     0.1848 | E_err:   0.006717
[2025-10-03 04:22:50] [Iter 1069/2250] R3[1086/1200] | LR: 0.005553 | E: -27.138796 | E_var:     0.3011 | E_err:   0.008574
[2025-10-03 04:22:55] [Iter 1070/2250] R3[1088/1200] | LR: 0.005534 | E: -27.136078 | E_var:     0.1309 | E_err:   0.005653
[2025-10-03 04:23:00] [Iter 1071/2250] R3[1090/1200] | LR: 0.005515 | E: -27.125329 | E_var:     0.1798 | E_err:   0.006626
[2025-10-03 04:23:05] [Iter 1072/2250] R3[1092/1200] | LR: 0.005496 | E: -27.122360 | E_var:     0.1787 | E_err:   0.006605
[2025-10-03 04:23:10] [Iter 1073/2250] R3[1094/1200] | LR: 0.005478 | E: -27.137925 | E_var:     0.1779 | E_err:   0.006591
[2025-10-03 04:23:15] [Iter 1074/2250] R3[1096/1200] | LR: 0.005460 | E: -27.126849 | E_var:     0.1602 | E_err:   0.006254
[2025-10-03 04:23:20] [Iter 1075/2250] R3[1098/1200] | LR: 0.005443 | E: -27.128930 | E_var:     0.1910 | E_err:   0.006829
[2025-10-03 04:23:24] [Iter 1076/2250] R3[1100/1200] | LR: 0.005426 | E: -27.129734 | E_var:     0.1993 | E_err:   0.006976
[2025-10-03 04:23:29] [Iter 1077/2250] R3[1102/1200] | LR: 0.005409 | E: -27.131903 | E_var:     0.2187 | E_err:   0.007306
[2025-10-03 04:23:34] [Iter 1078/2250] R3[1104/1200] | LR: 0.005393 | E: -27.136532 | E_var:     0.1582 | E_err:   0.006216
[2025-10-03 04:23:39] [Iter 1079/2250] R3[1106/1200] | LR: 0.005377 | E: -27.143462 | E_var:     0.1806 | E_err:   0.006640
[2025-10-03 04:23:44] [Iter 1080/2250] R3[1108/1200] | LR: 0.005361 | E: -27.132384 | E_var:     0.2571 | E_err:   0.007922
[2025-10-03 04:23:49] [Iter 1081/2250] R3[1110/1200] | LR: 0.005345 | E: -27.130312 | E_var:     0.1382 | E_err:   0.005808
[2025-10-03 04:23:54] [Iter 1082/2250] R3[1112/1200] | LR: 0.005330 | E: -27.132686 | E_var:     0.1840 | E_err:   0.006702
[2025-10-03 04:23:59] [Iter 1083/2250] R3[1114/1200] | LR: 0.005315 | E: -27.126284 | E_var:     0.1438 | E_err:   0.005926
[2025-10-03 04:24:04] [Iter 1084/2250] R3[1116/1200] | LR: 0.005301 | E: -27.116516 | E_var:     0.1820 | E_err:   0.006666
[2025-10-03 04:24:09] [Iter 1085/2250] R3[1118/1200] | LR: 0.005287 | E: -27.125335 | E_var:     0.2177 | E_err:   0.007290
[2025-10-03 04:24:14] [Iter 1086/2250] R3[1120/1200] | LR: 0.005273 | E: -27.128882 | E_var:     0.1744 | E_err:   0.006525
[2025-10-03 04:24:19] [Iter 1087/2250] R3[1122/1200] | LR: 0.005260 | E: -27.121122 | E_var:     0.3703 | E_err:   0.009508
[2025-10-03 04:24:24] [Iter 1088/2250] R3[1124/1200] | LR: 0.005247 | E: -27.128598 | E_var:     0.1591 | E_err:   0.006232
[2025-10-03 04:24:29] [Iter 1089/2250] R3[1126/1200] | LR: 0.005234 | E: -27.130629 | E_var:     0.1690 | E_err:   0.006423
[2025-10-03 04:24:34] [Iter 1090/2250] R3[1128/1200] | LR: 0.005221 | E: -27.124383 | E_var:     0.1803 | E_err:   0.006634
[2025-10-03 04:24:39] [Iter 1091/2250] R3[1130/1200] | LR: 0.005209 | E: -27.138243 | E_var:     0.1648 | E_err:   0.006344
[2025-10-03 04:24:43] [Iter 1092/2250] R3[1132/1200] | LR: 0.005198 | E: -27.124442 | E_var:     0.1727 | E_err:   0.006493
[2025-10-03 04:24:48] [Iter 1093/2250] R3[1134/1200] | LR: 0.005186 | E: -27.141955 | E_var:     0.1644 | E_err:   0.006335
[2025-10-03 04:24:53] [Iter 1094/2250] R3[1136/1200] | LR: 0.005175 | E: -27.130936 | E_var:     0.1988 | E_err:   0.006967
[2025-10-03 04:24:58] [Iter 1095/2250] R3[1138/1200] | LR: 0.005164 | E: -27.131027 | E_var:     0.2463 | E_err:   0.007754
[2025-10-03 04:25:03] [Iter 1096/2250] R3[1140/1200] | LR: 0.005154 | E: -27.135864 | E_var:     0.1703 | E_err:   0.006448
[2025-10-03 04:25:08] [Iter 1097/2250] R3[1142/1200] | LR: 0.005144 | E: -27.128076 | E_var:     0.1564 | E_err:   0.006180
[2025-10-03 04:25:13] [Iter 1098/2250] R3[1144/1200] | LR: 0.005134 | E: -27.136537 | E_var:     0.1694 | E_err:   0.006431
[2025-10-03 04:25:18] [Iter 1099/2250] R3[1146/1200] | LR: 0.005125 | E: -27.128148 | E_var:     0.1845 | E_err:   0.006711
[2025-10-03 04:25:23] [Iter 1100/2250] R3[1148/1200] | LR: 0.005116 | E: -27.128989 | E_var:     0.1922 | E_err:   0.006851
[2025-10-03 04:25:28] [Iter 1101/2250] R3[1150/1200] | LR: 0.005107 | E: -27.132719 | E_var:     0.1751 | E_err:   0.006538
[2025-10-03 04:25:33] [Iter 1102/2250] R3[1152/1200] | LR: 0.005099 | E: -27.124656 | E_var:     0.1524 | E_err:   0.006099
[2025-10-03 04:25:38] [Iter 1103/2250] R3[1154/1200] | LR: 0.005091 | E: -27.118839 | E_var:     0.1633 | E_err:   0.006315
[2025-10-03 04:25:43] [Iter 1104/2250] R3[1156/1200] | LR: 0.005083 | E: -27.130081 | E_var:     0.2034 | E_err:   0.007047
[2025-10-03 04:25:48] [Iter 1105/2250] R3[1158/1200] | LR: 0.005075 | E: -27.130714 | E_var:     0.1994 | E_err:   0.006977
[2025-10-03 04:25:53] [Iter 1106/2250] R3[1160/1200] | LR: 0.005068 | E: -27.122229 | E_var:     0.1491 | E_err:   0.006034
[2025-10-03 04:25:57] [Iter 1107/2250] R3[1162/1200] | LR: 0.005062 | E: -27.132586 | E_var:     0.2017 | E_err:   0.007018
[2025-10-03 04:26:02] [Iter 1108/2250] R3[1164/1200] | LR: 0.005055 | E: -27.127878 | E_var:     0.1923 | E_err:   0.006851
[2025-10-03 04:26:07] [Iter 1109/2250] R3[1166/1200] | LR: 0.005049 | E: -27.130253 | E_var:     0.1570 | E_err:   0.006191
[2025-10-03 04:26:12] [Iter 1110/2250] R3[1168/1200] | LR: 0.005044 | E: -27.129663 | E_var:     0.1620 | E_err:   0.006289
[2025-10-03 04:26:17] [Iter 1111/2250] R3[1170/1200] | LR: 0.005039 | E: -27.124962 | E_var:     0.2163 | E_err:   0.007267
[2025-10-03 04:26:22] [Iter 1112/2250] R3[1172/1200] | LR: 0.005034 | E: -27.128377 | E_var:     0.1974 | E_err:   0.006942
[2025-10-03 04:26:27] [Iter 1113/2250] R3[1174/1200] | LR: 0.005029 | E: -27.143841 | E_var:     0.1718 | E_err:   0.006476
[2025-10-03 04:26:32] [Iter 1114/2250] R3[1176/1200] | LR: 0.005025 | E: -27.139583 | E_var:     0.2674 | E_err:   0.008080
[2025-10-03 04:26:37] [Iter 1115/2250] R3[1178/1200] | LR: 0.005021 | E: -27.127372 | E_var:     0.2019 | E_err:   0.007020
[2025-10-03 04:26:42] [Iter 1116/2250] R3[1180/1200] | LR: 0.005017 | E: -27.129819 | E_var:     0.2055 | E_err:   0.007083
[2025-10-03 04:26:47] [Iter 1117/2250] R3[1182/1200] | LR: 0.005014 | E: -27.129253 | E_var:     0.2091 | E_err:   0.007145
[2025-10-03 04:26:52] [Iter 1118/2250] R3[1184/1200] | LR: 0.005011 | E: -27.132948 | E_var:     0.2096 | E_err:   0.007153
[2025-10-03 04:26:57] [Iter 1119/2250] R3[1186/1200] | LR: 0.005008 | E: -27.136216 | E_var:     0.1584 | E_err:   0.006219
[2025-10-03 04:27:02] [Iter 1120/2250] R3[1188/1200] | LR: 0.005006 | E: -27.132188 | E_var:     0.1684 | E_err:   0.006412
[2025-10-03 04:27:07] [Iter 1121/2250] R3[1190/1200] | LR: 0.005004 | E: -27.123311 | E_var:     0.2388 | E_err:   0.007636
[2025-10-03 04:27:12] [Iter 1122/2250] R3[1192/1200] | LR: 0.005003 | E: -27.128454 | E_var:     0.1747 | E_err:   0.006531
[2025-10-03 04:27:17] [Iter 1123/2250] R3[1194/1200] | LR: 0.005002 | E: -27.127990 | E_var:     0.1670 | E_err:   0.006386
[2025-10-03 04:27:21] [Iter 1124/2250] R3[1196/1200] | LR: 0.005001 | E: -27.138781 | E_var:     0.1980 | E_err:   0.006953
[2025-10-03 04:27:26] [Iter 1125/2250] R3[1198/1200] | LR: 0.005000 | E: -27.134045 | E_var:     0.3144 | E_err:   0.008761
[2025-10-03 04:27:26] 🔄 RESTART #4 | Period: 2400
[2025-10-03 04:27:31] [Iter 1126/2250] R4[0/2400]   | LR: 0.030000 | E: -27.133504 | E_var:     0.2002 | E_err:   0.006992
[2025-10-03 04:27:36] [Iter 1127/2250] R4[2/2400]   | LR: 0.030000 | E: -27.137070 | E_var:     0.1860 | E_err:   0.006738
[2025-10-03 04:27:41] [Iter 1128/2250] R4[4/2400]   | LR: 0.030000 | E: -27.125974 | E_var:     0.1858 | E_err:   0.006735
[2025-10-03 04:27:46] [Iter 1129/2250] R4[6/2400]   | LR: 0.030000 | E: -27.130611 | E_var:     0.1497 | E_err:   0.006046
[2025-10-03 04:27:51] [Iter 1130/2250] R4[8/2400]   | LR: 0.029999 | E: -27.129079 | E_var:     0.1581 | E_err:   0.006213
[2025-10-03 04:27:56] [Iter 1131/2250] R4[10/2400]  | LR: 0.029999 | E: -27.132930 | E_var:     0.1885 | E_err:   0.006783
[2025-10-03 04:28:01] [Iter 1132/2250] R4[12/2400]  | LR: 0.029998 | E: -27.137387 | E_var:     0.1522 | E_err:   0.006095
[2025-10-03 04:28:06] [Iter 1133/2250] R4[14/2400]  | LR: 0.029998 | E: -27.127484 | E_var:     0.1392 | E_err:   0.005830
[2025-10-03 04:28:11] [Iter 1134/2250] R4[16/2400]  | LR: 0.029997 | E: -27.138776 | E_var:     0.1510 | E_err:   0.006072
[2025-10-03 04:28:16] [Iter 1135/2250] R4[18/2400]  | LR: 0.029997 | E: -27.131030 | E_var:     0.1490 | E_err:   0.006032
[2025-10-03 04:28:21] [Iter 1136/2250] R4[20/2400]  | LR: 0.029996 | E: -27.121474 | E_var:     0.1870 | E_err:   0.006757
[2025-10-03 04:28:26] [Iter 1137/2250] R4[22/2400]  | LR: 0.029995 | E: -27.128717 | E_var:     0.1730 | E_err:   0.006499
[2025-10-03 04:28:31] [Iter 1138/2250] R4[24/2400]  | LR: 0.029994 | E: -27.128297 | E_var:     0.1653 | E_err:   0.006352
[2025-10-03 04:28:35] [Iter 1139/2250] R4[26/2400]  | LR: 0.029993 | E: -27.130647 | E_var:     0.1923 | E_err:   0.006853
[2025-10-03 04:28:40] [Iter 1140/2250] R4[28/2400]  | LR: 0.029992 | E: -27.129955 | E_var:     0.1932 | E_err:   0.006868
[2025-10-03 04:28:45] [Iter 1141/2250] R4[30/2400]  | LR: 0.029990 | E: -27.136661 | E_var:     0.1834 | E_err:   0.006691
[2025-10-03 04:28:50] [Iter 1142/2250] R4[32/2400]  | LR: 0.029989 | E: -27.128540 | E_var:     0.1792 | E_err:   0.006614
[2025-10-03 04:28:55] [Iter 1143/2250] R4[34/2400]  | LR: 0.029988 | E: -27.123014 | E_var:     0.1724 | E_err:   0.006487
[2025-10-03 04:29:00] [Iter 1144/2250] R4[36/2400]  | LR: 0.029986 | E: -27.128747 | E_var:     0.2358 | E_err:   0.007587
[2025-10-03 04:29:05] [Iter 1145/2250] R4[38/2400]  | LR: 0.029985 | E: -27.139361 | E_var:     0.1492 | E_err:   0.006036
[2025-10-03 04:29:10] [Iter 1146/2250] R4[40/2400]  | LR: 0.029983 | E: -27.133969 | E_var:     0.1873 | E_err:   0.006761
[2025-10-03 04:29:15] [Iter 1147/2250] R4[42/2400]  | LR: 0.029981 | E: -27.132517 | E_var:     0.1556 | E_err:   0.006163
[2025-10-03 04:29:20] [Iter 1148/2250] R4[44/2400]  | LR: 0.029979 | E: -27.124852 | E_var:     0.1879 | E_err:   0.006773
[2025-10-03 04:29:25] [Iter 1149/2250] R4[46/2400]  | LR: 0.029977 | E: -27.135042 | E_var:     0.1971 | E_err:   0.006937
[2025-10-03 04:29:30] [Iter 1150/2250] R4[48/2400]  | LR: 0.029975 | E: -27.141251 | E_var:     0.1437 | E_err:   0.005923
[2025-10-03 04:29:35] [Iter 1151/2250] R4[50/2400]  | LR: 0.029973 | E: -27.132408 | E_var:     0.1553 | E_err:   0.006157
[2025-10-03 04:29:40] [Iter 1152/2250] R4[52/2400]  | LR: 0.029971 | E: -27.129515 | E_var:     0.1966 | E_err:   0.006928
[2025-10-03 04:29:45] [Iter 1153/2250] R4[54/2400]  | LR: 0.029969 | E: -27.124926 | E_var:     0.1626 | E_err:   0.006300
[2025-10-03 04:29:50] [Iter 1154/2250] R4[56/2400]  | LR: 0.029966 | E: -27.131657 | E_var:     0.1570 | E_err:   0.006191
[2025-10-03 04:29:54] [Iter 1155/2250] R4[58/2400]  | LR: 0.029964 | E: -27.133557 | E_var:     0.1764 | E_err:   0.006562
[2025-10-03 04:29:59] [Iter 1156/2250] R4[60/2400]  | LR: 0.029961 | E: -27.118766 | E_var:     0.1671 | E_err:   0.006388
[2025-10-03 04:30:04] [Iter 1157/2250] R4[62/2400]  | LR: 0.029959 | E: -27.137602 | E_var:     0.1615 | E_err:   0.006280
[2025-10-03 04:30:09] [Iter 1158/2250] R4[64/2400]  | LR: 0.029956 | E: -27.136702 | E_var:     0.1316 | E_err:   0.005669
[2025-10-03 04:30:14] [Iter 1159/2250] R4[66/2400]  | LR: 0.029953 | E: -27.130397 | E_var:     0.1407 | E_err:   0.005861
[2025-10-03 04:30:19] [Iter 1160/2250] R4[68/2400]  | LR: 0.029951 | E: -27.130350 | E_var:     0.2248 | E_err:   0.007408
[2025-10-03 04:30:24] [Iter 1161/2250] R4[70/2400]  | LR: 0.029948 | E: -27.134944 | E_var:     0.1561 | E_err:   0.006174
[2025-10-03 04:30:29] [Iter 1162/2250] R4[72/2400]  | LR: 0.029945 | E: -27.136782 | E_var:     0.1750 | E_err:   0.006536
[2025-10-03 04:30:34] [Iter 1163/2250] R4[74/2400]  | LR: 0.029941 | E: -27.130764 | E_var:     0.1431 | E_err:   0.005911
[2025-10-03 04:30:39] [Iter 1164/2250] R4[76/2400]  | LR: 0.029938 | E: -27.124565 | E_var:     0.2202 | E_err:   0.007333
[2025-10-03 04:30:44] [Iter 1165/2250] R4[78/2400]  | LR: 0.029935 | E: -27.130981 | E_var:     0.1640 | E_err:   0.006329
[2025-10-03 04:30:49] [Iter 1166/2250] R4[80/2400]  | LR: 0.029932 | E: -27.140262 | E_var:     0.1721 | E_err:   0.006481
[2025-10-03 04:30:54] [Iter 1167/2250] R4[82/2400]  | LR: 0.029928 | E: -27.131211 | E_var:     0.2105 | E_err:   0.007169
[2025-10-03 04:30:59] [Iter 1168/2250] R4[84/2400]  | LR: 0.029925 | E: -27.129008 | E_var:     0.1796 | E_err:   0.006623
[2025-10-03 04:31:04] [Iter 1169/2250] R4[86/2400]  | LR: 0.029921 | E: -27.126222 | E_var:     0.2026 | E_err:   0.007032
[2025-10-03 04:31:09] [Iter 1170/2250] R4[88/2400]  | LR: 0.029917 | E: -27.123908 | E_var:     0.1924 | E_err:   0.006854
[2025-10-03 04:31:13] [Iter 1171/2250] R4[90/2400]  | LR: 0.029913 | E: -27.138324 | E_var:     0.1498 | E_err:   0.006048
[2025-10-03 04:31:18] [Iter 1172/2250] R4[92/2400]  | LR: 0.029909 | E: -27.132551 | E_var:     0.1563 | E_err:   0.006178
[2025-10-03 04:31:23] [Iter 1173/2250] R4[94/2400]  | LR: 0.029905 | E: -27.131063 | E_var:     0.1456 | E_err:   0.005961
[2025-10-03 04:31:28] [Iter 1174/2250] R4[96/2400]  | LR: 0.029901 | E: -27.129246 | E_var:     0.1843 | E_err:   0.006708
[2025-10-03 04:31:33] [Iter 1175/2250] R4[98/2400]  | LR: 0.029897 | E: -27.129734 | E_var:     0.2472 | E_err:   0.007769
[2025-10-03 04:31:38] [Iter 1176/2250] R4[100/2400] | LR: 0.029893 | E: -27.131294 | E_var:     0.1971 | E_err:   0.006936
[2025-10-03 04:31:43] [Iter 1177/2250] R4[102/2400] | LR: 0.029889 | E: -27.126504 | E_var:     0.1845 | E_err:   0.006712
[2025-10-03 04:31:48] [Iter 1178/2250] R4[104/2400] | LR: 0.029884 | E: -27.143890 | E_var:     0.2042 | E_err:   0.007061
[2025-10-03 04:31:53] [Iter 1179/2250] R4[106/2400] | LR: 0.029880 | E: -27.137776 | E_var:     0.1515 | E_err:   0.006082
[2025-10-03 04:31:58] [Iter 1180/2250] R4[108/2400] | LR: 0.029875 | E: -27.132926 | E_var:     0.1863 | E_err:   0.006743
[2025-10-03 04:32:03] [Iter 1181/2250] R4[110/2400] | LR: 0.029871 | E: -27.133751 | E_var:     0.2534 | E_err:   0.007866
[2025-10-03 04:32:08] [Iter 1182/2250] R4[112/2400] | LR: 0.029866 | E: -27.131932 | E_var:     0.1843 | E_err:   0.006707
[2025-10-03 04:32:13] [Iter 1183/2250] R4[114/2400] | LR: 0.029861 | E: -27.143782 | E_var:     0.1341 | E_err:   0.005721
[2025-10-03 04:32:18] [Iter 1184/2250] R4[116/2400] | LR: 0.029856 | E: -27.133428 | E_var:     0.1703 | E_err:   0.006447
[2025-10-03 04:32:23] [Iter 1185/2250] R4[118/2400] | LR: 0.029851 | E: -27.126019 | E_var:     0.1838 | E_err:   0.006699
[2025-10-03 04:32:28] [Iter 1186/2250] R4[120/2400] | LR: 0.029846 | E: -27.129992 | E_var:     0.1759 | E_err:   0.006554
[2025-10-03 04:32:32] [Iter 1187/2250] R4[122/2400] | LR: 0.029841 | E: -27.132181 | E_var:     0.1458 | E_err:   0.005965
[2025-10-03 04:32:37] [Iter 1188/2250] R4[124/2400] | LR: 0.029836 | E: -27.131564 | E_var:     0.1764 | E_err:   0.006562
[2025-10-03 04:32:42] [Iter 1189/2250] R4[126/2400] | LR: 0.029830 | E: -27.121562 | E_var:     0.1449 | E_err:   0.005948
[2025-10-03 04:32:47] [Iter 1190/2250] R4[128/2400] | LR: 0.029825 | E: -27.137687 | E_var:     0.1488 | E_err:   0.006027
[2025-10-03 04:32:52] [Iter 1191/2250] R4[130/2400] | LR: 0.029819 | E: -27.134265 | E_var:     0.1477 | E_err:   0.006005
[2025-10-03 04:32:57] [Iter 1192/2250] R4[132/2400] | LR: 0.029814 | E: -27.122186 | E_var:     0.1463 | E_err:   0.005976
[2025-10-03 04:33:02] [Iter 1193/2250] R4[134/2400] | LR: 0.029808 | E: -27.128445 | E_var:     0.1937 | E_err:   0.006878
[2025-10-03 04:33:07] [Iter 1194/2250] R4[136/2400] | LR: 0.029802 | E: -27.132077 | E_var:     0.2092 | E_err:   0.007147
[2025-10-03 04:33:12] [Iter 1195/2250] R4[138/2400] | LR: 0.029797 | E: -27.131191 | E_var:     0.1980 | E_err:   0.006953
[2025-10-03 04:33:17] [Iter 1196/2250] R4[140/2400] | LR: 0.029791 | E: -27.139975 | E_var:     0.1961 | E_err:   0.006920
[2025-10-03 04:33:22] [Iter 1197/2250] R4[142/2400] | LR: 0.029785 | E: -27.130685 | E_var:     0.2454 | E_err:   0.007740
[2025-10-03 04:33:27] [Iter 1198/2250] R4[144/2400] | LR: 0.029779 | E: -27.135845 | E_var:     0.3421 | E_err:   0.009139
[2025-10-03 04:33:32] [Iter 1199/2250] R4[146/2400] | LR: 0.029772 | E: -27.130787 | E_var:     0.1772 | E_err:   0.006577
[2025-10-03 04:33:37] [Iter 1200/2250] R4[148/2400] | LR: 0.029766 | E: -27.134655 | E_var:     0.1434 | E_err:   0.005917
[2025-10-03 04:33:37] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-03 04:33:42] [Iter 1201/2250] R4[150/2400] | LR: 0.029760 | E: -27.133691 | E_var:     0.1444 | E_err:   0.005938
[2025-10-03 04:33:47] [Iter 1202/2250] R4[152/2400] | LR: 0.029753 | E: -27.129566 | E_var:     0.1923 | E_err:   0.006852
[2025-10-03 04:33:52] [Iter 1203/2250] R4[154/2400] | LR: 0.029747 | E: -27.139436 | E_var:     0.9896 | E_err:   0.015543
[2025-10-03 04:33:56] [Iter 1204/2250] R4[156/2400] | LR: 0.029740 | E: -27.124301 | E_var:     0.1716 | E_err:   0.006472
[2025-10-03 04:34:01] [Iter 1205/2250] R4[158/2400] | LR: 0.029734 | E: -27.136033 | E_var:     0.1518 | E_err:   0.006088
[2025-10-03 04:34:06] [Iter 1206/2250] R4[160/2400] | LR: 0.029727 | E: -27.117663 | E_var:     0.1962 | E_err:   0.006921
[2025-10-03 04:34:11] [Iter 1207/2250] R4[162/2400] | LR: 0.029720 | E: -27.136398 | E_var:     0.2593 | E_err:   0.007956
[2025-10-03 04:34:16] [Iter 1208/2250] R4[164/2400] | LR: 0.029713 | E: -27.128728 | E_var:     0.1636 | E_err:   0.006320
[2025-10-03 04:34:21] [Iter 1209/2250] R4[166/2400] | LR: 0.029706 | E: -27.123156 | E_var:     0.2514 | E_err:   0.007834
[2025-10-03 04:34:26] [Iter 1210/2250] R4[168/2400] | LR: 0.029699 | E: -27.131455 | E_var:     0.1702 | E_err:   0.006447
[2025-10-03 04:34:31] [Iter 1211/2250] R4[170/2400] | LR: 0.029692 | E: -27.148568 | E_var:     0.1901 | E_err:   0.006813
[2025-10-03 04:34:36] [Iter 1212/2250] R4[172/2400] | LR: 0.029685 | E: -27.125019 | E_var:     0.1497 | E_err:   0.006045
[2025-10-03 04:34:41] [Iter 1213/2250] R4[174/2400] | LR: 0.029677 | E: -27.131748 | E_var:     0.1622 | E_err:   0.006293
[2025-10-03 04:34:46] [Iter 1214/2250] R4[176/2400] | LR: 0.029670 | E: -27.135957 | E_var:     0.1733 | E_err:   0.006505
[2025-10-03 04:34:51] [Iter 1215/2250] R4[178/2400] | LR: 0.029662 | E: -27.123106 | E_var:     0.1659 | E_err:   0.006364
[2025-10-03 04:34:56] [Iter 1216/2250] R4[180/2400] | LR: 0.029655 | E: -27.127813 | E_var:     0.1644 | E_err:   0.006335
[2025-10-03 04:35:01] [Iter 1217/2250] R4[182/2400] | LR: 0.029647 | E: -27.127032 | E_var:     0.1668 | E_err:   0.006381
[2025-10-03 04:35:06] [Iter 1218/2250] R4[184/2400] | LR: 0.029639 | E: -27.138724 | E_var:     0.1493 | E_err:   0.006038
[2025-10-03 04:35:11] [Iter 1219/2250] R4[186/2400] | LR: 0.029631 | E: -27.137698 | E_var:     0.1678 | E_err:   0.006401
[2025-10-03 04:35:15] [Iter 1220/2250] R4[188/2400] | LR: 0.029623 | E: -27.129486 | E_var:     0.1698 | E_err:   0.006439
[2025-10-03 04:35:20] [Iter 1221/2250] R4[190/2400] | LR: 0.029615 | E: -27.130718 | E_var:     0.1582 | E_err:   0.006216
[2025-10-03 04:35:25] [Iter 1222/2250] R4[192/2400] | LR: 0.029607 | E: -27.118404 | E_var:     0.1720 | E_err:   0.006480
[2025-10-03 04:35:30] [Iter 1223/2250] R4[194/2400] | LR: 0.029599 | E: -27.148925 | E_var:     0.3681 | E_err:   0.009479
[2025-10-03 04:35:35] [Iter 1224/2250] R4[196/2400] | LR: 0.029591 | E: -27.136905 | E_var:     0.1704 | E_err:   0.006450
[2025-10-03 04:35:40] [Iter 1225/2250] R4[198/2400] | LR: 0.029583 | E: -27.136281 | E_var:     0.2277 | E_err:   0.007455
[2025-10-03 04:35:45] [Iter 1226/2250] R4[200/2400] | LR: 0.029574 | E: -27.125562 | E_var:     0.1813 | E_err:   0.006653
[2025-10-03 04:35:50] [Iter 1227/2250] R4[202/2400] | LR: 0.029566 | E: -27.114936 | E_var:     0.4051 | E_err:   0.009945
[2025-10-03 04:35:55] [Iter 1228/2250] R4[204/2400] | LR: 0.029557 | E: -27.131895 | E_var:     0.2106 | E_err:   0.007170
[2025-10-03 04:36:00] [Iter 1229/2250] R4[206/2400] | LR: 0.029548 | E: -27.135424 | E_var:     0.1520 | E_err:   0.006092
[2025-10-03 04:36:05] [Iter 1230/2250] R4[208/2400] | LR: 0.029540 | E: -27.133344 | E_var:     0.1479 | E_err:   0.006008
[2025-10-03 04:36:10] [Iter 1231/2250] R4[210/2400] | LR: 0.029531 | E: -27.131088 | E_var:     0.1496 | E_err:   0.006043
[2025-10-03 04:36:15] [Iter 1232/2250] R4[212/2400] | LR: 0.029522 | E: -27.141674 | E_var:     0.1582 | E_err:   0.006215
[2025-10-03 04:36:20] [Iter 1233/2250] R4[214/2400] | LR: 0.029513 | E: -27.148610 | E_var:     0.1368 | E_err:   0.005778
[2025-10-03 04:36:25] [Iter 1234/2250] R4[216/2400] | LR: 0.029504 | E: -27.125690 | E_var:     0.1754 | E_err:   0.006544
[2025-10-03 04:36:30] [Iter 1235/2250] R4[218/2400] | LR: 0.029494 | E: -27.129783 | E_var:     0.1395 | E_err:   0.005835
[2025-10-03 04:36:34] [Iter 1236/2250] R4[220/2400] | LR: 0.029485 | E: -27.133422 | E_var:     0.1604 | E_err:   0.006257
[2025-10-03 04:36:39] [Iter 1237/2250] R4[222/2400] | LR: 0.029476 | E: -27.124616 | E_var:     0.1489 | E_err:   0.006029
[2025-10-03 04:36:44] [Iter 1238/2250] R4[224/2400] | LR: 0.029466 | E: -27.135753 | E_var:     0.2830 | E_err:   0.008313
[2025-10-03 04:36:49] [Iter 1239/2250] R4[226/2400] | LR: 0.029457 | E: -27.116499 | E_var:     0.1497 | E_err:   0.006046
[2025-10-03 04:36:54] [Iter 1240/2250] R4[228/2400] | LR: 0.029447 | E: -27.140555 | E_var:     0.1958 | E_err:   0.006915
[2025-10-03 04:36:59] [Iter 1241/2250] R4[230/2400] | LR: 0.029438 | E: -27.128819 | E_var:     0.1372 | E_err:   0.005789
[2025-10-03 04:37:04] [Iter 1242/2250] R4[232/2400] | LR: 0.029428 | E: -27.134878 | E_var:     0.1485 | E_err:   0.006022
[2025-10-03 04:37:09] [Iter 1243/2250] R4[234/2400] | LR: 0.029418 | E: -27.129687 | E_var:     0.1656 | E_err:   0.006359
[2025-10-03 04:37:14] [Iter 1244/2250] R4[236/2400] | LR: 0.029408 | E: -27.131848 | E_var:     0.1474 | E_err:   0.005998
[2025-10-03 04:37:19] [Iter 1245/2250] R4[238/2400] | LR: 0.029398 | E: -27.127962 | E_var:     0.1601 | E_err:   0.006252
[2025-10-03 04:37:24] [Iter 1246/2250] R4[240/2400] | LR: 0.029388 | E: -27.140132 | E_var:     0.1366 | E_err:   0.005774
[2025-10-03 04:37:29] [Iter 1247/2250] R4[242/2400] | LR: 0.029378 | E: -27.132358 | E_var:     0.1865 | E_err:   0.006748
[2025-10-03 04:37:34] [Iter 1248/2250] R4[244/2400] | LR: 0.029368 | E: -27.126699 | E_var:     0.1687 | E_err:   0.006418
[2025-10-03 04:37:39] [Iter 1249/2250] R4[246/2400] | LR: 0.029358 | E: -27.122066 | E_var:     0.1981 | E_err:   0.006954
[2025-10-03 04:37:44] [Iter 1250/2250] R4[248/2400] | LR: 0.029347 | E: -27.122026 | E_var:     0.1250 | E_err:   0.005525
[2025-10-03 04:37:48] [Iter 1251/2250] R4[250/2400] | LR: 0.029337 | E: -27.131842 | E_var:     0.1420 | E_err:   0.005889
[2025-10-03 04:37:53] [Iter 1252/2250] R4[252/2400] | LR: 0.029326 | E: -27.127241 | E_var:     0.1470 | E_err:   0.005992
[2025-10-03 04:37:58] [Iter 1253/2250] R4[254/2400] | LR: 0.029315 | E: -27.125417 | E_var:     0.1707 | E_err:   0.006455
[2025-10-03 04:38:03] [Iter 1254/2250] R4[256/2400] | LR: 0.029305 | E: -27.148526 | E_var:     0.1713 | E_err:   0.006467
[2025-10-03 04:38:08] [Iter 1255/2250] R4[258/2400] | LR: 0.029294 | E: -27.140516 | E_var:     0.1547 | E_err:   0.006146
[2025-10-03 04:38:13] [Iter 1256/2250] R4[260/2400] | LR: 0.029283 | E: -27.123879 | E_var:     0.1649 | E_err:   0.006345
[2025-10-03 04:38:18] [Iter 1257/2250] R4[262/2400] | LR: 0.029272 | E: -27.122195 | E_var:     0.1561 | E_err:   0.006172
[2025-10-03 04:38:23] [Iter 1258/2250] R4[264/2400] | LR: 0.029261 | E: -27.135294 | E_var:     0.1237 | E_err:   0.005497
[2025-10-03 04:38:28] [Iter 1259/2250] R4[266/2400] | LR: 0.029250 | E: -27.137803 | E_var:     0.1377 | E_err:   0.005797
[2025-10-03 04:38:33] [Iter 1260/2250] R4[268/2400] | LR: 0.029239 | E: -27.138561 | E_var:     0.1541 | E_err:   0.006133
[2025-10-03 04:38:38] [Iter 1261/2250] R4[270/2400] | LR: 0.029227 | E: -27.131262 | E_var:     0.1466 | E_err:   0.005983
[2025-10-03 04:38:43] [Iter 1262/2250] R4[272/2400] | LR: 0.029216 | E: -27.124421 | E_var:     0.1447 | E_err:   0.005943
[2025-10-03 04:38:48] [Iter 1263/2250] R4[274/2400] | LR: 0.029205 | E: -27.137013 | E_var:     0.1728 | E_err:   0.006496
[2025-10-03 04:38:53] [Iter 1264/2250] R4[276/2400] | LR: 0.029193 | E: -27.137392 | E_var:     0.1546 | E_err:   0.006144
[2025-10-03 04:38:58] [Iter 1265/2250] R4[278/2400] | LR: 0.029181 | E: -27.128128 | E_var:     0.1591 | E_err:   0.006232
[2025-10-03 04:39:03] [Iter 1266/2250] R4[280/2400] | LR: 0.029170 | E: -27.136364 | E_var:     0.1528 | E_err:   0.006107
[2025-10-03 04:39:07] [Iter 1267/2250] R4[282/2400] | LR: 0.029158 | E: -27.133250 | E_var:     0.1503 | E_err:   0.006058
[2025-10-03 04:39:12] [Iter 1268/2250] R4[284/2400] | LR: 0.029146 | E: -27.144193 | E_var:     0.1902 | E_err:   0.006814
[2025-10-03 04:39:17] [Iter 1269/2250] R4[286/2400] | LR: 0.029134 | E: -27.134486 | E_var:     0.1982 | E_err:   0.006955
[2025-10-03 04:39:22] [Iter 1270/2250] R4[288/2400] | LR: 0.029122 | E: -27.118136 | E_var:     0.1905 | E_err:   0.006820
[2025-10-03 04:39:27] [Iter 1271/2250] R4[290/2400] | LR: 0.029110 | E: -27.125711 | E_var:     0.1523 | E_err:   0.006097
[2025-10-03 04:39:32] [Iter 1272/2250] R4[292/2400] | LR: 0.029098 | E: -27.133406 | E_var:     0.1301 | E_err:   0.005635
[2025-10-03 04:39:37] [Iter 1273/2250] R4[294/2400] | LR: 0.029086 | E: -27.134837 | E_var:     0.1673 | E_err:   0.006392
[2025-10-03 04:39:42] [Iter 1274/2250] R4[296/2400] | LR: 0.029073 | E: -27.119890 | E_var:     0.2054 | E_err:   0.007081
[2025-10-03 04:39:47] [Iter 1275/2250] R4[298/2400] | LR: 0.029061 | E: -27.128349 | E_var:     0.1448 | E_err:   0.005945
[2025-10-03 04:39:52] [Iter 1276/2250] R4[300/2400] | LR: 0.029048 | E: -27.122480 | E_var:     0.1608 | E_err:   0.006265
[2025-10-03 04:39:57] [Iter 1277/2250] R4[302/2400] | LR: 0.029036 | E: -27.110035 | E_var:     0.1974 | E_err:   0.006942
[2025-10-03 04:40:02] [Iter 1278/2250] R4[304/2400] | LR: 0.029023 | E: -27.134697 | E_var:     0.1446 | E_err:   0.005942
[2025-10-03 04:40:07] [Iter 1279/2250] R4[306/2400] | LR: 0.029011 | E: -27.135630 | E_var:     0.1319 | E_err:   0.005675
[2025-10-03 04:40:12] [Iter 1280/2250] R4[308/2400] | LR: 0.028998 | E: -27.139620 | E_var:     0.1235 | E_err:   0.005491
[2025-10-03 04:40:17] [Iter 1281/2250] R4[310/2400] | LR: 0.028985 | E: -27.134054 | E_var:     0.1569 | E_err:   0.006190
[2025-10-03 04:40:21] [Iter 1282/2250] R4[312/2400] | LR: 0.028972 | E: -27.132421 | E_var:     0.1913 | E_err:   0.006834
[2025-10-03 04:40:26] [Iter 1283/2250] R4[314/2400] | LR: 0.028959 | E: -27.129311 | E_var:     0.1663 | E_err:   0.006372
[2025-10-03 04:40:31] [Iter 1284/2250] R4[316/2400] | LR: 0.028946 | E: -27.130043 | E_var:     0.1610 | E_err:   0.006269
[2025-10-03 04:40:36] [Iter 1285/2250] R4[318/2400] | LR: 0.028933 | E: -27.122797 | E_var:     0.1836 | E_err:   0.006696
[2025-10-03 04:40:41] [Iter 1286/2250] R4[320/2400] | LR: 0.028919 | E: -27.137097 | E_var:     1.1354 | E_err:   0.016649
[2025-10-03 04:40:46] [Iter 1287/2250] R4[322/2400] | LR: 0.028906 | E: -27.127139 | E_var:     0.1730 | E_err:   0.006500
[2025-10-03 04:40:51] [Iter 1288/2250] R4[324/2400] | LR: 0.028893 | E: -27.147394 | E_var:     0.1889 | E_err:   0.006792
[2025-10-03 04:40:56] [Iter 1289/2250] R4[326/2400] | LR: 0.028879 | E: -27.132925 | E_var:     0.1440 | E_err:   0.005929
[2025-10-03 04:41:01] [Iter 1290/2250] R4[328/2400] | LR: 0.028865 | E: -27.129675 | E_var:     0.1785 | E_err:   0.006601
[2025-10-03 04:41:06] [Iter 1291/2250] R4[330/2400] | LR: 0.028852 | E: -27.130301 | E_var:     0.1399 | E_err:   0.005844
[2025-10-03 04:41:11] [Iter 1292/2250] R4[332/2400] | LR: 0.028838 | E: -27.126355 | E_var:     0.1478 | E_err:   0.006006
[2025-10-03 04:41:16] [Iter 1293/2250] R4[334/2400] | LR: 0.028824 | E: -27.136211 | E_var:     0.1199 | E_err:   0.005410
[2025-10-03 04:41:21] [Iter 1294/2250] R4[336/2400] | LR: 0.028810 | E: -27.135295 | E_var:     0.1540 | E_err:   0.006132
[2025-10-03 04:41:26] [Iter 1295/2250] R4[338/2400] | LR: 0.028796 | E: -27.134538 | E_var:     0.2319 | E_err:   0.007525
[2025-10-03 04:41:31] [Iter 1296/2250] R4[340/2400] | LR: 0.028782 | E: -27.136370 | E_var:     0.1690 | E_err:   0.006423
[2025-10-03 04:41:36] [Iter 1297/2250] R4[342/2400] | LR: 0.028768 | E: -27.131114 | E_var:     0.2310 | E_err:   0.007510
[2025-10-03 04:41:40] [Iter 1298/2250] R4[344/2400] | LR: 0.028754 | E: -27.136879 | E_var:     0.1524 | E_err:   0.006099
[2025-10-03 04:41:45] [Iter 1299/2250] R4[346/2400] | LR: 0.028740 | E: -27.130563 | E_var:     0.1318 | E_err:   0.005673
[2025-10-03 04:41:50] [Iter 1300/2250] R4[348/2400] | LR: 0.028725 | E: -27.136148 | E_var:     0.1858 | E_err:   0.006736
[2025-10-03 04:41:55] [Iter 1301/2250] R4[350/2400] | LR: 0.028711 | E: -27.140317 | E_var:     0.1585 | E_err:   0.006221
[2025-10-03 04:42:00] [Iter 1302/2250] R4[352/2400] | LR: 0.028696 | E: -27.136204 | E_var:     0.1363 | E_err:   0.005769
[2025-10-03 04:42:05] [Iter 1303/2250] R4[354/2400] | LR: 0.028682 | E: -27.135277 | E_var:     0.3130 | E_err:   0.008741
[2025-10-03 04:42:10] [Iter 1304/2250] R4[356/2400] | LR: 0.028667 | E: -27.132070 | E_var:     0.1893 | E_err:   0.006797
[2025-10-03 04:42:15] [Iter 1305/2250] R4[358/2400] | LR: 0.028652 | E: -27.135372 | E_var:     0.1594 | E_err:   0.006237
[2025-10-03 04:42:20] [Iter 1306/2250] R4[360/2400] | LR: 0.028638 | E: -27.130551 | E_var:     0.1988 | E_err:   0.006967
[2025-10-03 04:42:25] [Iter 1307/2250] R4[362/2400] | LR: 0.028623 | E: -27.136705 | E_var:     0.1526 | E_err:   0.006104
[2025-10-03 04:42:30] [Iter 1308/2250] R4[364/2400] | LR: 0.028608 | E: -27.125303 | E_var:     0.2258 | E_err:   0.007425
[2025-10-03 04:42:35] [Iter 1309/2250] R4[366/2400] | LR: 0.028593 | E: -27.121872 | E_var:     0.1557 | E_err:   0.006165
[2025-10-03 04:42:40] [Iter 1310/2250] R4[368/2400] | LR: 0.028578 | E: -27.129862 | E_var:     0.1408 | E_err:   0.005863
[2025-10-03 04:42:45] [Iter 1311/2250] R4[370/2400] | LR: 0.028562 | E: -27.129646 | E_var:     0.1669 | E_err:   0.006383
[2025-10-03 04:42:50] [Iter 1312/2250] R4[372/2400] | LR: 0.028547 | E: -27.137951 | E_var:     0.1625 | E_err:   0.006298
[2025-10-03 04:42:54] [Iter 1313/2250] R4[374/2400] | LR: 0.028532 | E: -27.140313 | E_var:     0.1779 | E_err:   0.006590
[2025-10-03 04:42:59] [Iter 1314/2250] R4[376/2400] | LR: 0.028516 | E: -27.134661 | E_var:     0.1382 | E_err:   0.005809
[2025-10-03 04:43:04] [Iter 1315/2250] R4[378/2400] | LR: 0.028501 | E: -27.127907 | E_var:     0.1732 | E_err:   0.006502
[2025-10-03 04:43:09] [Iter 1316/2250] R4[380/2400] | LR: 0.028485 | E: -27.126368 | E_var:     0.2155 | E_err:   0.007253
[2025-10-03 04:43:14] [Iter 1317/2250] R4[382/2400] | LR: 0.028470 | E: -27.132195 | E_var:     0.2016 | E_err:   0.007015
[2025-10-03 04:43:19] [Iter 1318/2250] R4[384/2400] | LR: 0.028454 | E: -27.144712 | E_var:     0.1570 | E_err:   0.006190
[2025-10-03 04:43:24] [Iter 1319/2250] R4[386/2400] | LR: 0.028438 | E: -27.126698 | E_var:     0.1575 | E_err:   0.006201
[2025-10-03 04:43:29] [Iter 1320/2250] R4[388/2400] | LR: 0.028422 | E: -27.126937 | E_var:     0.1472 | E_err:   0.005996
[2025-10-03 04:43:34] [Iter 1321/2250] R4[390/2400] | LR: 0.028406 | E: -27.133483 | E_var:     0.1962 | E_err:   0.006921
[2025-10-03 04:43:39] [Iter 1322/2250] R4[392/2400] | LR: 0.028390 | E: -27.130063 | E_var:     0.3505 | E_err:   0.009251
[2025-10-03 04:43:44] [Iter 1323/2250] R4[394/2400] | LR: 0.028374 | E: -27.132309 | E_var:     0.1655 | E_err:   0.006356
[2025-10-03 04:43:49] [Iter 1324/2250] R4[396/2400] | LR: 0.028358 | E: -27.125181 | E_var:     0.1382 | E_err:   0.005810
[2025-10-03 04:43:54] [Iter 1325/2250] R4[398/2400] | LR: 0.028342 | E: -27.134475 | E_var:     0.3787 | E_err:   0.009616
[2025-10-03 04:43:59] [Iter 1326/2250] R4[400/2400] | LR: 0.028325 | E: -27.128611 | E_var:     0.2285 | E_err:   0.007469
[2025-10-03 04:44:04] [Iter 1327/2250] R4[402/2400] | LR: 0.028309 | E: -27.137593 | E_var:     0.1491 | E_err:   0.006033
[2025-10-03 04:44:09] [Iter 1328/2250] R4[404/2400] | LR: 0.028292 | E: -27.127314 | E_var:     0.1424 | E_err:   0.005895
[2025-10-03 04:44:13] [Iter 1329/2250] R4[406/2400] | LR: 0.028276 | E: -27.137577 | E_var:     0.1522 | E_err:   0.006096
[2025-10-03 04:44:18] [Iter 1330/2250] R4[408/2400] | LR: 0.028259 | E: -27.135004 | E_var:     0.1324 | E_err:   0.005684
[2025-10-03 04:44:23] [Iter 1331/2250] R4[410/2400] | LR: 0.028243 | E: -27.138334 | E_var:     0.1649 | E_err:   0.006344
[2025-10-03 04:44:28] [Iter 1332/2250] R4[412/2400] | LR: 0.028226 | E: -27.138790 | E_var:     0.1696 | E_err:   0.006435
[2025-10-03 04:44:33] [Iter 1333/2250] R4[414/2400] | LR: 0.028209 | E: -27.132486 | E_var:     0.1580 | E_err:   0.006210
[2025-10-03 04:44:38] [Iter 1334/2250] R4[416/2400] | LR: 0.028192 | E: -27.132845 | E_var:     0.2198 | E_err:   0.007325
[2025-10-03 04:44:43] [Iter 1335/2250] R4[418/2400] | LR: 0.028175 | E: -27.145383 | E_var:     0.1818 | E_err:   0.006663
[2025-10-03 04:44:48] [Iter 1336/2250] R4[420/2400] | LR: 0.028158 | E: -27.124795 | E_var:     0.1375 | E_err:   0.005794
[2025-10-03 04:44:53] [Iter 1337/2250] R4[422/2400] | LR: 0.028141 | E: -27.135349 | E_var:     0.1595 | E_err:   0.006241
[2025-10-03 04:44:58] [Iter 1338/2250] R4[424/2400] | LR: 0.028124 | E: -27.126137 | E_var:     0.1539 | E_err:   0.006130
[2025-10-03 04:45:03] [Iter 1339/2250] R4[426/2400] | LR: 0.028106 | E: -27.135903 | E_var:     0.1505 | E_err:   0.006061
[2025-10-03 04:45:08] [Iter 1340/2250] R4[428/2400] | LR: 0.028089 | E: -27.134535 | E_var:     0.1835 | E_err:   0.006693
[2025-10-03 04:45:13] [Iter 1341/2250] R4[430/2400] | LR: 0.028072 | E: -27.144364 | E_var:     0.1605 | E_err:   0.006260
[2025-10-03 04:45:18] [Iter 1342/2250] R4[432/2400] | LR: 0.028054 | E: -27.120456 | E_var:     0.1641 | E_err:   0.006329
[2025-10-03 04:45:23] [Iter 1343/2250] R4[434/2400] | LR: 0.028037 | E: -27.130610 | E_var:     0.1654 | E_err:   0.006354
[2025-10-03 04:45:28] [Iter 1344/2250] R4[436/2400] | LR: 0.028019 | E: -27.136383 | E_var:     0.1432 | E_err:   0.005913
[2025-10-03 04:45:32] [Iter 1345/2250] R4[438/2400] | LR: 0.028001 | E: -27.135990 | E_var:     0.1474 | E_err:   0.005999
[2025-10-03 04:45:37] [Iter 1346/2250] R4[440/2400] | LR: 0.027983 | E: -27.139403 | E_var:     0.1494 | E_err:   0.006039
[2025-10-03 04:45:42] [Iter 1347/2250] R4[442/2400] | LR: 0.027966 | E: -27.135077 | E_var:     0.1462 | E_err:   0.005975
[2025-10-03 04:45:47] [Iter 1348/2250] R4[444/2400] | LR: 0.027948 | E: -27.133825 | E_var:     0.1732 | E_err:   0.006502
[2025-10-03 04:45:52] [Iter 1349/2250] R4[446/2400] | LR: 0.027930 | E: -27.123393 | E_var:     0.1701 | E_err:   0.006444
[2025-10-03 04:45:57] [Iter 1350/2250] R4[448/2400] | LR: 0.027912 | E: -27.133299 | E_var:     0.1559 | E_err:   0.006170
[2025-10-03 04:46:02] [Iter 1351/2250] R4[450/2400] | LR: 0.027893 | E: -27.135340 | E_var:     0.1641 | E_err:   0.006329
[2025-10-03 04:46:07] [Iter 1352/2250] R4[452/2400] | LR: 0.027875 | E: -27.140490 | E_var:     0.1364 | E_err:   0.005771
[2025-10-03 04:46:12] [Iter 1353/2250] R4[454/2400] | LR: 0.027857 | E: -27.140342 | E_var:     0.1662 | E_err:   0.006369
[2025-10-03 04:46:17] [Iter 1354/2250] R4[456/2400] | LR: 0.027839 | E: -27.128741 | E_var:     0.1416 | E_err:   0.005880
[2025-10-03 04:46:22] [Iter 1355/2250] R4[458/2400] | LR: 0.027820 | E: -27.125733 | E_var:     0.2228 | E_err:   0.007375
[2025-10-03 04:46:27] [Iter 1356/2250] R4[460/2400] | LR: 0.027802 | E: -27.140819 | E_var:     0.1518 | E_err:   0.006087
[2025-10-03 04:46:32] [Iter 1357/2250] R4[462/2400] | LR: 0.027783 | E: -27.145986 | E_var:     0.1479 | E_err:   0.006009
[2025-10-03 04:46:37] [Iter 1358/2250] R4[464/2400] | LR: 0.027764 | E: -27.141822 | E_var:     0.1769 | E_err:   0.006572
[2025-10-03 04:46:42] [Iter 1359/2250] R4[466/2400] | LR: 0.027746 | E: -27.136679 | E_var:     0.1411 | E_err:   0.005869
[2025-10-03 04:46:47] [Iter 1360/2250] R4[468/2400] | LR: 0.027727 | E: -27.136281 | E_var:     0.1897 | E_err:   0.006806
[2025-10-03 04:46:51] [Iter 1361/2250] R4[470/2400] | LR: 0.027708 | E: -27.132292 | E_var:     0.1292 | E_err:   0.005616
[2025-10-03 04:46:56] [Iter 1362/2250] R4[472/2400] | LR: 0.027689 | E: -27.136007 | E_var:     0.1498 | E_err:   0.006048
[2025-10-03 04:47:01] [Iter 1363/2250] R4[474/2400] | LR: 0.027670 | E: -27.135746 | E_var:     0.2142 | E_err:   0.007232
[2025-10-03 04:47:06] [Iter 1364/2250] R4[476/2400] | LR: 0.027651 | E: -27.119619 | E_var:     0.1699 | E_err:   0.006440
[2025-10-03 04:47:11] [Iter 1365/2250] R4[478/2400] | LR: 0.027632 | E: -27.141064 | E_var:     0.1819 | E_err:   0.006664
[2025-10-03 04:47:16] [Iter 1366/2250] R4[480/2400] | LR: 0.027613 | E: -27.129022 | E_var:     0.1344 | E_err:   0.005728
[2025-10-03 04:47:21] [Iter 1367/2250] R4[482/2400] | LR: 0.027593 | E: -27.133540 | E_var:     0.1964 | E_err:   0.006925
[2025-10-03 04:47:26] [Iter 1368/2250] R4[484/2400] | LR: 0.027574 | E: -27.135328 | E_var:     0.1383 | E_err:   0.005811
[2025-10-03 04:47:31] [Iter 1369/2250] R4[486/2400] | LR: 0.027555 | E: -27.137185 | E_var:     0.1806 | E_err:   0.006641
[2025-10-03 04:47:36] [Iter 1370/2250] R4[488/2400] | LR: 0.027535 | E: -27.138993 | E_var:     0.1457 | E_err:   0.005964
[2025-10-03 04:47:41] [Iter 1371/2250] R4[490/2400] | LR: 0.027516 | E: -27.141695 | E_var:     0.1337 | E_err:   0.005713
[2025-10-03 04:47:46] [Iter 1372/2250] R4[492/2400] | LR: 0.027496 | E: -27.143810 | E_var:     0.1347 | E_err:   0.005735
[2025-10-03 04:47:51] [Iter 1373/2250] R4[494/2400] | LR: 0.027476 | E: -27.137892 | E_var:     0.2010 | E_err:   0.007005
[2025-10-03 04:47:56] [Iter 1374/2250] R4[496/2400] | LR: 0.027457 | E: -27.135555 | E_var:     0.1401 | E_err:   0.005848
[2025-10-03 04:48:01] [Iter 1375/2250] R4[498/2400] | LR: 0.027437 | E: -27.123259 | E_var:     0.5212 | E_err:   0.011280
[2025-10-03 04:48:05] [Iter 1376/2250] R4[500/2400] | LR: 0.027417 | E: -27.127000 | E_var:     0.4102 | E_err:   0.010007
[2025-10-03 04:48:10] [Iter 1377/2250] R4[502/2400] | LR: 0.027397 | E: -27.126426 | E_var:     0.2997 | E_err:   0.008554
[2025-10-03 04:48:15] [Iter 1378/2250] R4[504/2400] | LR: 0.027377 | E: -27.114200 | E_var:     0.5295 | E_err:   0.011370
[2025-10-03 04:48:20] [Iter 1379/2250] R4[506/2400] | LR: 0.027357 | E: -27.130476 | E_var:     0.6492 | E_err:   0.012589
[2025-10-03 04:48:25] [Iter 1380/2250] R4[508/2400] | LR: 0.027337 | E: -27.125374 | E_var:     0.6333 | E_err:   0.012434
[2025-10-03 04:48:30] [Iter 1381/2250] R4[510/2400] | LR: 0.027316 | E: -27.120451 | E_var:     0.6002 | E_err:   0.012105
[2025-10-03 04:48:35] [Iter 1382/2250] R4[512/2400] | LR: 0.027296 | E: -27.128370 | E_var:     0.5641 | E_err:   0.011735
[2025-10-03 04:48:40] [Iter 1383/2250] R4[514/2400] | LR: 0.027276 | E: -27.116749 | E_var:     0.6441 | E_err:   0.012540
[2025-10-03 04:48:45] [Iter 1384/2250] R4[516/2400] | LR: 0.027255 | E: -27.139799 | E_var:     0.1835 | E_err:   0.006693
[2025-10-03 04:48:50] [Iter 1385/2250] R4[518/2400] | LR: 0.027235 | E: -27.128352 | E_var:     0.1416 | E_err:   0.005881
[2025-10-03 04:48:55] [Iter 1386/2250] R4[520/2400] | LR: 0.027214 | E: -27.140293 | E_var:     0.1453 | E_err:   0.005956
[2025-10-03 04:49:00] [Iter 1387/2250] R4[522/2400] | LR: 0.027194 | E: -27.142657 | E_var:     0.1623 | E_err:   0.006294
[2025-10-03 04:49:05] [Iter 1388/2250] R4[524/2400] | LR: 0.027173 | E: -27.125557 | E_var:     0.1777 | E_err:   0.006586
[2025-10-03 04:49:10] [Iter 1389/2250] R4[526/2400] | LR: 0.027152 | E: -27.135238 | E_var:     0.1432 | E_err:   0.005913
[2025-10-03 04:49:15] [Iter 1390/2250] R4[528/2400] | LR: 0.027131 | E: -27.142824 | E_var:     0.1537 | E_err:   0.006125
[2025-10-03 04:49:20] [Iter 1391/2250] R4[530/2400] | LR: 0.027111 | E: -27.129205 | E_var:     0.1888 | E_err:   0.006789
[2025-10-03 04:49:24] [Iter 1392/2250] R4[532/2400] | LR: 0.027090 | E: -27.138666 | E_var:     0.1793 | E_err:   0.006616
[2025-10-03 04:49:29] [Iter 1393/2250] R4[534/2400] | LR: 0.027069 | E: -27.139758 | E_var:     0.1393 | E_err:   0.005832
[2025-10-03 04:49:34] [Iter 1394/2250] R4[536/2400] | LR: 0.027047 | E: -27.134518 | E_var:     0.1611 | E_err:   0.006272
[2025-10-03 04:49:39] [Iter 1395/2250] R4[538/2400] | LR: 0.027026 | E: -27.132877 | E_var:     0.1510 | E_err:   0.006072
[2025-10-03 04:49:44] [Iter 1396/2250] R4[540/2400] | LR: 0.027005 | E: -27.131531 | E_var:     0.1829 | E_err:   0.006681
[2025-10-03 04:49:49] [Iter 1397/2250] R4[542/2400] | LR: 0.026984 | E: -27.140712 | E_var:     0.1906 | E_err:   0.006821
[2025-10-03 04:49:54] [Iter 1398/2250] R4[544/2400] | LR: 0.026962 | E: -27.129746 | E_var:     0.1417 | E_err:   0.005881
[2025-10-03 04:49:59] [Iter 1399/2250] R4[546/2400] | LR: 0.026941 | E: -27.133635 | E_var:     0.1409 | E_err:   0.005865
[2025-10-03 04:50:04] [Iter 1400/2250] R4[548/2400] | LR: 0.026920 | E: -27.114553 | E_var:     0.3254 | E_err:   0.008913
[2025-10-03 04:50:04] ✓ Checkpoint saved: checkpoint_iter_001400.pkl
[2025-10-03 04:50:09] [Iter 1401/2250] R4[550/2400] | LR: 0.026898 | E: -27.146026 | E_var:     0.1824 | E_err:   0.006674
[2025-10-03 04:50:14] [Iter 1402/2250] R4[552/2400] | LR: 0.026876 | E: -27.131956 | E_var:     0.1646 | E_err:   0.006339
[2025-10-03 04:50:19] [Iter 1403/2250] R4[554/2400] | LR: 0.026855 | E: -27.132532 | E_var:     0.1499 | E_err:   0.006050
[2025-10-03 04:50:24] [Iter 1404/2250] R4[556/2400] | LR: 0.026833 | E: -27.139095 | E_var:     0.2137 | E_err:   0.007223
[2025-10-03 04:50:29] [Iter 1405/2250] R4[558/2400] | LR: 0.026811 | E: -27.125403 | E_var:     0.3228 | E_err:   0.008878
[2025-10-03 04:50:34] [Iter 1406/2250] R4[560/2400] | LR: 0.026789 | E: -27.145009 | E_var:     0.1488 | E_err:   0.006028
[2025-10-03 04:50:39] [Iter 1407/2250] R4[562/2400] | LR: 0.026767 | E: -27.137089 | E_var:     0.1444 | E_err:   0.005937
[2025-10-03 04:50:43] [Iter 1408/2250] R4[564/2400] | LR: 0.026745 | E: -27.142519 | E_var:     0.2021 | E_err:   0.007025
[2025-10-03 04:50:48] [Iter 1409/2250] R4[566/2400] | LR: 0.026723 | E: -27.144321 | E_var:     0.1184 | E_err:   0.005376
[2025-10-03 04:50:53] [Iter 1410/2250] R4[568/2400] | LR: 0.026701 | E: -27.127800 | E_var:     0.1448 | E_err:   0.005946
[2025-10-03 04:50:58] [Iter 1411/2250] R4[570/2400] | LR: 0.026679 | E: -27.121519 | E_var:     0.2050 | E_err:   0.007075
[2025-10-03 04:51:03] [Iter 1412/2250] R4[572/2400] | LR: 0.026657 | E: -27.121126 | E_var:     0.2614 | E_err:   0.007989
[2025-10-03 04:51:08] [Iter 1413/2250] R4[574/2400] | LR: 0.026634 | E: -27.129522 | E_var:     0.1867 | E_err:   0.006751
[2025-10-03 04:51:13] [Iter 1414/2250] R4[576/2400] | LR: 0.026612 | E: -27.139133 | E_var:     0.1597 | E_err:   0.006245
[2025-10-03 04:51:18] [Iter 1415/2250] R4[578/2400] | LR: 0.026590 | E: -27.133615 | E_var:     0.1613 | E_err:   0.006275
[2025-10-03 04:51:23] [Iter 1416/2250] R4[580/2400] | LR: 0.026567 | E: -27.143799 | E_var:     0.2081 | E_err:   0.007127
[2025-10-03 04:51:28] [Iter 1417/2250] R4[582/2400] | LR: 0.026545 | E: -27.134777 | E_var:     0.1485 | E_err:   0.006022
[2025-10-03 04:51:33] [Iter 1418/2250] R4[584/2400] | LR: 0.026522 | E: -27.119453 | E_var:     0.1758 | E_err:   0.006551
[2025-10-03 04:51:38] [Iter 1419/2250] R4[586/2400] | LR: 0.026499 | E: -27.133018 | E_var:     0.1341 | E_err:   0.005723
[2025-10-03 04:51:43] [Iter 1420/2250] R4[588/2400] | LR: 0.026477 | E: -27.141557 | E_var:     0.1451 | E_err:   0.005951
[2025-10-03 04:51:48] [Iter 1421/2250] R4[590/2400] | LR: 0.026454 | E: -27.133419 | E_var:     0.1557 | E_err:   0.006165
[2025-10-03 04:51:53] [Iter 1422/2250] R4[592/2400] | LR: 0.026431 | E: -27.133978 | E_var:     0.1264 | E_err:   0.005556
[2025-10-03 04:51:58] [Iter 1423/2250] R4[594/2400] | LR: 0.026408 | E: -27.138063 | E_var:     0.1524 | E_err:   0.006100
[2025-10-03 04:52:02] [Iter 1424/2250] R4[596/2400] | LR: 0.026385 | E: -27.127190 | E_var:     0.1411 | E_err:   0.005869
[2025-10-03 04:52:07] [Iter 1425/2250] R4[598/2400] | LR: 0.026362 | E: -27.140984 | E_var:     0.1575 | E_err:   0.006202
[2025-10-03 04:52:12] [Iter 1426/2250] R4[600/2400] | LR: 0.026339 | E: -27.148772 | E_var:     0.1285 | E_err:   0.005602
[2025-10-03 04:52:17] [Iter 1427/2250] R4[602/2400] | LR: 0.026316 | E: -27.150302 | E_var:     0.1606 | E_err:   0.006262
[2025-10-03 04:52:22] [Iter 1428/2250] R4[604/2400] | LR: 0.026292 | E: -27.143620 | E_var:     0.1365 | E_err:   0.005773
[2025-10-03 04:52:27] [Iter 1429/2250] R4[606/2400] | LR: 0.026269 | E: -27.144705 | E_var:     0.1484 | E_err:   0.006018
[2025-10-03 04:52:32] [Iter 1430/2250] R4[608/2400] | LR: 0.026246 | E: -27.136379 | E_var:     0.1535 | E_err:   0.006122
[2025-10-03 04:52:37] [Iter 1431/2250] R4[610/2400] | LR: 0.026222 | E: -27.134978 | E_var:     0.1574 | E_err:   0.006200
[2025-10-03 04:52:42] [Iter 1432/2250] R4[612/2400] | LR: 0.026199 | E: -27.135202 | E_var:     0.1421 | E_err:   0.005890
[2025-10-03 04:52:47] [Iter 1433/2250] R4[614/2400] | LR: 0.026175 | E: -27.130318 | E_var:     0.1148 | E_err:   0.005293
[2025-10-03 04:52:52] [Iter 1434/2250] R4[616/2400] | LR: 0.026152 | E: -27.133655 | E_var:     0.1379 | E_err:   0.005803
[2025-10-03 04:52:57] [Iter 1435/2250] R4[618/2400] | LR: 0.026128 | E: -27.137327 | E_var:     0.1683 | E_err:   0.006410
[2025-10-03 04:53:02] [Iter 1436/2250] R4[620/2400] | LR: 0.026104 | E: -27.132176 | E_var:     0.1384 | E_err:   0.005812
[2025-10-03 04:53:07] [Iter 1437/2250] R4[622/2400] | LR: 0.026081 | E: -27.125078 | E_var:     0.2322 | E_err:   0.007529
[2025-10-03 04:53:12] [Iter 1438/2250] R4[624/2400] | LR: 0.026057 | E: -27.141564 | E_var:     0.1733 | E_err:   0.006505
[2025-10-03 04:53:17] [Iter 1439/2250] R4[626/2400] | LR: 0.026033 | E: -27.127421 | E_var:     0.1338 | E_err:   0.005716
[2025-10-03 04:53:21] [Iter 1440/2250] R4[628/2400] | LR: 0.026009 | E: -27.123270 | E_var:     0.1694 | E_err:   0.006431
[2025-10-03 04:53:26] [Iter 1441/2250] R4[630/2400] | LR: 0.025985 | E: -27.138344 | E_var:     0.1137 | E_err:   0.005269
[2025-10-03 04:53:31] [Iter 1442/2250] R4[632/2400] | LR: 0.025961 | E: -27.131972 | E_var:     0.1903 | E_err:   0.006816
[2025-10-03 04:53:36] [Iter 1443/2250] R4[634/2400] | LR: 0.025937 | E: -27.134782 | E_var:     0.1478 | E_err:   0.006007
[2025-10-03 04:53:41] [Iter 1444/2250] R4[636/2400] | LR: 0.025913 | E: -27.128440 | E_var:     0.1549 | E_err:   0.006149
[2025-10-03 04:53:46] [Iter 1445/2250] R4[638/2400] | LR: 0.025888 | E: -27.141562 | E_var:     0.1588 | E_err:   0.006226
[2025-10-03 04:53:51] [Iter 1446/2250] R4[640/2400] | LR: 0.025864 | E: -27.134469 | E_var:     0.1616 | E_err:   0.006281
[2025-10-03 04:53:56] [Iter 1447/2250] R4[642/2400] | LR: 0.025840 | E: -27.137062 | E_var:     0.1159 | E_err:   0.005319
[2025-10-03 04:54:01] [Iter 1448/2250] R4[644/2400] | LR: 0.025815 | E: -27.141408 | E_var:     0.1772 | E_err:   0.006577
[2025-10-03 04:54:06] [Iter 1449/2250] R4[646/2400] | LR: 0.025791 | E: -27.147423 | E_var:     0.1616 | E_err:   0.006282
[2025-10-03 04:54:11] [Iter 1450/2250] R4[648/2400] | LR: 0.025766 | E: -27.139766 | E_var:     0.1413 | E_err:   0.005873
[2025-10-03 04:54:16] [Iter 1451/2250] R4[650/2400] | LR: 0.025742 | E: -27.123326 | E_var:     0.1667 | E_err:   0.006379
[2025-10-03 04:54:21] [Iter 1452/2250] R4[652/2400] | LR: 0.025717 | E: -27.128654 | E_var:     0.1412 | E_err:   0.005872
[2025-10-03 04:54:26] [Iter 1453/2250] R4[654/2400] | LR: 0.025693 | E: -27.135445 | E_var:     0.1586 | E_err:   0.006224
[2025-10-03 04:54:31] [Iter 1454/2250] R4[656/2400] | LR: 0.025668 | E: -27.131175 | E_var:     0.1413 | E_err:   0.005873
[2025-10-03 04:54:36] [Iter 1455/2250] R4[658/2400] | LR: 0.025643 | E: -27.142367 | E_var:     0.1467 | E_err:   0.005986
[2025-10-03 04:54:40] [Iter 1456/2250] R4[660/2400] | LR: 0.025618 | E: -27.136099 | E_var:     0.1705 | E_err:   0.006453
[2025-10-03 04:54:45] [Iter 1457/2250] R4[662/2400] | LR: 0.025593 | E: -27.140273 | E_var:     0.1417 | E_err:   0.005881
[2025-10-03 04:54:50] [Iter 1458/2250] R4[664/2400] | LR: 0.025568 | E: -27.148806 | E_var:     0.1447 | E_err:   0.005944
[2025-10-03 04:54:55] [Iter 1459/2250] R4[666/2400] | LR: 0.025543 | E: -27.137539 | E_var:     0.1315 | E_err:   0.005667
[2025-10-03 04:55:00] [Iter 1460/2250] R4[668/2400] | LR: 0.025518 | E: -27.129635 | E_var:     0.1342 | E_err:   0.005723
[2025-10-03 04:55:05] [Iter 1461/2250] R4[670/2400] | LR: 0.025493 | E: -27.122357 | E_var:     0.1244 | E_err:   0.005512
[2025-10-03 04:55:10] [Iter 1462/2250] R4[672/2400] | LR: 0.025468 | E: -27.133633 | E_var:     0.1305 | E_err:   0.005644
[2025-10-03 04:55:15] [Iter 1463/2250] R4[674/2400] | LR: 0.025443 | E: -27.126495 | E_var:     0.1387 | E_err:   0.005818
[2025-10-03 04:55:20] [Iter 1464/2250] R4[676/2400] | LR: 0.025417 | E: -27.145907 | E_var:     0.2107 | E_err:   0.007173
[2025-10-03 04:55:25] [Iter 1465/2250] R4[678/2400] | LR: 0.025392 | E: -27.138691 | E_var:     0.2421 | E_err:   0.007688
[2025-10-03 04:55:30] [Iter 1466/2250] R4[680/2400] | LR: 0.025367 | E: -27.142469 | E_var:     0.1919 | E_err:   0.006845
[2025-10-03 04:55:35] [Iter 1467/2250] R4[682/2400] | LR: 0.025341 | E: -27.132553 | E_var:     0.2129 | E_err:   0.007210
[2025-10-03 04:55:40] [Iter 1468/2250] R4[684/2400] | LR: 0.025316 | E: -27.139476 | E_var:     0.1477 | E_err:   0.006005
[2025-10-03 04:55:45] [Iter 1469/2250] R4[686/2400] | LR: 0.025290 | E: -27.133210 | E_var:     0.1662 | E_err:   0.006369
[2025-10-03 04:55:50] [Iter 1470/2250] R4[688/2400] | LR: 0.025264 | E: -27.134964 | E_var:     0.1366 | E_err:   0.005774
[2025-10-03 04:55:55] [Iter 1471/2250] R4[690/2400] | LR: 0.025239 | E: -27.145886 | E_var:     0.1515 | E_err:   0.006081
[2025-10-03 04:55:59] [Iter 1472/2250] R4[692/2400] | LR: 0.025213 | E: -27.136699 | E_var:     0.1607 | E_err:   0.006263
[2025-10-03 04:56:04] [Iter 1473/2250] R4[694/2400] | LR: 0.025187 | E: -27.141345 | E_var:     0.1274 | E_err:   0.005578
[2025-10-03 04:56:09] [Iter 1474/2250] R4[696/2400] | LR: 0.025161 | E: -27.139958 | E_var:     0.1467 | E_err:   0.005985
[2025-10-03 04:56:14] [Iter 1475/2250] R4[698/2400] | LR: 0.025135 | E: -27.136564 | E_var:     0.1510 | E_err:   0.006072
[2025-10-03 04:56:19] [Iter 1476/2250] R4[700/2400] | LR: 0.025110 | E: -27.134918 | E_var:     0.1292 | E_err:   0.005616
[2025-10-03 04:56:24] [Iter 1477/2250] R4[702/2400] | LR: 0.025084 | E: -27.137994 | E_var:     0.1632 | E_err:   0.006312
[2025-10-03 04:56:29] [Iter 1478/2250] R4[704/2400] | LR: 0.025057 | E: -27.142943 | E_var:     0.1633 | E_err:   0.006314
[2025-10-03 04:56:34] [Iter 1479/2250] R4[706/2400] | LR: 0.025031 | E: -27.154372 | E_var:     0.1825 | E_err:   0.006675
[2025-10-03 04:56:39] [Iter 1480/2250] R4[708/2400] | LR: 0.025005 | E: -27.135043 | E_var:     0.1644 | E_err:   0.006336
[2025-10-03 04:56:44] [Iter 1481/2250] R4[710/2400] | LR: 0.024979 | E: -27.140115 | E_var:     0.1415 | E_err:   0.005877
[2025-10-03 04:56:49] [Iter 1482/2250] R4[712/2400] | LR: 0.024953 | E: -27.138595 | E_var:     0.1814 | E_err:   0.006656
[2025-10-03 04:56:54] [Iter 1483/2250] R4[714/2400] | LR: 0.024927 | E: -27.143957 | E_var:     0.1869 | E_err:   0.006755
[2025-10-03 04:56:59] [Iter 1484/2250] R4[716/2400] | LR: 0.024900 | E: -27.137405 | E_var:     0.1821 | E_err:   0.006668
[2025-10-03 04:57:04] [Iter 1485/2250] R4[718/2400] | LR: 0.024874 | E: -27.139782 | E_var:     0.1897 | E_err:   0.006806
[2025-10-03 04:57:09] [Iter 1486/2250] R4[720/2400] | LR: 0.024847 | E: -27.133218 | E_var:     0.1445 | E_err:   0.005940
[2025-10-03 04:57:13] [Iter 1487/2250] R4[722/2400] | LR: 0.024821 | E: -27.145254 | E_var:     0.1367 | E_err:   0.005777
[2025-10-03 04:57:18] [Iter 1488/2250] R4[724/2400] | LR: 0.024794 | E: -27.129739 | E_var:     0.1504 | E_err:   0.006060
[2025-10-03 04:57:23] [Iter 1489/2250] R4[726/2400] | LR: 0.024768 | E: -27.131477 | E_var:     0.2059 | E_err:   0.007090
[2025-10-03 04:57:28] [Iter 1490/2250] R4[728/2400] | LR: 0.024741 | E: -27.134754 | E_var:     0.1710 | E_err:   0.006461
[2025-10-03 04:57:33] [Iter 1491/2250] R4[730/2400] | LR: 0.024714 | E: -27.137177 | E_var:     0.1396 | E_err:   0.005838
[2025-10-03 04:57:38] [Iter 1492/2250] R4[732/2400] | LR: 0.024688 | E: -27.148174 | E_var:     0.1681 | E_err:   0.006405
[2025-10-03 04:57:43] [Iter 1493/2250] R4[734/2400] | LR: 0.024661 | E: -27.130010 | E_var:     0.1223 | E_err:   0.005464
[2025-10-03 04:57:48] [Iter 1494/2250] R4[736/2400] | LR: 0.024634 | E: -27.136557 | E_var:     0.2171 | E_err:   0.007281
[2025-10-03 04:57:53] [Iter 1495/2250] R4[738/2400] | LR: 0.024607 | E: -27.132009 | E_var:     0.1114 | E_err:   0.005215
[2025-10-03 04:57:58] [Iter 1496/2250] R4[740/2400] | LR: 0.024580 | E: -27.135069 | E_var:     0.1477 | E_err:   0.006004
[2025-10-03 04:58:03] [Iter 1497/2250] R4[742/2400] | LR: 0.024553 | E: -27.134532 | E_var:     0.1625 | E_err:   0.006299
[2025-10-03 04:58:08] [Iter 1498/2250] R4[744/2400] | LR: 0.024526 | E: -27.127964 | E_var:     0.1546 | E_err:   0.006143
[2025-10-03 04:58:13] [Iter 1499/2250] R4[746/2400] | LR: 0.024499 | E: -27.131221 | E_var:     0.1696 | E_err:   0.006435
[2025-10-03 04:58:18] [Iter 1500/2250] R4[748/2400] | LR: 0.024472 | E: -27.132780 | E_var:     0.1377 | E_err:   0.005798
[2025-10-03 04:58:23] [Iter 1501/2250] R4[750/2400] | LR: 0.024445 | E: -27.144675 | E_var:     0.1563 | E_err:   0.006177
[2025-10-03 04:58:28] [Iter 1502/2250] R4[752/2400] | LR: 0.024417 | E: -27.132235 | E_var:     0.1299 | E_err:   0.005632
[2025-10-03 04:58:32] [Iter 1503/2250] R4[754/2400] | LR: 0.024390 | E: -27.131289 | E_var:     0.1898 | E_err:   0.006808
[2025-10-03 04:58:37] [Iter 1504/2250] R4[756/2400] | LR: 0.024363 | E: -27.142335 | E_var:     0.1376 | E_err:   0.005796
[2025-10-03 04:58:42] [Iter 1505/2250] R4[758/2400] | LR: 0.024335 | E: -27.129185 | E_var:     0.1707 | E_err:   0.006455
[2025-10-03 04:58:47] [Iter 1506/2250] R4[760/2400] | LR: 0.024308 | E: -27.133011 | E_var:     0.2140 | E_err:   0.007228
[2025-10-03 04:58:52] [Iter 1507/2250] R4[762/2400] | LR: 0.024281 | E: -27.127471 | E_var:     0.1561 | E_err:   0.006174
[2025-10-03 04:58:57] [Iter 1508/2250] R4[764/2400] | LR: 0.024253 | E: -27.133729 | E_var:     0.1837 | E_err:   0.006697
[2025-10-03 04:59:02] [Iter 1509/2250] R4[766/2400] | LR: 0.024225 | E: -27.138570 | E_var:     0.1334 | E_err:   0.005706
[2025-10-03 04:59:07] [Iter 1510/2250] R4[768/2400] | LR: 0.024198 | E: -27.136080 | E_var:     0.1626 | E_err:   0.006300
[2025-10-03 04:59:12] [Iter 1511/2250] R4[770/2400] | LR: 0.024170 | E: -27.129746 | E_var:     0.2163 | E_err:   0.007267
[2025-10-03 04:59:17] [Iter 1512/2250] R4[772/2400] | LR: 0.024142 | E: -27.135700 | E_var:     0.1546 | E_err:   0.006143
[2025-10-03 04:59:22] [Iter 1513/2250] R4[774/2400] | LR: 0.024115 | E: -27.141437 | E_var:     0.1603 | E_err:   0.006256
[2025-10-03 04:59:27] [Iter 1514/2250] R4[776/2400] | LR: 0.024087 | E: -27.132304 | E_var:     0.1679 | E_err:   0.006402
[2025-10-03 04:59:32] [Iter 1515/2250] R4[778/2400] | LR: 0.024059 | E: -27.131705 | E_var:     0.1282 | E_err:   0.005594
[2025-10-03 04:59:37] [Iter 1516/2250] R4[780/2400] | LR: 0.024031 | E: -27.133895 | E_var:     0.1351 | E_err:   0.005743
[2025-10-03 04:59:42] [Iter 1517/2250] R4[782/2400] | LR: 0.024003 | E: -27.130094 | E_var:     0.1688 | E_err:   0.006419
[2025-10-03 04:59:47] [Iter 1518/2250] R4[784/2400] | LR: 0.023975 | E: -27.136916 | E_var:     0.2994 | E_err:   0.008549
[2025-10-03 04:59:51] [Iter 1519/2250] R4[786/2400] | LR: 0.023947 | E: -27.134271 | E_var:     0.2419 | E_err:   0.007684
[2025-10-03 04:59:56] [Iter 1520/2250] R4[788/2400] | LR: 0.023919 | E: -27.132938 | E_var:     0.1788 | E_err:   0.006607
[2025-10-03 05:00:01] [Iter 1521/2250] R4[790/2400] | LR: 0.023891 | E: -27.152335 | E_var:     0.1732 | E_err:   0.006502
[2025-10-03 05:00:06] [Iter 1522/2250] R4[792/2400] | LR: 0.023863 | E: -27.132667 | E_var:     0.1710 | E_err:   0.006461
[2025-10-03 05:00:11] [Iter 1523/2250] R4[794/2400] | LR: 0.023835 | E: -27.142964 | E_var:     0.1638 | E_err:   0.006324
[2025-10-03 05:00:16] [Iter 1524/2250] R4[796/2400] | LR: 0.023807 | E: -27.138598 | E_var:     0.1313 | E_err:   0.005662
[2025-10-03 05:00:21] [Iter 1525/2250] R4[798/2400] | LR: 0.023778 | E: -27.124811 | E_var:     0.1681 | E_err:   0.006406
[2025-10-03 05:00:26] [Iter 1526/2250] R4[800/2400] | LR: 0.023750 | E: -27.134428 | E_var:     0.2188 | E_err:   0.007308
[2025-10-03 05:00:31] [Iter 1527/2250] R4[802/2400] | LR: 0.023722 | E: -27.135118 | E_var:     0.3323 | E_err:   0.009008
[2025-10-03 05:00:36] [Iter 1528/2250] R4[804/2400] | LR: 0.023693 | E: -27.139407 | E_var:     0.2531 | E_err:   0.007861
[2025-10-03 05:00:41] [Iter 1529/2250] R4[806/2400] | LR: 0.023665 | E: -27.140891 | E_var:     0.1601 | E_err:   0.006253
[2025-10-03 05:00:46] [Iter 1530/2250] R4[808/2400] | LR: 0.023636 | E: -27.129705 | E_var:     0.1447 | E_err:   0.005944
[2025-10-03 05:00:51] [Iter 1531/2250] R4[810/2400] | LR: 0.023608 | E: -27.139295 | E_var:     0.3066 | E_err:   0.008652
[2025-10-03 05:00:56] [Iter 1532/2250] R4[812/2400] | LR: 0.023579 | E: -27.136329 | E_var:     0.1388 | E_err:   0.005821
[2025-10-03 05:01:01] [Iter 1533/2250] R4[814/2400] | LR: 0.023551 | E: -27.130585 | E_var:     0.2365 | E_err:   0.007599
[2025-10-03 05:01:06] [Iter 1534/2250] R4[816/2400] | LR: 0.023522 | E: -27.135572 | E_var:     0.2167 | E_err:   0.007273
[2025-10-03 05:01:11] [Iter 1535/2250] R4[818/2400] | LR: 0.023493 | E: -27.134564 | E_var:     0.1922 | E_err:   0.006849
[2025-10-03 05:01:15] [Iter 1536/2250] R4[820/2400] | LR: 0.023464 | E: -27.131306 | E_var:     0.1431 | E_err:   0.005911
[2025-10-03 05:01:20] [Iter 1537/2250] R4[822/2400] | LR: 0.023436 | E: -27.133257 | E_var:     0.2027 | E_err:   0.007035
[2025-10-03 05:01:25] [Iter 1538/2250] R4[824/2400] | LR: 0.023407 | E: -27.136582 | E_var:     0.1106 | E_err:   0.005195
[2025-10-03 05:01:30] [Iter 1539/2250] R4[826/2400] | LR: 0.023378 | E: -27.125106 | E_var:     0.1751 | E_err:   0.006537
[2025-10-03 05:01:35] [Iter 1540/2250] R4[828/2400] | LR: 0.023349 | E: -27.137930 | E_var:     0.1763 | E_err:   0.006561
[2025-10-03 05:01:40] [Iter 1541/2250] R4[830/2400] | LR: 0.023320 | E: -27.138324 | E_var:     0.1880 | E_err:   0.006775
[2025-10-03 05:01:45] [Iter 1542/2250] R4[832/2400] | LR: 0.023291 | E: -27.131845 | E_var:     0.1529 | E_err:   0.006110
[2025-10-03 05:01:50] [Iter 1543/2250] R4[834/2400] | LR: 0.023262 | E: -27.137253 | E_var:     0.1324 | E_err:   0.005685
[2025-10-03 05:01:55] [Iter 1544/2250] R4[836/2400] | LR: 0.023233 | E: -27.137353 | E_var:     0.2429 | E_err:   0.007700
[2025-10-03 05:02:00] [Iter 1545/2250] R4[838/2400] | LR: 0.023204 | E: -27.144580 | E_var:     0.1501 | E_err:   0.006054
[2025-10-03 05:02:05] [Iter 1546/2250] R4[840/2400] | LR: 0.023175 | E: -27.130033 | E_var:     0.2126 | E_err:   0.007204
[2025-10-03 05:02:10] [Iter 1547/2250] R4[842/2400] | LR: 0.023146 | E: -27.145704 | E_var:     0.1384 | E_err:   0.005812
[2025-10-03 05:02:15] [Iter 1548/2250] R4[844/2400] | LR: 0.023116 | E: -27.129062 | E_var:     0.1332 | E_err:   0.005703
[2025-10-03 05:02:20] [Iter 1549/2250] R4[846/2400] | LR: 0.023087 | E: -27.132138 | E_var:     0.1494 | E_err:   0.006040
[2025-10-03 05:02:25] [Iter 1550/2250] R4[848/2400] | LR: 0.023058 | E: -27.144949 | E_var:     0.1350 | E_err:   0.005740
[2025-10-03 05:02:30] [Iter 1551/2250] R4[850/2400] | LR: 0.023029 | E: -27.133648 | E_var:     0.1494 | E_err:   0.006039
[2025-10-03 05:02:34] [Iter 1552/2250] R4[852/2400] | LR: 0.022999 | E: -27.139887 | E_var:     0.1394 | E_err:   0.005834
[2025-10-03 05:02:39] [Iter 1553/2250] R4[854/2400] | LR: 0.022970 | E: -27.144947 | E_var:     0.1432 | E_err:   0.005913
[2025-10-03 05:02:44] [Iter 1554/2250] R4[856/2400] | LR: 0.022940 | E: -27.131640 | E_var:     0.1646 | E_err:   0.006339
[2025-10-03 05:02:49] [Iter 1555/2250] R4[858/2400] | LR: 0.022911 | E: -27.128681 | E_var:     0.1381 | E_err:   0.005807
[2025-10-03 05:02:54] [Iter 1556/2250] R4[860/2400] | LR: 0.022881 | E: -27.135315 | E_var:     0.1423 | E_err:   0.005894
[2025-10-03 05:02:59] [Iter 1557/2250] R4[862/2400] | LR: 0.022852 | E: -27.132059 | E_var:     0.1696 | E_err:   0.006436
[2025-10-03 05:03:04] [Iter 1558/2250] R4[864/2400] | LR: 0.022822 | E: -27.138675 | E_var:     0.1626 | E_err:   0.006300
[2025-10-03 05:03:09] [Iter 1559/2250] R4[866/2400] | LR: 0.022793 | E: -27.132650 | E_var:     0.1597 | E_err:   0.006244
[2025-10-03 05:03:14] [Iter 1560/2250] R4[868/2400] | LR: 0.022763 | E: -27.136689 | E_var:     0.1458 | E_err:   0.005967
[2025-10-03 05:03:19] [Iter 1561/2250] R4[870/2400] | LR: 0.022733 | E: -27.132942 | E_var:     0.1894 | E_err:   0.006799
[2025-10-03 05:03:24] [Iter 1562/2250] R4[872/2400] | LR: 0.022704 | E: -27.124900 | E_var:     0.1672 | E_err:   0.006389
[2025-10-03 05:03:29] [Iter 1563/2250] R4[874/2400] | LR: 0.022674 | E: -27.138697 | E_var:     0.1878 | E_err:   0.006772
[2025-10-03 05:03:34] [Iter 1564/2250] R4[876/2400] | LR: 0.022644 | E: -27.136465 | E_var:     0.3644 | E_err:   0.009432
[2025-10-03 05:03:39] [Iter 1565/2250] R4[878/2400] | LR: 0.022614 | E: -27.133962 | E_var:     0.2103 | E_err:   0.007165
[2025-10-03 05:03:44] [Iter 1566/2250] R4[880/2400] | LR: 0.022584 | E: -27.142969 | E_var:     0.1261 | E_err:   0.005547
[2025-10-03 05:03:49] [Iter 1567/2250] R4[882/2400] | LR: 0.022554 | E: -27.137615 | E_var:     0.1451 | E_err:   0.005952
[2025-10-03 05:03:53] [Iter 1568/2250] R4[884/2400] | LR: 0.022524 | E: -27.140934 | E_var:     0.1290 | E_err:   0.005613
[2025-10-03 05:03:58] [Iter 1569/2250] R4[886/2400] | LR: 0.022494 | E: -27.138665 | E_var:     0.1502 | E_err:   0.006056
[2025-10-03 05:04:03] [Iter 1570/2250] R4[888/2400] | LR: 0.022464 | E: -27.141923 | E_var:     0.1509 | E_err:   0.006070
[2025-10-03 05:04:08] [Iter 1571/2250] R4[890/2400] | LR: 0.022434 | E: -27.137430 | E_var:     0.1819 | E_err:   0.006664
[2025-10-03 05:04:13] [Iter 1572/2250] R4[892/2400] | LR: 0.022404 | E: -27.131634 | E_var:     0.1692 | E_err:   0.006427
[2025-10-03 05:04:18] [Iter 1573/2250] R4[894/2400] | LR: 0.022374 | E: -27.129627 | E_var:     0.1413 | E_err:   0.005874
[2025-10-03 05:04:23] [Iter 1574/2250] R4[896/2400] | LR: 0.022344 | E: -27.136447 | E_var:     0.1913 | E_err:   0.006834
[2025-10-03 05:04:28] [Iter 1575/2250] R4[898/2400] | LR: 0.022314 | E: -27.134124 | E_var:     0.1568 | E_err:   0.006188
[2025-10-03 05:04:33] [Iter 1576/2250] R4[900/2400] | LR: 0.022284 | E: -27.138462 | E_var:     0.1274 | E_err:   0.005577
[2025-10-03 05:04:38] [Iter 1577/2250] R4[902/2400] | LR: 0.022253 | E: -27.147811 | E_var:     0.1837 | E_err:   0.006697
[2025-10-03 05:04:43] [Iter 1578/2250] R4[904/2400] | LR: 0.022223 | E: -27.150200 | E_var:     0.1423 | E_err:   0.005895
[2025-10-03 05:04:48] [Iter 1579/2250] R4[906/2400] | LR: 0.022193 | E: -27.125069 | E_var:     0.1605 | E_err:   0.006260
[2025-10-03 05:04:53] [Iter 1580/2250] R4[908/2400] | LR: 0.022162 | E: -27.133606 | E_var:     0.1746 | E_err:   0.006529
[2025-10-03 05:04:58] [Iter 1581/2250] R4[910/2400] | LR: 0.022132 | E: -27.148801 | E_var:     0.1614 | E_err:   0.006278
[2025-10-03 05:05:03] [Iter 1582/2250] R4[912/2400] | LR: 0.022102 | E: -27.127790 | E_var:     0.1232 | E_err:   0.005485
[2025-10-03 05:05:08] [Iter 1583/2250] R4[914/2400] | LR: 0.022071 | E: -27.132653 | E_var:     0.2040 | E_err:   0.007058
[2025-10-03 05:05:12] [Iter 1584/2250] R4[916/2400] | LR: 0.022041 | E: -27.139518 | E_var:     0.1677 | E_err:   0.006399
[2025-10-03 05:05:17] [Iter 1585/2250] R4[918/2400] | LR: 0.022010 | E: -27.127323 | E_var:     0.1310 | E_err:   0.005656
[2025-10-03 05:05:22] [Iter 1586/2250] R4[920/2400] | LR: 0.021980 | E: -27.132737 | E_var:     0.2251 | E_err:   0.007413
[2025-10-03 05:05:27] [Iter 1587/2250] R4[922/2400] | LR: 0.021949 | E: -27.134682 | E_var:     0.1623 | E_err:   0.006295
[2025-10-03 05:05:32] [Iter 1588/2250] R4[924/2400] | LR: 0.021918 | E: -27.122677 | E_var:     0.1563 | E_err:   0.006177
[2025-10-03 05:05:37] [Iter 1589/2250] R4[926/2400] | LR: 0.021888 | E: -27.122941 | E_var:     0.3996 | E_err:   0.009878
[2025-10-03 05:05:42] [Iter 1590/2250] R4[928/2400] | LR: 0.021857 | E: -27.131521 | E_var:     0.1508 | E_err:   0.006068
[2025-10-03 05:05:47] [Iter 1591/2250] R4[930/2400] | LR: 0.021826 | E: -27.125661 | E_var:     0.1873 | E_err:   0.006761
[2025-10-03 05:05:52] [Iter 1592/2250] R4[932/2400] | LR: 0.021796 | E: -27.145600 | E_var:     0.1429 | E_err:   0.005907
[2025-10-03 05:05:57] [Iter 1593/2250] R4[934/2400] | LR: 0.021765 | E: -27.123639 | E_var:     0.1402 | E_err:   0.005850
[2025-10-03 05:06:02] [Iter 1594/2250] R4[936/2400] | LR: 0.021734 | E: -27.130260 | E_var:     0.1223 | E_err:   0.005465
[2025-10-03 05:06:07] [Iter 1595/2250] R4[938/2400] | LR: 0.021703 | E: -27.136939 | E_var:     0.1405 | E_err:   0.005858
[2025-10-03 05:06:12] [Iter 1596/2250] R4[940/2400] | LR: 0.021673 | E: -27.135839 | E_var:     0.1501 | E_err:   0.006054
[2025-10-03 05:06:17] [Iter 1597/2250] R4[942/2400] | LR: 0.021642 | E: -27.136166 | E_var:     0.1201 | E_err:   0.005414
[2025-10-03 05:06:22] [Iter 1598/2250] R4[944/2400] | LR: 0.021611 | E: -27.141879 | E_var:     0.1288 | E_err:   0.005607
[2025-10-03 05:06:27] [Iter 1599/2250] R4[946/2400] | LR: 0.021580 | E: -27.130362 | E_var:     0.1582 | E_err:   0.006216
[2025-10-03 05:06:31] [Iter 1600/2250] R4[948/2400] | LR: 0.021549 | E: -27.126662 | E_var:     0.1810 | E_err:   0.006648
[2025-10-03 05:06:32] ✓ Checkpoint saved: checkpoint_iter_001600.pkl
[2025-10-03 05:06:36] [Iter 1601/2250] R4[950/2400] | LR: 0.021518 | E: -27.133624 | E_var:     0.1401 | E_err:   0.005848
[2025-10-03 05:06:41] [Iter 1602/2250] R4[952/2400] | LR: 0.021487 | E: -27.133140 | E_var:     0.1497 | E_err:   0.006046
[2025-10-03 05:06:46] [Iter 1603/2250] R4[954/2400] | LR: 0.021456 | E: -27.141280 | E_var:     0.1821 | E_err:   0.006668
[2025-10-03 05:06:51] [Iter 1604/2250] R4[956/2400] | LR: 0.021425 | E: -27.140805 | E_var:     0.1577 | E_err:   0.006204
[2025-10-03 05:06:56] [Iter 1605/2250] R4[958/2400] | LR: 0.021394 | E: -27.140105 | E_var:     0.1852 | E_err:   0.006724
[2025-10-03 05:07:01] [Iter 1606/2250] R4[960/2400] | LR: 0.021363 | E: -27.129861 | E_var:     0.1455 | E_err:   0.005960
[2025-10-03 05:07:06] [Iter 1607/2250] R4[962/2400] | LR: 0.021332 | E: -27.134908 | E_var:     0.1542 | E_err:   0.006136
[2025-10-03 05:07:11] [Iter 1608/2250] R4[964/2400] | LR: 0.021300 | E: -27.138464 | E_var:     0.1927 | E_err:   0.006859
[2025-10-03 05:07:16] [Iter 1609/2250] R4[966/2400] | LR: 0.021269 | E: -27.135770 | E_var:     0.1099 | E_err:   0.005180
[2025-10-03 05:07:21] [Iter 1610/2250] R4[968/2400] | LR: 0.021238 | E: -27.137079 | E_var:     0.1725 | E_err:   0.006489
[2025-10-03 05:07:26] [Iter 1611/2250] R4[970/2400] | LR: 0.021207 | E: -27.141413 | E_var:     0.1391 | E_err:   0.005828
[2025-10-03 05:07:31] [Iter 1612/2250] R4[972/2400] | LR: 0.021176 | E: -27.137790 | E_var:     0.1274 | E_err:   0.005577
[2025-10-03 05:07:36] [Iter 1613/2250] R4[974/2400] | LR: 0.021144 | E: -27.136372 | E_var:     0.1467 | E_err:   0.005984
[2025-10-03 05:07:41] [Iter 1614/2250] R4[976/2400] | LR: 0.021113 | E: -27.138363 | E_var:     0.1644 | E_err:   0.006336
[2025-10-03 05:07:46] [Iter 1615/2250] R4[978/2400] | LR: 0.021082 | E: -27.130090 | E_var:     0.2198 | E_err:   0.007325
[2025-10-03 05:07:50] [Iter 1616/2250] R4[980/2400] | LR: 0.021050 | E: -27.127397 | E_var:     0.1344 | E_err:   0.005729
[2025-10-03 05:07:55] [Iter 1617/2250] R4[982/2400] | LR: 0.021019 | E: -27.129791 | E_var:     0.1406 | E_err:   0.005860
[2025-10-03 05:08:00] [Iter 1618/2250] R4[984/2400] | LR: 0.020987 | E: -27.142697 | E_var:     0.1368 | E_err:   0.005779
[2025-10-03 05:08:05] [Iter 1619/2250] R4[986/2400] | LR: 0.020956 | E: -27.141711 | E_var:     0.1474 | E_err:   0.005999
[2025-10-03 05:08:10] [Iter 1620/2250] R4[988/2400] | LR: 0.020924 | E: -27.133287 | E_var:     0.1308 | E_err:   0.005650
[2025-10-03 05:08:15] [Iter 1621/2250] R4[990/2400] | LR: 0.020893 | E: -27.123117 | E_var:     0.1596 | E_err:   0.006243
[2025-10-03 05:08:20] [Iter 1622/2250] R4[992/2400] | LR: 0.020861 | E: -27.132594 | E_var:     0.1941 | E_err:   0.006884
[2025-10-03 05:08:25] [Iter 1623/2250] R4[994/2400] | LR: 0.020830 | E: -27.141607 | E_var:     0.1729 | E_err:   0.006497
[2025-10-03 05:08:30] [Iter 1624/2250] R4[996/2400] | LR: 0.020798 | E: -27.137667 | E_var:     0.1457 | E_err:   0.005963
[2025-10-03 05:08:35] [Iter 1625/2250] R4[998/2400] | LR: 0.020767 | E: -27.141949 | E_var:     0.1276 | E_err:   0.005580
[2025-10-03 05:08:40] [Iter 1626/2250] R4[1000/2400] | LR: 0.020735 | E: -27.131667 | E_var:     0.1889 | E_err:   0.006791
[2025-10-03 05:08:45] [Iter 1627/2250] R4[1002/2400] | LR: 0.020704 | E: -27.132984 | E_var:     0.1322 | E_err:   0.005681
[2025-10-03 05:08:50] [Iter 1628/2250] R4[1004/2400] | LR: 0.020672 | E: -27.135335 | E_var:     0.1538 | E_err:   0.006128
[2025-10-03 05:08:55] [Iter 1629/2250] R4[1006/2400] | LR: 0.020640 | E: -27.140894 | E_var:     0.1602 | E_err:   0.006253
[2025-10-03 05:09:00] [Iter 1630/2250] R4[1008/2400] | LR: 0.020609 | E: -27.138228 | E_var:     0.1273 | E_err:   0.005575
[2025-10-03 05:09:05] [Iter 1631/2250] R4[1010/2400] | LR: 0.020577 | E: -27.135546 | E_var:     0.1217 | E_err:   0.005451
[2025-10-03 05:09:10] [Iter 1632/2250] R4[1012/2400] | LR: 0.020545 | E: -27.139220 | E_var:     0.1478 | E_err:   0.006008
[2025-10-03 05:09:14] [Iter 1633/2250] R4[1014/2400] | LR: 0.020513 | E: -27.136204 | E_var:     0.1982 | E_err:   0.006957
[2025-10-03 05:09:19] [Iter 1634/2250] R4[1016/2400] | LR: 0.020482 | E: -27.134125 | E_var:     0.1365 | E_err:   0.005772
[2025-10-03 05:09:24] [Iter 1635/2250] R4[1018/2400] | LR: 0.020450 | E: -27.132771 | E_var:     0.1468 | E_err:   0.005986
[2025-10-03 05:09:29] [Iter 1636/2250] R4[1020/2400] | LR: 0.020418 | E: -27.140198 | E_var:     0.1182 | E_err:   0.005373
[2025-10-03 05:09:34] [Iter 1637/2250] R4[1022/2400] | LR: 0.020386 | E: -27.133993 | E_var:     0.2375 | E_err:   0.007614
[2025-10-03 05:09:39] [Iter 1638/2250] R4[1024/2400] | LR: 0.020354 | E: -27.140073 | E_var:     0.3126 | E_err:   0.008735
[2025-10-03 05:09:44] [Iter 1639/2250] R4[1026/2400] | LR: 0.020323 | E: -27.143723 | E_var:     0.1171 | E_err:   0.005346
[2025-10-03 05:09:49] [Iter 1640/2250] R4[1028/2400] | LR: 0.020291 | E: -27.142967 | E_var:     0.1516 | E_err:   0.006084
[2025-10-03 05:09:54] [Iter 1641/2250] R4[1030/2400] | LR: 0.020259 | E: -27.134739 | E_var:     0.1649 | E_err:   0.006345
[2025-10-03 05:09:59] [Iter 1642/2250] R4[1032/2400] | LR: 0.020227 | E: -27.133521 | E_var:     0.1180 | E_err:   0.005366
[2025-10-03 05:10:04] [Iter 1643/2250] R4[1034/2400] | LR: 0.020195 | E: -27.136437 | E_var:     0.1345 | E_err:   0.005730
[2025-10-03 05:10:09] [Iter 1644/2250] R4[1036/2400] | LR: 0.020163 | E: -27.140512 | E_var:     0.3038 | E_err:   0.008612
[2025-10-03 05:10:14] [Iter 1645/2250] R4[1038/2400] | LR: 0.020131 | E: -27.130811 | E_var:     0.1274 | E_err:   0.005577
[2025-10-03 05:10:19] [Iter 1646/2250] R4[1040/2400] | LR: 0.020099 | E: -27.133570 | E_var:     0.1420 | E_err:   0.005887
[2025-10-03 05:10:24] [Iter 1647/2250] R4[1042/2400] | LR: 0.020067 | E: -27.144910 | E_var:     0.1514 | E_err:   0.006080
[2025-10-03 05:10:28] [Iter 1648/2250] R4[1044/2400] | LR: 0.020035 | E: -27.139062 | E_var:     1.3975 | E_err:   0.018471
[2025-10-03 05:10:33] [Iter 1649/2250] R4[1046/2400] | LR: 0.020003 | E: -27.149664 | E_var:     0.1429 | E_err:   0.005906
[2025-10-03 05:10:38] [Iter 1650/2250] R4[1048/2400] | LR: 0.019971 | E: -27.138797 | E_var:     0.1653 | E_err:   0.006354
[2025-10-03 05:10:43] [Iter 1651/2250] R4[1050/2400] | LR: 0.019939 | E: -27.129347 | E_var:     0.1702 | E_err:   0.006445
[2025-10-03 05:10:48] [Iter 1652/2250] R4[1052/2400] | LR: 0.019907 | E: -27.134078 | E_var:     0.1520 | E_err:   0.006092
[2025-10-03 05:10:53] [Iter 1653/2250] R4[1054/2400] | LR: 0.019874 | E: -27.131212 | E_var:     0.1523 | E_err:   0.006097
[2025-10-03 05:10:58] [Iter 1654/2250] R4[1056/2400] | LR: 0.019842 | E: -27.118322 | E_var:     0.2091 | E_err:   0.007144
[2025-10-03 05:11:03] [Iter 1655/2250] R4[1058/2400] | LR: 0.019810 | E: -27.148164 | E_var:     0.1462 | E_err:   0.005975
[2025-10-03 05:11:08] [Iter 1656/2250] R4[1060/2400] | LR: 0.019778 | E: -27.137895 | E_var:     0.1149 | E_err:   0.005297
[2025-10-03 05:11:13] [Iter 1657/2250] R4[1062/2400] | LR: 0.019746 | E: -27.138289 | E_var:     0.1153 | E_err:   0.005305
[2025-10-03 05:11:18] [Iter 1658/2250] R4[1064/2400] | LR: 0.019714 | E: -27.135084 | E_var:     0.1152 | E_err:   0.005304
[2025-10-03 05:11:23] [Iter 1659/2250] R4[1066/2400] | LR: 0.019681 | E: -27.142673 | E_var:     0.1810 | E_err:   0.006648
[2025-10-03 05:11:28] [Iter 1660/2250] R4[1068/2400] | LR: 0.019649 | E: -27.141190 | E_var:     0.1782 | E_err:   0.006597
[2025-10-03 05:11:33] [Iter 1661/2250] R4[1070/2400] | LR: 0.019617 | E: -27.133180 | E_var:     0.1366 | E_err:   0.005775
[2025-10-03 05:11:38] [Iter 1662/2250] R4[1072/2400] | LR: 0.019585 | E: -27.136372 | E_var:     0.1398 | E_err:   0.005843
[2025-10-03 05:11:42] [Iter 1663/2250] R4[1074/2400] | LR: 0.019552 | E: -27.128568 | E_var:     0.1642 | E_err:   0.006332
[2025-10-03 05:11:47] [Iter 1664/2250] R4[1076/2400] | LR: 0.019520 | E: -27.135087 | E_var:     0.1165 | E_err:   0.005333
[2025-10-03 05:11:52] [Iter 1665/2250] R4[1078/2400] | LR: 0.019488 | E: -27.144289 | E_var:     0.1158 | E_err:   0.005317
[2025-10-03 05:11:57] [Iter 1666/2250] R4[1080/2400] | LR: 0.019455 | E: -27.139875 | E_var:     0.1495 | E_err:   0.006042
[2025-10-03 05:12:02] [Iter 1667/2250] R4[1082/2400] | LR: 0.019423 | E: -27.124481 | E_var:     0.2385 | E_err:   0.007631
[2025-10-03 05:12:07] [Iter 1668/2250] R4[1084/2400] | LR: 0.019391 | E: -27.145566 | E_var:     0.1294 | E_err:   0.005620
[2025-10-03 05:12:12] [Iter 1669/2250] R4[1086/2400] | LR: 0.019358 | E: -27.143691 | E_var:     0.1592 | E_err:   0.006235
[2025-10-03 05:12:17] [Iter 1670/2250] R4[1088/2400] | LR: 0.019326 | E: -27.136645 | E_var:     0.1311 | E_err:   0.005657
[2025-10-03 05:12:22] [Iter 1671/2250] R4[1090/2400] | LR: 0.019294 | E: -27.141861 | E_var:     0.1263 | E_err:   0.005554
[2025-10-03 05:12:27] [Iter 1672/2250] R4[1092/2400] | LR: 0.019261 | E: -27.137286 | E_var:     0.1448 | E_err:   0.005945
[2025-10-03 05:12:32] [Iter 1673/2250] R4[1094/2400] | LR: 0.019229 | E: -27.131800 | E_var:     0.1336 | E_err:   0.005710
[2025-10-03 05:12:37] [Iter 1674/2250] R4[1096/2400] | LR: 0.019196 | E: -27.127101 | E_var:     0.1365 | E_err:   0.005773
[2025-10-03 05:12:42] [Iter 1675/2250] R4[1098/2400] | LR: 0.019164 | E: -27.132270 | E_var:     0.1592 | E_err:   0.006235
[2025-10-03 05:12:47] [Iter 1676/2250] R4[1100/2400] | LR: 0.019132 | E: -27.136218 | E_var:     0.1589 | E_err:   0.006229
[2025-10-03 05:12:52] [Iter 1677/2250] R4[1102/2400] | LR: 0.019099 | E: -27.132000 | E_var:     0.1609 | E_err:   0.006268
[2025-10-03 05:12:57] [Iter 1678/2250] R4[1104/2400] | LR: 0.019067 | E: -27.136831 | E_var:     0.1966 | E_err:   0.006928
[2025-10-03 05:13:01] [Iter 1679/2250] R4[1106/2400] | LR: 0.019034 | E: -27.138973 | E_var:     0.1453 | E_err:   0.005957
[2025-10-03 05:13:06] [Iter 1680/2250] R4[1108/2400] | LR: 0.019002 | E: -27.136815 | E_var:     0.1336 | E_err:   0.005711
[2025-10-03 05:13:11] [Iter 1681/2250] R4[1110/2400] | LR: 0.018969 | E: -27.136257 | E_var:     0.1954 | E_err:   0.006907
[2025-10-03 05:13:16] [Iter 1682/2250] R4[1112/2400] | LR: 0.018937 | E: -27.142139 | E_var:     0.1270 | E_err:   0.005569
[2025-10-03 05:13:21] [Iter 1683/2250] R4[1114/2400] | LR: 0.018904 | E: -27.133224 | E_var:     0.1628 | E_err:   0.006305
[2025-10-03 05:13:26] [Iter 1684/2250] R4[1116/2400] | LR: 0.018872 | E: -27.137915 | E_var:     0.1498 | E_err:   0.006047
[2025-10-03 05:13:31] [Iter 1685/2250] R4[1118/2400] | LR: 0.018839 | E: -27.131590 | E_var:     0.1395 | E_err:   0.005835
[2025-10-03 05:13:36] [Iter 1686/2250] R4[1120/2400] | LR: 0.018807 | E: -27.131003 | E_var:     0.1206 | E_err:   0.005426
[2025-10-03 05:13:41] [Iter 1687/2250] R4[1122/2400] | LR: 0.018774 | E: -27.141524 | E_var:     0.1627 | E_err:   0.006302
[2025-10-03 05:13:46] [Iter 1688/2250] R4[1124/2400] | LR: 0.018741 | E: -27.136224 | E_var:     0.1333 | E_err:   0.005706
[2025-10-03 05:13:51] [Iter 1689/2250] R4[1126/2400] | LR: 0.018709 | E: -27.140891 | E_var:     0.1366 | E_err:   0.005775
[2025-10-03 05:13:56] [Iter 1690/2250] R4[1128/2400] | LR: 0.018676 | E: -27.133569 | E_var:     0.1308 | E_err:   0.005650
[2025-10-03 05:14:01] [Iter 1691/2250] R4[1130/2400] | LR: 0.018644 | E: -27.135955 | E_var:     0.1409 | E_err:   0.005866
[2025-10-03 05:14:06] [Iter 1692/2250] R4[1132/2400] | LR: 0.018611 | E: -27.128440 | E_var:     0.1734 | E_err:   0.006506
[2025-10-03 05:14:11] [Iter 1693/2250] R4[1134/2400] | LR: 0.018579 | E: -27.141899 | E_var:     0.1311 | E_err:   0.005658
[2025-10-03 05:14:16] [Iter 1694/2250] R4[1136/2400] | LR: 0.018546 | E: -27.134225 | E_var:     0.1786 | E_err:   0.006604
[2025-10-03 05:14:20] [Iter 1695/2250] R4[1138/2400] | LR: 0.018513 | E: -27.120818 | E_var:     0.1907 | E_err:   0.006824
[2025-10-03 05:14:25] [Iter 1696/2250] R4[1140/2400] | LR: 0.018481 | E: -27.132264 | E_var:     0.1573 | E_err:   0.006198
[2025-10-03 05:14:30] [Iter 1697/2250] R4[1142/2400] | LR: 0.018448 | E: -27.136500 | E_var:     0.1292 | E_err:   0.005616
[2025-10-03 05:14:35] [Iter 1698/2250] R4[1144/2400] | LR: 0.018415 | E: -27.131322 | E_var:     0.1470 | E_err:   0.005991
[2025-10-03 05:14:40] [Iter 1699/2250] R4[1146/2400] | LR: 0.018383 | E: -27.131256 | E_var:     0.2560 | E_err:   0.007906
[2025-10-03 05:14:45] [Iter 1700/2250] R4[1148/2400] | LR: 0.018350 | E: -27.150852 | E_var:     0.1541 | E_err:   0.006133
[2025-10-03 05:14:50] [Iter 1701/2250] R4[1150/2400] | LR: 0.018318 | E: -27.137295 | E_var:     0.1347 | E_err:   0.005734
[2025-10-03 05:14:55] [Iter 1702/2250] R4[1152/2400] | LR: 0.018285 | E: -27.129933 | E_var:     0.1363 | E_err:   0.005768
[2025-10-03 05:15:00] [Iter 1703/2250] R4[1154/2400] | LR: 0.018252 | E: -27.137591 | E_var:     0.1362 | E_err:   0.005766
[2025-10-03 05:15:05] [Iter 1704/2250] R4[1156/2400] | LR: 0.018220 | E: -27.142512 | E_var:     0.1448 | E_err:   0.005947
[2025-10-03 05:15:10] [Iter 1705/2250] R4[1158/2400] | LR: 0.018187 | E: -27.135972 | E_var:     0.1922 | E_err:   0.006851
[2025-10-03 05:15:15] [Iter 1706/2250] R4[1160/2400] | LR: 0.018154 | E: -27.135907 | E_var:     0.1412 | E_err:   0.005872
[2025-10-03 05:15:20] [Iter 1707/2250] R4[1162/2400] | LR: 0.018122 | E: -27.134502 | E_var:     0.2357 | E_err:   0.007585
[2025-10-03 05:15:25] [Iter 1708/2250] R4[1164/2400] | LR: 0.018089 | E: -27.130951 | E_var:     0.1513 | E_err:   0.006077
[2025-10-03 05:15:30] [Iter 1709/2250] R4[1166/2400] | LR: 0.018056 | E: -27.144625 | E_var:     0.1393 | E_err:   0.005832
[2025-10-03 05:15:35] [Iter 1710/2250] R4[1168/2400] | LR: 0.018023 | E: -27.138171 | E_var:     0.1326 | E_err:   0.005689
[2025-10-03 05:15:39] [Iter 1711/2250] R4[1170/2400] | LR: 0.017991 | E: -27.141993 | E_var:     0.1605 | E_err:   0.006261
[2025-10-03 05:15:44] [Iter 1712/2250] R4[1172/2400] | LR: 0.017958 | E: -27.136091 | E_var:     0.1628 | E_err:   0.006305
[2025-10-03 05:15:49] [Iter 1713/2250] R4[1174/2400] | LR: 0.017925 | E: -27.135644 | E_var:     0.1511 | E_err:   0.006074
[2025-10-03 05:15:54] [Iter 1714/2250] R4[1176/2400] | LR: 0.017893 | E: -27.133685 | E_var:     0.1273 | E_err:   0.005574
[2025-10-03 05:15:59] [Iter 1715/2250] R4[1178/2400] | LR: 0.017860 | E: -27.139697 | E_var:     0.1354 | E_err:   0.005749
[2025-10-03 05:16:04] [Iter 1716/2250] R4[1180/2400] | LR: 0.017827 | E: -27.139479 | E_var:     0.1048 | E_err:   0.005058
[2025-10-03 05:16:09] [Iter 1717/2250] R4[1182/2400] | LR: 0.017794 | E: -27.130506 | E_var:     0.1163 | E_err:   0.005328
[2025-10-03 05:16:14] [Iter 1718/2250] R4[1184/2400] | LR: 0.017762 | E: -27.142391 | E_var:     0.1848 | E_err:   0.006717
[2025-10-03 05:16:19] [Iter 1719/2250] R4[1186/2400] | LR: 0.017729 | E: -27.134334 | E_var:     0.1696 | E_err:   0.006436
[2025-10-03 05:16:24] [Iter 1720/2250] R4[1188/2400] | LR: 0.017696 | E: -27.124990 | E_var:     0.2125 | E_err:   0.007203
[2025-10-03 05:16:29] [Iter 1721/2250] R4[1190/2400] | LR: 0.017664 | E: -27.134230 | E_var:     0.1434 | E_err:   0.005916
[2025-10-03 05:16:34] [Iter 1722/2250] R4[1192/2400] | LR: 0.017631 | E: -27.139028 | E_var:     0.1184 | E_err:   0.005377
[2025-10-03 05:16:39] [Iter 1723/2250] R4[1194/2400] | LR: 0.017598 | E: -27.131968 | E_var:     0.1545 | E_err:   0.006143
[2025-10-03 05:16:44] [Iter 1724/2250] R4[1196/2400] | LR: 0.017565 | E: -27.122226 | E_var:     0.1781 | E_err:   0.006594
[2025-10-03 05:16:49] [Iter 1725/2250] R4[1198/2400] | LR: 0.017533 | E: -27.139026 | E_var:     0.1800 | E_err:   0.006630
[2025-10-03 05:16:54] [Iter 1726/2250] R4[1200/2400] | LR: 0.017500 | E: -27.131314 | E_var:     0.1349 | E_err:   0.005738
[2025-10-03 05:16:58] [Iter 1727/2250] R4[1202/2400] | LR: 0.017467 | E: -27.140338 | E_var:     0.1155 | E_err:   0.005311
[2025-10-03 05:17:03] [Iter 1728/2250] R4[1204/2400] | LR: 0.017435 | E: -27.139311 | E_var:     0.1233 | E_err:   0.005487
[2025-10-03 05:17:08] [Iter 1729/2250] R4[1206/2400] | LR: 0.017402 | E: -27.139171 | E_var:     0.1171 | E_err:   0.005347
[2025-10-03 05:17:13] [Iter 1730/2250] R4[1208/2400] | LR: 0.017369 | E: -27.140427 | E_var:     0.1498 | E_err:   0.006047
[2025-10-03 05:17:18] [Iter 1731/2250] R4[1210/2400] | LR: 0.017336 | E: -27.152955 | E_var:     0.2423 | E_err:   0.007691
[2025-10-03 05:17:23] [Iter 1732/2250] R4[1212/2400] | LR: 0.017304 | E: -27.127117 | E_var:     0.1453 | E_err:   0.005956
[2025-10-03 05:17:28] [Iter 1733/2250] R4[1214/2400] | LR: 0.017271 | E: -27.137038 | E_var:     0.1359 | E_err:   0.005759
[2025-10-03 05:17:33] [Iter 1734/2250] R4[1216/2400] | LR: 0.017238 | E: -27.145513 | E_var:     0.1973 | E_err:   0.006940
[2025-10-03 05:17:38] [Iter 1735/2250] R4[1218/2400] | LR: 0.017206 | E: -27.145632 | E_var:     0.1525 | E_err:   0.006102
[2025-10-03 05:17:43] [Iter 1736/2250] R4[1220/2400] | LR: 0.017173 | E: -27.147226 | E_var:     0.1354 | E_err:   0.005750
[2025-10-03 05:17:48] [Iter 1737/2250] R4[1222/2400] | LR: 0.017140 | E: -27.141721 | E_var:     0.2166 | E_err:   0.007273
[2025-10-03 05:17:53] [Iter 1738/2250] R4[1224/2400] | LR: 0.017107 | E: -27.140359 | E_var:     0.1162 | E_err:   0.005326
[2025-10-03 05:17:58] [Iter 1739/2250] R4[1226/2400] | LR: 0.017075 | E: -27.137330 | E_var:     0.1724 | E_err:   0.006489
[2025-10-03 05:18:03] [Iter 1740/2250] R4[1228/2400] | LR: 0.017042 | E: -27.141452 | E_var:     0.1244 | E_err:   0.005510
[2025-10-03 05:18:08] [Iter 1741/2250] R4[1230/2400] | LR: 0.017009 | E: -27.140614 | E_var:     0.2995 | E_err:   0.008552
[2025-10-03 05:18:13] [Iter 1742/2250] R4[1232/2400] | LR: 0.016977 | E: -27.140702 | E_var:     0.1337 | E_err:   0.005713
[2025-10-03 05:18:17] [Iter 1743/2250] R4[1234/2400] | LR: 0.016944 | E: -27.142463 | E_var:     0.1405 | E_err:   0.005856
[2025-10-03 05:18:22] [Iter 1744/2250] R4[1236/2400] | LR: 0.016911 | E: -27.142186 | E_var:     0.1501 | E_err:   0.006054
[2025-10-03 05:18:27] [Iter 1745/2250] R4[1238/2400] | LR: 0.016878 | E: -27.137649 | E_var:     0.2080 | E_err:   0.007127
[2025-10-03 05:18:32] [Iter 1746/2250] R4[1240/2400] | LR: 0.016846 | E: -27.134079 | E_var:     0.1749 | E_err:   0.006534
[2025-10-03 05:18:37] [Iter 1747/2250] R4[1242/2400] | LR: 0.016813 | E: -27.144431 | E_var:     0.1486 | E_err:   0.006024
[2025-10-03 05:18:42] [Iter 1748/2250] R4[1244/2400] | LR: 0.016780 | E: -27.130955 | E_var:     0.1462 | E_err:   0.005975
[2025-10-03 05:18:47] [Iter 1749/2250] R4[1246/2400] | LR: 0.016748 | E: -27.134989 | E_var:     0.1400 | E_err:   0.005846
[2025-10-03 05:18:52] [Iter 1750/2250] R4[1248/2400] | LR: 0.016715 | E: -27.141052 | E_var:     0.1453 | E_err:   0.005956
[2025-10-03 05:18:57] [Iter 1751/2250] R4[1250/2400] | LR: 0.016682 | E: -27.142116 | E_var:     0.1409 | E_err:   0.005865
[2025-10-03 05:19:02] [Iter 1752/2250] R4[1252/2400] | LR: 0.016650 | E: -27.132921 | E_var:     0.1637 | E_err:   0.006321
[2025-10-03 05:19:07] [Iter 1753/2250] R4[1254/2400] | LR: 0.016617 | E: -27.145001 | E_var:     0.1310 | E_err:   0.005656
[2025-10-03 05:19:12] [Iter 1754/2250] R4[1256/2400] | LR: 0.016585 | E: -27.140102 | E_var:     0.1626 | E_err:   0.006300
[2025-10-03 05:19:17] [Iter 1755/2250] R4[1258/2400] | LR: 0.016552 | E: -27.143195 | E_var:     0.1291 | E_err:   0.005614
[2025-10-03 05:19:22] [Iter 1756/2250] R4[1260/2400] | LR: 0.016519 | E: -27.145609 | E_var:     0.1526 | E_err:   0.006104
[2025-10-03 05:19:27] [Iter 1757/2250] R4[1262/2400] | LR: 0.016487 | E: -27.133177 | E_var:     0.1254 | E_err:   0.005534
[2025-10-03 05:19:32] [Iter 1758/2250] R4[1264/2400] | LR: 0.016454 | E: -27.141976 | E_var:     0.1978 | E_err:   0.006949
[2025-10-03 05:19:36] [Iter 1759/2250] R4[1266/2400] | LR: 0.016421 | E: -27.141603 | E_var:     0.1914 | E_err:   0.006835
[2025-10-03 05:19:41] [Iter 1760/2250] R4[1268/2400] | LR: 0.016389 | E: -27.133401 | E_var:     0.1957 | E_err:   0.006912
[2025-10-03 05:19:46] [Iter 1761/2250] R4[1270/2400] | LR: 0.016356 | E: -27.139824 | E_var:     0.1398 | E_err:   0.005843
[2025-10-03 05:19:51] [Iter 1762/2250] R4[1272/2400] | LR: 0.016324 | E: -27.138159 | E_var:     0.1704 | E_err:   0.006450
[2025-10-03 05:19:56] [Iter 1763/2250] R4[1274/2400] | LR: 0.016291 | E: -27.138100 | E_var:     0.2129 | E_err:   0.007210
[2025-10-03 05:20:01] [Iter 1764/2250] R4[1276/2400] | LR: 0.016259 | E: -27.141724 | E_var:     0.1339 | E_err:   0.005718
[2025-10-03 05:20:06] [Iter 1765/2250] R4[1278/2400] | LR: 0.016226 | E: -27.130845 | E_var:     0.1642 | E_err:   0.006331
[2025-10-03 05:20:11] [Iter 1766/2250] R4[1280/2400] | LR: 0.016193 | E: -27.134521 | E_var:     0.1733 | E_err:   0.006504
[2025-10-03 05:20:16] [Iter 1767/2250] R4[1282/2400] | LR: 0.016161 | E: -27.135707 | E_var:     0.1452 | E_err:   0.005953
[2025-10-03 05:20:21] [Iter 1768/2250] R4[1284/2400] | LR: 0.016128 | E: -27.135989 | E_var:     0.1623 | E_err:   0.006295
[2025-10-03 05:20:26] [Iter 1769/2250] R4[1286/2400] | LR: 0.016096 | E: -27.128500 | E_var:     0.1465 | E_err:   0.005980
[2025-10-03 05:20:31] [Iter 1770/2250] R4[1288/2400] | LR: 0.016063 | E: -27.136815 | E_var:     0.1367 | E_err:   0.005776
[2025-10-03 05:20:36] [Iter 1771/2250] R4[1290/2400] | LR: 0.016031 | E: -27.149369 | E_var:     0.2132 | E_err:   0.007214
[2025-10-03 05:20:41] [Iter 1772/2250] R4[1292/2400] | LR: 0.015998 | E: -27.139893 | E_var:     0.2465 | E_err:   0.007758
[2025-10-03 05:20:46] [Iter 1773/2250] R4[1294/2400] | LR: 0.015966 | E: -27.146094 | E_var:     0.1633 | E_err:   0.006314
[2025-10-03 05:20:51] [Iter 1774/2250] R4[1296/2400] | LR: 0.015933 | E: -27.142031 | E_var:     0.2074 | E_err:   0.007116
[2025-10-03 05:20:55] [Iter 1775/2250] R4[1298/2400] | LR: 0.015901 | E: -27.132605 | E_var:     0.1340 | E_err:   0.005720
[2025-10-03 05:21:00] [Iter 1776/2250] R4[1300/2400] | LR: 0.015868 | E: -27.147384 | E_var:     0.1376 | E_err:   0.005795
[2025-10-03 05:21:05] [Iter 1777/2250] R4[1302/2400] | LR: 0.015836 | E: -27.148269 | E_var:     0.1526 | E_err:   0.006104
[2025-10-03 05:21:10] [Iter 1778/2250] R4[1304/2400] | LR: 0.015804 | E: -27.133078 | E_var:     0.1584 | E_err:   0.006218
[2025-10-03 05:21:15] [Iter 1779/2250] R4[1306/2400] | LR: 0.015771 | E: -27.141479 | E_var:     0.1221 | E_err:   0.005460
[2025-10-03 05:21:20] [Iter 1780/2250] R4[1308/2400] | LR: 0.015739 | E: -27.136795 | E_var:     0.1515 | E_err:   0.006081
[2025-10-03 05:21:25] [Iter 1781/2250] R4[1310/2400] | LR: 0.015706 | E: -27.144511 | E_var:     0.1766 | E_err:   0.006567
[2025-10-03 05:21:30] [Iter 1782/2250] R4[1312/2400] | LR: 0.015674 | E: -27.143299 | E_var:     0.1468 | E_err:   0.005986
[2025-10-03 05:21:35] [Iter 1783/2250] R4[1314/2400] | LR: 0.015642 | E: -27.150205 | E_var:     0.1689 | E_err:   0.006421
[2025-10-03 05:21:40] [Iter 1784/2250] R4[1316/2400] | LR: 0.015609 | E: -27.132892 | E_var:     0.1487 | E_err:   0.006024
[2025-10-03 05:21:45] [Iter 1785/2250] R4[1318/2400] | LR: 0.015577 | E: -27.140275 | E_var:     0.1983 | E_err:   0.006957
[2025-10-03 05:21:50] [Iter 1786/2250] R4[1320/2400] | LR: 0.015545 | E: -27.142367 | E_var:     0.1240 | E_err:   0.005503
[2025-10-03 05:21:55] [Iter 1787/2250] R4[1322/2400] | LR: 0.015512 | E: -27.137936 | E_var:     0.1355 | E_err:   0.005751
[2025-10-03 05:22:00] [Iter 1788/2250] R4[1324/2400] | LR: 0.015480 | E: -27.133195 | E_var:     0.1703 | E_err:   0.006448
[2025-10-03 05:22:05] [Iter 1789/2250] R4[1326/2400] | LR: 0.015448 | E: -27.146671 | E_var:     0.1662 | E_err:   0.006370
[2025-10-03 05:22:10] [Iter 1790/2250] R4[1328/2400] | LR: 0.015415 | E: -27.140333 | E_var:     0.1212 | E_err:   0.005440
[2025-10-03 05:22:14] [Iter 1791/2250] R4[1330/2400] | LR: 0.015383 | E: -27.132938 | E_var:     0.1552 | E_err:   0.006155
[2025-10-03 05:22:19] [Iter 1792/2250] R4[1332/2400] | LR: 0.015351 | E: -27.145019 | E_var:     0.1275 | E_err:   0.005579
[2025-10-03 05:22:24] [Iter 1793/2250] R4[1334/2400] | LR: 0.015319 | E: -27.144265 | E_var:     0.2408 | E_err:   0.007668
[2025-10-03 05:22:29] [Iter 1794/2250] R4[1336/2400] | LR: 0.015286 | E: -27.127067 | E_var:     0.1316 | E_err:   0.005667
[2025-10-03 05:22:34] [Iter 1795/2250] R4[1338/2400] | LR: 0.015254 | E: -27.143741 | E_var:     0.1326 | E_err:   0.005690
[2025-10-03 05:22:39] [Iter 1796/2250] R4[1340/2400] | LR: 0.015222 | E: -27.144957 | E_var:     0.2784 | E_err:   0.008244
[2025-10-03 05:22:44] [Iter 1797/2250] R4[1342/2400] | LR: 0.015190 | E: -27.134924 | E_var:     0.1455 | E_err:   0.005960
[2025-10-03 05:22:49] [Iter 1798/2250] R4[1344/2400] | LR: 0.015158 | E: -27.138902 | E_var:     0.1206 | E_err:   0.005425
[2025-10-03 05:22:54] [Iter 1799/2250] R4[1346/2400] | LR: 0.015126 | E: -27.140998 | E_var:     0.1164 | E_err:   0.005331
[2025-10-03 05:22:59] [Iter 1800/2250] R4[1348/2400] | LR: 0.015093 | E: -27.136675 | E_var:     0.3810 | E_err:   0.009645
[2025-10-03 05:22:59] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-03 05:23:04] [Iter 1801/2250] R4[1350/2400] | LR: 0.015061 | E: -27.139976 | E_var:     0.1252 | E_err:   0.005529
[2025-10-03 05:23:09] [Iter 1802/2250] R4[1352/2400] | LR: 0.015029 | E: -27.147879 | E_var:     0.1604 | E_err:   0.006259
[2025-10-03 05:23:14] [Iter 1803/2250] R4[1354/2400] | LR: 0.014997 | E: -27.139638 | E_var:     0.1585 | E_err:   0.006222
[2025-10-03 05:23:19] [Iter 1804/2250] R4[1356/2400] | LR: 0.014965 | E: -27.140755 | E_var:     0.1405 | E_err:   0.005857
[2025-10-03 05:23:24] [Iter 1805/2250] R4[1358/2400] | LR: 0.014933 | E: -27.130172 | E_var:     0.1258 | E_err:   0.005542
[2025-10-03 05:23:29] [Iter 1806/2250] R4[1360/2400] | LR: 0.014901 | E: -27.147721 | E_var:     0.1242 | E_err:   0.005507
[2025-10-03 05:23:34] [Iter 1807/2250] R4[1362/2400] | LR: 0.014869 | E: -27.137392 | E_var:     0.1904 | E_err:   0.006817
[2025-10-03 05:23:38] [Iter 1808/2250] R4[1364/2400] | LR: 0.014837 | E: -27.139519 | E_var:     0.1149 | E_err:   0.005295
[2025-10-03 05:23:43] [Iter 1809/2250] R4[1366/2400] | LR: 0.014805 | E: -27.145050 | E_var:     0.1200 | E_err:   0.005413
[2025-10-03 05:23:48] [Iter 1810/2250] R4[1368/2400] | LR: 0.014773 | E: -27.138781 | E_var:     0.1212 | E_err:   0.005440
[2025-10-03 05:23:53] [Iter 1811/2250] R4[1370/2400] | LR: 0.014741 | E: -27.140020 | E_var:     0.1675 | E_err:   0.006394
[2025-10-03 05:23:58] [Iter 1812/2250] R4[1372/2400] | LR: 0.014709 | E: -27.138451 | E_var:     0.1572 | E_err:   0.006194
[2025-10-03 05:24:03] [Iter 1813/2250] R4[1374/2400] | LR: 0.014677 | E: -27.147993 | E_var:     0.1261 | E_err:   0.005549
[2025-10-03 05:24:08] [Iter 1814/2250] R4[1376/2400] | LR: 0.014646 | E: -27.145720 | E_var:     0.1194 | E_err:   0.005400
[2025-10-03 05:24:13] [Iter 1815/2250] R4[1378/2400] | LR: 0.014614 | E: -27.135898 | E_var:     0.1294 | E_err:   0.005622
[2025-10-03 05:24:18] [Iter 1816/2250] R4[1380/2400] | LR: 0.014582 | E: -27.135034 | E_var:     0.4315 | E_err:   0.010264
[2025-10-03 05:24:23] [Iter 1817/2250] R4[1382/2400] | LR: 0.014550 | E: -27.143848 | E_var:     0.1365 | E_err:   0.005772
[2025-10-03 05:24:28] [Iter 1818/2250] R4[1384/2400] | LR: 0.014518 | E: -27.152991 | E_var:     0.1356 | E_err:   0.005754
[2025-10-03 05:24:33] [Iter 1819/2250] R4[1386/2400] | LR: 0.014487 | E: -27.136465 | E_var:     0.2258 | E_err:   0.007425
[2025-10-03 05:24:38] [Iter 1820/2250] R4[1388/2400] | LR: 0.014455 | E: -27.139751 | E_var:     0.1378 | E_err:   0.005801
[2025-10-03 05:24:43] [Iter 1821/2250] R4[1390/2400] | LR: 0.014423 | E: -27.136138 | E_var:     0.1411 | E_err:   0.005870
[2025-10-03 05:24:48] [Iter 1822/2250] R4[1392/2400] | LR: 0.014391 | E: -27.132814 | E_var:     0.1415 | E_err:   0.005877
[2025-10-03 05:24:52] [Iter 1823/2250] R4[1394/2400] | LR: 0.014360 | E: -27.134100 | E_var:     0.1391 | E_err:   0.005828
[2025-10-03 05:24:57] [Iter 1824/2250] R4[1396/2400] | LR: 0.014328 | E: -27.143063 | E_var:     0.2425 | E_err:   0.007694
[2025-10-03 05:25:02] [Iter 1825/2250] R4[1398/2400] | LR: 0.014296 | E: -27.142241 | E_var:     0.1386 | E_err:   0.005818
[2025-10-03 05:25:07] [Iter 1826/2250] R4[1400/2400] | LR: 0.014265 | E: -27.139860 | E_var:     0.1348 | E_err:   0.005737
[2025-10-03 05:25:12] [Iter 1827/2250] R4[1402/2400] | LR: 0.014233 | E: -27.136601 | E_var:     0.1913 | E_err:   0.006833
[2025-10-03 05:25:17] [Iter 1828/2250] R4[1404/2400] | LR: 0.014202 | E: -27.133066 | E_var:     0.1353 | E_err:   0.005748
[2025-10-03 05:25:22] [Iter 1829/2250] R4[1406/2400] | LR: 0.014170 | E: -27.119191 | E_var:     0.3817 | E_err:   0.009653
[2025-10-03 05:25:27] [Iter 1830/2250] R4[1408/2400] | LR: 0.014139 | E: -27.124535 | E_var:     0.7427 | E_err:   0.013466
[2025-10-03 05:25:32] [Iter 1831/2250] R4[1410/2400] | LR: 0.014107 | E: -27.120383 | E_var:     1.0828 | E_err:   0.016259
[2025-10-03 05:25:37] [Iter 1832/2250] R4[1412/2400] | LR: 0.014076 | E: -27.116208 | E_var:     0.7861 | E_err:   0.013854
[2025-10-03 05:25:42] [Iter 1833/2250] R4[1414/2400] | LR: 0.014044 | E: -27.126958 | E_var:     0.9903 | E_err:   0.015549
[2025-10-03 05:25:47] [Iter 1834/2250] R4[1416/2400] | LR: 0.014013 | E: -27.113709 | E_var:     1.0769 | E_err:   0.016215
[2025-10-03 05:25:52] [Iter 1835/2250] R4[1418/2400] | LR: 0.013981 | E: -27.119874 | E_var:     0.9718 | E_err:   0.015403
[2025-10-03 05:25:57] [Iter 1836/2250] R4[1420/2400] | LR: 0.013950 | E: -27.123401 | E_var:     0.5839 | E_err:   0.011939
[2025-10-03 05:26:02] [Iter 1837/2250] R4[1422/2400] | LR: 0.013918 | E: -27.133693 | E_var:     0.6156 | E_err:   0.012260
[2025-10-03 05:26:07] [Iter 1838/2250] R4[1424/2400] | LR: 0.013887 | E: -27.133579 | E_var:     0.1670 | E_err:   0.006384
[2025-10-03 05:26:12] [Iter 1839/2250] R4[1426/2400] | LR: 0.013856 | E: -27.142780 | E_var:     0.1691 | E_err:   0.006425
[2025-10-03 05:26:16] [Iter 1840/2250] R4[1428/2400] | LR: 0.013824 | E: -27.142528 | E_var:     0.1381 | E_err:   0.005806
[2025-10-03 05:26:21] [Iter 1841/2250] R4[1430/2400] | LR: 0.013793 | E: -27.140719 | E_var:     0.1353 | E_err:   0.005747
[2025-10-03 05:26:26] [Iter 1842/2250] R4[1432/2400] | LR: 0.013762 | E: -27.138219 | E_var:     0.1250 | E_err:   0.005524
[2025-10-03 05:26:31] [Iter 1843/2250] R4[1434/2400] | LR: 0.013731 | E: -27.130792 | E_var:     0.1780 | E_err:   0.006592
[2025-10-03 05:26:36] [Iter 1844/2250] R4[1436/2400] | LR: 0.013700 | E: -27.131929 | E_var:     0.1279 | E_err:   0.005588
[2025-10-03 05:26:41] [Iter 1845/2250] R4[1438/2400] | LR: 0.013668 | E: -27.139527 | E_var:     0.1530 | E_err:   0.006112
[2025-10-03 05:26:46] [Iter 1846/2250] R4[1440/2400] | LR: 0.013637 | E: -27.149468 | E_var:     0.1535 | E_err:   0.006122
[2025-10-03 05:26:51] [Iter 1847/2250] R4[1442/2400] | LR: 0.013606 | E: -27.135372 | E_var:     0.1277 | E_err:   0.005584
[2025-10-03 05:26:56] [Iter 1848/2250] R4[1444/2400] | LR: 0.013575 | E: -27.141756 | E_var:     0.1271 | E_err:   0.005571
[2025-10-03 05:27:01] [Iter 1849/2250] R4[1446/2400] | LR: 0.013544 | E: -27.140647 | E_var:     0.1096 | E_err:   0.005172
[2025-10-03 05:27:06] [Iter 1850/2250] R4[1448/2400] | LR: 0.013513 | E: -27.132460 | E_var:     0.1652 | E_err:   0.006350
[2025-10-03 05:27:11] [Iter 1851/2250] R4[1450/2400] | LR: 0.013482 | E: -27.138464 | E_var:     0.1324 | E_err:   0.005685
[2025-10-03 05:27:16] [Iter 1852/2250] R4[1452/2400] | LR: 0.013451 | E: -27.144965 | E_var:     0.1439 | E_err:   0.005927
[2025-10-03 05:27:21] [Iter 1853/2250] R4[1454/2400] | LR: 0.013420 | E: -27.141091 | E_var:     0.1738 | E_err:   0.006514
[2025-10-03 05:27:26] [Iter 1854/2250] R4[1456/2400] | LR: 0.013389 | E: -27.140071 | E_var:     0.1887 | E_err:   0.006788
[2025-10-03 05:27:30] [Iter 1855/2250] R4[1458/2400] | LR: 0.013358 | E: -27.139719 | E_var:     0.1232 | E_err:   0.005483
[2025-10-03 05:27:35] [Iter 1856/2250] R4[1460/2400] | LR: 0.013327 | E: -27.134593 | E_var:     0.1212 | E_err:   0.005440
[2025-10-03 05:27:40] [Iter 1857/2250] R4[1462/2400] | LR: 0.013297 | E: -27.133107 | E_var:     0.1421 | E_err:   0.005891
[2025-10-03 05:27:45] [Iter 1858/2250] R4[1464/2400] | LR: 0.013266 | E: -27.141263 | E_var:     0.1257 | E_err:   0.005540
[2025-10-03 05:27:50] [Iter 1859/2250] R4[1466/2400] | LR: 0.013235 | E: -27.136009 | E_var:     0.1346 | E_err:   0.005732
[2025-10-03 05:27:55] [Iter 1860/2250] R4[1468/2400] | LR: 0.013204 | E: -27.134551 | E_var:     0.1468 | E_err:   0.005987
[2025-10-03 05:28:00] [Iter 1861/2250] R4[1470/2400] | LR: 0.013174 | E: -27.149039 | E_var:     0.1799 | E_err:   0.006627
[2025-10-03 05:28:05] [Iter 1862/2250] R4[1472/2400] | LR: 0.013143 | E: -27.138382 | E_var:     0.1445 | E_err:   0.005940
[2025-10-03 05:28:10] [Iter 1863/2250] R4[1474/2400] | LR: 0.013112 | E: -27.143904 | E_var:     0.1196 | E_err:   0.005405
[2025-10-03 05:28:15] [Iter 1864/2250] R4[1476/2400] | LR: 0.013082 | E: -27.135018 | E_var:     0.1791 | E_err:   0.006613
[2025-10-03 05:28:20] [Iter 1865/2250] R4[1478/2400] | LR: 0.013051 | E: -27.136837 | E_var:     0.2059 | E_err:   0.007090
[2025-10-03 05:28:25] [Iter 1866/2250] R4[1480/2400] | LR: 0.013020 | E: -27.141964 | E_var:     0.1225 | E_err:   0.005469
[2025-10-03 05:28:30] [Iter 1867/2250] R4[1482/2400] | LR: 0.012990 | E: -27.149287 | E_var:     0.1733 | E_err:   0.006504
[2025-10-03 05:28:35] [Iter 1868/2250] R4[1484/2400] | LR: 0.012959 | E: -27.140497 | E_var:     0.1402 | E_err:   0.005851
[2025-10-03 05:28:40] [Iter 1869/2250] R4[1486/2400] | LR: 0.012929 | E: -27.130785 | E_var:     0.1261 | E_err:   0.005549
[2025-10-03 05:28:45] [Iter 1870/2250] R4[1488/2400] | LR: 0.012898 | E: -27.137643 | E_var:     0.1314 | E_err:   0.005663
[2025-10-03 05:28:49] [Iter 1871/2250] R4[1490/2400] | LR: 0.012868 | E: -27.135557 | E_var:     0.1225 | E_err:   0.005470
[2025-10-03 05:28:54] [Iter 1872/2250] R4[1492/2400] | LR: 0.012838 | E: -27.142185 | E_var:     0.1861 | E_err:   0.006740
[2025-10-03 05:28:59] [Iter 1873/2250] R4[1494/2400] | LR: 0.012807 | E: -27.137158 | E_var:     0.1126 | E_err:   0.005243
[2025-10-03 05:29:04] [Iter 1874/2250] R4[1496/2400] | LR: 0.012777 | E: -27.139028 | E_var:     0.1241 | E_err:   0.005505
[2025-10-03 05:29:09] [Iter 1875/2250] R4[1498/2400] | LR: 0.012747 | E: -27.129475 | E_var:     0.1536 | E_err:   0.006123
[2025-10-03 05:29:14] [Iter 1876/2250] R4[1500/2400] | LR: 0.012716 | E: -27.139881 | E_var:     0.1412 | E_err:   0.005872
[2025-10-03 05:29:19] [Iter 1877/2250] R4[1502/2400] | LR: 0.012686 | E: -27.132930 | E_var:     0.1303 | E_err:   0.005639
[2025-10-03 05:29:24] [Iter 1878/2250] R4[1504/2400] | LR: 0.012656 | E: -27.140771 | E_var:     0.2132 | E_err:   0.007214
[2025-10-03 05:29:29] [Iter 1879/2250] R4[1506/2400] | LR: 0.012626 | E: -27.131519 | E_var:     0.1444 | E_err:   0.005937
[2025-10-03 05:29:34] [Iter 1880/2250] R4[1508/2400] | LR: 0.012596 | E: -27.135348 | E_var:     0.1112 | E_err:   0.005210
[2025-10-03 05:29:39] [Iter 1881/2250] R4[1510/2400] | LR: 0.012566 | E: -27.136539 | E_var:     0.1204 | E_err:   0.005422
[2025-10-03 05:29:44] [Iter 1882/2250] R4[1512/2400] | LR: 0.012536 | E: -27.130541 | E_var:     0.1255 | E_err:   0.005536
[2025-10-03 05:29:49] [Iter 1883/2250] R4[1514/2400] | LR: 0.012506 | E: -27.141209 | E_var:     0.1457 | E_err:   0.005964
[2025-10-03 05:29:54] [Iter 1884/2250] R4[1516/2400] | LR: 0.012476 | E: -27.142129 | E_var:     0.1665 | E_err:   0.006375
[2025-10-03 05:29:59] [Iter 1885/2250] R4[1518/2400] | LR: 0.012446 | E: -27.145720 | E_var:     0.1401 | E_err:   0.005848
[2025-10-03 05:30:03] [Iter 1886/2250] R4[1520/2400] | LR: 0.012416 | E: -27.139954 | E_var:     0.1441 | E_err:   0.005931
[2025-10-03 05:30:08] [Iter 1887/2250] R4[1522/2400] | LR: 0.012386 | E: -27.140557 | E_var:     0.1543 | E_err:   0.006137
[2025-10-03 05:30:13] [Iter 1888/2250] R4[1524/2400] | LR: 0.012356 | E: -27.133056 | E_var:     0.1226 | E_err:   0.005471
[2025-10-03 05:30:18] [Iter 1889/2250] R4[1526/2400] | LR: 0.012326 | E: -27.136490 | E_var:     0.1456 | E_err:   0.005962
[2025-10-03 05:30:23] [Iter 1890/2250] R4[1528/2400] | LR: 0.012296 | E: -27.143898 | E_var:     0.1477 | E_err:   0.006006
[2025-10-03 05:30:28] [Iter 1891/2250] R4[1530/2400] | LR: 0.012267 | E: -27.139930 | E_var:     0.1105 | E_err:   0.005195
[2025-10-03 05:30:33] [Iter 1892/2250] R4[1532/2400] | LR: 0.012237 | E: -27.139517 | E_var:     0.1308 | E_err:   0.005652
[2025-10-03 05:30:38] [Iter 1893/2250] R4[1534/2400] | LR: 0.012207 | E: -27.133829 | E_var:     0.1567 | E_err:   0.006184
[2025-10-03 05:30:43] [Iter 1894/2250] R4[1536/2400] | LR: 0.012178 | E: -27.142338 | E_var:     0.1263 | E_err:   0.005554
[2025-10-03 05:30:48] [Iter 1895/2250] R4[1538/2400] | LR: 0.012148 | E: -27.151368 | E_var:     0.1307 | E_err:   0.005648
[2025-10-03 05:30:53] [Iter 1896/2250] R4[1540/2400] | LR: 0.012119 | E: -27.141324 | E_var:     0.1819 | E_err:   0.006664
[2025-10-03 05:30:58] [Iter 1897/2250] R4[1542/2400] | LR: 0.012089 | E: -27.136780 | E_var:     0.1609 | E_err:   0.006267
[2025-10-03 05:31:03] [Iter 1898/2250] R4[1544/2400] | LR: 0.012060 | E: -27.131683 | E_var:     0.1607 | E_err:   0.006263
[2025-10-03 05:31:08] [Iter 1899/2250] R4[1546/2400] | LR: 0.012030 | E: -27.141868 | E_var:     0.1369 | E_err:   0.005782
[2025-10-03 05:31:13] [Iter 1900/2250] R4[1548/2400] | LR: 0.012001 | E: -27.142984 | E_var:     0.1183 | E_err:   0.005374
[2025-10-03 05:31:18] [Iter 1901/2250] R4[1550/2400] | LR: 0.011971 | E: -27.144798 | E_var:     0.1297 | E_err:   0.005628
[2025-10-03 05:31:22] [Iter 1902/2250] R4[1552/2400] | LR: 0.011942 | E: -27.146040 | E_var:     0.1261 | E_err:   0.005548
[2025-10-03 05:31:27] [Iter 1903/2250] R4[1554/2400] | LR: 0.011913 | E: -27.147164 | E_var:     0.1939 | E_err:   0.006881
[2025-10-03 05:31:32] [Iter 1904/2250] R4[1556/2400] | LR: 0.011884 | E: -27.141158 | E_var:     0.6553 | E_err:   0.012648
[2025-10-03 05:31:37] [Iter 1905/2250] R4[1558/2400] | LR: 0.011854 | E: -27.136551 | E_var:     0.1298 | E_err:   0.005630
[2025-10-03 05:31:42] [Iter 1906/2250] R4[1560/2400] | LR: 0.011825 | E: -27.137485 | E_var:     0.1296 | E_err:   0.005626
[2025-10-03 05:31:47] [Iter 1907/2250] R4[1562/2400] | LR: 0.011796 | E: -27.143960 | E_var:     0.1346 | E_err:   0.005733
[2025-10-03 05:31:52] [Iter 1908/2250] R4[1564/2400] | LR: 0.011767 | E: -27.140101 | E_var:     0.1300 | E_err:   0.005634
[2025-10-03 05:31:57] [Iter 1909/2250] R4[1566/2400] | LR: 0.011738 | E: -27.150141 | E_var:     0.1127 | E_err:   0.005246
[2025-10-03 05:32:02] [Iter 1910/2250] R4[1568/2400] | LR: 0.011709 | E: -27.128980 | E_var:     0.1831 | E_err:   0.006686
[2025-10-03 05:32:07] [Iter 1911/2250] R4[1570/2400] | LR: 0.011680 | E: -27.141758 | E_var:     0.2028 | E_err:   0.007036
[2025-10-03 05:32:12] [Iter 1912/2250] R4[1572/2400] | LR: 0.011651 | E: -27.151745 | E_var:     0.1642 | E_err:   0.006332
[2025-10-03 05:32:17] [Iter 1913/2250] R4[1574/2400] | LR: 0.011622 | E: -27.140429 | E_var:     0.1746 | E_err:   0.006529
[2025-10-03 05:32:22] [Iter 1914/2250] R4[1576/2400] | LR: 0.011593 | E: -27.139523 | E_var:     0.1471 | E_err:   0.005992
[2025-10-03 05:32:27] [Iter 1915/2250] R4[1578/2400] | LR: 0.011564 | E: -27.134936 | E_var:     0.1974 | E_err:   0.006942
[2025-10-03 05:32:32] [Iter 1916/2250] R4[1580/2400] | LR: 0.011536 | E: -27.141160 | E_var:     0.1154 | E_err:   0.005307
[2025-10-03 05:32:37] [Iter 1917/2250] R4[1582/2400] | LR: 0.011507 | E: -27.139898 | E_var:     0.1612 | E_err:   0.006274
[2025-10-03 05:32:41] [Iter 1918/2250] R4[1584/2400] | LR: 0.011478 | E: -27.139664 | E_var:     0.1392 | E_err:   0.005829
[2025-10-03 05:32:46] [Iter 1919/2250] R4[1586/2400] | LR: 0.011449 | E: -27.150439 | E_var:     0.1187 | E_err:   0.005383
[2025-10-03 05:32:51] [Iter 1920/2250] R4[1588/2400] | LR: 0.011421 | E: -27.136871 | E_var:     0.1394 | E_err:   0.005835
[2025-10-03 05:32:56] [Iter 1921/2250] R4[1590/2400] | LR: 0.011392 | E: -27.139458 | E_var:     0.1256 | E_err:   0.005537
[2025-10-03 05:33:01] [Iter 1922/2250] R4[1592/2400] | LR: 0.011364 | E: -27.146336 | E_var:     0.1628 | E_err:   0.006304
[2025-10-03 05:33:06] [Iter 1923/2250] R4[1594/2400] | LR: 0.011335 | E: -27.142682 | E_var:     0.1268 | E_err:   0.005565
[2025-10-03 05:33:11] [Iter 1924/2250] R4[1596/2400] | LR: 0.011307 | E: -27.146439 | E_var:     0.1529 | E_err:   0.006111
[2025-10-03 05:33:16] [Iter 1925/2250] R4[1598/2400] | LR: 0.011278 | E: -27.147508 | E_var:     0.1479 | E_err:   0.006009
[2025-10-03 05:33:21] [Iter 1926/2250] R4[1600/2400] | LR: 0.011250 | E: -27.140911 | E_var:     0.1639 | E_err:   0.006325
[2025-10-03 05:33:26] [Iter 1927/2250] R4[1602/2400] | LR: 0.011222 | E: -27.135741 | E_var:     0.1213 | E_err:   0.005442
[2025-10-03 05:33:31] [Iter 1928/2250] R4[1604/2400] | LR: 0.011193 | E: -27.141081 | E_var:     0.1170 | E_err:   0.005344
[2025-10-03 05:33:36] [Iter 1929/2250] R4[1606/2400] | LR: 0.011165 | E: -27.138594 | E_var:     0.1312 | E_err:   0.005660
[2025-10-03 05:33:41] [Iter 1930/2250] R4[1608/2400] | LR: 0.011137 | E: -27.141678 | E_var:     0.1486 | E_err:   0.006024
[2025-10-03 05:33:46] [Iter 1931/2250] R4[1610/2400] | LR: 0.011109 | E: -27.137236 | E_var:     0.1547 | E_err:   0.006145
[2025-10-03 05:33:51] [Iter 1932/2250] R4[1612/2400] | LR: 0.011081 | E: -27.144585 | E_var:     0.1471 | E_err:   0.005994
[2025-10-03 05:33:55] [Iter 1933/2250] R4[1614/2400] | LR: 0.011053 | E: -27.139674 | E_var:     0.1180 | E_err:   0.005366
[2025-10-03 05:34:00] [Iter 1934/2250] R4[1616/2400] | LR: 0.011025 | E: -27.139930 | E_var:     0.1231 | E_err:   0.005482
[2025-10-03 05:34:05] [Iter 1935/2250] R4[1618/2400] | LR: 0.010997 | E: -27.149203 | E_var:     0.1592 | E_err:   0.006235
[2025-10-03 05:34:10] [Iter 1936/2250] R4[1620/2400] | LR: 0.010969 | E: -27.141417 | E_var:     0.1253 | E_err:   0.005532
[2025-10-03 05:34:15] [Iter 1937/2250] R4[1622/2400] | LR: 0.010941 | E: -27.141004 | E_var:     0.1945 | E_err:   0.006892
[2025-10-03 05:34:20] [Iter 1938/2250] R4[1624/2400] | LR: 0.010913 | E: -27.140257 | E_var:     0.1302 | E_err:   0.005639
[2025-10-03 05:34:31] [Iter 1939/2250] R4[1626/2400] | LR: 0.010885 | E: -27.133542 | E_var:     0.0960 | E_err:   0.004841
[2025-10-03 05:34:36] [Iter 1940/2250] R4[1628/2400] | LR: 0.010858 | E: -27.136016 | E_var:     0.1385 | E_err:   0.005814
[2025-10-03 05:34:41] [Iter 1941/2250] R4[1630/2400] | LR: 0.010830 | E: -27.142171 | E_var:     0.1306 | E_err:   0.005647
[2025-10-03 05:34:46] [Iter 1942/2250] R4[1632/2400] | LR: 0.010802 | E: -27.134653 | E_var:     0.2347 | E_err:   0.007570
[2025-10-03 05:34:51] [Iter 1943/2250] R4[1634/2400] | LR: 0.010775 | E: -27.141190 | E_var:     0.1486 | E_err:   0.006023
[2025-10-03 05:34:56] [Iter 1944/2250] R4[1636/2400] | LR: 0.010747 | E: -27.139993 | E_var:     0.1130 | E_err:   0.005252
[2025-10-03 05:35:01] [Iter 1945/2250] R4[1638/2400] | LR: 0.010719 | E: -27.137604 | E_var:     0.1446 | E_err:   0.005942
[2025-10-03 05:35:06] [Iter 1946/2250] R4[1640/2400] | LR: 0.010692 | E: -27.144597 | E_var:     0.1211 | E_err:   0.005436
[2025-10-03 05:35:11] [Iter 1947/2250] R4[1642/2400] | LR: 0.010665 | E: -27.145130 | E_var:     0.1271 | E_err:   0.005570
[2025-10-03 05:35:16] [Iter 1948/2250] R4[1644/2400] | LR: 0.010637 | E: -27.138310 | E_var:     0.1025 | E_err:   0.005003
[2025-10-03 05:35:21] [Iter 1949/2250] R4[1646/2400] | LR: 0.010610 | E: -27.149936 | E_var:     0.1271 | E_err:   0.005570
[2025-10-03 05:35:25] [Iter 1950/2250] R4[1648/2400] | LR: 0.010583 | E: -27.145173 | E_var:     0.1136 | E_err:   0.005265
[2025-10-03 05:35:30] [Iter 1951/2250] R4[1650/2400] | LR: 0.010555 | E: -27.134781 | E_var:     0.1357 | E_err:   0.005756
[2025-10-03 05:35:35] [Iter 1952/2250] R4[1652/2400] | LR: 0.010528 | E: -27.140258 | E_var:     0.1234 | E_err:   0.005489
[2025-10-03 05:35:40] [Iter 1953/2250] R4[1654/2400] | LR: 0.010501 | E: -27.133029 | E_var:     0.2823 | E_err:   0.008302
[2025-10-03 05:35:45] [Iter 1954/2250] R4[1656/2400] | LR: 0.010474 | E: -27.141199 | E_var:     0.1541 | E_err:   0.006135
[2025-10-03 05:35:50] [Iter 1955/2250] R4[1658/2400] | LR: 0.010447 | E: -27.138175 | E_var:     0.3636 | E_err:   0.009421
[2025-10-03 05:35:55] [Iter 1956/2250] R4[1660/2400] | LR: 0.010420 | E: -27.137223 | E_var:     0.1604 | E_err:   0.006258
[2025-10-03 05:36:00] [Iter 1957/2250] R4[1662/2400] | LR: 0.010393 | E: -27.143605 | E_var:     0.1330 | E_err:   0.005699
[2025-10-03 05:36:05] [Iter 1958/2250] R4[1664/2400] | LR: 0.010366 | E: -27.141771 | E_var:     0.1249 | E_err:   0.005522
[2025-10-03 05:36:10] [Iter 1959/2250] R4[1666/2400] | LR: 0.010339 | E: -27.147694 | E_var:     0.1473 | E_err:   0.005996
[2025-10-03 05:36:15] [Iter 1960/2250] R4[1668/2400] | LR: 0.010312 | E: -27.134555 | E_var:     0.1399 | E_err:   0.005845
[2025-10-03 05:36:20] [Iter 1961/2250] R4[1670/2400] | LR: 0.010286 | E: -27.154350 | E_var:     0.2021 | E_err:   0.007024
[2025-10-03 05:36:25] [Iter 1962/2250] R4[1672/2400] | LR: 0.010259 | E: -27.134601 | E_var:     0.1767 | E_err:   0.006568
[2025-10-03 05:36:30] [Iter 1963/2250] R4[1674/2400] | LR: 0.010232 | E: -27.139916 | E_var:     0.1282 | E_err:   0.005595
[2025-10-03 05:36:35] [Iter 1964/2250] R4[1676/2400] | LR: 0.010206 | E: -27.141722 | E_var:     0.1367 | E_err:   0.005778
[2025-10-03 05:36:40] [Iter 1965/2250] R4[1678/2400] | LR: 0.010179 | E: -27.132586 | E_var:     0.1326 | E_err:   0.005689
[2025-10-03 05:36:44] [Iter 1966/2250] R4[1680/2400] | LR: 0.010153 | E: -27.141100 | E_var:     0.1389 | E_err:   0.005823
[2025-10-03 05:36:49] [Iter 1967/2250] R4[1682/2400] | LR: 0.010126 | E: -27.128048 | E_var:     0.1259 | E_err:   0.005545
[2025-10-03 05:36:54] [Iter 1968/2250] R4[1684/2400] | LR: 0.010100 | E: -27.139521 | E_var:     0.1512 | E_err:   0.006075
[2025-10-03 05:36:59] [Iter 1969/2250] R4[1686/2400] | LR: 0.010073 | E: -27.137596 | E_var:     0.1653 | E_err:   0.006353
[2025-10-03 05:37:04] [Iter 1970/2250] R4[1688/2400] | LR: 0.010047 | E: -27.137532 | E_var:     0.1484 | E_err:   0.006019
[2025-10-03 05:37:09] [Iter 1971/2250] R4[1690/2400] | LR: 0.010021 | E: -27.140459 | E_var:     0.1340 | E_err:   0.005719
[2025-10-03 05:37:14] [Iter 1972/2250] R4[1692/2400] | LR: 0.009995 | E: -27.143179 | E_var:     0.1570 | E_err:   0.006190
[2025-10-03 05:37:19] [Iter 1973/2250] R4[1694/2400] | LR: 0.009969 | E: -27.145389 | E_var:     0.1636 | E_err:   0.006320
[2025-10-03 05:37:24] [Iter 1974/2250] R4[1696/2400] | LR: 0.009943 | E: -27.142072 | E_var:     0.1527 | E_err:   0.006105
[2025-10-03 05:37:29] [Iter 1975/2250] R4[1698/2400] | LR: 0.009916 | E: -27.142042 | E_var:     0.1279 | E_err:   0.005587
[2025-10-03 05:37:34] [Iter 1976/2250] R4[1700/2400] | LR: 0.009890 | E: -27.143124 | E_var:     0.1286 | E_err:   0.005603
[2025-10-03 05:37:39] [Iter 1977/2250] R4[1702/2400] | LR: 0.009865 | E: -27.142435 | E_var:     0.1401 | E_err:   0.005848
[2025-10-03 05:37:44] [Iter 1978/2250] R4[1704/2400] | LR: 0.009839 | E: -27.138653 | E_var:     0.1560 | E_err:   0.006172
[2025-10-03 05:37:49] [Iter 1979/2250] R4[1706/2400] | LR: 0.009813 | E: -27.138060 | E_var:     0.1531 | E_err:   0.006114
[2025-10-03 05:37:54] [Iter 1980/2250] R4[1708/2400] | LR: 0.009787 | E: -27.134245 | E_var:     0.1903 | E_err:   0.006816
[2025-10-03 05:37:59] [Iter 1981/2250] R4[1710/2400] | LR: 0.009761 | E: -27.142127 | E_var:     0.1133 | E_err:   0.005259
[2025-10-03 05:38:04] [Iter 1982/2250] R4[1712/2400] | LR: 0.009736 | E: -27.140219 | E_var:     0.1604 | E_err:   0.006258
[2025-10-03 05:38:08] [Iter 1983/2250] R4[1714/2400] | LR: 0.009710 | E: -27.148451 | E_var:     0.1553 | E_err:   0.006157
[2025-10-03 05:38:13] [Iter 1984/2250] R4[1716/2400] | LR: 0.009684 | E: -27.142093 | E_var:     0.1469 | E_err:   0.005989
[2025-10-03 05:38:18] [Iter 1985/2250] R4[1718/2400] | LR: 0.009659 | E: -27.146945 | E_var:     0.1267 | E_err:   0.005561
[2025-10-03 05:38:23] [Iter 1986/2250] R4[1720/2400] | LR: 0.009633 | E: -27.142987 | E_var:     0.1717 | E_err:   0.006474
[2025-10-03 05:38:28] [Iter 1987/2250] R4[1722/2400] | LR: 0.009608 | E: -27.136711 | E_var:     0.2421 | E_err:   0.007688
[2025-10-03 05:38:33] [Iter 1988/2250] R4[1724/2400] | LR: 0.009583 | E: -27.138560 | E_var:     0.1547 | E_err:   0.006145
[2025-10-03 05:38:38] [Iter 1989/2250] R4[1726/2400] | LR: 0.009557 | E: -27.141045 | E_var:     0.1339 | E_err:   0.005718
[2025-10-03 05:38:43] [Iter 1990/2250] R4[1728/2400] | LR: 0.009532 | E: -27.129386 | E_var:     0.1281 | E_err:   0.005592
[2025-10-03 05:38:48] [Iter 1991/2250] R4[1730/2400] | LR: 0.009507 | E: -27.131912 | E_var:     0.1523 | E_err:   0.006098
[2025-10-03 05:38:53] [Iter 1992/2250] R4[1732/2400] | LR: 0.009482 | E: -27.135806 | E_var:     0.1210 | E_err:   0.005435
[2025-10-03 05:38:58] [Iter 1993/2250] R4[1734/2400] | LR: 0.009457 | E: -27.139584 | E_var:     0.2332 | E_err:   0.007546
[2025-10-03 05:39:03] [Iter 1994/2250] R4[1736/2400] | LR: 0.009432 | E: -27.138618 | E_var:     0.1381 | E_err:   0.005806
[2025-10-03 05:39:08] [Iter 1995/2250] R4[1738/2400] | LR: 0.009407 | E: -27.149426 | E_var:     0.1682 | E_err:   0.006409
[2025-10-03 05:39:13] [Iter 1996/2250] R4[1740/2400] | LR: 0.009382 | E: -27.144659 | E_var:     0.1294 | E_err:   0.005620
[2025-10-03 05:39:18] [Iter 1997/2250] R4[1742/2400] | LR: 0.009357 | E: -27.140485 | E_var:     0.1551 | E_err:   0.006154
[2025-10-03 05:39:23] [Iter 1998/2250] R4[1744/2400] | LR: 0.009332 | E: -27.143160 | E_var:     0.1422 | E_err:   0.005892
[2025-10-03 05:39:27] [Iter 1999/2250] R4[1746/2400] | LR: 0.009307 | E: -27.140269 | E_var:     0.1427 | E_err:   0.005901
[2025-10-03 05:39:32] [Iter 2000/2250] R4[1748/2400] | LR: 0.009283 | E: -27.133966 | E_var:     0.1467 | E_err:   0.005985
[2025-10-03 05:39:32] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-10-03 05:39:37] [Iter 2001/2250] R4[1750/2400] | LR: 0.009258 | E: -27.135869 | E_var:     0.1493 | E_err:   0.006036
[2025-10-03 05:39:42] [Iter 2002/2250] R4[1752/2400] | LR: 0.009234 | E: -27.140644 | E_var:     0.1147 | E_err:   0.005292
[2025-10-03 05:39:47] [Iter 2003/2250] R4[1754/2400] | LR: 0.009209 | E: -27.146237 | E_var:     0.2640 | E_err:   0.008029
[2025-10-03 05:39:52] [Iter 2004/2250] R4[1756/2400] | LR: 0.009185 | E: -27.142492 | E_var:     0.1870 | E_err:   0.006756
[2025-10-03 05:39:57] [Iter 2005/2250] R4[1758/2400] | LR: 0.009160 | E: -27.133204 | E_var:     0.1801 | E_err:   0.006630
[2025-10-03 05:40:02] [Iter 2006/2250] R4[1760/2400] | LR: 0.009136 | E: -27.136623 | E_var:     0.1455 | E_err:   0.005959
[2025-10-03 05:40:07] [Iter 2007/2250] R4[1762/2400] | LR: 0.009112 | E: -27.144716 | E_var:     0.1449 | E_err:   0.005949
[2025-10-03 05:40:12] [Iter 2008/2250] R4[1764/2400] | LR: 0.009087 | E: -27.146624 | E_var:     0.1135 | E_err:   0.005264
[2025-10-03 05:40:17] [Iter 2009/2250] R4[1766/2400] | LR: 0.009063 | E: -27.138995 | E_var:     0.1676 | E_err:   0.006396
[2025-10-03 05:40:22] [Iter 2010/2250] R4[1768/2400] | LR: 0.009039 | E: -27.136336 | E_var:     0.1463 | E_err:   0.005977
[2025-10-03 05:40:27] [Iter 2011/2250] R4[1770/2400] | LR: 0.009015 | E: -27.140643 | E_var:     0.1585 | E_err:   0.006221
[2025-10-03 05:40:32] [Iter 2012/2250] R4[1772/2400] | LR: 0.008991 | E: -27.152403 | E_var:     0.1222 | E_err:   0.005461
[2025-10-03 05:40:37] [Iter 2013/2250] R4[1774/2400] | LR: 0.008967 | E: -27.153943 | E_var:     0.1747 | E_err:   0.006530
[2025-10-03 05:40:42] [Iter 2014/2250] R4[1776/2400] | LR: 0.008943 | E: -27.135425 | E_var:     0.1498 | E_err:   0.006047
[2025-10-03 05:40:46] [Iter 2015/2250] R4[1778/2400] | LR: 0.008919 | E: -27.140901 | E_var:     0.2412 | E_err:   0.007674
[2025-10-03 05:40:51] [Iter 2016/2250] R4[1780/2400] | LR: 0.008896 | E: -27.141189 | E_var:     0.1251 | E_err:   0.005526
[2025-10-03 05:40:56] [Iter 2017/2250] R4[1782/2400] | LR: 0.008872 | E: -27.137120 | E_var:     0.1284 | E_err:   0.005599
[2025-10-03 05:41:01] [Iter 2018/2250] R4[1784/2400] | LR: 0.008848 | E: -27.141398 | E_var:     0.1391 | E_err:   0.005828
[2025-10-03 05:41:06] [Iter 2019/2250] R4[1786/2400] | LR: 0.008825 | E: -27.152430 | E_var:     0.1583 | E_err:   0.006216
[2025-10-03 05:41:11] [Iter 2020/2250] R4[1788/2400] | LR: 0.008801 | E: -27.141943 | E_var:     0.1319 | E_err:   0.005676
[2025-10-03 05:41:16] [Iter 2021/2250] R4[1790/2400] | LR: 0.008778 | E: -27.134023 | E_var:     0.1329 | E_err:   0.005695
[2025-10-03 05:41:21] [Iter 2022/2250] R4[1792/2400] | LR: 0.008754 | E: -27.150415 | E_var:     0.1370 | E_err:   0.005783
[2025-10-03 05:41:26] [Iter 2023/2250] R4[1794/2400] | LR: 0.008731 | E: -27.136907 | E_var:     0.1332 | E_err:   0.005703
[2025-10-03 05:41:31] [Iter 2024/2250] R4[1796/2400] | LR: 0.008708 | E: -27.133099 | E_var:     0.1231 | E_err:   0.005482
[2025-10-03 05:41:36] [Iter 2025/2250] R4[1798/2400] | LR: 0.008684 | E: -27.146947 | E_var:     0.1497 | E_err:   0.006046
[2025-10-03 05:41:41] [Iter 2026/2250] R4[1800/2400] | LR: 0.008661 | E: -27.137673 | E_var:     0.1869 | E_err:   0.006755
[2025-10-03 05:41:46] [Iter 2027/2250] R4[1802/2400] | LR: 0.008638 | E: -27.135036 | E_var:     0.1264 | E_err:   0.005555
[2025-10-03 05:41:51] [Iter 2028/2250] R4[1804/2400] | LR: 0.008615 | E: -27.147078 | E_var:     0.1247 | E_err:   0.005517
[2025-10-03 05:41:56] [Iter 2029/2250] R4[1806/2400] | LR: 0.008592 | E: -27.143748 | E_var:     0.1254 | E_err:   0.005532
[2025-10-03 05:42:00] [Iter 2030/2250] R4[1808/2400] | LR: 0.008569 | E: -27.142290 | E_var:     0.2694 | E_err:   0.008110
[2025-10-03 05:42:05] [Iter 2031/2250] R4[1810/2400] | LR: 0.008546 | E: -27.142658 | E_var:     0.1216 | E_err:   0.005449
[2025-10-03 05:42:10] [Iter 2032/2250] R4[1812/2400] | LR: 0.008523 | E: -27.137437 | E_var:     0.1222 | E_err:   0.005461
[2025-10-03 05:42:15] [Iter 2033/2250] R4[1814/2400] | LR: 0.008501 | E: -27.140969 | E_var:     0.1741 | E_err:   0.006519
[2025-10-03 05:42:20] [Iter 2034/2250] R4[1816/2400] | LR: 0.008478 | E: -27.130963 | E_var:     0.1485 | E_err:   0.006021
[2025-10-03 05:42:25] [Iter 2035/2250] R4[1818/2400] | LR: 0.008455 | E: -27.145625 | E_var:     0.1307 | E_err:   0.005648
[2025-10-03 05:42:30] [Iter 2036/2250] R4[1820/2400] | LR: 0.008433 | E: -27.146908 | E_var:     0.1795 | E_err:   0.006620
[2025-10-03 05:42:35] [Iter 2037/2250] R4[1822/2400] | LR: 0.008410 | E: -27.136150 | E_var:     0.1212 | E_err:   0.005440
[2025-10-03 05:42:40] [Iter 2038/2250] R4[1824/2400] | LR: 0.008388 | E: -27.122491 | E_var:     0.1229 | E_err:   0.005478
[2025-10-03 05:42:45] [Iter 2039/2250] R4[1826/2400] | LR: 0.008366 | E: -27.137248 | E_var:     0.1177 | E_err:   0.005361
[2025-10-03 05:42:50] [Iter 2040/2250] R4[1828/2400] | LR: 0.008343 | E: -27.127224 | E_var:     0.2100 | E_err:   0.007161
[2025-10-03 05:42:55] [Iter 2041/2250] R4[1830/2400] | LR: 0.008321 | E: -27.131610 | E_var:     0.1351 | E_err:   0.005743
[2025-10-03 05:43:00] [Iter 2042/2250] R4[1832/2400] | LR: 0.008299 | E: -27.142523 | E_var:     0.1266 | E_err:   0.005560
[2025-10-03 05:43:05] [Iter 2043/2250] R4[1834/2400] | LR: 0.008277 | E: -27.133960 | E_var:     0.1500 | E_err:   0.006051
[2025-10-03 05:43:10] [Iter 2044/2250] R4[1836/2400] | LR: 0.008255 | E: -27.137457 | E_var:     0.1410 | E_err:   0.005867
[2025-10-03 05:43:15] [Iter 2045/2250] R4[1838/2400] | LR: 0.008233 | E: -27.132334 | E_var:     0.1487 | E_err:   0.006025
[2025-10-03 05:43:19] [Iter 2046/2250] R4[1840/2400] | LR: 0.008211 | E: -27.151937 | E_var:     0.1874 | E_err:   0.006763
[2025-10-03 05:43:24] [Iter 2047/2250] R4[1842/2400] | LR: 0.008189 | E: -27.132728 | E_var:     0.1405 | E_err:   0.005857
[2025-10-03 05:43:29] [Iter 2048/2250] R4[1844/2400] | LR: 0.008167 | E: -27.134944 | E_var:     0.1187 | E_err:   0.005384
[2025-10-03 05:43:34] [Iter 2049/2250] R4[1846/2400] | LR: 0.008145 | E: -27.133581 | E_var:     0.1675 | E_err:   0.006395
[2025-10-03 05:43:39] [Iter 2050/2250] R4[1848/2400] | LR: 0.008124 | E: -27.141078 | E_var:     0.1273 | E_err:   0.005574
[2025-10-03 05:43:44] [Iter 2051/2250] R4[1850/2400] | LR: 0.008102 | E: -27.146782 | E_var:     0.1128 | E_err:   0.005247
[2025-10-03 05:43:49] [Iter 2052/2250] R4[1852/2400] | LR: 0.008080 | E: -27.139959 | E_var:     0.1385 | E_err:   0.005816
[2025-10-03 05:43:54] [Iter 2053/2250] R4[1854/2400] | LR: 0.008059 | E: -27.141453 | E_var:     0.1359 | E_err:   0.005759
[2025-10-03 05:43:59] [Iter 2054/2250] R4[1856/2400] | LR: 0.008038 | E: -27.134278 | E_var:     0.1318 | E_err:   0.005673
[2025-10-03 05:44:04] [Iter 2055/2250] R4[1858/2400] | LR: 0.008016 | E: -27.149030 | E_var:     0.1141 | E_err:   0.005278
[2025-10-03 05:44:09] [Iter 2056/2250] R4[1860/2400] | LR: 0.007995 | E: -27.140736 | E_var:     0.1015 | E_err:   0.004979
[2025-10-03 05:44:14] [Iter 2057/2250] R4[1862/2400] | LR: 0.007974 | E: -27.146093 | E_var:     0.1403 | E_err:   0.005852
[2025-10-03 05:44:19] [Iter 2058/2250] R4[1864/2400] | LR: 0.007953 | E: -27.141596 | E_var:     0.1934 | E_err:   0.006871
[2025-10-03 05:44:24] [Iter 2059/2250] R4[1866/2400] | LR: 0.007931 | E: -27.149861 | E_var:     0.1393 | E_err:   0.005832
[2025-10-03 05:44:29] [Iter 2060/2250] R4[1868/2400] | LR: 0.007910 | E: -27.136035 | E_var:     0.1151 | E_err:   0.005302
[2025-10-03 05:44:34] [Iter 2061/2250] R4[1870/2400] | LR: 0.007889 | E: -27.147585 | E_var:     0.1695 | E_err:   0.006433
[2025-10-03 05:44:38] [Iter 2062/2250] R4[1872/2400] | LR: 0.007869 | E: -27.141404 | E_var:     0.2003 | E_err:   0.006994
[2025-10-03 05:44:43] [Iter 2063/2250] R4[1874/2400] | LR: 0.007848 | E: -27.130816 | E_var:     0.1675 | E_err:   0.006395
[2025-10-03 05:44:48] [Iter 2064/2250] R4[1876/2400] | LR: 0.007827 | E: -27.135898 | E_var:     0.1394 | E_err:   0.005834
[2025-10-03 05:44:53] [Iter 2065/2250] R4[1878/2400] | LR: 0.007806 | E: -27.139024 | E_var:     0.1109 | E_err:   0.005205
[2025-10-03 05:44:58] [Iter 2066/2250] R4[1880/2400] | LR: 0.007786 | E: -27.140316 | E_var:     0.3176 | E_err:   0.008806
[2025-10-03 05:45:03] [Iter 2067/2250] R4[1882/2400] | LR: 0.007765 | E: -27.131702 | E_var:     0.1866 | E_err:   0.006750
[2025-10-03 05:45:08] [Iter 2068/2250] R4[1884/2400] | LR: 0.007745 | E: -27.138460 | E_var:     0.1412 | E_err:   0.005871
[2025-10-03 05:45:13] [Iter 2069/2250] R4[1886/2400] | LR: 0.007724 | E: -27.140210 | E_var:     0.1160 | E_err:   0.005322
[2025-10-03 05:45:18] [Iter 2070/2250] R4[1888/2400] | LR: 0.007704 | E: -27.135877 | E_var:     0.1222 | E_err:   0.005462
[2025-10-03 05:45:23] [Iter 2071/2250] R4[1890/2400] | LR: 0.007684 | E: -27.140350 | E_var:     0.2519 | E_err:   0.007842
[2025-10-03 05:45:28] [Iter 2072/2250] R4[1892/2400] | LR: 0.007663 | E: -27.132209 | E_var:     0.1405 | E_err:   0.005856
[2025-10-03 05:45:33] [Iter 2073/2250] R4[1894/2400] | LR: 0.007643 | E: -27.143883 | E_var:     0.1257 | E_err:   0.005540
[2025-10-03 05:45:38] [Iter 2074/2250] R4[1896/2400] | LR: 0.007623 | E: -27.141029 | E_var:     0.1296 | E_err:   0.005625
[2025-10-03 05:45:43] [Iter 2075/2250] R4[1898/2400] | LR: 0.007603 | E: -27.145915 | E_var:     0.1325 | E_err:   0.005688
[2025-10-03 05:45:48] [Iter 2076/2250] R4[1900/2400] | LR: 0.007583 | E: -27.144242 | E_var:     0.1160 | E_err:   0.005323
[2025-10-03 05:45:53] [Iter 2077/2250] R4[1902/2400] | LR: 0.007563 | E: -27.134543 | E_var:     0.1678 | E_err:   0.006401
[2025-10-03 05:45:57] [Iter 2078/2250] R4[1904/2400] | LR: 0.007543 | E: -27.142298 | E_var:     0.1332 | E_err:   0.005704
[2025-10-03 05:46:02] [Iter 2079/2250] R4[1906/2400] | LR: 0.007524 | E: -27.140410 | E_var:     0.1424 | E_err:   0.005896
[2025-10-03 05:46:07] [Iter 2080/2250] R4[1908/2400] | LR: 0.007504 | E: -27.135809 | E_var:     0.1454 | E_err:   0.005957
[2025-10-03 05:46:12] [Iter 2081/2250] R4[1910/2400] | LR: 0.007484 | E: -27.141439 | E_var:     0.1393 | E_err:   0.005832
[2025-10-03 05:46:17] [Iter 2082/2250] R4[1912/2400] | LR: 0.007465 | E: -27.141713 | E_var:     0.1293 | E_err:   0.005618
[2025-10-03 05:46:22] [Iter 2083/2250] R4[1914/2400] | LR: 0.007445 | E: -27.136468 | E_var:     0.1105 | E_err:   0.005193
[2025-10-03 05:46:27] [Iter 2084/2250] R4[1916/2400] | LR: 0.007426 | E: -27.139130 | E_var:     0.1374 | E_err:   0.005792
[2025-10-03 05:46:32] [Iter 2085/2250] R4[1918/2400] | LR: 0.007407 | E: -27.144239 | E_var:     0.1223 | E_err:   0.005465
[2025-10-03 05:46:37] [Iter 2086/2250] R4[1920/2400] | LR: 0.007387 | E: -27.142506 | E_var:     0.1486 | E_err:   0.006024
[2025-10-03 05:46:42] [Iter 2087/2250] R4[1922/2400] | LR: 0.007368 | E: -27.147502 | E_var:     0.1535 | E_err:   0.006121
[2025-10-03 05:46:47] [Iter 2088/2250] R4[1924/2400] | LR: 0.007349 | E: -27.141011 | E_var:     0.1316 | E_err:   0.005669
[2025-10-03 05:46:52] [Iter 2089/2250] R4[1926/2400] | LR: 0.007330 | E: -27.139074 | E_var:     0.1700 | E_err:   0.006442
[2025-10-03 05:46:57] [Iter 2090/2250] R4[1928/2400] | LR: 0.007311 | E: -27.138957 | E_var:     0.1400 | E_err:   0.005847
[2025-10-03 05:47:02] [Iter 2091/2250] R4[1930/2400] | LR: 0.007292 | E: -27.118135 | E_var:     0.4584 | E_err:   0.010579
[2025-10-03 05:47:07] [Iter 2092/2250] R4[1932/2400] | LR: 0.007273 | E: -27.131471 | E_var:     0.4170 | E_err:   0.010090
[2025-10-03 05:47:12] [Iter 2093/2250] R4[1934/2400] | LR: 0.007254 | E: -27.132838 | E_var:     0.5281 | E_err:   0.011355
[2025-10-03 05:47:16] [Iter 2094/2250] R4[1936/2400] | LR: 0.007236 | E: -27.141200 | E_var:     0.4226 | E_err:   0.010158
[2025-10-03 05:47:21] [Iter 2095/2250] R4[1938/2400] | LR: 0.007217 | E: -27.133609 | E_var:     0.5116 | E_err:   0.011177
[2025-10-03 05:47:26] [Iter 2096/2250] R4[1940/2400] | LR: 0.007198 | E: -27.139085 | E_var:     0.5572 | E_err:   0.011664
[2025-10-03 05:47:31] [Iter 2097/2250] R4[1942/2400] | LR: 0.007180 | E: -27.124156 | E_var:     0.5312 | E_err:   0.011388
[2025-10-03 05:47:36] [Iter 2098/2250] R4[1944/2400] | LR: 0.007161 | E: -27.136944 | E_var:     0.5127 | E_err:   0.011187
[2025-10-03 05:47:41] [Iter 2099/2250] R4[1946/2400] | LR: 0.007143 | E: -27.141606 | E_var:     0.1604 | E_err:   0.006258
[2025-10-03 05:47:46] [Iter 2100/2250] R4[1948/2400] | LR: 0.007125 | E: -27.139100 | E_var:     0.1354 | E_err:   0.005749
[2025-10-03 05:47:51] [Iter 2101/2250] R4[1950/2400] | LR: 0.007107 | E: -27.148302 | E_var:     0.1193 | E_err:   0.005396
[2025-10-03 05:47:56] [Iter 2102/2250] R4[1952/2400] | LR: 0.007088 | E: -27.131273 | E_var:     0.1079 | E_err:   0.005132
[2025-10-03 05:48:01] [Iter 2103/2250] R4[1954/2400] | LR: 0.007070 | E: -27.136023 | E_var:     0.1306 | E_err:   0.005647
[2025-10-03 05:48:06] [Iter 2104/2250] R4[1956/2400] | LR: 0.007052 | E: -27.139313 | E_var:     0.1441 | E_err:   0.005932
[2025-10-03 05:48:11] [Iter 2105/2250] R4[1958/2400] | LR: 0.007034 | E: -27.140636 | E_var:     0.1552 | E_err:   0.006156
[2025-10-03 05:48:16] [Iter 2106/2250] R4[1960/2400] | LR: 0.007017 | E: -27.144492 | E_var:     0.1544 | E_err:   0.006140
[2025-10-03 05:48:21] [Iter 2107/2250] R4[1962/2400] | LR: 0.006999 | E: -27.144870 | E_var:     0.1371 | E_err:   0.005785
[2025-10-03 05:48:26] [Iter 2108/2250] R4[1964/2400] | LR: 0.006981 | E: -27.143131 | E_var:     0.1638 | E_err:   0.006325
[2025-10-03 05:48:31] [Iter 2109/2250] R4[1966/2400] | LR: 0.006963 | E: -27.140932 | E_var:     0.1223 | E_err:   0.005464
[2025-10-03 05:48:35] [Iter 2110/2250] R4[1968/2400] | LR: 0.006946 | E: -27.134946 | E_var:     0.1962 | E_err:   0.006920
[2025-10-03 05:48:40] [Iter 2111/2250] R4[1970/2400] | LR: 0.006928 | E: -27.130561 | E_var:     0.1424 | E_err:   0.005896
[2025-10-03 05:48:45] [Iter 2112/2250] R4[1972/2400] | LR: 0.006911 | E: -27.143232 | E_var:     0.1132 | E_err:   0.005258
[2025-10-03 05:48:50] [Iter 2113/2250] R4[1974/2400] | LR: 0.006894 | E: -27.139913 | E_var:     0.1368 | E_err:   0.005780
[2025-10-03 05:48:55] [Iter 2114/2250] R4[1976/2400] | LR: 0.006876 | E: -27.138404 | E_var:     0.1532 | E_err:   0.006116
[2025-10-03 05:49:00] [Iter 2115/2250] R4[1978/2400] | LR: 0.006859 | E: -27.131606 | E_var:     0.1361 | E_err:   0.005765
[2025-10-03 05:49:05] [Iter 2116/2250] R4[1980/2400] | LR: 0.006842 | E: -27.129273 | E_var:     0.4217 | E_err:   0.010147
[2025-10-03 05:49:10] [Iter 2117/2250] R4[1982/2400] | LR: 0.006825 | E: -27.143347 | E_var:     0.1294 | E_err:   0.005622
[2025-10-03 05:49:15] [Iter 2118/2250] R4[1984/2400] | LR: 0.006808 | E: -27.140615 | E_var:     0.1115 | E_err:   0.005217
[2025-10-03 05:49:20] [Iter 2119/2250] R4[1986/2400] | LR: 0.006791 | E: -27.137802 | E_var:     0.1247 | E_err:   0.005517
[2025-10-03 05:49:25] [Iter 2120/2250] R4[1988/2400] | LR: 0.006774 | E: -27.144866 | E_var:     0.1080 | E_err:   0.005135
[2025-10-03 05:49:30] [Iter 2121/2250] R4[1990/2400] | LR: 0.006757 | E: -27.141399 | E_var:     0.1175 | E_err:   0.005357
[2025-10-03 05:49:35] [Iter 2122/2250] R4[1992/2400] | LR: 0.006741 | E: -27.138164 | E_var:     0.1468 | E_err:   0.005987
[2025-10-03 05:49:40] [Iter 2123/2250] R4[1994/2400] | LR: 0.006724 | E: -27.143529 | E_var:     0.1199 | E_err:   0.005411
[2025-10-03 05:49:45] [Iter 2124/2250] R4[1996/2400] | LR: 0.006708 | E: -27.141318 | E_var:     0.1148 | E_err:   0.005294
[2025-10-03 05:49:49] [Iter 2125/2250] R4[1998/2400] | LR: 0.006691 | E: -27.132741 | E_var:     0.4418 | E_err:   0.010386
[2025-10-03 05:49:54] [Iter 2126/2250] R4[2000/2400] | LR: 0.006675 | E: -27.140213 | E_var:     0.4629 | E_err:   0.010631
[2025-10-03 05:49:59] [Iter 2127/2250] R4[2002/2400] | LR: 0.006658 | E: -27.128140 | E_var:     0.4328 | E_err:   0.010279
[2025-10-03 05:50:04] [Iter 2128/2250] R4[2004/2400] | LR: 0.006642 | E: -27.136549 | E_var:     0.4663 | E_err:   0.010670
[2025-10-03 05:50:09] [Iter 2129/2250] R4[2006/2400] | LR: 0.006626 | E: -27.125931 | E_var:     0.4513 | E_err:   0.010497
[2025-10-03 05:50:14] [Iter 2130/2250] R4[2008/2400] | LR: 0.006610 | E: -27.123739 | E_var:     0.4838 | E_err:   0.010868
[2025-10-03 05:50:19] [Iter 2131/2250] R4[2010/2400] | LR: 0.006594 | E: -27.138143 | E_var:     0.4051 | E_err:   0.009945
[2025-10-03 05:50:24] [Iter 2132/2250] R4[2012/2400] | LR: 0.006578 | E: -27.148352 | E_var:     0.1133 | E_err:   0.005260
[2025-10-03 05:50:29] [Iter 2133/2250] R4[2014/2400] | LR: 0.006562 | E: -27.144607 | E_var:     0.1310 | E_err:   0.005656
[2025-10-03 05:50:34] [Iter 2134/2250] R4[2016/2400] | LR: 0.006546 | E: -27.144567 | E_var:     0.1239 | E_err:   0.005500
[2025-10-03 05:50:39] [Iter 2135/2250] R4[2018/2400] | LR: 0.006530 | E: -27.147790 | E_var:     0.1145 | E_err:   0.005288
[2025-10-03 05:50:44] [Iter 2136/2250] R4[2020/2400] | LR: 0.006515 | E: -27.139699 | E_var:     0.1348 | E_err:   0.005736
[2025-10-03 05:50:49] [Iter 2137/2250] R4[2022/2400] | LR: 0.006499 | E: -27.141858 | E_var:     0.1447 | E_err:   0.005943
[2025-10-03 05:50:54] [Iter 2138/2250] R4[2024/2400] | LR: 0.006484 | E: -27.138651 | E_var:     0.1136 | E_err:   0.005267
[2025-10-03 05:50:59] [Iter 2139/2250] R4[2026/2400] | LR: 0.006468 | E: -27.140029 | E_var:     0.1600 | E_err:   0.006250
[2025-10-03 05:51:04] [Iter 2140/2250] R4[2028/2400] | LR: 0.006453 | E: -27.136867 | E_var:     0.1475 | E_err:   0.006001
[2025-10-03 05:51:08] [Iter 2141/2250] R4[2030/2400] | LR: 0.006438 | E: -27.141792 | E_var:     0.1249 | E_err:   0.005523
[2025-10-03 05:51:13] [Iter 2142/2250] R4[2032/2400] | LR: 0.006422 | E: -27.137966 | E_var:     0.1185 | E_err:   0.005378
[2025-10-03 05:51:18] [Iter 2143/2250] R4[2034/2400] | LR: 0.006407 | E: -27.142419 | E_var:     0.1824 | E_err:   0.006674
[2025-10-03 05:51:23] [Iter 2144/2250] R4[2036/2400] | LR: 0.006392 | E: -27.139616 | E_var:     0.1227 | E_err:   0.005473
[2025-10-03 05:51:28] [Iter 2145/2250] R4[2038/2400] | LR: 0.006377 | E: -27.138659 | E_var:     0.1259 | E_err:   0.005544
[2025-10-03 05:51:33] [Iter 2146/2250] R4[2040/2400] | LR: 0.006362 | E: -27.136698 | E_var:     0.1325 | E_err:   0.005687
[2025-10-03 05:51:38] [Iter 2147/2250] R4[2042/2400] | LR: 0.006348 | E: -27.148007 | E_var:     0.1235 | E_err:   0.005492
[2025-10-03 05:51:43] [Iter 2148/2250] R4[2044/2400] | LR: 0.006333 | E: -27.143324 | E_var:     0.1258 | E_err:   0.005541
[2025-10-03 05:51:48] [Iter 2149/2250] R4[2046/2400] | LR: 0.006318 | E: -27.144145 | E_var:     0.1751 | E_err:   0.006538
[2025-10-03 05:51:53] [Iter 2150/2250] R4[2048/2400] | LR: 0.006304 | E: -27.143552 | E_var:     0.1756 | E_err:   0.006547
[2025-10-03 05:51:58] [Iter 2151/2250] R4[2050/2400] | LR: 0.006289 | E: -27.141428 | E_var:     0.1477 | E_err:   0.006004
[2025-10-03 05:52:03] [Iter 2152/2250] R4[2052/2400] | LR: 0.006275 | E: -27.132310 | E_var:     0.1586 | E_err:   0.006223
[2025-10-03 05:52:08] [Iter 2153/2250] R4[2054/2400] | LR: 0.006260 | E: -27.130929 | E_var:     0.1339 | E_err:   0.005717
[2025-10-03 05:52:13] [Iter 2154/2250] R4[2056/2400] | LR: 0.006246 | E: -27.140530 | E_var:     0.1239 | E_err:   0.005500
[2025-10-03 05:52:18] [Iter 2155/2250] R4[2058/2400] | LR: 0.006232 | E: -27.136834 | E_var:     0.1250 | E_err:   0.005524
[2025-10-03 05:52:22] [Iter 2156/2250] R4[2060/2400] | LR: 0.006218 | E: -27.137088 | E_var:     0.1544 | E_err:   0.006140
[2025-10-03 05:52:27] [Iter 2157/2250] R4[2062/2400] | LR: 0.006204 | E: -27.151676 | E_var:     0.1464 | E_err:   0.005979
[2025-10-03 05:52:32] [Iter 2158/2250] R4[2064/2400] | LR: 0.006190 | E: -27.137618 | E_var:     0.1241 | E_err:   0.005505
[2025-10-03 05:52:37] [Iter 2159/2250] R4[2066/2400] | LR: 0.006176 | E: -27.131727 | E_var:     0.1815 | E_err:   0.006657
[2025-10-03 05:52:42] [Iter 2160/2250] R4[2068/2400] | LR: 0.006162 | E: -27.142162 | E_var:     0.1361 | E_err:   0.005763
[2025-10-03 05:52:47] [Iter 2161/2250] R4[2070/2400] | LR: 0.006148 | E: -27.139097 | E_var:     0.1459 | E_err:   0.005968
[2025-10-03 05:52:52] [Iter 2162/2250] R4[2072/2400] | LR: 0.006135 | E: -27.143987 | E_var:     0.1600 | E_err:   0.006250
[2025-10-03 05:52:57] [Iter 2163/2250] R4[2074/2400] | LR: 0.006121 | E: -27.141531 | E_var:     0.1305 | E_err:   0.005645
[2025-10-03 05:53:02] [Iter 2164/2250] R4[2076/2400] | LR: 0.006107 | E: -27.146553 | E_var:     0.0982 | E_err:   0.004896
[2025-10-03 05:53:07] [Iter 2165/2250] R4[2078/2400] | LR: 0.006094 | E: -27.135150 | E_var:     0.2849 | E_err:   0.008340
[2025-10-03 05:53:12] [Iter 2166/2250] R4[2080/2400] | LR: 0.006081 | E: -27.150237 | E_var:     0.1182 | E_err:   0.005372
[2025-10-03 05:53:17] [Iter 2167/2250] R4[2082/2400] | LR: 0.006067 | E: -27.154234 | E_var:     0.3894 | E_err:   0.009750
[2025-10-03 05:53:22] [Iter 2168/2250] R4[2084/2400] | LR: 0.006054 | E: -27.136619 | E_var:     0.1794 | E_err:   0.006619
[2025-10-03 05:53:27] [Iter 2169/2250] R4[2086/2400] | LR: 0.006041 | E: -27.130556 | E_var:     0.1753 | E_err:   0.006541
[2025-10-03 05:53:32] [Iter 2170/2250] R4[2088/2400] | LR: 0.006028 | E: -27.147042 | E_var:     0.1451 | E_err:   0.005952
[2025-10-03 05:53:37] [Iter 2171/2250] R4[2090/2400] | LR: 0.006015 | E: -27.139312 | E_var:     0.1531 | E_err:   0.006114
[2025-10-03 05:53:41] [Iter 2172/2250] R4[2092/2400] | LR: 0.006002 | E: -27.142573 | E_var:     0.3747 | E_err:   0.009564
[2025-10-03 05:53:46] [Iter 2173/2250] R4[2094/2400] | LR: 0.005989 | E: -27.134143 | E_var:     0.2015 | E_err:   0.007013
[2025-10-03 05:53:51] [Iter 2174/2250] R4[2096/2400] | LR: 0.005977 | E: -27.141634 | E_var:     0.1898 | E_err:   0.006807
[2025-10-03 05:53:56] [Iter 2175/2250] R4[2098/2400] | LR: 0.005964 | E: -27.143718 | E_var:     0.1364 | E_err:   0.005770
[2025-10-03 05:54:01] [Iter 2176/2250] R4[2100/2400] | LR: 0.005952 | E: -27.133969 | E_var:     0.1277 | E_err:   0.005585
[2025-10-03 05:54:06] [Iter 2177/2250] R4[2102/2400] | LR: 0.005939 | E: -27.152743 | E_var:     0.1174 | E_err:   0.005353
[2025-10-03 05:54:11] [Iter 2178/2250] R4[2104/2400] | LR: 0.005927 | E: -27.125696 | E_var:     0.2318 | E_err:   0.007522
[2025-10-03 05:54:16] [Iter 2179/2250] R4[2106/2400] | LR: 0.005914 | E: -27.126300 | E_var:     0.1667 | E_err:   0.006379
[2025-10-03 05:54:21] [Iter 2180/2250] R4[2108/2400] | LR: 0.005902 | E: -27.148911 | E_var:     0.1195 | E_err:   0.005401
[2025-10-03 05:54:26] [Iter 2181/2250] R4[2110/2400] | LR: 0.005890 | E: -27.151039 | E_var:     0.1112 | E_err:   0.005210
[2025-10-03 05:54:31] [Iter 2182/2250] R4[2112/2400] | LR: 0.005878 | E: -27.148667 | E_var:     0.2452 | E_err:   0.007736
[2025-10-03 05:54:36] [Iter 2183/2250] R4[2114/2400] | LR: 0.005866 | E: -27.130106 | E_var:     0.1571 | E_err:   0.006193
[2025-10-03 05:54:41] [Iter 2184/2250] R4[2116/2400] | LR: 0.005854 | E: -27.143922 | E_var:     0.1192 | E_err:   0.005395
[2025-10-03 05:54:46] [Iter 2185/2250] R4[2118/2400] | LR: 0.005842 | E: -27.128159 | E_var:     0.1653 | E_err:   0.006352
[2025-10-03 05:54:51] [Iter 2186/2250] R4[2120/2400] | LR: 0.005830 | E: -27.152804 | E_var:     0.1773 | E_err:   0.006579
[2025-10-03 05:54:56] [Iter 2187/2250] R4[2122/2400] | LR: 0.005819 | E: -27.147768 | E_var:     0.1272 | E_err:   0.005572
[2025-10-03 05:55:00] [Iter 2188/2250] R4[2124/2400] | LR: 0.005807 | E: -27.148597 | E_var:     0.1618 | E_err:   0.006285
[2025-10-03 05:55:05] [Iter 2189/2250] R4[2126/2400] | LR: 0.005795 | E: -27.146603 | E_var:     0.2056 | E_err:   0.007085
[2025-10-03 05:55:10] [Iter 2190/2250] R4[2128/2400] | LR: 0.005784 | E: -27.137666 | E_var:     0.1172 | E_err:   0.005350
[2025-10-03 05:55:15] [Iter 2191/2250] R4[2130/2400] | LR: 0.005773 | E: -27.144047 | E_var:     0.1782 | E_err:   0.006596
[2025-10-03 05:55:20] [Iter 2192/2250] R4[2132/2400] | LR: 0.005761 | E: -27.148178 | E_var:     0.1441 | E_err:   0.005932
[2025-10-03 05:55:25] [Iter 2193/2250] R4[2134/2400] | LR: 0.005750 | E: -27.141930 | E_var:     0.1089 | E_err:   0.005157
[2025-10-03 05:55:30] [Iter 2194/2250] R4[2136/2400] | LR: 0.005739 | E: -27.124715 | E_var:     0.1414 | E_err:   0.005876
[2025-10-03 05:55:35] [Iter 2195/2250] R4[2138/2400] | LR: 0.005728 | E: -27.146830 | E_var:     0.1216 | E_err:   0.005449
[2025-10-03 05:55:40] [Iter 2196/2250] R4[2140/2400] | LR: 0.005717 | E: -27.147442 | E_var:     0.1537 | E_err:   0.006126
[2025-10-03 05:55:45] [Iter 2197/2250] R4[2142/2400] | LR: 0.005706 | E: -27.142268 | E_var:     0.1323 | E_err:   0.005683
[2025-10-03 05:55:50] [Iter 2198/2250] R4[2144/2400] | LR: 0.005695 | E: -27.143605 | E_var:     0.1271 | E_err:   0.005571
[2025-10-03 05:55:55] [Iter 2199/2250] R4[2146/2400] | LR: 0.005685 | E: -27.135330 | E_var:     0.1199 | E_err:   0.005411
[2025-10-03 05:56:00] [Iter 2200/2250] R4[2148/2400] | LR: 0.005674 | E: -27.141658 | E_var:     0.1247 | E_err:   0.005519
[2025-10-03 05:56:00] ✓ Checkpoint saved: checkpoint_iter_002200.pkl
[2025-10-03 05:56:05] [Iter 2201/2250] R4[2150/2400] | LR: 0.005663 | E: -27.145022 | E_var:     0.1337 | E_err:   0.005714
[2025-10-03 05:56:10] [Iter 2202/2250] R4[2152/2400] | LR: 0.005653 | E: -27.133737 | E_var:     0.2118 | E_err:   0.007191
[2025-10-03 05:56:15] [Iter 2203/2250] R4[2154/2400] | LR: 0.005642 | E: -27.140688 | E_var:     0.1607 | E_err:   0.006263
[2025-10-03 05:56:19] [Iter 2204/2250] R4[2156/2400] | LR: 0.005632 | E: -27.141853 | E_var:     0.1265 | E_err:   0.005558
[2025-10-03 05:56:24] [Iter 2205/2250] R4[2158/2400] | LR: 0.005622 | E: -27.134120 | E_var:     0.1269 | E_err:   0.005567
[2025-10-03 05:56:29] [Iter 2206/2250] R4[2160/2400] | LR: 0.005612 | E: -27.139030 | E_var:     0.1403 | E_err:   0.005852
[2025-10-03 05:56:34] [Iter 2207/2250] R4[2162/2400] | LR: 0.005602 | E: -27.146024 | E_var:     0.1241 | E_err:   0.005505
[2025-10-03 05:56:39] [Iter 2208/2250] R4[2164/2400] | LR: 0.005592 | E: -27.144826 | E_var:     0.1478 | E_err:   0.006008
[2025-10-03 05:56:44] [Iter 2209/2250] R4[2166/2400] | LR: 0.005582 | E: -27.152339 | E_var:     0.1281 | E_err:   0.005592
[2025-10-03 05:56:49] [Iter 2210/2250] R4[2168/2400] | LR: 0.005572 | E: -27.139331 | E_var:     0.1163 | E_err:   0.005328
[2025-10-03 05:56:54] [Iter 2211/2250] R4[2170/2400] | LR: 0.005562 | E: -27.141319 | E_var:     0.1539 | E_err:   0.006130
[2025-10-03 05:56:59] [Iter 2212/2250] R4[2172/2400] | LR: 0.005553 | E: -27.138283 | E_var:     0.1532 | E_err:   0.006115
[2025-10-03 05:57:04] [Iter 2213/2250] R4[2174/2400] | LR: 0.005543 | E: -27.146553 | E_var:     0.1313 | E_err:   0.005662
[2025-10-03 05:57:09] [Iter 2214/2250] R4[2176/2400] | LR: 0.005534 | E: -27.143200 | E_var:     0.1580 | E_err:   0.006212
[2025-10-03 05:57:14] [Iter 2215/2250] R4[2178/2400] | LR: 0.005524 | E: -27.154413 | E_var:     0.1422 | E_err:   0.005891
[2025-10-03 05:57:19] [Iter 2216/2250] R4[2180/2400] | LR: 0.005515 | E: -27.142075 | E_var:     0.1283 | E_err:   0.005598
[2025-10-03 05:57:24] [Iter 2217/2250] R4[2182/2400] | LR: 0.005506 | E: -27.147661 | E_var:     0.1314 | E_err:   0.005664
[2025-10-03 05:57:29] [Iter 2218/2250] R4[2184/2400] | LR: 0.005496 | E: -27.136894 | E_var:     0.1440 | E_err:   0.005929
[2025-10-03 05:57:34] [Iter 2219/2250] R4[2186/2400] | LR: 0.005487 | E: -27.148204 | E_var:     0.1181 | E_err:   0.005369
[2025-10-03 05:57:38] [Iter 2220/2250] R4[2188/2400] | LR: 0.005478 | E: -27.140522 | E_var:     0.1005 | E_err:   0.004953
[2025-10-03 05:57:43] [Iter 2221/2250] R4[2190/2400] | LR: 0.005469 | E: -27.144805 | E_var:     0.1316 | E_err:   0.005668
[2025-10-03 05:57:48] [Iter 2222/2250] R4[2192/2400] | LR: 0.005460 | E: -27.146444 | E_var:     0.1089 | E_err:   0.005155
[2025-10-03 05:57:53] [Iter 2223/2250] R4[2194/2400] | LR: 0.005452 | E: -27.153277 | E_var:     0.1945 | E_err:   0.006891
[2025-10-03 05:57:58] [Iter 2224/2250] R4[2196/2400] | LR: 0.005443 | E: -27.144775 | E_var:     0.1308 | E_err:   0.005652
[2025-10-03 05:58:03] [Iter 2225/2250] R4[2198/2400] | LR: 0.005434 | E: -27.141202 | E_var:     0.1196 | E_err:   0.005404
[2025-10-03 05:58:08] [Iter 2226/2250] R4[2200/2400] | LR: 0.005426 | E: -27.148283 | E_var:     0.1295 | E_err:   0.005624
[2025-10-03 05:58:13] [Iter 2227/2250] R4[2202/2400] | LR: 0.005417 | E: -27.147461 | E_var:     0.1434 | E_err:   0.005917
[2025-10-03 05:58:18] [Iter 2228/2250] R4[2204/2400] | LR: 0.005409 | E: -27.140790 | E_var:     0.1150 | E_err:   0.005298
[2025-10-03 05:58:23] [Iter 2229/2250] R4[2206/2400] | LR: 0.005401 | E: -27.132869 | E_var:     0.3767 | E_err:   0.009590
[2025-10-03 05:58:28] [Iter 2230/2250] R4[2208/2400] | LR: 0.005393 | E: -27.141100 | E_var:     0.1418 | E_err:   0.005883
[2025-10-03 05:58:33] [Iter 2231/2250] R4[2210/2400] | LR: 0.005385 | E: -27.136249 | E_var:     0.1103 | E_err:   0.005190
[2025-10-03 05:58:38] [Iter 2232/2250] R4[2212/2400] | LR: 0.005377 | E: -27.125989 | E_var:     0.1818 | E_err:   0.006663
[2025-10-03 05:58:43] [Iter 2233/2250] R4[2214/2400] | LR: 0.005369 | E: -27.141064 | E_var:     0.1157 | E_err:   0.005315
[2025-10-03 05:58:48] [Iter 2234/2250] R4[2216/2400] | LR: 0.005361 | E: -27.143915 | E_var:     0.1431 | E_err:   0.005910
[2025-10-03 05:58:52] [Iter 2235/2250] R4[2218/2400] | LR: 0.005353 | E: -27.139457 | E_var:     0.1281 | E_err:   0.005592
[2025-10-03 05:58:57] [Iter 2236/2250] R4[2220/2400] | LR: 0.005345 | E: -27.148708 | E_var:     0.1305 | E_err:   0.005645
[2025-10-03 05:59:02] [Iter 2237/2250] R4[2222/2400] | LR: 0.005338 | E: -27.134316 | E_var:     0.1425 | E_err:   0.005899
[2025-10-03 05:59:07] [Iter 2238/2250] R4[2224/2400] | LR: 0.005330 | E: -27.140455 | E_var:     0.1515 | E_err:   0.006081
[2025-10-03 05:59:12] [Iter 2239/2250] R4[2226/2400] | LR: 0.005323 | E: -27.139975 | E_var:     0.1347 | E_err:   0.005734
[2025-10-03 05:59:17] [Iter 2240/2250] R4[2228/2400] | LR: 0.005315 | E: -27.133400 | E_var:     0.1277 | E_err:   0.005583
[2025-10-03 05:59:22] [Iter 2241/2250] R4[2230/2400] | LR: 0.005308 | E: -27.141694 | E_var:     0.2682 | E_err:   0.008091
[2025-10-03 05:59:27] [Iter 2242/2250] R4[2232/2400] | LR: 0.005301 | E: -27.146037 | E_var:     0.1369 | E_err:   0.005781
[2025-10-03 05:59:32] [Iter 2243/2250] R4[2234/2400] | LR: 0.005294 | E: -27.146139 | E_var:     0.1151 | E_err:   0.005301
[2025-10-03 05:59:37] [Iter 2244/2250] R4[2236/2400] | LR: 0.005287 | E: -27.146680 | E_var:     0.1428 | E_err:   0.005904
[2025-10-03 05:59:42] [Iter 2245/2250] R4[2238/2400] | LR: 0.005280 | E: -27.134218 | E_var:     0.1134 | E_err:   0.005262
[2025-10-03 05:59:47] [Iter 2246/2250] R4[2240/2400] | LR: 0.005273 | E: -27.139823 | E_var:     0.1047 | E_err:   0.005056
[2025-10-03 05:59:52] [Iter 2247/2250] R4[2242/2400] | LR: 0.005266 | E: -27.138938 | E_var:     0.1101 | E_err:   0.005186
[2025-10-03 05:59:57] [Iter 2248/2250] R4[2244/2400] | LR: 0.005260 | E: -27.137657 | E_var:     0.1537 | E_err:   0.006125
[2025-10-03 06:00:02] [Iter 2249/2250] R4[2246/2400] | LR: 0.005253 | E: -27.144976 | E_var:     0.1436 | E_err:   0.005921
[2025-10-03 06:00:07] [Iter 2250/2250] R4[2248/2400] | LR: 0.005247 | E: -27.148452 | E_var:     0.1329 | E_err:   0.005697
[2025-10-03 06:00:07] ================================================================================
[2025-10-03 06:00:07] ✅ Training completed successfully
[2025-10-03 06:00:07] Total restarts: 4
[2025-10-03 06:00:08] Final Energy: -27.14845214 ± 0.00569713
[2025-10-03 06:00:08] Final Variance: 0.132945
[2025-10-03 06:00:08] ================================================================================
[2025-10-03 06:00:08] ============================================================
[2025-10-03 06:00:08] Training completed | Runtime: 11162.1s
[2025-10-03 06:00:10] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-03 06:00:10] ============================================================
