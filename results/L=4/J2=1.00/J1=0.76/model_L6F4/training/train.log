[2025-10-02 19:41:09] ==================================================
[2025-10-02 19:41:09] GCNN for Shastry-Sutherland Model
[2025-10-02 19:41:09] ==================================================
[2025-10-02 19:41:09] System parameters:
[2025-10-02 19:41:09]   - System size: L=4, N=64
[2025-10-02 19:41:09]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-02 19:41:09] --------------------------------------------------
[2025-10-02 19:41:09] Model parameters:
[2025-10-02 19:41:09]   - Number of layers = 6
[2025-10-02 19:41:09]   - Number of features = 4
[2025-10-02 19:41:09]   - Total parameters = 20780
[2025-10-02 19:41:09] --------------------------------------------------
[2025-10-02 19:41:09] Training parameters:
[2025-10-02 19:41:09]   - Total iterations: 2250
[2025-10-02 19:41:09]   - Annealing cycles: 4
[2025-10-02 19:41:09]   - Initial period: 150
[2025-10-02 19:41:09]   - Period multiplier: 2.0
[2025-10-02 19:41:09]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-02 19:41:09]   - Samples: 4096
[2025-10-02 19:41:09]   - Discarded samples: 0
[2025-10-02 19:41:09]   - Chunk size: 4096
[2025-10-02 19:41:09]   - Diagonal shift: 0.15
[2025-10-02 19:41:09]   - Gradient clipping: 1.0
[2025-10-02 19:41:09]   - Checkpoint enabled: interval=200
[2025-10-02 19:41:09]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.76/model_L6F4/training/checkpoints
[2025-10-02 19:41:09] --------------------------------------------------
[2025-10-02 19:41:09] Device status:
[2025-10-02 19:41:09]   - Devices model: NVIDIA H200 NVL
[2025-10-02 19:41:09]   - Number of devices: 1
[2025-10-02 19:41:09]   - Sharding: True
[2025-10-02 19:41:51] [Iter    1/2250] R0[0/150]    | LR: 0.030000 | E:  32.302411 | E_var:     0.1232 | E_err:   0.005485
[2025-10-02 19:41:55] [Iter    2/2250] R0[2/150]    | LR: 0.029989 | E:  32.284831 | E_var:     0.1811 | E_err:   0.006650
[2025-10-02 19:41:58] [Iter    3/2250] R0[4/150]    | LR: 0.029956 | E:  32.283611 | E_var:     0.2874 | E_err:   0.008376
[2025-10-02 19:42:02] [Iter    4/2250] R0[6/150]    | LR: 0.029901 | E:  32.251412 | E_var:     0.5083 | E_err:   0.011140
[2025-10-02 19:42:06] [Iter    5/2250] R0[8/150]    | LR: 0.029825 | E:  32.188701 | E_var:     0.9741 | E_err:   0.015422
[2025-10-02 19:42:09] [Iter    6/2250] R0[10/150]   | LR: 0.029727 | E:  32.035572 | E_var:     2.2062 | E_err:   0.023208
[2025-10-02 19:42:13] [Iter    7/2250] R0[12/150]   | LR: 0.029607 | E:  31.663114 | E_var:     6.4720 | E_err:   0.039750
[2025-10-02 19:42:17] [Iter    8/2250] R0[14/150]   | LR: 0.029466 | E:  30.424869 | E_var:    21.0450 | E_err:   0.071679
[2025-10-02 19:42:20] [Iter    9/2250] R0[16/150]   | LR: 0.029305 | E:  26.286341 | E_var:    55.6196 | E_err:   0.116529
[2025-10-02 19:42:24] [Iter   10/2250] R0[18/150]   | LR: 0.029122 | E:  19.128336 | E_var:    66.2786 | E_err:   0.127206
[2025-10-02 19:42:27] [Iter   11/2250] R0[20/150]   | LR: 0.028919 | E:  13.434981 | E_var:    42.8084 | E_err:   0.102231
[2025-10-02 19:42:31] [Iter   12/2250] R0[22/150]   | LR: 0.028696 | E:   9.881214 | E_var:    35.9723 | E_err:   0.093714
[2025-10-02 19:42:35] [Iter   13/2250] R0[24/150]   | LR: 0.028454 | E:   7.276126 | E_var:    33.5954 | E_err:   0.090565
[2025-10-02 19:42:38] [Iter   14/2250] R0[26/150]   | LR: 0.028192 | E:   4.531566 | E_var:    32.9015 | E_err:   0.089625
[2025-10-02 19:42:42] [Iter   15/2250] R0[28/150]   | LR: 0.027912 | E:   2.378600 | E_var:    31.8082 | E_err:   0.088123
[2025-10-02 19:42:46] [Iter   16/2250] R0[30/150]   | LR: 0.027613 | E:   0.318986 | E_var:    29.1741 | E_err:   0.084395
[2025-10-02 19:42:49] [Iter   17/2250] R0[32/150]   | LR: 0.027296 | E:  -1.574669 | E_var:    27.8797 | E_err:   0.082502
[2025-10-02 19:42:53] [Iter   18/2250] R0[34/150]   | LR: 0.026962 | E:  -3.053483 | E_var:    25.5012 | E_err:   0.078904
[2025-10-02 19:42:57] [Iter   19/2250] R0[36/150]   | LR: 0.026612 | E:  -4.536862 | E_var:    24.4435 | E_err:   0.077251
[2025-10-02 19:43:00] [Iter   20/2250] R0[38/150]   | LR: 0.026246 | E:  -5.700701 | E_var:    25.1192 | E_err:   0.078311
[2025-10-02 19:43:04] [Iter   21/2250] R0[40/150]   | LR: 0.025864 | E:  -6.721033 | E_var:    23.9257 | E_err:   0.076428
[2025-10-02 19:43:08] [Iter   22/2250] R0[42/150]   | LR: 0.025468 | E:  -7.631187 | E_var:    21.0302 | E_err:   0.071654
[2025-10-02 19:43:11] [Iter   23/2250] R0[44/150]   | LR: 0.025057 | E:  -8.429386 | E_var:    23.8814 | E_err:   0.076357
[2025-10-02 19:43:15] [Iter   24/2250] R0[46/150]   | LR: 0.024634 | E:  -9.233329 | E_var:    22.2984 | E_err:   0.073783
[2025-10-02 19:43:19] [Iter   25/2250] R0[48/150]   | LR: 0.024198 | E:  -9.899684 | E_var:    23.1515 | E_err:   0.075181
[2025-10-02 19:43:22] [Iter   26/2250] R0[50/150]   | LR: 0.023750 | E: -10.461706 | E_var:    21.8589 | E_err:   0.073052
[2025-10-02 19:43:26] [Iter   27/2250] R0[52/150]   | LR: 0.023291 | E: -11.096400 | E_var:    21.3749 | E_err:   0.072239
[2025-10-02 19:43:30] [Iter   28/2250] R0[54/150]   | LR: 0.022822 | E: -11.538087 | E_var:    17.2215 | E_err:   0.064842
[2025-10-02 19:43:33] [Iter   29/2250] R0[56/150]   | LR: 0.022344 | E: -12.087618 | E_var:    17.0437 | E_err:   0.064506
[2025-10-02 19:43:37] [Iter   30/2250] R0[58/150]   | LR: 0.021857 | E: -12.501883 | E_var:    16.7888 | E_err:   0.064022
[2025-10-02 19:43:41] [Iter   31/2250] R0[60/150]   | LR: 0.021363 | E: -13.048926 | E_var:    17.3653 | E_err:   0.065112
[2025-10-02 19:43:44] [Iter   32/2250] R0[62/150]   | LR: 0.020861 | E: -13.512106 | E_var:    17.7230 | E_err:   0.065779
[2025-10-02 19:43:48] [Iter   33/2250] R0[64/150]   | LR: 0.020354 | E: -13.793310 | E_var:    15.9918 | E_err:   0.062484
[2025-10-02 19:43:52] [Iter   34/2250] R0[66/150]   | LR: 0.019842 | E: -14.169134 | E_var:    15.8143 | E_err:   0.062136
[2025-10-02 19:43:55] [Iter   35/2250] R0[68/150]   | LR: 0.019326 | E: -14.548463 | E_var:    15.8121 | E_err:   0.062132
[2025-10-02 19:43:59] [Iter   36/2250] R0[70/150]   | LR: 0.018807 | E: -14.968232 | E_var:    15.7309 | E_err:   0.061972
[2025-10-02 19:44:03] [Iter   37/2250] R0[72/150]   | LR: 0.018285 | E: -15.321955 | E_var:    13.9981 | E_err:   0.058459
[2025-10-02 19:44:06] [Iter   38/2250] R0[74/150]   | LR: 0.017762 | E: -15.584545 | E_var:    15.0934 | E_err:   0.060703
[2025-10-02 19:44:10] [Iter   39/2250] R0[76/150]   | LR: 0.017238 | E: -15.885633 | E_var:    15.6914 | E_err:   0.061894
[2025-10-02 19:44:14] [Iter   40/2250] R0[78/150]   | LR: 0.016715 | E: -16.268560 | E_var:    15.3046 | E_err:   0.061127
[2025-10-02 19:44:17] [Iter   41/2250] R0[80/150]   | LR: 0.016193 | E: -16.477560 | E_var:    14.1995 | E_err:   0.058879
[2025-10-02 19:44:21] [Iter   42/2250] R0[82/150]   | LR: 0.015674 | E: -16.686190 | E_var:    12.9743 | E_err:   0.056281
[2025-10-02 19:44:25] [Iter   43/2250] R0[84/150]   | LR: 0.015158 | E: -16.993205 | E_var:    13.1699 | E_err:   0.056704
[2025-10-02 19:44:28] [Iter   44/2250] R0[86/150]   | LR: 0.014646 | E: -17.135484 | E_var:    13.2068 | E_err:   0.056783
[2025-10-02 19:44:32] [Iter   45/2250] R0[88/150]   | LR: 0.014139 | E: -17.396464 | E_var:    11.7101 | E_err:   0.053469
[2025-10-02 19:44:35] [Iter   46/2250] R0[90/150]   | LR: 0.013637 | E: -17.554134 | E_var:    12.3012 | E_err:   0.054802
[2025-10-02 19:44:39] [Iter   47/2250] R0[92/150]   | LR: 0.013143 | E: -17.828181 | E_var:    12.2835 | E_err:   0.054762
[2025-10-02 19:44:43] [Iter   48/2250] R0[94/150]   | LR: 0.012656 | E: -17.949960 | E_var:    11.3533 | E_err:   0.052648
[2025-10-02 19:44:46] [Iter   49/2250] R0[96/150]   | LR: 0.012178 | E: -18.137569 | E_var:    11.1348 | E_err:   0.052139
[2025-10-02 19:44:50] [Iter   50/2250] R0[98/150]   | LR: 0.011709 | E: -18.301237 | E_var:    11.9439 | E_err:   0.054000
[2025-10-02 19:44:54] [Iter   51/2250] R0[100/150]  | LR: 0.011250 | E: -18.409648 | E_var:    12.0549 | E_err:   0.054250
[2025-10-02 19:44:57] [Iter   52/2250] R0[102/150]  | LR: 0.010802 | E: -18.611407 | E_var:    12.9587 | E_err:   0.056247
[2025-10-02 19:45:01] [Iter   53/2250] R0[104/150]  | LR: 0.010366 | E: -18.729359 | E_var:    11.5625 | E_err:   0.053131
[2025-10-02 19:45:05] [Iter   54/2250] R0[106/150]  | LR: 0.009943 | E: -18.856023 | E_var:    11.4807 | E_err:   0.052943
[2025-10-02 19:45:08] [Iter   55/2250] R0[108/150]  | LR: 0.009532 | E: -18.955919 | E_var:    10.6362 | E_err:   0.050958
[2025-10-02 19:45:12] [Iter   56/2250] R0[110/150]  | LR: 0.009136 | E: -19.110479 | E_var:    10.6315 | E_err:   0.050947
[2025-10-02 19:45:16] [Iter   57/2250] R0[112/150]  | LR: 0.008754 | E: -19.283085 | E_var:    10.7416 | E_err:   0.051210
[2025-10-02 19:45:19] [Iter   58/2250] R0[114/150]  | LR: 0.008388 | E: -19.467683 | E_var:    12.8260 | E_err:   0.055958
[2025-10-02 19:45:23] [Iter   59/2250] R0[116/150]  | LR: 0.008038 | E: -19.540495 | E_var:    10.8494 | E_err:   0.051466
[2025-10-02 19:45:27] [Iter   60/2250] R0[118/150]  | LR: 0.007704 | E: -19.681850 | E_var:    11.0174 | E_err:   0.051863
[2025-10-02 19:45:30] [Iter   61/2250] R0[120/150]  | LR: 0.007387 | E: -19.678702 | E_var:    10.9242 | E_err:   0.051643
[2025-10-02 19:45:34] [Iter   62/2250] R0[122/150]  | LR: 0.007088 | E: -19.903995 | E_var:    10.1385 | E_err:   0.049752
[2025-10-02 19:45:38] [Iter   63/2250] R0[124/150]  | LR: 0.006808 | E: -20.006895 | E_var:    13.0531 | E_err:   0.056452
[2025-10-02 19:45:41] [Iter   64/2250] R0[126/150]  | LR: 0.006546 | E: -20.015624 | E_var:    10.1655 | E_err:   0.049818
[2025-10-02 19:45:45] [Iter   65/2250] R0[128/150]  | LR: 0.006304 | E: -20.157548 | E_var:     9.6837 | E_err:   0.048623
[2025-10-02 19:45:49] [Iter   66/2250] R0[130/150]  | LR: 0.006081 | E: -20.222135 | E_var:     9.6603 | E_err:   0.048564
[2025-10-02 19:45:52] [Iter   67/2250] R0[132/150]  | LR: 0.005878 | E: -20.297916 | E_var:     9.9488 | E_err:   0.049284
[2025-10-02 19:45:56] [Iter   68/2250] R0[134/150]  | LR: 0.005695 | E: -20.348207 | E_var:     8.5047 | E_err:   0.045567
[2025-10-02 19:46:00] [Iter   69/2250] R0[136/150]  | LR: 0.005534 | E: -20.449189 | E_var:     8.5926 | E_err:   0.045802
[2025-10-02 19:46:03] [Iter   70/2250] R0[138/150]  | LR: 0.005393 | E: -20.579122 | E_var:     9.2874 | E_err:   0.047617
[2025-10-02 19:46:07] [Iter   71/2250] R0[140/150]  | LR: 0.005273 | E: -20.640904 | E_var:     9.4849 | E_err:   0.048121
[2025-10-02 19:46:10] [Iter   72/2250] R0[142/150]  | LR: 0.005175 | E: -20.778095 | E_var:     9.0884 | E_err:   0.047105
[2025-10-02 19:46:14] [Iter   73/2250] R0[144/150]  | LR: 0.005099 | E: -20.776929 | E_var:     8.5340 | E_err:   0.045645
[2025-10-02 19:46:18] [Iter   74/2250] R0[146/150]  | LR: 0.005044 | E: -20.845718 | E_var:     9.2766 | E_err:   0.047590
[2025-10-02 19:46:21] [Iter   75/2250] R0[148/150]  | LR: 0.005011 | E: -20.950789 | E_var:     9.1772 | E_err:   0.047334
[2025-10-02 19:46:21] 🔄 RESTART #1 | Period: 300
[2025-10-02 19:46:25] [Iter   76/2250] R1[0/300]    | LR: 0.030000 | E: -21.045217 | E_var:     8.7945 | E_err:   0.046337
[2025-10-02 19:46:29] [Iter   77/2250] R1[2/300]    | LR: 0.029997 | E: -21.091609 | E_var:     8.9773 | E_err:   0.046816
[2025-10-02 19:46:32] [Iter   78/2250] R1[4/300]    | LR: 0.029989 | E: -21.221458 | E_var:     8.5928 | E_err:   0.045802
[2025-10-02 19:46:36] [Iter   79/2250] R1[6/300]    | LR: 0.029975 | E: -21.228926 | E_var:     8.4307 | E_err:   0.045368
[2025-10-02 19:46:40] [Iter   80/2250] R1[8/300]    | LR: 0.029956 | E: -21.331238 | E_var:     8.7246 | E_err:   0.046152
[2025-10-02 19:46:43] [Iter   81/2250] R1[10/300]   | LR: 0.029932 | E: -21.440574 | E_var:     8.4830 | E_err:   0.045509
[2025-10-02 19:46:47] [Iter   82/2250] R1[12/300]   | LR: 0.029901 | E: -21.470592 | E_var:     9.1066 | E_err:   0.047152
[2025-10-02 19:46:51] [Iter   83/2250] R1[14/300]   | LR: 0.029866 | E: -21.556632 | E_var:     8.5191 | E_err:   0.045606
[2025-10-02 19:46:54] [Iter   84/2250] R1[16/300]   | LR: 0.029825 | E: -21.618880 | E_var:     8.1614 | E_err:   0.044638
[2025-10-02 19:46:58] [Iter   85/2250] R1[18/300]   | LR: 0.029779 | E: -21.711491 | E_var:     8.0535 | E_err:   0.044342
[2025-10-02 19:47:02] [Iter   86/2250] R1[20/300]   | LR: 0.029727 | E: -21.724749 | E_var:     7.9599 | E_err:   0.044083
[2025-10-02 19:47:05] [Iter   87/2250] R1[22/300]   | LR: 0.029670 | E: -21.770680 | E_var:     7.9390 | E_err:   0.044025
[2025-10-02 19:47:09] [Iter   88/2250] R1[24/300]   | LR: 0.029607 | E: -21.846684 | E_var:     8.3835 | E_err:   0.045241
[2025-10-02 19:47:13] [Iter   89/2250] R1[26/300]   | LR: 0.029540 | E: -21.887038 | E_var:     7.7531 | E_err:   0.043507
[2025-10-02 19:47:16] [Iter   90/2250] R1[28/300]   | LR: 0.029466 | E: -21.997335 | E_var:     7.3561 | E_err:   0.042378
[2025-10-02 19:47:20] [Iter   91/2250] R1[30/300]   | LR: 0.029388 | E: -22.039363 | E_var:     8.3853 | E_err:   0.045246
[2025-10-02 19:47:24] [Iter   92/2250] R1[32/300]   | LR: 0.029305 | E: -22.117436 | E_var:     7.5210 | E_err:   0.042851
[2025-10-02 19:47:27] [Iter   93/2250] R1[34/300]   | LR: 0.029216 | E: -22.129738 | E_var:     7.3625 | E_err:   0.042397
[2025-10-02 19:47:31] [Iter   94/2250] R1[36/300]   | LR: 0.029122 | E: -22.225602 | E_var:     7.4048 | E_err:   0.042519
[2025-10-02 19:47:35] [Iter   95/2250] R1[38/300]   | LR: 0.029023 | E: -22.266587 | E_var:     8.2854 | E_err:   0.044976
[2025-10-02 19:47:38] [Iter   96/2250] R1[40/300]   | LR: 0.028919 | E: -22.345108 | E_var:     7.5732 | E_err:   0.042999
[2025-10-02 19:47:42] [Iter   97/2250] R1[42/300]   | LR: 0.028810 | E: -22.412768 | E_var:     7.4708 | E_err:   0.042707
[2025-10-02 19:47:46] [Iter   98/2250] R1[44/300]   | LR: 0.028696 | E: -22.522375 | E_var:     7.3527 | E_err:   0.042369
[2025-10-02 19:47:49] [Iter   99/2250] R1[46/300]   | LR: 0.028578 | E: -22.572956 | E_var:     7.3608 | E_err:   0.042392
[2025-10-02 19:47:53] [Iter  100/2250] R1[48/300]   | LR: 0.028454 | E: -22.653087 | E_var:     7.2504 | E_err:   0.042073
[2025-10-02 19:47:56] [Iter  101/2250] R1[50/300]   | LR: 0.028325 | E: -22.700745 | E_var:     7.0087 | E_err:   0.041365
[2025-10-02 19:48:00] [Iter  102/2250] R1[52/300]   | LR: 0.028192 | E: -22.872209 | E_var:     7.7639 | E_err:   0.043537
[2025-10-02 19:48:04] [Iter  103/2250] R1[54/300]   | LR: 0.028054 | E: -22.949250 | E_var:     7.6497 | E_err:   0.043216
[2025-10-02 19:48:07] [Iter  104/2250] R1[56/300]   | LR: 0.027912 | E: -23.007514 | E_var:     7.8772 | E_err:   0.043854
[2025-10-02 19:48:11] [Iter  105/2250] R1[58/300]   | LR: 0.027764 | E: -23.125618 | E_var:     7.4870 | E_err:   0.042754
[2025-10-02 19:48:15] [Iter  106/2250] R1[60/300]   | LR: 0.027613 | E: -23.223636 | E_var:     7.3284 | E_err:   0.042299
[2025-10-02 19:48:18] [Iter  107/2250] R1[62/300]   | LR: 0.027457 | E: -23.432157 | E_var:     7.5998 | E_err:   0.043075
[2025-10-02 19:48:22] [Iter  108/2250] R1[64/300]   | LR: 0.027296 | E: -23.570366 | E_var:     7.8538 | E_err:   0.043789
[2025-10-02 19:48:26] [Iter  109/2250] R1[66/300]   | LR: 0.027131 | E: -23.787169 | E_var:     7.6971 | E_err:   0.043349
[2025-10-02 19:48:29] [Iter  110/2250] R1[68/300]   | LR: 0.026962 | E: -23.945069 | E_var:     8.1507 | E_err:   0.044609
[2025-10-02 19:48:33] [Iter  111/2250] R1[70/300]   | LR: 0.026789 | E: -24.152744 | E_var:     7.5810 | E_err:   0.043021
[2025-10-02 19:48:37] [Iter  112/2250] R1[72/300]   | LR: 0.026612 | E: -24.375817 | E_var:     7.6293 | E_err:   0.043158
[2025-10-02 19:48:40] [Iter  113/2250] R1[74/300]   | LR: 0.026431 | E: -24.648177 | E_var:     7.1417 | E_err:   0.041756
[2025-10-02 19:48:44] [Iter  114/2250] R1[76/300]   | LR: 0.026246 | E: -24.827794 | E_var:     6.8494 | E_err:   0.040893
[2025-10-02 19:48:48] [Iter  115/2250] R1[78/300]   | LR: 0.026057 | E: -25.045569 | E_var:     6.4210 | E_err:   0.039593
[2025-10-02 19:48:51] [Iter  116/2250] R1[80/300]   | LR: 0.025864 | E: -25.259427 | E_var:     6.1659 | E_err:   0.038799
[2025-10-02 19:48:55] [Iter  117/2250] R1[82/300]   | LR: 0.025668 | E: -25.482233 | E_var:     6.0449 | E_err:   0.038416
[2025-10-02 19:48:59] [Iter  118/2250] R1[84/300]   | LR: 0.025468 | E: -25.619320 | E_var:     5.3373 | E_err:   0.036098
[2025-10-02 19:49:02] [Iter  119/2250] R1[86/300]   | LR: 0.025264 | E: -25.799668 | E_var:     4.5523 | E_err:   0.033338
[2025-10-02 19:49:06] [Iter  120/2250] R1[88/300]   | LR: 0.025057 | E: -25.909326 | E_var:     3.9841 | E_err:   0.031188
[2025-10-02 19:49:10] [Iter  121/2250] R1[90/300]   | LR: 0.024847 | E: -26.088448 | E_var:     3.4936 | E_err:   0.029205
[2025-10-02 19:49:13] [Iter  122/2250] R1[92/300]   | LR: 0.024634 | E: -26.118920 | E_var:     3.1408 | E_err:   0.027691
[2025-10-02 19:49:17] [Iter  123/2250] R1[94/300]   | LR: 0.024417 | E: -26.196701 | E_var:     2.6645 | E_err:   0.025505
[2025-10-02 19:49:21] [Iter  124/2250] R1[96/300]   | LR: 0.024198 | E: -26.202668 | E_var:     3.0559 | E_err:   0.027314
[2025-10-02 19:49:24] [Iter  125/2250] R1[98/300]   | LR: 0.023975 | E: -26.311943 | E_var:     2.4847 | E_err:   0.024630
[2025-10-02 19:49:28] [Iter  126/2250] R1[100/300]  | LR: 0.023750 | E: -26.340606 | E_var:     2.4054 | E_err:   0.024233
[2025-10-02 19:49:32] [Iter  127/2250] R1[102/300]  | LR: 0.023522 | E: -26.412637 | E_var:     2.4186 | E_err:   0.024300
[2025-10-02 19:49:35] [Iter  128/2250] R1[104/300]  | LR: 0.023291 | E: -26.438594 | E_var:     2.0488 | E_err:   0.022365
[2025-10-02 19:49:39] [Iter  129/2250] R1[106/300]  | LR: 0.023058 | E: -26.432078 | E_var:     2.0122 | E_err:   0.022164
[2025-10-02 19:49:42] [Iter  130/2250] R1[108/300]  | LR: 0.022822 | E: -26.478714 | E_var:     1.9933 | E_err:   0.022060
[2025-10-02 19:49:46] [Iter  131/2250] R1[110/300]  | LR: 0.022584 | E: -26.501884 | E_var:     1.5463 | E_err:   0.019429
[2025-10-02 19:49:50] [Iter  132/2250] R1[112/300]  | LR: 0.022344 | E: -26.505102 | E_var:     1.8351 | E_err:   0.021167
[2025-10-02 19:49:53] [Iter  133/2250] R1[114/300]  | LR: 0.022102 | E: -26.504657 | E_var:     1.8708 | E_err:   0.021371
[2025-10-02 19:49:57] [Iter  134/2250] R1[116/300]  | LR: 0.021857 | E: -26.529977 | E_var:     1.6895 | E_err:   0.020310
[2025-10-02 19:50:01] [Iter  135/2250] R1[118/300]  | LR: 0.021611 | E: -26.558520 | E_var:     1.7354 | E_err:   0.020584
[2025-10-02 19:50:04] [Iter  136/2250] R1[120/300]  | LR: 0.021363 | E: -26.560569 | E_var:     1.4884 | E_err:   0.019062
[2025-10-02 19:50:08] [Iter  137/2250] R1[122/300]  | LR: 0.021113 | E: -26.526869 | E_var:     2.2931 | E_err:   0.023661
[2025-10-02 19:50:12] [Iter  138/2250] R1[124/300]  | LR: 0.020861 | E: -26.588704 | E_var:     1.7959 | E_err:   0.020939
[2025-10-02 19:50:15] [Iter  139/2250] R1[126/300]  | LR: 0.020609 | E: -26.597008 | E_var:     1.4419 | E_err:   0.018762
[2025-10-02 19:50:19] [Iter  140/2250] R1[128/300]  | LR: 0.020354 | E: -26.615188 | E_var:     1.2863 | E_err:   0.017721
[2025-10-02 19:50:23] [Iter  141/2250] R1[130/300]  | LR: 0.020099 | E: -26.609276 | E_var:     1.6565 | E_err:   0.020110
[2025-10-02 19:50:26] [Iter  142/2250] R1[132/300]  | LR: 0.019842 | E: -26.636533 | E_var:     1.4112 | E_err:   0.018561
[2025-10-02 19:50:30] [Iter  143/2250] R1[134/300]  | LR: 0.019585 | E: -26.628409 | E_var:     1.8864 | E_err:   0.021460
[2025-10-02 19:50:34] [Iter  144/2250] R1[136/300]  | LR: 0.019326 | E: -26.624526 | E_var:     1.6718 | E_err:   0.020203
[2025-10-02 19:50:37] [Iter  145/2250] R1[138/300]  | LR: 0.019067 | E: -26.640662 | E_var:     1.4835 | E_err:   0.019031
[2025-10-02 19:50:41] [Iter  146/2250] R1[140/300]  | LR: 0.018807 | E: -26.662211 | E_var:     1.8738 | E_err:   0.021389
[2025-10-02 19:50:45] [Iter  147/2250] R1[142/300]  | LR: 0.018546 | E: -26.667051 | E_var:     1.4647 | E_err:   0.018910
[2025-10-02 19:50:48] [Iter  148/2250] R1[144/300]  | LR: 0.018285 | E: -26.682081 | E_var:     1.2769 | E_err:   0.017656
[2025-10-02 19:50:52] [Iter  149/2250] R1[146/300]  | LR: 0.018023 | E: -26.667627 | E_var:     1.3910 | E_err:   0.018428
[2025-10-02 19:50:56] [Iter  150/2250] R1[148/300]  | LR: 0.017762 | E: -26.711712 | E_var:     1.3754 | E_err:   0.018325
[2025-10-02 19:50:59] [Iter  151/2250] R1[150/300]  | LR: 0.017500 | E: -26.697944 | E_var:     1.2991 | E_err:   0.017809
[2025-10-02 19:51:03] [Iter  152/2250] R1[152/300]  | LR: 0.017238 | E: -26.695448 | E_var:     1.3836 | E_err:   0.018379
[2025-10-02 19:51:07] [Iter  153/2250] R1[154/300]  | LR: 0.016977 | E: -26.710233 | E_var:     1.3723 | E_err:   0.018304
[2025-10-02 19:51:10] [Iter  154/2250] R1[156/300]  | LR: 0.016715 | E: -26.709437 | E_var:     1.5658 | E_err:   0.019552
[2025-10-02 19:51:14] [Iter  155/2250] R1[158/300]  | LR: 0.016454 | E: -26.718116 | E_var:     1.7387 | E_err:   0.020603
[2025-10-02 19:51:17] [Iter  156/2250] R1[160/300]  | LR: 0.016193 | E: -26.711900 | E_var:     1.2316 | E_err:   0.017340
[2025-10-02 19:51:21] [Iter  157/2250] R1[162/300]  | LR: 0.015933 | E: -26.732571 | E_var:     1.9965 | E_err:   0.022078
[2025-10-02 19:51:25] [Iter  158/2250] R1[164/300]  | LR: 0.015674 | E: -26.760695 | E_var:     1.1799 | E_err:   0.016972
[2025-10-02 19:51:28] [Iter  159/2250] R1[166/300]  | LR: 0.015415 | E: -26.760925 | E_var:     1.3487 | E_err:   0.018146
[2025-10-02 19:51:32] [Iter  160/2250] R1[168/300]  | LR: 0.015158 | E: -26.718203 | E_var:     1.2671 | E_err:   0.017588
[2025-10-02 19:51:36] [Iter  161/2250] R1[170/300]  | LR: 0.014901 | E: -26.726855 | E_var:     1.6147 | E_err:   0.019855
[2025-10-02 19:51:39] [Iter  162/2250] R1[172/300]  | LR: 0.014646 | E: -26.764539 | E_var:     1.1874 | E_err:   0.017026
[2025-10-02 19:51:43] [Iter  163/2250] R1[174/300]  | LR: 0.014391 | E: -26.751932 | E_var:     1.1722 | E_err:   0.016917
[2025-10-02 19:51:47] [Iter  164/2250] R1[176/300]  | LR: 0.014139 | E: -26.766941 | E_var:     1.0631 | E_err:   0.016110
[2025-10-02 19:51:50] [Iter  165/2250] R1[178/300]  | LR: 0.013887 | E: -26.804284 | E_var:     0.9349 | E_err:   0.015108
[2025-10-02 19:51:54] [Iter  166/2250] R1[180/300]  | LR: 0.013637 | E: -26.792141 | E_var:     0.9828 | E_err:   0.015490
[2025-10-02 19:51:58] [Iter  167/2250] R1[182/300]  | LR: 0.013389 | E: -26.783725 | E_var:     0.9695 | E_err:   0.015385
[2025-10-02 19:52:01] [Iter  168/2250] R1[184/300]  | LR: 0.013143 | E: -26.795810 | E_var:     1.0206 | E_err:   0.015785
[2025-10-02 19:52:05] [Iter  169/2250] R1[186/300]  | LR: 0.012898 | E: -26.819485 | E_var:     1.1204 | E_err:   0.016539
[2025-10-02 19:52:09] [Iter  170/2250] R1[188/300]  | LR: 0.012656 | E: -26.813770 | E_var:     0.8759 | E_err:   0.014623
[2025-10-02 19:52:12] [Iter  171/2250] R1[190/300]  | LR: 0.012416 | E: -26.800860 | E_var:     1.2142 | E_err:   0.017218
[2025-10-02 19:52:16] [Iter  172/2250] R1[192/300]  | LR: 0.012178 | E: -26.792662 | E_var:     1.2862 | E_err:   0.017720
[2025-10-02 19:52:20] [Iter  173/2250] R1[194/300]  | LR: 0.011942 | E: -26.807537 | E_var:     1.2526 | E_err:   0.017488
[2025-10-02 19:52:23] [Iter  174/2250] R1[196/300]  | LR: 0.011709 | E: -26.842063 | E_var:     1.4321 | E_err:   0.018699
[2025-10-02 19:52:27] [Iter  175/2250] R1[198/300]  | LR: 0.011478 | E: -26.829380 | E_var:     1.0390 | E_err:   0.015927
[2025-10-02 19:52:31] [Iter  176/2250] R1[200/300]  | LR: 0.011250 | E: -26.839860 | E_var:     0.9129 | E_err:   0.014929
[2025-10-02 19:52:34] [Iter  177/2250] R1[202/300]  | LR: 0.011025 | E: -26.834030 | E_var:     0.8859 | E_err:   0.014707
[2025-10-02 19:52:38] [Iter  178/2250] R1[204/300]  | LR: 0.010802 | E: -26.828388 | E_var:     1.0553 | E_err:   0.016052
[2025-10-02 19:52:41] [Iter  179/2250] R1[206/300]  | LR: 0.010583 | E: -26.847911 | E_var:     0.9542 | E_err:   0.015263
[2025-10-02 19:52:45] [Iter  180/2250] R1[208/300]  | LR: 0.010366 | E: -26.843431 | E_var:     1.0496 | E_err:   0.016008
[2025-10-02 19:52:49] [Iter  181/2250] R1[210/300]  | LR: 0.010153 | E: -26.850722 | E_var:     1.0082 | E_err:   0.015689
[2025-10-02 19:52:52] [Iter  182/2250] R1[212/300]  | LR: 0.009943 | E: -26.865954 | E_var:     1.0923 | E_err:   0.016330
[2025-10-02 19:52:56] [Iter  183/2250] R1[214/300]  | LR: 0.009736 | E: -26.856366 | E_var:     0.8949 | E_err:   0.014781
[2025-10-02 19:53:00] [Iter  184/2250] R1[216/300]  | LR: 0.009532 | E: -26.879863 | E_var:     0.7911 | E_err:   0.013898
[2025-10-02 19:53:03] [Iter  185/2250] R1[218/300]  | LR: 0.009332 | E: -26.873230 | E_var:     0.9499 | E_err:   0.015229
[2025-10-02 19:53:07] [Iter  186/2250] R1[220/300]  | LR: 0.009136 | E: -26.841169 | E_var:     1.3005 | E_err:   0.017819
[2025-10-02 19:53:11] [Iter  187/2250] R1[222/300]  | LR: 0.008943 | E: -26.880168 | E_var:     0.7455 | E_err:   0.013491
[2025-10-02 19:53:14] [Iter  188/2250] R1[224/300]  | LR: 0.008754 | E: -26.868313 | E_var:     0.9790 | E_err:   0.015460
[2025-10-02 19:53:18] [Iter  189/2250] R1[226/300]  | LR: 0.008569 | E: -26.896167 | E_var:     0.8576 | E_err:   0.014470
[2025-10-02 19:53:22] [Iter  190/2250] R1[228/300]  | LR: 0.008388 | E: -26.869165 | E_var:     0.9661 | E_err:   0.015358
[2025-10-02 19:53:25] [Iter  191/2250] R1[230/300]  | LR: 0.008211 | E: -26.888494 | E_var:     0.7662 | E_err:   0.013677
[2025-10-02 19:53:29] [Iter  192/2250] R1[232/300]  | LR: 0.008038 | E: -26.900371 | E_var:     0.7545 | E_err:   0.013572
[2025-10-02 19:53:33] [Iter  193/2250] R1[234/300]  | LR: 0.007869 | E: -26.881239 | E_var:     0.7479 | E_err:   0.013513
[2025-10-02 19:53:36] [Iter  194/2250] R1[236/300]  | LR: 0.007704 | E: -26.910498 | E_var:     0.8987 | E_err:   0.014813
[2025-10-02 19:53:40] [Iter  195/2250] R1[238/300]  | LR: 0.007543 | E: -26.892125 | E_var:     0.9154 | E_err:   0.014950
[2025-10-02 19:53:44] [Iter  196/2250] R1[240/300]  | LR: 0.007387 | E: -26.920124 | E_var:     0.7725 | E_err:   0.013733
[2025-10-02 19:53:47] [Iter  197/2250] R1[242/300]  | LR: 0.007236 | E: -26.894976 | E_var:     1.0870 | E_err:   0.016291
[2025-10-02 19:53:51] [Iter  198/2250] R1[244/300]  | LR: 0.007088 | E: -26.906158 | E_var:     1.0952 | E_err:   0.016352
[2025-10-02 19:53:55] [Iter  199/2250] R1[246/300]  | LR: 0.006946 | E: -26.914989 | E_var:     0.5962 | E_err:   0.012065
[2025-10-02 19:53:58] [Iter  200/2250] R1[248/300]  | LR: 0.006808 | E: -26.904015 | E_var:     0.8201 | E_err:   0.014150
[2025-10-02 19:53:58] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-02 19:54:02] [Iter  201/2250] R1[250/300]  | LR: 0.006675 | E: -26.908246 | E_var:     1.0321 | E_err:   0.015874
[2025-10-02 19:54:06] [Iter  202/2250] R1[252/300]  | LR: 0.006546 | E: -26.902917 | E_var:     1.3803 | E_err:   0.018357
[2025-10-02 19:54:09] [Iter  203/2250] R1[254/300]  | LR: 0.006422 | E: -26.942832 | E_var:     0.7884 | E_err:   0.013874
[2025-10-02 19:54:13] [Iter  204/2250] R1[256/300]  | LR: 0.006304 | E: -26.921653 | E_var:     0.7574 | E_err:   0.013598
[2025-10-02 19:54:17] [Iter  205/2250] R1[258/300]  | LR: 0.006190 | E: -26.928326 | E_var:     1.1795 | E_err:   0.016969
[2025-10-02 19:54:20] [Iter  206/2250] R1[260/300]  | LR: 0.006081 | E: -26.921800 | E_var:     0.8769 | E_err:   0.014632
[2025-10-02 19:54:24] [Iter  207/2250] R1[262/300]  | LR: 0.005977 | E: -26.911357 | E_var:     1.0193 | E_err:   0.015775
[2025-10-02 19:54:27] [Iter  208/2250] R1[264/300]  | LR: 0.005878 | E: -26.935747 | E_var:     0.7555 | E_err:   0.013581
[2025-10-02 19:54:31] [Iter  209/2250] R1[266/300]  | LR: 0.005784 | E: -26.950195 | E_var:     0.7971 | E_err:   0.013950
[2025-10-02 19:54:35] [Iter  210/2250] R1[268/300]  | LR: 0.005695 | E: -26.931547 | E_var:     0.8723 | E_err:   0.014593
[2025-10-02 19:54:38] [Iter  211/2250] R1[270/300]  | LR: 0.005612 | E: -26.952231 | E_var:     0.6744 | E_err:   0.012832
[2025-10-02 19:54:42] [Iter  212/2250] R1[272/300]  | LR: 0.005534 | E: -26.943752 | E_var:     0.6283 | E_err:   0.012386
[2025-10-02 19:54:46] [Iter  213/2250] R1[274/300]  | LR: 0.005460 | E: -26.946001 | E_var:     0.6699 | E_err:   0.012789
[2025-10-02 19:54:49] [Iter  214/2250] R1[276/300]  | LR: 0.005393 | E: -26.944762 | E_var:     0.7241 | E_err:   0.013296
[2025-10-02 19:54:53] [Iter  215/2250] R1[278/300]  | LR: 0.005330 | E: -26.970481 | E_var:     0.8907 | E_err:   0.014747
[2025-10-02 19:54:57] [Iter  216/2250] R1[280/300]  | LR: 0.005273 | E: -26.964641 | E_var:     0.8053 | E_err:   0.014021
[2025-10-02 19:55:00] [Iter  217/2250] R1[282/300]  | LR: 0.005221 | E: -26.942359 | E_var:     0.6353 | E_err:   0.012454
[2025-10-02 19:55:04] [Iter  218/2250] R1[284/300]  | LR: 0.005175 | E: -26.954517 | E_var:     1.1501 | E_err:   0.016756
[2025-10-02 19:55:08] [Iter  219/2250] R1[286/300]  | LR: 0.005134 | E: -26.947042 | E_var:     0.6885 | E_err:   0.012965
[2025-10-02 19:55:11] [Iter  220/2250] R1[288/300]  | LR: 0.005099 | E: -26.957953 | E_var:     0.6240 | E_err:   0.012343
[2025-10-02 19:55:15] [Iter  221/2250] R1[290/300]  | LR: 0.005068 | E: -26.956135 | E_var:     0.6776 | E_err:   0.012862
[2025-10-02 19:55:19] [Iter  222/2250] R1[292/300]  | LR: 0.005044 | E: -26.947499 | E_var:     1.1918 | E_err:   0.017057
[2025-10-02 19:55:22] [Iter  223/2250] R1[294/300]  | LR: 0.005025 | E: -26.970903 | E_var:     0.6662 | E_err:   0.012753
[2025-10-02 19:55:26] [Iter  224/2250] R1[296/300]  | LR: 0.005011 | E: -26.958278 | E_var:     0.9721 | E_err:   0.015405
[2025-10-02 19:55:30] [Iter  225/2250] R1[298/300]  | LR: 0.005003 | E: -26.938390 | E_var:     1.0959 | E_err:   0.016357
[2025-10-02 19:55:30] 🔄 RESTART #2 | Period: 600
[2025-10-02 19:55:33] [Iter  226/2250] R2[0/600]    | LR: 0.030000 | E: -26.962788 | E_var:     0.5867 | E_err:   0.011968
[2025-10-02 19:55:37] [Iter  227/2250] R2[2/600]    | LR: 0.029999 | E: -26.969644 | E_var:     0.6538 | E_err:   0.012634
[2025-10-02 19:55:41] [Iter  228/2250] R2[4/600]    | LR: 0.029997 | E: -26.963677 | E_var:     0.7745 | E_err:   0.013751
[2025-10-02 19:55:44] [Iter  229/2250] R2[6/600]    | LR: 0.029994 | E: -26.967947 | E_var:     0.6976 | E_err:   0.013050
[2025-10-02 19:55:48] [Iter  230/2250] R2[8/600]    | LR: 0.029989 | E: -26.976099 | E_var:     0.5278 | E_err:   0.011351
[2025-10-02 19:55:51] [Iter  231/2250] R2[10/600]   | LR: 0.029983 | E: -26.962280 | E_var:     0.7334 | E_err:   0.013381
[2025-10-02 19:55:55] [Iter  232/2250] R2[12/600]   | LR: 0.029975 | E: -26.964572 | E_var:     0.5592 | E_err:   0.011684
[2025-10-02 19:55:59] [Iter  233/2250] R2[14/600]   | LR: 0.029966 | E: -26.967001 | E_var:     0.6991 | E_err:   0.013064
[2025-10-02 19:56:02] [Iter  234/2250] R2[16/600]   | LR: 0.029956 | E: -26.979164 | E_var:     0.5990 | E_err:   0.012093
[2025-10-02 19:56:06] [Iter  235/2250] R2[18/600]   | LR: 0.029945 | E: -26.972497 | E_var:     0.5907 | E_err:   0.012009
[2025-10-02 19:56:10] [Iter  236/2250] R2[20/600]   | LR: 0.029932 | E: -26.963680 | E_var:     0.5914 | E_err:   0.012016
[2025-10-02 19:56:13] [Iter  237/2250] R2[22/600]   | LR: 0.029917 | E: -26.979147 | E_var:     1.4820 | E_err:   0.019022
[2025-10-02 19:56:17] [Iter  238/2250] R2[24/600]   | LR: 0.029901 | E: -26.987698 | E_var:     0.5538 | E_err:   0.011628
[2025-10-02 19:56:21] [Iter  239/2250] R2[26/600]   | LR: 0.029884 | E: -26.982659 | E_var:     0.6010 | E_err:   0.012114
[2025-10-02 19:56:24] [Iter  240/2250] R2[28/600]   | LR: 0.029866 | E: -26.996528 | E_var:     0.5646 | E_err:   0.011741
[2025-10-02 19:56:28] [Iter  241/2250] R2[30/600]   | LR: 0.029846 | E: -26.982446 | E_var:     0.6554 | E_err:   0.012650
[2025-10-02 19:56:32] [Iter  242/2250] R2[32/600]   | LR: 0.029825 | E: -26.982517 | E_var:     0.5973 | E_err:   0.012076
[2025-10-02 19:56:35] [Iter  243/2250] R2[34/600]   | LR: 0.029802 | E: -26.984402 | E_var:     0.6768 | E_err:   0.012855
[2025-10-02 19:56:39] [Iter  244/2250] R2[36/600]   | LR: 0.029779 | E: -26.966822 | E_var:     0.6419 | E_err:   0.012519
[2025-10-02 19:56:43] [Iter  245/2250] R2[38/600]   | LR: 0.029753 | E: -26.992240 | E_var:     0.5497 | E_err:   0.011584
[2025-10-02 19:56:46] [Iter  246/2250] R2[40/600]   | LR: 0.029727 | E: -26.986085 | E_var:     0.5769 | E_err:   0.011867
[2025-10-02 19:56:50] [Iter  247/2250] R2[42/600]   | LR: 0.029699 | E: -26.974896 | E_var:     0.6012 | E_err:   0.012115
[2025-10-02 19:56:54] [Iter  248/2250] R2[44/600]   | LR: 0.029670 | E: -26.987359 | E_var:     1.5493 | E_err:   0.019448
[2025-10-02 19:56:57] [Iter  249/2250] R2[46/600]   | LR: 0.029639 | E: -26.990085 | E_var:     0.4854 | E_err:   0.010887
[2025-10-02 19:57:01] [Iter  250/2250] R2[48/600]   | LR: 0.029607 | E: -26.988139 | E_var:     0.5675 | E_err:   0.011771
[2025-10-02 19:57:05] [Iter  251/2250] R2[50/600]   | LR: 0.029574 | E: -27.001091 | E_var:     0.4970 | E_err:   0.011015
[2025-10-02 19:57:08] [Iter  252/2250] R2[52/600]   | LR: 0.029540 | E: -27.003818 | E_var:     0.5325 | E_err:   0.011402
[2025-10-02 19:57:12] [Iter  253/2250] R2[54/600]   | LR: 0.029504 | E: -26.997567 | E_var:     0.4754 | E_err:   0.010774
[2025-10-02 19:57:15] [Iter  254/2250] R2[56/600]   | LR: 0.029466 | E: -27.008715 | E_var:     0.4306 | E_err:   0.010253
[2025-10-02 19:57:19] [Iter  255/2250] R2[58/600]   | LR: 0.029428 | E: -26.986670 | E_var:     0.5353 | E_err:   0.011432
[2025-10-02 19:57:23] [Iter  256/2250] R2[60/600]   | LR: 0.029388 | E: -27.012988 | E_var:     0.5413 | E_err:   0.011496
[2025-10-02 19:57:26] [Iter  257/2250] R2[62/600]   | LR: 0.029347 | E: -27.005479 | E_var:     0.5974 | E_err:   0.012077
[2025-10-02 19:57:30] [Iter  258/2250] R2[64/600]   | LR: 0.029305 | E: -26.994233 | E_var:     0.5316 | E_err:   0.011392
[2025-10-02 19:57:34] [Iter  259/2250] R2[66/600]   | LR: 0.029261 | E: -27.005622 | E_var:     0.5383 | E_err:   0.011463
[2025-10-02 19:57:37] [Iter  260/2250] R2[68/600]   | LR: 0.029216 | E: -27.001438 | E_var:     0.4907 | E_err:   0.010945
[2025-10-02 19:57:41] [Iter  261/2250] R2[70/600]   | LR: 0.029170 | E: -27.000303 | E_var:     0.5552 | E_err:   0.011642
[2025-10-02 19:57:45] [Iter  262/2250] R2[72/600]   | LR: 0.029122 | E: -27.023952 | E_var:     0.7772 | E_err:   0.013775
[2025-10-02 19:57:48] [Iter  263/2250] R2[74/600]   | LR: 0.029073 | E: -27.021598 | E_var:     0.4830 | E_err:   0.010859
[2025-10-02 19:57:52] [Iter  264/2250] R2[76/600]   | LR: 0.029023 | E: -27.005422 | E_var:     0.4516 | E_err:   0.010500
[2025-10-02 19:57:56] [Iter  265/2250] R2[78/600]   | LR: 0.028972 | E: -27.019765 | E_var:     0.6585 | E_err:   0.012680
[2025-10-02 19:57:59] [Iter  266/2250] R2[80/600]   | LR: 0.028919 | E: -27.003093 | E_var:     0.6598 | E_err:   0.012692
[2025-10-02 19:58:03] [Iter  267/2250] R2[82/600]   | LR: 0.028865 | E: -27.010893 | E_var:     0.8856 | E_err:   0.014704
[2025-10-02 19:58:07] [Iter  268/2250] R2[84/600]   | LR: 0.028810 | E: -26.993802 | E_var:     0.6292 | E_err:   0.012394
[2025-10-02 19:58:10] [Iter  269/2250] R2[86/600]   | LR: 0.028754 | E: -27.003857 | E_var:     0.6179 | E_err:   0.012282
[2025-10-02 19:58:14] [Iter  270/2250] R2[88/600]   | LR: 0.028696 | E: -27.001799 | E_var:     0.5706 | E_err:   0.011803
[2025-10-02 19:58:18] [Iter  271/2250] R2[90/600]   | LR: 0.028638 | E: -27.017979 | E_var:     0.4984 | E_err:   0.011030
[2025-10-02 19:58:21] [Iter  272/2250] R2[92/600]   | LR: 0.028578 | E: -27.013500 | E_var:     0.4961 | E_err:   0.011006
[2025-10-02 19:58:25] [Iter  273/2250] R2[94/600]   | LR: 0.028516 | E: -27.013918 | E_var:     0.4430 | E_err:   0.010400
[2025-10-02 19:58:29] [Iter  274/2250] R2[96/600]   | LR: 0.028454 | E: -27.016292 | E_var:     0.4079 | E_err:   0.009979
[2025-10-02 19:58:32] [Iter  275/2250] R2[98/600]   | LR: 0.028390 | E: -27.026229 | E_var:     0.4713 | E_err:   0.010727
[2025-10-02 19:58:36] [Iter  276/2250] R2[100/600]  | LR: 0.028325 | E: -27.022208 | E_var:     0.4684 | E_err:   0.010694
[2025-10-02 19:58:40] [Iter  277/2250] R2[102/600]  | LR: 0.028259 | E: -27.025401 | E_var:     0.6677 | E_err:   0.012768
[2025-10-02 19:58:43] [Iter  278/2250] R2[104/600]  | LR: 0.028192 | E: -27.001324 | E_var:     0.4517 | E_err:   0.010501
[2025-10-02 19:58:47] [Iter  279/2250] R2[106/600]  | LR: 0.028124 | E: -27.031768 | E_var:     0.3950 | E_err:   0.009820
[2025-10-02 19:58:51] [Iter  280/2250] R2[108/600]  | LR: 0.028054 | E: -27.003800 | E_var:     0.5001 | E_err:   0.011049
[2025-10-02 19:58:54] [Iter  281/2250] R2[110/600]  | LR: 0.027983 | E: -27.033877 | E_var:     0.5189 | E_err:   0.011255
[2025-10-02 19:58:58] [Iter  282/2250] R2[112/600]  | LR: 0.027912 | E: -27.025750 | E_var:     0.5162 | E_err:   0.011227
[2025-10-02 19:59:01] [Iter  283/2250] R2[114/600]  | LR: 0.027839 | E: -27.018894 | E_var:     0.5830 | E_err:   0.011930
[2025-10-02 19:59:05] [Iter  284/2250] R2[116/600]  | LR: 0.027764 | E: -27.017042 | E_var:     0.6751 | E_err:   0.012838
[2025-10-02 19:59:09] [Iter  285/2250] R2[118/600]  | LR: 0.027689 | E: -27.011784 | E_var:     0.4332 | E_err:   0.010284
[2025-10-02 19:59:12] [Iter  286/2250] R2[120/600]  | LR: 0.027613 | E: -27.010835 | E_var:     0.5122 | E_err:   0.011182
[2025-10-02 19:59:16] [Iter  287/2250] R2[122/600]  | LR: 0.027535 | E: -27.031419 | E_var:     1.1842 | E_err:   0.017004
[2025-10-02 19:59:20] [Iter  288/2250] R2[124/600]  | LR: 0.027457 | E: -27.022926 | E_var:     0.5548 | E_err:   0.011638
[2025-10-02 19:59:23] [Iter  289/2250] R2[126/600]  | LR: 0.027377 | E: -27.022161 | E_var:     0.4264 | E_err:   0.010203
[2025-10-02 19:59:27] [Iter  290/2250] R2[128/600]  | LR: 0.027296 | E: -27.021286 | E_var:     0.4319 | E_err:   0.010268
[2025-10-02 19:59:31] [Iter  291/2250] R2[130/600]  | LR: 0.027214 | E: -27.020663 | E_var:     0.4357 | E_err:   0.010314
[2025-10-02 19:59:34] [Iter  292/2250] R2[132/600]  | LR: 0.027131 | E: -27.024730 | E_var:     0.4259 | E_err:   0.010197
[2025-10-02 19:59:38] [Iter  293/2250] R2[134/600]  | LR: 0.027047 | E: -27.032053 | E_var:     0.5043 | E_err:   0.011096
[2025-10-02 19:59:42] [Iter  294/2250] R2[136/600]  | LR: 0.026962 | E: -27.019740 | E_var:     0.4277 | E_err:   0.010218
[2025-10-02 19:59:45] [Iter  295/2250] R2[138/600]  | LR: 0.026876 | E: -27.042361 | E_var:     0.4418 | E_err:   0.010385
[2025-10-02 19:59:49] [Iter  296/2250] R2[140/600]  | LR: 0.026789 | E: -27.037772 | E_var:     0.4031 | E_err:   0.009920
[2025-10-02 19:59:53] [Iter  297/2250] R2[142/600]  | LR: 0.026701 | E: -27.021506 | E_var:     0.5153 | E_err:   0.011216
[2025-10-02 19:59:56] [Iter  298/2250] R2[144/600]  | LR: 0.026612 | E: -27.038584 | E_var:     0.4857 | E_err:   0.010889
[2025-10-02 20:00:00] [Iter  299/2250] R2[146/600]  | LR: 0.026522 | E: -27.035426 | E_var:     0.4285 | E_err:   0.010228
[2025-10-02 20:00:04] [Iter  300/2250] R2[148/600]  | LR: 0.026431 | E: -27.030204 | E_var:     0.5672 | E_err:   0.011768
[2025-10-02 20:00:07] [Iter  301/2250] R2[150/600]  | LR: 0.026339 | E: -27.028851 | E_var:     0.4184 | E_err:   0.010107
[2025-10-02 20:00:11] [Iter  302/2250] R2[152/600]  | LR: 0.026246 | E: -27.032777 | E_var:     0.4501 | E_err:   0.010483
[2025-10-02 20:00:15] [Iter  303/2250] R2[154/600]  | LR: 0.026152 | E: -27.037837 | E_var:     0.4248 | E_err:   0.010184
[2025-10-02 20:00:18] [Iter  304/2250] R2[156/600]  | LR: 0.026057 | E: -27.047497 | E_var:     0.4993 | E_err:   0.011041
[2025-10-02 20:00:22] [Iter  305/2250] R2[158/600]  | LR: 0.025961 | E: -27.028899 | E_var:     0.4591 | E_err:   0.010587
[2025-10-02 20:00:26] [Iter  306/2250] R2[160/600]  | LR: 0.025864 | E: -27.035701 | E_var:     0.4550 | E_err:   0.010540
[2025-10-02 20:00:29] [Iter  307/2250] R2[162/600]  | LR: 0.025766 | E: -27.045374 | E_var:     0.4550 | E_err:   0.010539
[2025-10-02 20:00:33] [Iter  308/2250] R2[164/600]  | LR: 0.025668 | E: -27.031301 | E_var:     0.5127 | E_err:   0.011188
[2025-10-02 20:00:36] [Iter  309/2250] R2[166/600]  | LR: 0.025568 | E: -27.038740 | E_var:     0.3731 | E_err:   0.009544
[2025-10-02 20:00:40] [Iter  310/2250] R2[168/600]  | LR: 0.025468 | E: -27.045017 | E_var:     0.3995 | E_err:   0.009876
[2025-10-02 20:00:44] [Iter  311/2250] R2[170/600]  | LR: 0.025367 | E: -27.027475 | E_var:     0.3622 | E_err:   0.009404
[2025-10-02 20:00:47] [Iter  312/2250] R2[172/600]  | LR: 0.025264 | E: -27.037466 | E_var:     0.5908 | E_err:   0.012010
[2025-10-02 20:00:51] [Iter  313/2250] R2[174/600]  | LR: 0.025161 | E: -27.048539 | E_var:     0.3848 | E_err:   0.009692
[2025-10-02 20:00:55] [Iter  314/2250] R2[176/600]  | LR: 0.025057 | E: -27.039449 | E_var:     0.3925 | E_err:   0.009789
[2025-10-02 20:00:58] [Iter  315/2250] R2[178/600]  | LR: 0.024953 | E: -27.043973 | E_var:     0.4933 | E_err:   0.010974
[2025-10-02 20:01:02] [Iter  316/2250] R2[180/600]  | LR: 0.024847 | E: -27.033011 | E_var:     0.5927 | E_err:   0.012029
[2025-10-02 20:01:06] [Iter  317/2250] R2[182/600]  | LR: 0.024741 | E: -27.033714 | E_var:     0.3103 | E_err:   0.008704
[2025-10-02 20:01:09] [Iter  318/2250] R2[184/600]  | LR: 0.024634 | E: -27.043720 | E_var:     0.5360 | E_err:   0.011440
[2025-10-02 20:01:13] [Iter  319/2250] R2[186/600]  | LR: 0.024526 | E: -27.049846 | E_var:     0.3809 | E_err:   0.009643
[2025-10-02 20:01:17] [Iter  320/2250] R2[188/600]  | LR: 0.024417 | E: -27.041596 | E_var:     0.3274 | E_err:   0.008941
[2025-10-02 20:01:20] [Iter  321/2250] R2[190/600]  | LR: 0.024308 | E: -27.042198 | E_var:     0.3943 | E_err:   0.009811
[2025-10-02 20:01:24] [Iter  322/2250] R2[192/600]  | LR: 0.024198 | E: -27.028774 | E_var:     0.3689 | E_err:   0.009490
[2025-10-02 20:01:28] [Iter  323/2250] R2[194/600]  | LR: 0.024087 | E: -27.033274 | E_var:     0.6623 | E_err:   0.012716
[2025-10-02 20:01:31] [Iter  324/2250] R2[196/600]  | LR: 0.023975 | E: -27.053149 | E_var:     0.4949 | E_err:   0.010992
[2025-10-02 20:01:35] [Iter  325/2250] R2[198/600]  | LR: 0.023863 | E: -27.050034 | E_var:     0.3952 | E_err:   0.009823
[2025-10-02 20:01:39] [Iter  326/2250] R2[200/600]  | LR: 0.023750 | E: -27.045267 | E_var:     0.3838 | E_err:   0.009679
[2025-10-02 20:01:42] [Iter  327/2250] R2[202/600]  | LR: 0.023636 | E: -27.043312 | E_var:     0.5633 | E_err:   0.011727
[2025-10-02 20:01:46] [Iter  328/2250] R2[204/600]  | LR: 0.023522 | E: -27.026835 | E_var:     0.4129 | E_err:   0.010040
[2025-10-02 20:01:50] [Iter  329/2250] R2[206/600]  | LR: 0.023407 | E: -27.061557 | E_var:     0.4321 | E_err:   0.010272
[2025-10-02 20:01:53] [Iter  330/2250] R2[208/600]  | LR: 0.023291 | E: -27.046648 | E_var:     0.3509 | E_err:   0.009255
[2025-10-02 20:01:57] [Iter  331/2250] R2[210/600]  | LR: 0.023175 | E: -27.055270 | E_var:     0.3007 | E_err:   0.008569
[2025-10-02 20:02:01] [Iter  332/2250] R2[212/600]  | LR: 0.023058 | E: -27.051069 | E_var:     0.4464 | E_err:   0.010439
[2025-10-02 20:02:04] [Iter  333/2250] R2[214/600]  | LR: 0.022940 | E: -27.050609 | E_var:     0.4965 | E_err:   0.011010
[2025-10-02 20:02:08] [Iter  334/2250] R2[216/600]  | LR: 0.022822 | E: -27.059833 | E_var:     0.4381 | E_err:   0.010342
[2025-10-02 20:02:11] [Iter  335/2250] R2[218/600]  | LR: 0.022704 | E: -27.045872 | E_var:     0.4095 | E_err:   0.009998
[2025-10-02 20:02:15] [Iter  336/2250] R2[220/600]  | LR: 0.022584 | E: -27.059065 | E_var:     0.3327 | E_err:   0.009012
[2025-10-02 20:02:19] [Iter  337/2250] R2[222/600]  | LR: 0.022464 | E: -27.049551 | E_var:     0.3832 | E_err:   0.009673
[2025-10-02 20:02:22] [Iter  338/2250] R2[224/600]  | LR: 0.022344 | E: -27.049636 | E_var:     0.4701 | E_err:   0.010713
[2025-10-02 20:02:26] [Iter  339/2250] R2[226/600]  | LR: 0.022223 | E: -27.029301 | E_var:     0.9445 | E_err:   0.015185
[2025-10-02 20:02:30] [Iter  340/2250] R2[228/600]  | LR: 0.022102 | E: -27.047233 | E_var:     0.3353 | E_err:   0.009048
[2025-10-02 20:02:33] [Iter  341/2250] R2[230/600]  | LR: 0.021980 | E: -27.056143 | E_var:     0.4892 | E_err:   0.010929
[2025-10-02 20:02:37] [Iter  342/2250] R2[232/600]  | LR: 0.021857 | E: -27.068752 | E_var:     0.4420 | E_err:   0.010388
[2025-10-02 20:02:41] [Iter  343/2250] R2[234/600]  | LR: 0.021734 | E: -27.058363 | E_var:     0.3929 | E_err:   0.009794
[2025-10-02 20:02:44] [Iter  344/2250] R2[236/600]  | LR: 0.021611 | E: -27.054771 | E_var:     0.3634 | E_err:   0.009419
[2025-10-02 20:02:48] [Iter  345/2250] R2[238/600]  | LR: 0.021487 | E: -27.052799 | E_var:     0.4115 | E_err:   0.010023
[2025-10-02 20:02:52] [Iter  346/2250] R2[240/600]  | LR: 0.021363 | E: -27.063500 | E_var:     0.3632 | E_err:   0.009416
[2025-10-02 20:02:55] [Iter  347/2250] R2[242/600]  | LR: 0.021238 | E: -27.043567 | E_var:     0.3658 | E_err:   0.009450
[2025-10-02 20:02:59] [Iter  348/2250] R2[244/600]  | LR: 0.021113 | E: -27.069199 | E_var:     0.3737 | E_err:   0.009551
[2025-10-02 20:03:03] [Iter  349/2250] R2[246/600]  | LR: 0.020987 | E: -27.048399 | E_var:     0.4379 | E_err:   0.010340
[2025-10-02 20:03:06] [Iter  350/2250] R2[248/600]  | LR: 0.020861 | E: -27.056756 | E_var:     0.3119 | E_err:   0.008727
[2025-10-02 20:03:10] [Iter  351/2250] R2[250/600]  | LR: 0.020735 | E: -27.047015 | E_var:     0.3312 | E_err:   0.008992
[2025-10-02 20:03:14] [Iter  352/2250] R2[252/600]  | LR: 0.020609 | E: -27.056841 | E_var:     0.4236 | E_err:   0.010170
[2025-10-02 20:03:17] [Iter  353/2250] R2[254/600]  | LR: 0.020482 | E: -27.062368 | E_var:     0.3707 | E_err:   0.009514
[2025-10-02 20:03:21] [Iter  354/2250] R2[256/600]  | LR: 0.020354 | E: -27.051208 | E_var:     0.3618 | E_err:   0.009398
[2025-10-02 20:03:25] [Iter  355/2250] R2[258/600]  | LR: 0.020227 | E: -27.044811 | E_var:     0.5308 | E_err:   0.011384
[2025-10-02 20:03:28] [Iter  356/2250] R2[260/600]  | LR: 0.020099 | E: -27.061429 | E_var:     0.4045 | E_err:   0.009938
[2025-10-02 20:03:32] [Iter  357/2250] R2[262/600]  | LR: 0.019971 | E: -27.076010 | E_var:     0.4245 | E_err:   0.010180
[2025-10-02 20:03:35] [Iter  358/2250] R2[264/600]  | LR: 0.019842 | E: -27.055518 | E_var:     0.3824 | E_err:   0.009662
[2025-10-02 20:03:39] [Iter  359/2250] R2[266/600]  | LR: 0.019714 | E: -27.050036 | E_var:     0.3570 | E_err:   0.009336
[2025-10-02 20:03:43] [Iter  360/2250] R2[268/600]  | LR: 0.019585 | E: -27.061038 | E_var:     0.3277 | E_err:   0.008944
[2025-10-02 20:03:46] [Iter  361/2250] R2[270/600]  | LR: 0.019455 | E: -27.064181 | E_var:     0.5770 | E_err:   0.011869
[2025-10-02 20:03:50] [Iter  362/2250] R2[272/600]  | LR: 0.019326 | E: -27.069011 | E_var:     0.3681 | E_err:   0.009480
[2025-10-02 20:03:54] [Iter  363/2250] R2[274/600]  | LR: 0.019196 | E: -27.062323 | E_var:     0.4144 | E_err:   0.010059
[2025-10-02 20:03:57] [Iter  364/2250] R2[276/600]  | LR: 0.019067 | E: -27.066526 | E_var:     0.2976 | E_err:   0.008524
[2025-10-02 20:04:01] [Iter  365/2250] R2[278/600]  | LR: 0.018937 | E: -27.045297 | E_var:     0.3760 | E_err:   0.009581
[2025-10-02 20:04:05] [Iter  366/2250] R2[280/600]  | LR: 0.018807 | E: -27.067103 | E_var:     0.3962 | E_err:   0.009835
[2025-10-02 20:04:08] [Iter  367/2250] R2[282/600]  | LR: 0.018676 | E: -27.053623 | E_var:     0.3687 | E_err:   0.009488
[2025-10-02 20:04:12] [Iter  368/2250] R2[284/600]  | LR: 0.018546 | E: -27.065451 | E_var:     0.3793 | E_err:   0.009623
[2025-10-02 20:04:16] [Iter  369/2250] R2[286/600]  | LR: 0.018415 | E: -27.063689 | E_var:     0.4518 | E_err:   0.010502
[2025-10-02 20:04:19] [Iter  370/2250] R2[288/600]  | LR: 0.018285 | E: -27.050549 | E_var:     0.4264 | E_err:   0.010203
[2025-10-02 20:04:23] [Iter  371/2250] R2[290/600]  | LR: 0.018154 | E: -27.069036 | E_var:     0.3576 | E_err:   0.009343
[2025-10-02 20:04:27] [Iter  372/2250] R2[292/600]  | LR: 0.018023 | E: -27.060914 | E_var:     0.3323 | E_err:   0.009008
[2025-10-02 20:04:30] [Iter  373/2250] R2[294/600]  | LR: 0.017893 | E: -27.061962 | E_var:     0.4225 | E_err:   0.010156
[2025-10-02 20:04:34] [Iter  374/2250] R2[296/600]  | LR: 0.017762 | E: -27.067253 | E_var:     0.3130 | E_err:   0.008742
[2025-10-02 20:04:38] [Iter  375/2250] R2[298/600]  | LR: 0.017631 | E: -27.058945 | E_var:     0.5383 | E_err:   0.011464
[2025-10-02 20:04:41] [Iter  376/2250] R2[300/600]  | LR: 0.017500 | E: -27.072715 | E_var:     0.3245 | E_err:   0.008901
[2025-10-02 20:04:45] [Iter  377/2250] R2[302/600]  | LR: 0.017369 | E: -27.067763 | E_var:     0.3499 | E_err:   0.009242
[2025-10-02 20:04:48] [Iter  378/2250] R2[304/600]  | LR: 0.017238 | E: -27.055059 | E_var:     0.3613 | E_err:   0.009392
[2025-10-02 20:04:52] [Iter  379/2250] R2[306/600]  | LR: 0.017107 | E: -27.069521 | E_var:     0.3147 | E_err:   0.008766
[2025-10-02 20:04:56] [Iter  380/2250] R2[308/600]  | LR: 0.016977 | E: -27.063218 | E_var:     0.4059 | E_err:   0.009954
[2025-10-02 20:04:59] [Iter  381/2250] R2[310/600]  | LR: 0.016846 | E: -27.053108 | E_var:     0.3981 | E_err:   0.009858
[2025-10-02 20:05:03] [Iter  382/2250] R2[312/600]  | LR: 0.016715 | E: -27.056181 | E_var:     0.4402 | E_err:   0.010367
[2025-10-02 20:05:07] [Iter  383/2250] R2[314/600]  | LR: 0.016585 | E: -27.058816 | E_var:     0.3737 | E_err:   0.009552
[2025-10-02 20:05:10] [Iter  384/2250] R2[316/600]  | LR: 0.016454 | E: -27.065733 | E_var:     0.3598 | E_err:   0.009372
[2025-10-02 20:05:14] [Iter  385/2250] R2[318/600]  | LR: 0.016324 | E: -27.052896 | E_var:     0.4663 | E_err:   0.010670
[2025-10-02 20:05:18] [Iter  386/2250] R2[320/600]  | LR: 0.016193 | E: -27.042883 | E_var:     0.4167 | E_err:   0.010087
[2025-10-02 20:05:21] [Iter  387/2250] R2[322/600]  | LR: 0.016063 | E: -27.064649 | E_var:     0.3429 | E_err:   0.009150
[2025-10-02 20:05:25] [Iter  388/2250] R2[324/600]  | LR: 0.015933 | E: -27.059454 | E_var:     0.3142 | E_err:   0.008759
[2025-10-02 20:05:29] [Iter  389/2250] R2[326/600]  | LR: 0.015804 | E: -27.065406 | E_var:     0.3223 | E_err:   0.008871
[2025-10-02 20:05:32] [Iter  390/2250] R2[328/600]  | LR: 0.015674 | E: -27.060131 | E_var:     0.2933 | E_err:   0.008463
[2025-10-02 20:05:36] [Iter  391/2250] R2[330/600]  | LR: 0.015545 | E: -27.062951 | E_var:     0.3937 | E_err:   0.009804
[2025-10-02 20:05:40] [Iter  392/2250] R2[332/600]  | LR: 0.015415 | E: -27.074400 | E_var:     0.3285 | E_err:   0.008955
[2025-10-02 20:05:43] [Iter  393/2250] R2[334/600]  | LR: 0.015286 | E: -27.067199 | E_var:     0.3113 | E_err:   0.008718
[2025-10-02 20:05:47] [Iter  394/2250] R2[336/600]  | LR: 0.015158 | E: -27.073922 | E_var:     0.3165 | E_err:   0.008790
[2025-10-02 20:05:51] [Iter  395/2250] R2[338/600]  | LR: 0.015029 | E: -27.054178 | E_var:     0.4319 | E_err:   0.010268
[2025-10-02 20:05:54] [Iter  396/2250] R2[340/600]  | LR: 0.014901 | E: -27.041736 | E_var:     0.7989 | E_err:   0.013965
[2025-10-02 20:05:58] [Iter  397/2250] R2[342/600]  | LR: 0.014773 | E: -27.079033 | E_var:     0.3366 | E_err:   0.009065
[2025-10-02 20:06:02] [Iter  398/2250] R2[344/600]  | LR: 0.014646 | E: -27.079149 | E_var:     0.3324 | E_err:   0.009009
[2025-10-02 20:06:05] [Iter  399/2250] R2[346/600]  | LR: 0.014518 | E: -27.069990 | E_var:     0.2714 | E_err:   0.008140
[2025-10-02 20:06:09] [Iter  400/2250] R2[348/600]  | LR: 0.014391 | E: -27.060676 | E_var:     0.4334 | E_err:   0.010287
[2025-10-02 20:06:09] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-02 20:06:13] [Iter  401/2250] R2[350/600]  | LR: 0.014265 | E: -27.061262 | E_var:     0.3864 | E_err:   0.009713
[2025-10-02 20:06:16] [Iter  402/2250] R2[352/600]  | LR: 0.014139 | E: -27.071685 | E_var:     0.3273 | E_err:   0.008939
[2025-10-02 20:06:20] [Iter  403/2250] R2[354/600]  | LR: 0.014013 | E: -27.065168 | E_var:     0.3335 | E_err:   0.009024
[2025-10-02 20:06:24] [Iter  404/2250] R2[356/600]  | LR: 0.013887 | E: -27.075631 | E_var:     0.3245 | E_err:   0.008901
[2025-10-02 20:06:27] [Iter  405/2250] R2[358/600]  | LR: 0.013762 | E: -27.083855 | E_var:     0.3085 | E_err:   0.008679
[2025-10-02 20:06:31] [Iter  406/2250] R2[360/600]  | LR: 0.013637 | E: -27.056711 | E_var:     0.3561 | E_err:   0.009325
[2025-10-02 20:06:35] [Iter  407/2250] R2[362/600]  | LR: 0.013513 | E: -27.068261 | E_var:     0.2903 | E_err:   0.008419
[2025-10-02 20:06:38] [Iter  408/2250] R2[364/600]  | LR: 0.013389 | E: -27.068083 | E_var:     0.3945 | E_err:   0.009814
[2025-10-02 20:06:42] [Iter  409/2250] R2[366/600]  | LR: 0.013266 | E: -27.076181 | E_var:     0.3343 | E_err:   0.009035
[2025-10-02 20:06:46] [Iter  410/2250] R2[368/600]  | LR: 0.013143 | E: -27.069383 | E_var:     0.3249 | E_err:   0.008906
[2025-10-02 20:06:49] [Iter  411/2250] R2[370/600]  | LR: 0.013020 | E: -27.077174 | E_var:     0.3264 | E_err:   0.008926
[2025-10-02 20:06:53] [Iter  412/2250] R2[372/600]  | LR: 0.012898 | E: -27.070331 | E_var:     0.3209 | E_err:   0.008851
[2025-10-02 20:06:56] [Iter  413/2250] R2[374/600]  | LR: 0.012777 | E: -27.058741 | E_var:     0.3929 | E_err:   0.009794
[2025-10-02 20:07:00] [Iter  414/2250] R2[376/600]  | LR: 0.012656 | E: -27.077005 | E_var:     0.3941 | E_err:   0.009809
[2025-10-02 20:07:04] [Iter  415/2250] R2[378/600]  | LR: 0.012536 | E: -27.080134 | E_var:     0.3688 | E_err:   0.009489
[2025-10-02 20:07:07] [Iter  416/2250] R2[380/600]  | LR: 0.012416 | E: -27.066154 | E_var:     0.3015 | E_err:   0.008580
[2025-10-02 20:07:11] [Iter  417/2250] R2[382/600]  | LR: 0.012296 | E: -27.077024 | E_var:     0.3110 | E_err:   0.008714
[2025-10-02 20:07:15] [Iter  418/2250] R2[384/600]  | LR: 0.012178 | E: -27.075053 | E_var:     0.3834 | E_err:   0.009675
[2025-10-02 20:07:18] [Iter  419/2250] R2[386/600]  | LR: 0.012060 | E: -27.080067 | E_var:     0.3596 | E_err:   0.009370
[2025-10-02 20:07:22] [Iter  420/2250] R2[388/600]  | LR: 0.011942 | E: -27.068012 | E_var:     0.3361 | E_err:   0.009059
[2025-10-02 20:07:26] [Iter  421/2250] R2[390/600]  | LR: 0.011825 | E: -27.076799 | E_var:     0.3402 | E_err:   0.009114
[2025-10-02 20:07:29] [Iter  422/2250] R2[392/600]  | LR: 0.011709 | E: -27.076089 | E_var:     0.3021 | E_err:   0.008587
[2025-10-02 20:07:33] [Iter  423/2250] R2[394/600]  | LR: 0.011593 | E: -27.082961 | E_var:     0.2889 | E_err:   0.008399
[2025-10-02 20:07:37] [Iter  424/2250] R2[396/600]  | LR: 0.011478 | E: -27.074902 | E_var:     0.4213 | E_err:   0.010141
[2025-10-02 20:07:40] [Iter  425/2250] R2[398/600]  | LR: 0.011364 | E: -27.082771 | E_var:     0.3481 | E_err:   0.009218
[2025-10-02 20:07:44] [Iter  426/2250] R2[400/600]  | LR: 0.011250 | E: -27.086026 | E_var:     0.3369 | E_err:   0.009070
[2025-10-02 20:07:48] [Iter  427/2250] R2[402/600]  | LR: 0.011137 | E: -27.073553 | E_var:     0.3327 | E_err:   0.009012
[2025-10-02 20:07:51] [Iter  428/2250] R2[404/600]  | LR: 0.011025 | E: -27.085139 | E_var:     0.3120 | E_err:   0.008727
[2025-10-02 20:07:55] [Iter  429/2250] R2[406/600]  | LR: 0.010913 | E: -27.085654 | E_var:     0.3335 | E_err:   0.009024
[2025-10-02 20:07:59] [Iter  430/2250] R2[408/600]  | LR: 0.010802 | E: -27.083871 | E_var:     0.3118 | E_err:   0.008724
[2025-10-02 20:08:02] [Iter  431/2250] R2[410/600]  | LR: 0.010692 | E: -27.072019 | E_var:     0.3620 | E_err:   0.009401
[2025-10-02 20:08:06] [Iter  432/2250] R2[412/600]  | LR: 0.010583 | E: -27.070475 | E_var:     0.3015 | E_err:   0.008580
[2025-10-02 20:08:10] [Iter  433/2250] R2[414/600]  | LR: 0.010474 | E: -27.076545 | E_var:     0.3857 | E_err:   0.009704
[2025-10-02 20:08:13] [Iter  434/2250] R2[416/600]  | LR: 0.010366 | E: -27.073725 | E_var:     0.3657 | E_err:   0.009449
[2025-10-02 20:08:17] [Iter  435/2250] R2[418/600]  | LR: 0.010259 | E: -27.076696 | E_var:     0.4283 | E_err:   0.010226
[2025-10-02 20:08:20] [Iter  436/2250] R2[420/600]  | LR: 0.010153 | E: -27.083650 | E_var:     0.2991 | E_err:   0.008545
[2025-10-02 20:08:24] [Iter  437/2250] R2[422/600]  | LR: 0.010047 | E: -27.073183 | E_var:     0.3259 | E_err:   0.008921
[2025-10-02 20:08:28] [Iter  438/2250] R2[424/600]  | LR: 0.009943 | E: -27.080889 | E_var:     0.2900 | E_err:   0.008414
[2025-10-02 20:08:31] [Iter  439/2250] R2[426/600]  | LR: 0.009839 | E: -27.072615 | E_var:     0.3471 | E_err:   0.009205
[2025-10-02 20:08:35] [Iter  440/2250] R2[428/600]  | LR: 0.009736 | E: -27.070768 | E_var:     0.3176 | E_err:   0.008806
[2025-10-02 20:08:39] [Iter  441/2250] R2[430/600]  | LR: 0.009633 | E: -27.073549 | E_var:     0.2921 | E_err:   0.008445
[2025-10-02 20:08:42] [Iter  442/2250] R2[432/600]  | LR: 0.009532 | E: -27.071332 | E_var:     0.3135 | E_err:   0.008748
[2025-10-02 20:08:46] [Iter  443/2250] R2[434/600]  | LR: 0.009432 | E: -27.073149 | E_var:     0.3855 | E_err:   0.009701
[2025-10-02 20:08:50] [Iter  444/2250] R2[436/600]  | LR: 0.009332 | E: -27.070247 | E_var:     0.3027 | E_err:   0.008596
[2025-10-02 20:08:53] [Iter  445/2250] R2[438/600]  | LR: 0.009234 | E: -27.085406 | E_var:     0.4089 | E_err:   0.009991
[2025-10-02 20:08:57] [Iter  446/2250] R2[440/600]  | LR: 0.009136 | E: -27.075512 | E_var:     0.5920 | E_err:   0.012023
[2025-10-02 20:09:01] [Iter  447/2250] R2[442/600]  | LR: 0.009039 | E: -27.094931 | E_var:     0.3555 | E_err:   0.009316
[2025-10-02 20:09:04] [Iter  448/2250] R2[444/600]  | LR: 0.008943 | E: -27.079514 | E_var:     0.2940 | E_err:   0.008473
[2025-10-02 20:09:08] [Iter  449/2250] R2[446/600]  | LR: 0.008848 | E: -27.081194 | E_var:     0.3231 | E_err:   0.008882
[2025-10-02 20:09:12] [Iter  450/2250] R2[448/600]  | LR: 0.008754 | E: -27.079128 | E_var:     0.3221 | E_err:   0.008868
[2025-10-02 20:09:15] [Iter  451/2250] R2[450/600]  | LR: 0.008661 | E: -27.067944 | E_var:     0.3356 | E_err:   0.009052
[2025-10-02 20:09:19] [Iter  452/2250] R2[452/600]  | LR: 0.008569 | E: -27.072136 | E_var:     0.3199 | E_err:   0.008837
[2025-10-02 20:09:23] [Iter  453/2250] R2[454/600]  | LR: 0.008478 | E: -27.080256 | E_var:     0.2692 | E_err:   0.008106
[2025-10-02 20:09:26] [Iter  454/2250] R2[456/600]  | LR: 0.008388 | E: -27.078910 | E_var:     0.2988 | E_err:   0.008541
[2025-10-02 20:09:30] [Iter  455/2250] R2[458/600]  | LR: 0.008299 | E: -27.084655 | E_var:     0.3767 | E_err:   0.009590
[2025-10-02 20:09:34] [Iter  456/2250] R2[460/600]  | LR: 0.008211 | E: -27.096172 | E_var:     0.3860 | E_err:   0.009707
[2025-10-02 20:09:37] [Iter  457/2250] R2[462/600]  | LR: 0.008124 | E: -27.077775 | E_var:     0.4668 | E_err:   0.010675
[2025-10-02 20:09:41] [Iter  458/2250] R2[464/600]  | LR: 0.008038 | E: -27.077520 | E_var:     0.2874 | E_err:   0.008377
[2025-10-02 20:09:45] [Iter  459/2250] R2[466/600]  | LR: 0.007953 | E: -27.087243 | E_var:     0.3112 | E_err:   0.008717
[2025-10-02 20:09:48] [Iter  460/2250] R2[468/600]  | LR: 0.007869 | E: -27.084630 | E_var:     0.3359 | E_err:   0.009056
[2025-10-02 20:09:52] [Iter  461/2250] R2[470/600]  | LR: 0.007786 | E: -27.086975 | E_var:     0.4328 | E_err:   0.010280
[2025-10-02 20:09:56] [Iter  462/2250] R2[472/600]  | LR: 0.007704 | E: -27.088310 | E_var:     0.3915 | E_err:   0.009777
[2025-10-02 20:09:59] [Iter  463/2250] R2[474/600]  | LR: 0.007623 | E: -27.073737 | E_var:     0.3294 | E_err:   0.008968
[2025-10-02 20:10:03] [Iter  464/2250] R2[476/600]  | LR: 0.007543 | E: -27.091979 | E_var:     0.3809 | E_err:   0.009643
[2025-10-02 20:10:06] [Iter  465/2250] R2[478/600]  | LR: 0.007465 | E: -27.090609 | E_var:     0.3106 | E_err:   0.008708
[2025-10-02 20:10:10] [Iter  466/2250] R2[480/600]  | LR: 0.007387 | E: -27.087973 | E_var:     0.3123 | E_err:   0.008732
[2025-10-02 20:10:14] [Iter  467/2250] R2[482/600]  | LR: 0.007311 | E: -27.081165 | E_var:     0.2870 | E_err:   0.008370
[2025-10-02 20:10:17] [Iter  468/2250] R2[484/600]  | LR: 0.007236 | E: -27.076304 | E_var:     0.2954 | E_err:   0.008493
[2025-10-02 20:10:21] [Iter  469/2250] R2[486/600]  | LR: 0.007161 | E: -27.068884 | E_var:     0.4030 | E_err:   0.009919
[2025-10-02 20:10:25] [Iter  470/2250] R2[488/600]  | LR: 0.007088 | E: -27.084779 | E_var:     0.3201 | E_err:   0.008840
[2025-10-02 20:10:28] [Iter  471/2250] R2[490/600]  | LR: 0.007017 | E: -27.089312 | E_var:     0.3207 | E_err:   0.008848
[2025-10-02 20:10:32] [Iter  472/2250] R2[492/600]  | LR: 0.006946 | E: -27.081340 | E_var:     0.3801 | E_err:   0.009634
[2025-10-02 20:10:36] [Iter  473/2250] R2[494/600]  | LR: 0.006876 | E: -27.086415 | E_var:     0.4092 | E_err:   0.009995
[2025-10-02 20:10:39] [Iter  474/2250] R2[496/600]  | LR: 0.006808 | E: -27.095843 | E_var:     0.2884 | E_err:   0.008390
[2025-10-02 20:10:43] [Iter  475/2250] R2[498/600]  | LR: 0.006741 | E: -27.099408 | E_var:     0.2981 | E_err:   0.008531
[2025-10-02 20:10:47] [Iter  476/2250] R2[500/600]  | LR: 0.006675 | E: -27.079595 | E_var:     0.3037 | E_err:   0.008611
[2025-10-02 20:10:50] [Iter  477/2250] R2[502/600]  | LR: 0.006610 | E: -27.087313 | E_var:     0.3417 | E_err:   0.009133
[2025-10-02 20:10:54] [Iter  478/2250] R2[504/600]  | LR: 0.006546 | E: -27.095820 | E_var:     0.2705 | E_err:   0.008126
[2025-10-02 20:10:58] [Iter  479/2250] R2[506/600]  | LR: 0.006484 | E: -27.095748 | E_var:     0.2910 | E_err:   0.008428
[2025-10-02 20:11:01] [Iter  480/2250] R2[508/600]  | LR: 0.006422 | E: -27.087335 | E_var:     0.3788 | E_err:   0.009616
[2025-10-02 20:11:05] [Iter  481/2250] R2[510/600]  | LR: 0.006362 | E: -27.080101 | E_var:     0.3158 | E_err:   0.008780
[2025-10-02 20:11:09] [Iter  482/2250] R2[512/600]  | LR: 0.006304 | E: -27.089808 | E_var:     0.3913 | E_err:   0.009775
[2025-10-02 20:11:12] [Iter  483/2250] R2[514/600]  | LR: 0.006246 | E: -27.076775 | E_var:     0.3783 | E_err:   0.009610
[2025-10-02 20:11:16] [Iter  484/2250] R2[516/600]  | LR: 0.006190 | E: -27.088170 | E_var:     0.3113 | E_err:   0.008718
[2025-10-02 20:11:20] [Iter  485/2250] R2[518/600]  | LR: 0.006135 | E: -27.077958 | E_var:     0.3153 | E_err:   0.008774
[2025-10-02 20:11:23] [Iter  486/2250] R2[520/600]  | LR: 0.006081 | E: -27.083262 | E_var:     0.4084 | E_err:   0.009986
[2025-10-02 20:11:27] [Iter  487/2250] R2[522/600]  | LR: 0.006028 | E: -27.085784 | E_var:     0.4431 | E_err:   0.010401
[2025-10-02 20:11:31] [Iter  488/2250] R2[524/600]  | LR: 0.005977 | E: -27.083915 | E_var:     0.3005 | E_err:   0.008566
[2025-10-02 20:11:34] [Iter  489/2250] R2[526/600]  | LR: 0.005927 | E: -27.090815 | E_var:     0.2905 | E_err:   0.008421
[2025-10-02 20:11:38] [Iter  490/2250] R2[528/600]  | LR: 0.005878 | E: -27.073411 | E_var:     0.2966 | E_err:   0.008510
[2025-10-02 20:11:41] [Iter  491/2250] R2[530/600]  | LR: 0.005830 | E: -27.082974 | E_var:     0.3181 | E_err:   0.008813
[2025-10-02 20:11:45] [Iter  492/2250] R2[532/600]  | LR: 0.005784 | E: -27.078076 | E_var:     0.2782 | E_err:   0.008242
[2025-10-02 20:11:49] [Iter  493/2250] R2[534/600]  | LR: 0.005739 | E: -27.088053 | E_var:     0.3180 | E_err:   0.008812
[2025-10-02 20:11:52] [Iter  494/2250] R2[536/600]  | LR: 0.005695 | E: -27.087530 | E_var:     0.3335 | E_err:   0.009024
[2025-10-02 20:11:56] [Iter  495/2250] R2[538/600]  | LR: 0.005653 | E: -27.094776 | E_var:     0.2735 | E_err:   0.008171
[2025-10-02 20:12:00] [Iter  496/2250] R2[540/600]  | LR: 0.005612 | E: -27.071691 | E_var:     0.4509 | E_err:   0.010491
[2025-10-02 20:12:03] [Iter  497/2250] R2[542/600]  | LR: 0.005572 | E: -27.103031 | E_var:     0.3631 | E_err:   0.009416
[2025-10-02 20:12:07] [Iter  498/2250] R2[544/600]  | LR: 0.005534 | E: -27.094061 | E_var:     0.2633 | E_err:   0.008018
[2025-10-02 20:12:11] [Iter  499/2250] R2[546/600]  | LR: 0.005496 | E: -27.081380 | E_var:     0.2540 | E_err:   0.007875
[2025-10-02 20:12:14] [Iter  500/2250] R2[548/600]  | LR: 0.005460 | E: -27.093790 | E_var:     0.3155 | E_err:   0.008776
[2025-10-02 20:12:18] [Iter  501/2250] R2[550/600]  | LR: 0.005426 | E: -27.081100 | E_var:     0.3911 | E_err:   0.009772
[2025-10-02 20:12:22] [Iter  502/2250] R2[552/600]  | LR: 0.005393 | E: -27.100270 | E_var:     0.3396 | E_err:   0.009105
[2025-10-02 20:12:25] [Iter  503/2250] R2[554/600]  | LR: 0.005361 | E: -27.090555 | E_var:     0.2745 | E_err:   0.008187
[2025-10-02 20:12:29] [Iter  504/2250] R2[556/600]  | LR: 0.005330 | E: -27.096096 | E_var:     0.2533 | E_err:   0.007864
[2025-10-02 20:12:33] [Iter  505/2250] R2[558/600]  | LR: 0.005301 | E: -27.098062 | E_var:     0.2881 | E_err:   0.008387
[2025-10-02 20:12:36] [Iter  506/2250] R2[560/600]  | LR: 0.005273 | E: -27.091347 | E_var:     0.3097 | E_err:   0.008695
[2025-10-02 20:12:40] [Iter  507/2250] R2[562/600]  | LR: 0.005247 | E: -27.088689 | E_var:     0.2418 | E_err:   0.007684
[2025-10-02 20:12:44] [Iter  508/2250] R2[564/600]  | LR: 0.005221 | E: -27.083265 | E_var:     0.3069 | E_err:   0.008657
[2025-10-02 20:12:47] [Iter  509/2250] R2[566/600]  | LR: 0.005198 | E: -27.084649 | E_var:     0.4649 | E_err:   0.010654
[2025-10-02 20:12:51] [Iter  510/2250] R2[568/600]  | LR: 0.005175 | E: -27.099891 | E_var:     0.3450 | E_err:   0.009178
[2025-10-02 20:12:55] [Iter  511/2250] R2[570/600]  | LR: 0.005154 | E: -27.083085 | E_var:     0.3548 | E_err:   0.009307
[2025-10-02 20:12:58] [Iter  512/2250] R2[572/600]  | LR: 0.005134 | E: -27.073592 | E_var:     0.2833 | E_err:   0.008317
[2025-10-02 20:13:02] [Iter  513/2250] R2[574/600]  | LR: 0.005116 | E: -27.091295 | E_var:     0.3069 | E_err:   0.008656
[2025-10-02 20:13:05] [Iter  514/2250] R2[576/600]  | LR: 0.005099 | E: -27.090268 | E_var:     0.2666 | E_err:   0.008068
[2025-10-02 20:13:09] [Iter  515/2250] R2[578/600]  | LR: 0.005083 | E: -27.096576 | E_var:     0.2730 | E_err:   0.008165
[2025-10-02 20:13:13] [Iter  516/2250] R2[580/600]  | LR: 0.005068 | E: -27.100266 | E_var:     0.3027 | E_err:   0.008597
[2025-10-02 20:13:16] [Iter  517/2250] R2[582/600]  | LR: 0.005055 | E: -27.089266 | E_var:     0.2790 | E_err:   0.008254
[2025-10-02 20:13:20] [Iter  518/2250] R2[584/600]  | LR: 0.005044 | E: -27.092597 | E_var:     0.2703 | E_err:   0.008124
[2025-10-02 20:13:24] [Iter  519/2250] R2[586/600]  | LR: 0.005034 | E: -27.087048 | E_var:     0.2769 | E_err:   0.008222
[2025-10-02 20:13:27] [Iter  520/2250] R2[588/600]  | LR: 0.005025 | E: -27.100653 | E_var:     0.2643 | E_err:   0.008033
[2025-10-02 20:13:31] [Iter  521/2250] R2[590/600]  | LR: 0.005017 | E: -27.093428 | E_var:     0.3109 | E_err:   0.008712
[2025-10-02 20:13:35] [Iter  522/2250] R2[592/600]  | LR: 0.005011 | E: -27.094066 | E_var:     0.3338 | E_err:   0.009028
[2025-10-02 20:13:38] [Iter  523/2250] R2[594/600]  | LR: 0.005006 | E: -27.086803 | E_var:     0.3348 | E_err:   0.009041
[2025-10-02 20:13:42] [Iter  524/2250] R2[596/600]  | LR: 0.005003 | E: -27.097161 | E_var:     0.2625 | E_err:   0.008006
[2025-10-02 20:13:46] [Iter  525/2250] R2[598/600]  | LR: 0.005001 | E: -27.084879 | E_var:     0.3844 | E_err:   0.009687
[2025-10-02 20:13:46] 🔄 RESTART #3 | Period: 1200
[2025-10-02 20:13:49] [Iter  526/2250] R3[0/1200]   | LR: 0.030000 | E: -27.089072 | E_var:     0.4577 | E_err:   0.010570
[2025-10-02 20:13:53] [Iter  527/2250] R3[2/1200]   | LR: 0.030000 | E: -27.084028 | E_var:     0.2645 | E_err:   0.008035
[2025-10-02 20:13:57] [Iter  528/2250] R3[4/1200]   | LR: 0.029999 | E: -27.091303 | E_var:     0.2857 | E_err:   0.008352
[2025-10-02 20:14:00] [Iter  529/2250] R3[6/1200]   | LR: 0.029998 | E: -27.092241 | E_var:     0.3687 | E_err:   0.009487
[2025-10-02 20:14:04] [Iter  530/2250] R3[8/1200]   | LR: 0.029997 | E: -27.096296 | E_var:     0.2691 | E_err:   0.008106
[2025-10-02 20:14:08] [Iter  531/2250] R3[10/1200]  | LR: 0.029996 | E: -27.096653 | E_var:     0.3372 | E_err:   0.009074
[2025-10-02 20:14:11] [Iter  532/2250] R3[12/1200]  | LR: 0.029994 | E: -27.101922 | E_var:     0.3589 | E_err:   0.009361
[2025-10-02 20:14:15] [Iter  533/2250] R3[14/1200]  | LR: 0.029992 | E: -27.094774 | E_var:     0.2997 | E_err:   0.008553
[2025-10-02 20:14:19] [Iter  534/2250] R3[16/1200]  | LR: 0.029989 | E: -27.075157 | E_var:     0.3364 | E_err:   0.009062
[2025-10-02 20:14:22] [Iter  535/2250] R3[18/1200]  | LR: 0.029986 | E: -27.099917 | E_var:     0.2739 | E_err:   0.008178
[2025-10-02 20:14:26] [Iter  536/2250] R3[20/1200]  | LR: 0.029983 | E: -27.092979 | E_var:     0.3100 | E_err:   0.008700
[2025-10-02 20:14:30] [Iter  537/2250] R3[22/1200]  | LR: 0.029979 | E: -27.076105 | E_var:     0.2779 | E_err:   0.008237
[2025-10-02 20:14:33] [Iter  538/2250] R3[24/1200]  | LR: 0.029975 | E: -27.094431 | E_var:     0.3559 | E_err:   0.009322
[2025-10-02 20:14:37] [Iter  539/2250] R3[26/1200]  | LR: 0.029971 | E: -27.093162 | E_var:     0.2653 | E_err:   0.008048
[2025-10-02 20:14:40] [Iter  540/2250] R3[28/1200]  | LR: 0.029966 | E: -27.090330 | E_var:     0.2957 | E_err:   0.008497
[2025-10-02 20:14:44] [Iter  541/2250] R3[30/1200]  | LR: 0.029961 | E: -27.102435 | E_var:     0.2710 | E_err:   0.008133
[2025-10-02 20:14:48] [Iter  542/2250] R3[32/1200]  | LR: 0.029956 | E: -27.083130 | E_var:     0.2747 | E_err:   0.008190
[2025-10-02 20:14:51] [Iter  543/2250] R3[34/1200]  | LR: 0.029951 | E: -27.108311 | E_var:     0.3036 | E_err:   0.008609
[2025-10-02 20:14:55] [Iter  544/2250] R3[36/1200]  | LR: 0.029945 | E: -27.097870 | E_var:     0.2622 | E_err:   0.008002
[2025-10-02 20:14:59] [Iter  545/2250] R3[38/1200]  | LR: 0.029938 | E: -27.092048 | E_var:     0.3635 | E_err:   0.009420
[2025-10-02 20:15:02] [Iter  546/2250] R3[40/1200]  | LR: 0.029932 | E: -27.084310 | E_var:     0.3425 | E_err:   0.009145
[2025-10-02 20:15:06] [Iter  547/2250] R3[42/1200]  | LR: 0.029925 | E: -27.097151 | E_var:     0.2391 | E_err:   0.007640
[2025-10-02 20:15:10] [Iter  548/2250] R3[44/1200]  | LR: 0.029917 | E: -27.111655 | E_var:     0.3363 | E_err:   0.009061
[2025-10-02 20:15:13] [Iter  549/2250] R3[46/1200]  | LR: 0.029909 | E: -27.098126 | E_var:     0.2776 | E_err:   0.008233
[2025-10-02 20:15:17] [Iter  550/2250] R3[48/1200]  | LR: 0.029901 | E: -27.084670 | E_var:     0.2962 | E_err:   0.008504
[2025-10-02 20:15:21] [Iter  551/2250] R3[50/1200]  | LR: 0.029893 | E: -27.087274 | E_var:     0.3212 | E_err:   0.008856
[2025-10-02 20:15:24] [Iter  552/2250] R3[52/1200]  | LR: 0.029884 | E: -27.096676 | E_var:     0.2772 | E_err:   0.008226
[2025-10-02 20:15:28] [Iter  553/2250] R3[54/1200]  | LR: 0.029875 | E: -27.099628 | E_var:     0.3106 | E_err:   0.008707
[2025-10-02 20:15:32] [Iter  554/2250] R3[56/1200]  | LR: 0.029866 | E: -27.098364 | E_var:     0.2882 | E_err:   0.008388
[2025-10-02 20:15:35] [Iter  555/2250] R3[58/1200]  | LR: 0.029856 | E: -27.099251 | E_var:     0.2737 | E_err:   0.008174
[2025-10-02 20:15:39] [Iter  556/2250] R3[60/1200]  | LR: 0.029846 | E: -27.102334 | E_var:     0.3360 | E_err:   0.009057
[2025-10-02 20:15:43] [Iter  557/2250] R3[62/1200]  | LR: 0.029836 | E: -27.088846 | E_var:     0.2704 | E_err:   0.008125
[2025-10-02 20:15:46] [Iter  558/2250] R3[64/1200]  | LR: 0.029825 | E: -27.096829 | E_var:     0.4803 | E_err:   0.010828
[2025-10-02 20:15:50] [Iter  559/2250] R3[66/1200]  | LR: 0.029814 | E: -27.094553 | E_var:     0.4676 | E_err:   0.010685
[2025-10-02 20:15:54] [Iter  560/2250] R3[68/1200]  | LR: 0.029802 | E: -27.093257 | E_var:     0.2910 | E_err:   0.008428
[2025-10-02 20:15:57] [Iter  561/2250] R3[70/1200]  | LR: 0.029791 | E: -27.089097 | E_var:     0.2770 | E_err:   0.008223
[2025-10-02 20:16:01] [Iter  562/2250] R3[72/1200]  | LR: 0.029779 | E: -27.089142 | E_var:     0.3501 | E_err:   0.009245
[2025-10-02 20:16:05] [Iter  563/2250] R3[74/1200]  | LR: 0.029766 | E: -27.090954 | E_var:     0.3216 | E_err:   0.008860
[2025-10-02 20:16:08] [Iter  564/2250] R3[76/1200]  | LR: 0.029753 | E: -27.101453 | E_var:     0.3593 | E_err:   0.009366
[2025-10-02 20:16:12] [Iter  565/2250] R3[78/1200]  | LR: 0.029740 | E: -27.105841 | E_var:     0.2930 | E_err:   0.008458
[2025-10-02 20:16:15] [Iter  566/2250] R3[80/1200]  | LR: 0.029727 | E: -27.085761 | E_var:     0.2954 | E_err:   0.008492
[2025-10-02 20:16:19] [Iter  567/2250] R3[82/1200]  | LR: 0.029713 | E: -27.102597 | E_var:     0.3433 | E_err:   0.009154
[2025-10-02 20:16:23] [Iter  568/2250] R3[84/1200]  | LR: 0.029699 | E: -27.107170 | E_var:     0.3256 | E_err:   0.008916
[2025-10-02 20:16:26] [Iter  569/2250] R3[86/1200]  | LR: 0.029685 | E: -27.088980 | E_var:     0.2961 | E_err:   0.008503
[2025-10-02 20:16:30] [Iter  570/2250] R3[88/1200]  | LR: 0.029670 | E: -27.095980 | E_var:     0.2993 | E_err:   0.008548
[2025-10-02 20:16:34] [Iter  571/2250] R3[90/1200]  | LR: 0.029655 | E: -27.086260 | E_var:     0.6488 | E_err:   0.012585
[2025-10-02 20:16:37] [Iter  572/2250] R3[92/1200]  | LR: 0.029639 | E: -27.101241 | E_var:     0.2287 | E_err:   0.007472
[2025-10-02 20:16:41] [Iter  573/2250] R3[94/1200]  | LR: 0.029623 | E: -27.102350 | E_var:     0.2418 | E_err:   0.007683
[2025-10-02 20:16:45] [Iter  574/2250] R3[96/1200]  | LR: 0.029607 | E: -27.091451 | E_var:     0.2400 | E_err:   0.007654
[2025-10-02 20:16:48] [Iter  575/2250] R3[98/1200]  | LR: 0.029591 | E: -27.091263 | E_var:     0.2719 | E_err:   0.008148
[2025-10-02 20:16:52] [Iter  576/2250] R3[100/1200] | LR: 0.029574 | E: -27.095671 | E_var:     0.3097 | E_err:   0.008696
[2025-10-02 20:16:56] [Iter  577/2250] R3[102/1200] | LR: 0.029557 | E: -27.081576 | E_var:     0.2491 | E_err:   0.007799
[2025-10-02 20:16:59] [Iter  578/2250] R3[104/1200] | LR: 0.029540 | E: -27.092439 | E_var:     0.2693 | E_err:   0.008108
[2025-10-02 20:17:03] [Iter  579/2250] R3[106/1200] | LR: 0.029522 | E: -27.094522 | E_var:     0.3521 | E_err:   0.009271
[2025-10-02 20:17:07] [Iter  580/2250] R3[108/1200] | LR: 0.029504 | E: -27.092096 | E_var:     0.2738 | E_err:   0.008175
[2025-10-02 20:17:10] [Iter  581/2250] R3[110/1200] | LR: 0.029485 | E: -27.100265 | E_var:     0.2978 | E_err:   0.008527
[2025-10-02 20:17:14] [Iter  582/2250] R3[112/1200] | LR: 0.029466 | E: -27.100021 | E_var:     0.3522 | E_err:   0.009273
[2025-10-02 20:17:18] [Iter  583/2250] R3[114/1200] | LR: 0.029447 | E: -27.113869 | E_var:     0.3483 | E_err:   0.009222
[2025-10-02 20:17:21] [Iter  584/2250] R3[116/1200] | LR: 0.029428 | E: -27.103095 | E_var:     0.2867 | E_err:   0.008366
[2025-10-02 20:17:25] [Iter  585/2250] R3[118/1200] | LR: 0.029408 | E: -27.095404 | E_var:     0.2907 | E_err:   0.008424
[2025-10-02 20:17:28] [Iter  586/2250] R3[120/1200] | LR: 0.029388 | E: -27.106522 | E_var:     0.2334 | E_err:   0.007548
[2025-10-02 20:17:32] [Iter  587/2250] R3[122/1200] | LR: 0.029368 | E: -27.089213 | E_var:     0.2316 | E_err:   0.007520
[2025-10-02 20:17:36] [Iter  588/2250] R3[124/1200] | LR: 0.029347 | E: -27.100363 | E_var:     0.2900 | E_err:   0.008414
[2025-10-02 20:17:39] [Iter  589/2250] R3[126/1200] | LR: 0.029326 | E: -27.094322 | E_var:     0.2447 | E_err:   0.007729
[2025-10-02 20:17:43] [Iter  590/2250] R3[128/1200] | LR: 0.029305 | E: -27.108515 | E_var:     0.2705 | E_err:   0.008126
[2025-10-02 20:17:47] [Iter  591/2250] R3[130/1200] | LR: 0.029283 | E: -27.102929 | E_var:     0.2813 | E_err:   0.008287
[2025-10-02 20:17:50] [Iter  592/2250] R3[132/1200] | LR: 0.029261 | E: -27.089536 | E_var:     0.4505 | E_err:   0.010488
[2025-10-02 20:17:54] [Iter  593/2250] R3[134/1200] | LR: 0.029239 | E: -27.109749 | E_var:     0.3110 | E_err:   0.008714
[2025-10-02 20:17:58] [Iter  594/2250] R3[136/1200] | LR: 0.029216 | E: -27.096123 | E_var:     0.3323 | E_err:   0.009007
[2025-10-02 20:18:01] [Iter  595/2250] R3[138/1200] | LR: 0.029193 | E: -27.111524 | E_var:     0.2806 | E_err:   0.008277
[2025-10-02 20:18:05] [Iter  596/2250] R3[140/1200] | LR: 0.029170 | E: -27.094772 | E_var:     0.2539 | E_err:   0.007873
[2025-10-02 20:18:09] [Iter  597/2250] R3[142/1200] | LR: 0.029146 | E: -27.100918 | E_var:     0.2560 | E_err:   0.007905
[2025-10-02 20:18:12] [Iter  598/2250] R3[144/1200] | LR: 0.029122 | E: -27.099763 | E_var:     0.2891 | E_err:   0.008401
[2025-10-02 20:18:16] [Iter  599/2250] R3[146/1200] | LR: 0.029098 | E: -27.082861 | E_var:     0.3339 | E_err:   0.009028
[2025-10-02 20:18:20] [Iter  600/2250] R3[148/1200] | LR: 0.029073 | E: -27.086586 | E_var:     0.2834 | E_err:   0.008317
[2025-10-02 20:18:20] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-02 20:18:23] [Iter  601/2250] R3[150/1200] | LR: 0.029048 | E: -27.111555 | E_var:     0.2678 | E_err:   0.008086
[2025-10-02 20:18:27] [Iter  602/2250] R3[152/1200] | LR: 0.029023 | E: -27.097995 | E_var:     0.2524 | E_err:   0.007850
[2025-10-02 20:18:31] [Iter  603/2250] R3[154/1200] | LR: 0.028998 | E: -27.092978 | E_var:     0.2893 | E_err:   0.008404
[2025-10-02 20:18:34] [Iter  604/2250] R3[156/1200] | LR: 0.028972 | E: -27.102969 | E_var:     0.2267 | E_err:   0.007440
[2025-10-02 20:18:38] [Iter  605/2250] R3[158/1200] | LR: 0.028946 | E: -27.098190 | E_var:     0.3403 | E_err:   0.009114
[2025-10-02 20:18:42] [Iter  606/2250] R3[160/1200] | LR: 0.028919 | E: -27.099569 | E_var:     0.2748 | E_err:   0.008191
[2025-10-02 20:18:45] [Iter  607/2250] R3[162/1200] | LR: 0.028893 | E: -27.090313 | E_var:     0.3688 | E_err:   0.009489
[2025-10-02 20:18:49] [Iter  608/2250] R3[164/1200] | LR: 0.028865 | E: -27.101770 | E_var:     0.3445 | E_err:   0.009171
[2025-10-02 20:18:53] [Iter  609/2250] R3[166/1200] | LR: 0.028838 | E: -27.099430 | E_var:     0.2206 | E_err:   0.007338
[2025-10-02 20:18:56] [Iter  610/2250] R3[168/1200] | LR: 0.028810 | E: -27.104466 | E_var:     0.2783 | E_err:   0.008242
[2025-10-02 20:19:00] [Iter  611/2250] R3[170/1200] | LR: 0.028782 | E: -27.113548 | E_var:     0.2999 | E_err:   0.008557
[2025-10-02 20:19:03] [Iter  612/2250] R3[172/1200] | LR: 0.028754 | E: -27.098946 | E_var:     0.2465 | E_err:   0.007758
[2025-10-02 20:19:07] [Iter  613/2250] R3[174/1200] | LR: 0.028725 | E: -27.094885 | E_var:     0.2803 | E_err:   0.008272
[2025-10-02 20:19:11] [Iter  614/2250] R3[176/1200] | LR: 0.028696 | E: -27.106108 | E_var:     0.2813 | E_err:   0.008287
[2025-10-02 20:19:14] [Iter  615/2250] R3[178/1200] | LR: 0.028667 | E: -27.092258 | E_var:     0.2717 | E_err:   0.008145
[2025-10-02 20:19:18] [Iter  616/2250] R3[180/1200] | LR: 0.028638 | E: -27.110447 | E_var:     0.2810 | E_err:   0.008282
[2025-10-02 20:19:22] [Iter  617/2250] R3[182/1200] | LR: 0.028608 | E: -27.091315 | E_var:     0.2784 | E_err:   0.008244
[2025-10-02 20:19:25] [Iter  618/2250] R3[184/1200] | LR: 0.028578 | E: -27.117820 | E_var:     0.2529 | E_err:   0.007858
[2025-10-02 20:19:29] [Iter  619/2250] R3[186/1200] | LR: 0.028547 | E: -27.098238 | E_var:     0.2872 | E_err:   0.008374
[2025-10-02 20:19:33] [Iter  620/2250] R3[188/1200] | LR: 0.028516 | E: -27.104392 | E_var:     0.2694 | E_err:   0.008110
[2025-10-02 20:19:36] [Iter  621/2250] R3[190/1200] | LR: 0.028485 | E: -27.102377 | E_var:     0.2462 | E_err:   0.007753
[2025-10-02 20:19:40] [Iter  622/2250] R3[192/1200] | LR: 0.028454 | E: -27.100539 | E_var:     0.2681 | E_err:   0.008091
[2025-10-02 20:19:44] [Iter  623/2250] R3[194/1200] | LR: 0.028422 | E: -27.100031 | E_var:     0.2694 | E_err:   0.008110
[2025-10-02 20:19:47] [Iter  624/2250] R3[196/1200] | LR: 0.028390 | E: -27.103631 | E_var:     0.3260 | E_err:   0.008922
[2025-10-02 20:19:51] [Iter  625/2250] R3[198/1200] | LR: 0.028358 | E: -27.102450 | E_var:     0.2358 | E_err:   0.007587
[2025-10-02 20:19:55] [Iter  626/2250] R3[200/1200] | LR: 0.028325 | E: -27.092695 | E_var:     0.2636 | E_err:   0.008022
[2025-10-02 20:19:58] [Iter  627/2250] R3[202/1200] | LR: 0.028292 | E: -27.096363 | E_var:     0.3102 | E_err:   0.008703
[2025-10-02 20:20:02] [Iter  628/2250] R3[204/1200] | LR: 0.028259 | E: -27.115691 | E_var:     0.2976 | E_err:   0.008524
[2025-10-02 20:20:06] [Iter  629/2250] R3[206/1200] | LR: 0.028226 | E: -27.105782 | E_var:     0.2405 | E_err:   0.007663
[2025-10-02 20:20:09] [Iter  630/2250] R3[208/1200] | LR: 0.028192 | E: -27.095395 | E_var:     0.2487 | E_err:   0.007792
[2025-10-02 20:20:13] [Iter  631/2250] R3[210/1200] | LR: 0.028158 | E: -27.107048 | E_var:     0.2868 | E_err:   0.008367
[2025-10-02 20:20:17] [Iter  632/2250] R3[212/1200] | LR: 0.028124 | E: -27.108454 | E_var:     0.3131 | E_err:   0.008743
[2025-10-02 20:20:20] [Iter  633/2250] R3[214/1200] | LR: 0.028089 | E: -27.104629 | E_var:     0.2408 | E_err:   0.007667
[2025-10-02 20:20:24] [Iter  634/2250] R3[216/1200] | LR: 0.028054 | E: -27.107110 | E_var:     0.2468 | E_err:   0.007763
[2025-10-02 20:20:28] [Iter  635/2250] R3[218/1200] | LR: 0.028019 | E: -27.089366 | E_var:     0.4687 | E_err:   0.010697
[2025-10-02 20:20:31] [Iter  636/2250] R3[220/1200] | LR: 0.027983 | E: -27.106653 | E_var:     0.2218 | E_err:   0.007359
[2025-10-02 20:20:35] [Iter  637/2250] R3[222/1200] | LR: 0.027948 | E: -27.108851 | E_var:     0.2970 | E_err:   0.008515
[2025-10-02 20:20:39] [Iter  638/2250] R3[224/1200] | LR: 0.027912 | E: -27.097593 | E_var:     0.3212 | E_err:   0.008855
[2025-10-02 20:20:42] [Iter  639/2250] R3[226/1200] | LR: 0.027875 | E: -27.087422 | E_var:     0.3637 | E_err:   0.009423
[2025-10-02 20:20:46] [Iter  640/2250] R3[228/1200] | LR: 0.027839 | E: -27.098931 | E_var:     0.2772 | E_err:   0.008227
[2025-10-02 20:20:49] [Iter  641/2250] R3[230/1200] | LR: 0.027802 | E: -27.089889 | E_var:     0.2808 | E_err:   0.008279
[2025-10-02 20:20:53] [Iter  642/2250] R3[232/1200] | LR: 0.027764 | E: -27.116096 | E_var:     0.3133 | E_err:   0.008746
[2025-10-02 20:20:57] [Iter  643/2250] R3[234/1200] | LR: 0.027727 | E: -27.104575 | E_var:     0.2943 | E_err:   0.008477
[2025-10-02 20:21:00] [Iter  644/2250] R3[236/1200] | LR: 0.027689 | E: -27.105912 | E_var:     0.2603 | E_err:   0.007972
[2025-10-02 20:21:04] [Iter  645/2250] R3[238/1200] | LR: 0.027651 | E: -27.112374 | E_var:     0.2446 | E_err:   0.007728
[2025-10-02 20:21:08] [Iter  646/2250] R3[240/1200] | LR: 0.027613 | E: -27.108059 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 20:21:11] [Iter  647/2250] R3[242/1200] | LR: 0.027574 | E: -27.091523 | E_var:     0.2222 | E_err:   0.007366
[2025-10-02 20:21:15] [Iter  648/2250] R3[244/1200] | LR: 0.027535 | E: -27.101553 | E_var:     0.2348 | E_err:   0.007572
[2025-10-02 20:21:19] [Iter  649/2250] R3[246/1200] | LR: 0.027496 | E: -27.110540 | E_var:     0.2547 | E_err:   0.007885
[2025-10-02 20:21:22] [Iter  650/2250] R3[248/1200] | LR: 0.027457 | E: -27.104605 | E_var:     0.3554 | E_err:   0.009315
[2025-10-02 20:21:26] [Iter  651/2250] R3[250/1200] | LR: 0.027417 | E: -27.106924 | E_var:     0.2164 | E_err:   0.007269
[2025-10-02 20:21:30] [Iter  652/2250] R3[252/1200] | LR: 0.027377 | E: -27.093944 | E_var:     0.2419 | E_err:   0.007684
[2025-10-02 20:21:33] [Iter  653/2250] R3[254/1200] | LR: 0.027337 | E: -27.119789 | E_var:     0.2879 | E_err:   0.008384
[2025-10-02 20:21:37] [Iter  654/2250] R3[256/1200] | LR: 0.027296 | E: -27.106436 | E_var:     0.2324 | E_err:   0.007532
[2025-10-02 20:21:41] [Iter  655/2250] R3[258/1200] | LR: 0.027255 | E: -27.103415 | E_var:     0.4058 | E_err:   0.009954
[2025-10-02 20:21:44] [Iter  656/2250] R3[260/1200] | LR: 0.027214 | E: -27.102022 | E_var:     0.2398 | E_err:   0.007651
[2025-10-02 20:21:48] [Iter  657/2250] R3[262/1200] | LR: 0.027173 | E: -27.100416 | E_var:     0.2764 | E_err:   0.008214
[2025-10-02 20:21:52] [Iter  658/2250] R3[264/1200] | LR: 0.027131 | E: -27.106017 | E_var:     0.2473 | E_err:   0.007770
[2025-10-02 20:21:55] [Iter  659/2250] R3[266/1200] | LR: 0.027090 | E: -27.109540 | E_var:     0.2545 | E_err:   0.007882
[2025-10-02 20:21:59] [Iter  660/2250] R3[268/1200] | LR: 0.027047 | E: -27.103976 | E_var:     0.4537 | E_err:   0.010525
[2025-10-02 20:22:03] [Iter  661/2250] R3[270/1200] | LR: 0.027005 | E: -27.102172 | E_var:     0.2618 | E_err:   0.007995
[2025-10-02 20:22:06] [Iter  662/2250] R3[272/1200] | LR: 0.026962 | E: -27.114578 | E_var:     0.2530 | E_err:   0.007860
[2025-10-02 20:22:10] [Iter  663/2250] R3[274/1200] | LR: 0.026920 | E: -27.108017 | E_var:     0.2663 | E_err:   0.008064
[2025-10-02 20:22:14] [Iter  664/2250] R3[276/1200] | LR: 0.026876 | E: -27.107102 | E_var:     0.3015 | E_err:   0.008580
[2025-10-02 20:22:17] [Iter  665/2250] R3[278/1200] | LR: 0.026833 | E: -27.113943 | E_var:     0.2276 | E_err:   0.007455
[2025-10-02 20:22:21] [Iter  666/2250] R3[280/1200] | LR: 0.026789 | E: -27.097722 | E_var:     0.2686 | E_err:   0.008098
[2025-10-02 20:22:25] [Iter  667/2250] R3[282/1200] | LR: 0.026745 | E: -27.117705 | E_var:     0.2270 | E_err:   0.007445
[2025-10-02 20:22:28] [Iter  668/2250] R3[284/1200] | LR: 0.026701 | E: -27.103594 | E_var:     0.2768 | E_err:   0.008221
[2025-10-02 20:22:32] [Iter  669/2250] R3[286/1200] | LR: 0.026657 | E: -27.114895 | E_var:     0.2467 | E_err:   0.007761
[2025-10-02 20:22:35] [Iter  670/2250] R3[288/1200] | LR: 0.026612 | E: -27.104426 | E_var:     0.3822 | E_err:   0.009660
[2025-10-02 20:22:39] [Iter  671/2250] R3[290/1200] | LR: 0.026567 | E: -27.095284 | E_var:     0.3083 | E_err:   0.008676
[2025-10-02 20:22:43] [Iter  672/2250] R3[292/1200] | LR: 0.026522 | E: -27.105684 | E_var:     0.2705 | E_err:   0.008126
[2025-10-02 20:22:46] [Iter  673/2250] R3[294/1200] | LR: 0.026477 | E: -27.113492 | E_var:     0.2591 | E_err:   0.007954
[2025-10-02 20:22:50] [Iter  674/2250] R3[296/1200] | LR: 0.026431 | E: -27.097293 | E_var:     0.3078 | E_err:   0.008668
[2025-10-02 20:22:54] [Iter  675/2250] R3[298/1200] | LR: 0.026385 | E: -27.111593 | E_var:     0.3194 | E_err:   0.008830
[2025-10-02 20:22:57] [Iter  676/2250] R3[300/1200] | LR: 0.026339 | E: -27.114732 | E_var:     0.2551 | E_err:   0.007891
[2025-10-02 20:23:01] [Iter  677/2250] R3[302/1200] | LR: 0.026292 | E: -27.093446 | E_var:     0.2431 | E_err:   0.007704
[2025-10-02 20:23:05] [Iter  678/2250] R3[304/1200] | LR: 0.026246 | E: -27.108422 | E_var:     0.2550 | E_err:   0.007890
[2025-10-02 20:23:08] [Iter  679/2250] R3[306/1200] | LR: 0.026199 | E: -27.104289 | E_var:     0.2224 | E_err:   0.007369
[2025-10-02 20:23:12] [Iter  680/2250] R3[308/1200] | LR: 0.026152 | E: -27.109826 | E_var:     0.2875 | E_err:   0.008378
[2025-10-02 20:23:16] [Iter  681/2250] R3[310/1200] | LR: 0.026104 | E: -27.096876 | E_var:     0.2610 | E_err:   0.007982
[2025-10-02 20:23:19] [Iter  682/2250] R3[312/1200] | LR: 0.026057 | E: -27.098751 | E_var:     0.2543 | E_err:   0.007880
[2025-10-02 20:23:23] [Iter  683/2250] R3[314/1200] | LR: 0.026009 | E: -27.113718 | E_var:     0.2242 | E_err:   0.007398
[2025-10-02 20:23:27] [Iter  684/2250] R3[316/1200] | LR: 0.025961 | E: -27.109552 | E_var:     0.2669 | E_err:   0.008073
[2025-10-02 20:23:30] [Iter  685/2250] R3[318/1200] | LR: 0.025913 | E: -27.107653 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 20:23:34] [Iter  686/2250] R3[320/1200] | LR: 0.025864 | E: -27.107485 | E_var:     0.3093 | E_err:   0.008689
[2025-10-02 20:23:38] [Iter  687/2250] R3[322/1200] | LR: 0.025815 | E: -27.102391 | E_var:     0.2662 | E_err:   0.008061
[2025-10-02 20:23:41] [Iter  688/2250] R3[324/1200] | LR: 0.025766 | E: -27.099472 | E_var:     0.2676 | E_err:   0.008083
[2025-10-02 20:23:45] [Iter  689/2250] R3[326/1200] | LR: 0.025717 | E: -27.115466 | E_var:     0.2173 | E_err:   0.007283
[2025-10-02 20:23:49] [Iter  690/2250] R3[328/1200] | LR: 0.025668 | E: -27.119986 | E_var:     0.3090 | E_err:   0.008686
[2025-10-02 20:23:52] [Iter  691/2250] R3[330/1200] | LR: 0.025618 | E: -27.109162 | E_var:     0.4233 | E_err:   0.010165
[2025-10-02 20:23:56] [Iter  692/2250] R3[332/1200] | LR: 0.025568 | E: -27.110715 | E_var:     0.2303 | E_err:   0.007499
[2025-10-02 20:23:59] [Iter  693/2250] R3[334/1200] | LR: 0.025518 | E: -27.099123 | E_var:     0.3439 | E_err:   0.009163
[2025-10-02 20:24:03] [Iter  694/2250] R3[336/1200] | LR: 0.025468 | E: -27.109371 | E_var:     0.2713 | E_err:   0.008138
[2025-10-02 20:24:07] [Iter  695/2250] R3[338/1200] | LR: 0.025417 | E: -27.096201 | E_var:     0.2136 | E_err:   0.007222
[2025-10-02 20:24:10] [Iter  696/2250] R3[340/1200] | LR: 0.025367 | E: -27.102501 | E_var:     0.2589 | E_err:   0.007951
[2025-10-02 20:24:14] [Iter  697/2250] R3[342/1200] | LR: 0.025316 | E: -27.111118 | E_var:     0.3557 | E_err:   0.009319
[2025-10-02 20:24:18] [Iter  698/2250] R3[344/1200] | LR: 0.025264 | E: -27.091693 | E_var:     0.1951 | E_err:   0.006901
[2025-10-02 20:24:21] [Iter  699/2250] R3[346/1200] | LR: 0.025213 | E: -27.116453 | E_var:     0.2395 | E_err:   0.007647
[2025-10-02 20:24:25] [Iter  700/2250] R3[348/1200] | LR: 0.025161 | E: -27.115187 | E_var:     0.2343 | E_err:   0.007562
[2025-10-02 20:24:29] [Iter  701/2250] R3[350/1200] | LR: 0.025110 | E: -27.106619 | E_var:     0.3183 | E_err:   0.008816
[2025-10-02 20:24:32] [Iter  702/2250] R3[352/1200] | LR: 0.025057 | E: -27.091951 | E_var:     0.2702 | E_err:   0.008123
[2025-10-02 20:24:36] [Iter  703/2250] R3[354/1200] | LR: 0.025005 | E: -27.111175 | E_var:     0.2392 | E_err:   0.007641
[2025-10-02 20:24:40] [Iter  704/2250] R3[356/1200] | LR: 0.024953 | E: -27.109779 | E_var:     0.2276 | E_err:   0.007454
[2025-10-02 20:24:43] [Iter  705/2250] R3[358/1200] | LR: 0.024900 | E: -27.099824 | E_var:     0.2647 | E_err:   0.008038
[2025-10-02 20:24:47] [Iter  706/2250] R3[360/1200] | LR: 0.024847 | E: -27.113986 | E_var:     0.2473 | E_err:   0.007771
[2025-10-02 20:24:51] [Iter  707/2250] R3[362/1200] | LR: 0.024794 | E: -27.114303 | E_var:     0.2500 | E_err:   0.007812
[2025-10-02 20:24:54] [Iter  708/2250] R3[364/1200] | LR: 0.024741 | E: -27.108442 | E_var:     0.2761 | E_err:   0.008210
[2025-10-02 20:24:58] [Iter  709/2250] R3[366/1200] | LR: 0.024688 | E: -27.119763 | E_var:     0.2452 | E_err:   0.007737
[2025-10-02 20:25:02] [Iter  710/2250] R3[368/1200] | LR: 0.024634 | E: -27.096720 | E_var:     0.2641 | E_err:   0.008030
[2025-10-02 20:25:05] [Iter  711/2250] R3[370/1200] | LR: 0.024580 | E: -27.115042 | E_var:     0.2296 | E_err:   0.007488
[2025-10-02 20:25:09] [Iter  712/2250] R3[372/1200] | LR: 0.024526 | E: -27.105709 | E_var:     0.3505 | E_err:   0.009251
[2025-10-02 20:25:13] [Iter  713/2250] R3[374/1200] | LR: 0.024472 | E: -27.103152 | E_var:     0.2334 | E_err:   0.007548
[2025-10-02 20:25:16] [Iter  714/2250] R3[376/1200] | LR: 0.024417 | E: -27.099851 | E_var:     0.2014 | E_err:   0.007012
[2025-10-02 20:25:20] [Iter  715/2250] R3[378/1200] | LR: 0.024363 | E: -27.116915 | E_var:     0.2146 | E_err:   0.007238
[2025-10-02 20:25:23] [Iter  716/2250] R3[380/1200] | LR: 0.024308 | E: -27.110378 | E_var:     0.2700 | E_err:   0.008119
[2025-10-02 20:25:27] [Iter  717/2250] R3[382/1200] | LR: 0.024253 | E: -27.120831 | E_var:     0.2241 | E_err:   0.007397
[2025-10-02 20:25:31] [Iter  718/2250] R3[384/1200] | LR: 0.024198 | E: -27.107302 | E_var:     0.2347 | E_err:   0.007569
[2025-10-02 20:25:34] [Iter  719/2250] R3[386/1200] | LR: 0.024142 | E: -27.092298 | E_var:     0.2394 | E_err:   0.007645
[2025-10-02 20:25:38] [Iter  720/2250] R3[388/1200] | LR: 0.024087 | E: -27.112066 | E_var:     0.2247 | E_err:   0.007407
[2025-10-02 20:25:42] [Iter  721/2250] R3[390/1200] | LR: 0.024031 | E: -27.102911 | E_var:     0.2362 | E_err:   0.007593
[2025-10-02 20:25:45] [Iter  722/2250] R3[392/1200] | LR: 0.023975 | E: -27.124286 | E_var:     0.1961 | E_err:   0.006918
[2025-10-02 20:25:49] [Iter  723/2250] R3[394/1200] | LR: 0.023919 | E: -27.108671 | E_var:     0.2307 | E_err:   0.007505
[2025-10-02 20:25:53] [Iter  724/2250] R3[396/1200] | LR: 0.023863 | E: -27.108030 | E_var:     0.2701 | E_err:   0.008120
[2025-10-02 20:25:56] [Iter  725/2250] R3[398/1200] | LR: 0.023807 | E: -27.103050 | E_var:     0.2276 | E_err:   0.007455
[2025-10-02 20:26:00] [Iter  726/2250] R3[400/1200] | LR: 0.023750 | E: -27.112964 | E_var:     0.2345 | E_err:   0.007566
[2025-10-02 20:26:04] [Iter  727/2250] R3[402/1200] | LR: 0.023693 | E: -27.093313 | E_var:     0.2953 | E_err:   0.008491
[2025-10-02 20:26:07] [Iter  728/2250] R3[404/1200] | LR: 0.023636 | E: -27.113830 | E_var:     0.2673 | E_err:   0.008078
[2025-10-02 20:26:11] [Iter  729/2250] R3[406/1200] | LR: 0.023579 | E: -27.103243 | E_var:     0.2484 | E_err:   0.007788
[2025-10-02 20:26:15] [Iter  730/2250] R3[408/1200] | LR: 0.023522 | E: -27.115867 | E_var:     0.2472 | E_err:   0.007769
[2025-10-02 20:26:18] [Iter  731/2250] R3[410/1200] | LR: 0.023464 | E: -27.112389 | E_var:     0.3061 | E_err:   0.008645
[2025-10-02 20:26:22] [Iter  732/2250] R3[412/1200] | LR: 0.023407 | E: -27.110409 | E_var:     0.2522 | E_err:   0.007847
[2025-10-02 20:26:26] [Iter  733/2250] R3[414/1200] | LR: 0.023349 | E: -27.105692 | E_var:     0.2655 | E_err:   0.008051
[2025-10-02 20:26:29] [Iter  734/2250] R3[416/1200] | LR: 0.023291 | E: -27.100743 | E_var:     0.2106 | E_err:   0.007170
[2025-10-02 20:26:33] [Iter  735/2250] R3[418/1200] | LR: 0.023233 | E: -27.111220 | E_var:     0.2130 | E_err:   0.007211
[2025-10-02 20:26:37] [Iter  736/2250] R3[420/1200] | LR: 0.023175 | E: -27.104028 | E_var:     0.2546 | E_err:   0.007884
[2025-10-02 20:26:40] [Iter  737/2250] R3[422/1200] | LR: 0.023116 | E: -27.117208 | E_var:     0.2407 | E_err:   0.007665
[2025-10-02 20:26:44] [Iter  738/2250] R3[424/1200] | LR: 0.023058 | E: -27.111054 | E_var:     0.2188 | E_err:   0.007309
[2025-10-02 20:26:47] [Iter  739/2250] R3[426/1200] | LR: 0.022999 | E: -27.106612 | E_var:     0.2670 | E_err:   0.008074
[2025-10-02 20:26:51] [Iter  740/2250] R3[428/1200] | LR: 0.022940 | E: -27.109119 | E_var:     0.3643 | E_err:   0.009431
[2025-10-02 20:26:55] [Iter  741/2250] R3[430/1200] | LR: 0.022881 | E: -27.099306 | E_var:     0.2324 | E_err:   0.007532
[2025-10-02 20:26:58] [Iter  742/2250] R3[432/1200] | LR: 0.022822 | E: -27.093841 | E_var:     0.2584 | E_err:   0.007942
[2025-10-02 20:27:02] [Iter  743/2250] R3[434/1200] | LR: 0.022763 | E: -27.112911 | E_var:     0.2700 | E_err:   0.008119
[2025-10-02 20:27:06] [Iter  744/2250] R3[436/1200] | LR: 0.022704 | E: -27.118048 | E_var:     0.2385 | E_err:   0.007631
[2025-10-02 20:27:09] [Iter  745/2250] R3[438/1200] | LR: 0.022644 | E: -27.122491 | E_var:     0.2128 | E_err:   0.007208
[2025-10-02 20:27:13] [Iter  746/2250] R3[440/1200] | LR: 0.022584 | E: -27.102461 | E_var:     0.2243 | E_err:   0.007400
[2025-10-02 20:27:17] [Iter  747/2250] R3[442/1200] | LR: 0.022524 | E: -27.114074 | E_var:     0.2315 | E_err:   0.007519
[2025-10-02 20:27:20] [Iter  748/2250] R3[444/1200] | LR: 0.022464 | E: -27.112699 | E_var:     0.2200 | E_err:   0.007328
[2025-10-02 20:27:24] [Iter  749/2250] R3[446/1200] | LR: 0.022404 | E: -27.096168 | E_var:     0.2585 | E_err:   0.007944
[2025-10-02 20:27:28] [Iter  750/2250] R3[448/1200] | LR: 0.022344 | E: -27.095001 | E_var:     0.3922 | E_err:   0.009786
[2025-10-02 20:27:31] [Iter  751/2250] R3[450/1200] | LR: 0.022284 | E: -27.108272 | E_var:     0.1969 | E_err:   0.006934
[2025-10-02 20:27:35] [Iter  752/2250] R3[452/1200] | LR: 0.022223 | E: -27.108992 | E_var:     0.2352 | E_err:   0.007578
[2025-10-02 20:27:39] [Iter  753/2250] R3[454/1200] | LR: 0.022162 | E: -27.107832 | E_var:     0.2317 | E_err:   0.007521
[2025-10-02 20:27:42] [Iter  754/2250] R3[456/1200] | LR: 0.022102 | E: -27.106185 | E_var:     0.1921 | E_err:   0.006848
[2025-10-02 20:27:46] [Iter  755/2250] R3[458/1200] | LR: 0.022041 | E: -27.099904 | E_var:     0.2270 | E_err:   0.007445
[2025-10-02 20:27:50] [Iter  756/2250] R3[460/1200] | LR: 0.021980 | E: -27.094777 | E_var:     0.2749 | E_err:   0.008193
[2025-10-02 20:27:53] [Iter  757/2250] R3[462/1200] | LR: 0.021918 | E: -27.107950 | E_var:     0.2711 | E_err:   0.008135
[2025-10-02 20:27:57] [Iter  758/2250] R3[464/1200] | LR: 0.021857 | E: -27.106876 | E_var:     0.2482 | E_err:   0.007784
[2025-10-02 20:28:01] [Iter  759/2250] R3[466/1200] | LR: 0.021796 | E: -27.106730 | E_var:     0.2236 | E_err:   0.007388
[2025-10-02 20:28:04] [Iter  760/2250] R3[468/1200] | LR: 0.021734 | E: -27.103126 | E_var:     0.2986 | E_err:   0.008538
[2025-10-02 20:28:08] [Iter  761/2250] R3[470/1200] | LR: 0.021673 | E: -27.118859 | E_var:     0.2493 | E_err:   0.007801
[2025-10-02 20:28:12] [Iter  762/2250] R3[472/1200] | LR: 0.021611 | E: -27.095498 | E_var:     0.3738 | E_err:   0.009554
[2025-10-02 20:28:15] [Iter  763/2250] R3[474/1200] | LR: 0.021549 | E: -27.111380 | E_var:     0.2227 | E_err:   0.007374
[2025-10-02 20:28:19] [Iter  764/2250] R3[476/1200] | LR: 0.021487 | E: -27.109611 | E_var:     0.2207 | E_err:   0.007340
[2025-10-02 20:28:23] [Iter  765/2250] R3[478/1200] | LR: 0.021425 | E: -27.101483 | E_var:     0.2456 | E_err:   0.007743
[2025-10-02 20:28:26] [Iter  766/2250] R3[480/1200] | LR: 0.021363 | E: -27.105415 | E_var:     0.2142 | E_err:   0.007232
[2025-10-02 20:28:30] [Iter  767/2250] R3[482/1200] | LR: 0.021300 | E: -27.108469 | E_var:     0.2432 | E_err:   0.007705
[2025-10-02 20:28:34] [Iter  768/2250] R3[484/1200] | LR: 0.021238 | E: -27.104192 | E_var:     0.2295 | E_err:   0.007485
[2025-10-02 20:28:37] [Iter  769/2250] R3[486/1200] | LR: 0.021176 | E: -27.120079 | E_var:     0.3031 | E_err:   0.008603
[2025-10-02 20:28:41] [Iter  770/2250] R3[488/1200] | LR: 0.021113 | E: -27.118511 | E_var:     0.2370 | E_err:   0.007607
[2025-10-02 20:28:44] [Iter  771/2250] R3[490/1200] | LR: 0.021050 | E: -27.110278 | E_var:     0.2852 | E_err:   0.008344
[2025-10-02 20:28:48] [Iter  772/2250] R3[492/1200] | LR: 0.020987 | E: -27.112809 | E_var:     0.2337 | E_err:   0.007554
[2025-10-02 20:28:52] [Iter  773/2250] R3[494/1200] | LR: 0.020924 | E: -27.104727 | E_var:     0.2047 | E_err:   0.007070
[2025-10-02 20:28:55] [Iter  774/2250] R3[496/1200] | LR: 0.020861 | E: -27.121269 | E_var:     0.2298 | E_err:   0.007490
[2025-10-02 20:28:59] [Iter  775/2250] R3[498/1200] | LR: 0.020798 | E: -27.112030 | E_var:     0.2150 | E_err:   0.007244
[2025-10-02 20:29:03] [Iter  776/2250] R3[500/1200] | LR: 0.020735 | E: -27.111473 | E_var:     0.2472 | E_err:   0.007769
[2025-10-02 20:29:06] [Iter  777/2250] R3[502/1200] | LR: 0.020672 | E: -27.114052 | E_var:     0.2668 | E_err:   0.008071
[2025-10-02 20:29:10] [Iter  778/2250] R3[504/1200] | LR: 0.020609 | E: -27.114261 | E_var:     0.2288 | E_err:   0.007474
[2025-10-02 20:29:14] [Iter  779/2250] R3[506/1200] | LR: 0.020545 | E: -27.103299 | E_var:     0.2071 | E_err:   0.007111
[2025-10-02 20:29:17] [Iter  780/2250] R3[508/1200] | LR: 0.020482 | E: -27.091579 | E_var:     0.2601 | E_err:   0.007969
[2025-10-02 20:29:21] [Iter  781/2250] R3[510/1200] | LR: 0.020418 | E: -27.109366 | E_var:     0.2113 | E_err:   0.007182
[2025-10-02 20:29:25] [Iter  782/2250] R3[512/1200] | LR: 0.020354 | E: -27.105032 | E_var:     0.2430 | E_err:   0.007702
[2025-10-02 20:29:28] [Iter  783/2250] R3[514/1200] | LR: 0.020291 | E: -27.109934 | E_var:     0.2266 | E_err:   0.007438
[2025-10-02 20:29:32] [Iter  784/2250] R3[516/1200] | LR: 0.020227 | E: -27.109320 | E_var:     0.2550 | E_err:   0.007891
[2025-10-02 20:29:36] [Iter  785/2250] R3[518/1200] | LR: 0.020163 | E: -27.117199 | E_var:     0.2333 | E_err:   0.007546
[2025-10-02 20:29:39] [Iter  786/2250] R3[520/1200] | LR: 0.020099 | E: -27.112508 | E_var:     0.2426 | E_err:   0.007696
[2025-10-02 20:29:43] [Iter  787/2250] R3[522/1200] | LR: 0.020035 | E: -27.115897 | E_var:     0.2660 | E_err:   0.008059
[2025-10-02 20:29:47] [Iter  788/2250] R3[524/1200] | LR: 0.019971 | E: -27.117324 | E_var:     0.2217 | E_err:   0.007357
[2025-10-02 20:29:50] [Iter  789/2250] R3[526/1200] | LR: 0.019907 | E: -27.105469 | E_var:     0.2159 | E_err:   0.007260
[2025-10-02 20:29:54] [Iter  790/2250] R3[528/1200] | LR: 0.019842 | E: -27.116401 | E_var:     0.1995 | E_err:   0.006979
[2025-10-02 20:29:58] [Iter  791/2250] R3[530/1200] | LR: 0.019778 | E: -27.108701 | E_var:     0.2398 | E_err:   0.007651
[2025-10-02 20:30:01] [Iter  792/2250] R3[532/1200] | LR: 0.019714 | E: -27.111997 | E_var:     0.2255 | E_err:   0.007419
[2025-10-02 20:30:05] [Iter  793/2250] R3[534/1200] | LR: 0.019649 | E: -27.104518 | E_var:     0.2606 | E_err:   0.007977
[2025-10-02 20:30:08] [Iter  794/2250] R3[536/1200] | LR: 0.019585 | E: -27.102945 | E_var:     0.2543 | E_err:   0.007879
[2025-10-02 20:30:12] [Iter  795/2250] R3[538/1200] | LR: 0.019520 | E: -27.100311 | E_var:     0.2176 | E_err:   0.007289
[2025-10-02 20:30:16] [Iter  796/2250] R3[540/1200] | LR: 0.019455 | E: -27.117059 | E_var:     0.2771 | E_err:   0.008225
[2025-10-02 20:30:19] [Iter  797/2250] R3[542/1200] | LR: 0.019391 | E: -27.113360 | E_var:     0.2366 | E_err:   0.007600
[2025-10-02 20:30:23] [Iter  798/2250] R3[544/1200] | LR: 0.019326 | E: -27.111363 | E_var:     0.2395 | E_err:   0.007647
[2025-10-02 20:30:27] [Iter  799/2250] R3[546/1200] | LR: 0.019261 | E: -27.109734 | E_var:     0.2545 | E_err:   0.007882
[2025-10-02 20:30:30] [Iter  800/2250] R3[548/1200] | LR: 0.019196 | E: -27.108632 | E_var:     0.2729 | E_err:   0.008163
[2025-10-02 20:30:30] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-02 20:30:34] [Iter  801/2250] R3[550/1200] | LR: 0.019132 | E: -27.111400 | E_var:     0.2679 | E_err:   0.008088
[2025-10-02 20:30:38] [Iter  802/2250] R3[552/1200] | LR: 0.019067 | E: -27.114491 | E_var:     0.2295 | E_err:   0.007486
[2025-10-02 20:30:41] [Iter  803/2250] R3[554/1200] | LR: 0.019002 | E: -27.116959 | E_var:     0.3257 | E_err:   0.008917
[2025-10-02 20:30:45] [Iter  804/2250] R3[556/1200] | LR: 0.018937 | E: -27.109922 | E_var:     0.2878 | E_err:   0.008382
[2025-10-02 20:30:49] [Iter  805/2250] R3[558/1200] | LR: 0.018872 | E: -27.107500 | E_var:     0.2690 | E_err:   0.008103
[2025-10-02 20:30:52] [Iter  806/2250] R3[560/1200] | LR: 0.018807 | E: -27.119110 | E_var:     0.2033 | E_err:   0.007045
[2025-10-02 20:30:56] [Iter  807/2250] R3[562/1200] | LR: 0.018741 | E: -27.114533 | E_var:     0.2216 | E_err:   0.007356
[2025-10-02 20:31:00] [Iter  808/2250] R3[564/1200] | LR: 0.018676 | E: -27.097312 | E_var:     0.2777 | E_err:   0.008235
[2025-10-02 20:31:03] [Iter  809/2250] R3[566/1200] | LR: 0.018611 | E: -27.113020 | E_var:     0.2372 | E_err:   0.007610
[2025-10-02 20:31:07] [Iter  810/2250] R3[568/1200] | LR: 0.018546 | E: -27.113205 | E_var:     0.2440 | E_err:   0.007718
[2025-10-02 20:31:11] [Iter  811/2250] R3[570/1200] | LR: 0.018481 | E: -27.109449 | E_var:     0.2463 | E_err:   0.007754
[2025-10-02 20:31:14] [Iter  812/2250] R3[572/1200] | LR: 0.018415 | E: -27.112026 | E_var:     0.2427 | E_err:   0.007698
[2025-10-02 20:31:18] [Iter  813/2250] R3[574/1200] | LR: 0.018350 | E: -27.110618 | E_var:     0.2413 | E_err:   0.007676
[2025-10-02 20:31:22] [Iter  814/2250] R3[576/1200] | LR: 0.018285 | E: -27.113470 | E_var:     0.2086 | E_err:   0.007136
[2025-10-02 20:31:25] [Iter  815/2250] R3[578/1200] | LR: 0.018220 | E: -27.112062 | E_var:     0.2178 | E_err:   0.007291
[2025-10-02 20:31:29] [Iter  816/2250] R3[580/1200] | LR: 0.018154 | E: -27.107765 | E_var:     0.2916 | E_err:   0.008437
[2025-10-02 20:31:33] [Iter  817/2250] R3[582/1200] | LR: 0.018089 | E: -27.100812 | E_var:     0.2427 | E_err:   0.007698
[2025-10-02 20:31:36] [Iter  818/2250] R3[584/1200] | LR: 0.018023 | E: -27.107761 | E_var:     0.2600 | E_err:   0.007967
[2025-10-02 20:31:40] [Iter  819/2250] R3[586/1200] | LR: 0.017958 | E: -27.115656 | E_var:     0.2344 | E_err:   0.007565
[2025-10-02 20:31:44] [Iter  820/2250] R3[588/1200] | LR: 0.017893 | E: -27.108061 | E_var:     0.2433 | E_err:   0.007707
[2025-10-02 20:31:47] [Iter  821/2250] R3[590/1200] | LR: 0.017827 | E: -27.103258 | E_var:     0.2628 | E_err:   0.008010
[2025-10-02 20:31:51] [Iter  822/2250] R3[592/1200] | LR: 0.017762 | E: -27.122246 | E_var:     0.2167 | E_err:   0.007274
[2025-10-02 20:31:55] [Iter  823/2250] R3[594/1200] | LR: 0.017696 | E: -27.111715 | E_var:     0.2795 | E_err:   0.008260
[2025-10-02 20:31:58] [Iter  824/2250] R3[596/1200] | LR: 0.017631 | E: -27.113524 | E_var:     0.2246 | E_err:   0.007405
[2025-10-02 20:32:02] [Iter  825/2250] R3[598/1200] | LR: 0.017565 | E: -27.113281 | E_var:     0.1890 | E_err:   0.006793
[2025-10-02 20:32:05] [Iter  826/2250] R3[600/1200] | LR: 0.017500 | E: -27.100820 | E_var:     0.2541 | E_err:   0.007876
[2025-10-02 20:32:09] [Iter  827/2250] R3[602/1200] | LR: 0.017435 | E: -27.113941 | E_var:     0.2313 | E_err:   0.007514
[2025-10-02 20:32:13] [Iter  828/2250] R3[604/1200] | LR: 0.017369 | E: -27.106842 | E_var:     0.2560 | E_err:   0.007906
[2025-10-02 20:32:16] [Iter  829/2250] R3[606/1200] | LR: 0.017304 | E: -27.114570 | E_var:     0.2960 | E_err:   0.008500
[2025-10-02 20:32:20] [Iter  830/2250] R3[608/1200] | LR: 0.017238 | E: -27.114818 | E_var:     0.2169 | E_err:   0.007277
[2025-10-02 20:32:24] [Iter  831/2250] R3[610/1200] | LR: 0.017173 | E: -27.111105 | E_var:     0.2168 | E_err:   0.007276
[2025-10-02 20:32:27] [Iter  832/2250] R3[612/1200] | LR: 0.017107 | E: -27.108248 | E_var:     0.2032 | E_err:   0.007044
[2025-10-02 20:32:31] [Iter  833/2250] R3[614/1200] | LR: 0.017042 | E: -27.118841 | E_var:     0.2179 | E_err:   0.007294
[2025-10-02 20:32:35] [Iter  834/2250] R3[616/1200] | LR: 0.016977 | E: -27.114983 | E_var:     0.3379 | E_err:   0.009083
[2025-10-02 20:32:38] [Iter  835/2250] R3[618/1200] | LR: 0.016911 | E: -27.125258 | E_var:     0.1757 | E_err:   0.006549
[2025-10-02 20:32:42] [Iter  836/2250] R3[620/1200] | LR: 0.016846 | E: -27.112588 | E_var:     0.2673 | E_err:   0.008078
[2025-10-02 20:32:46] [Iter  837/2250] R3[622/1200] | LR: 0.016780 | E: -27.119947 | E_var:     0.1986 | E_err:   0.006963
[2025-10-02 20:32:49] [Iter  838/2250] R3[624/1200] | LR: 0.016715 | E: -27.115526 | E_var:     0.2781 | E_err:   0.008239
[2025-10-02 20:32:53] [Iter  839/2250] R3[626/1200] | LR: 0.016650 | E: -27.127172 | E_var:     0.2311 | E_err:   0.007512
[2025-10-02 20:32:57] [Iter  840/2250] R3[628/1200] | LR: 0.016585 | E: -27.105801 | E_var:     0.3258 | E_err:   0.008919
[2025-10-02 20:33:00] [Iter  841/2250] R3[630/1200] | LR: 0.016519 | E: -27.119133 | E_var:     0.2225 | E_err:   0.007370
[2025-10-02 20:33:04] [Iter  842/2250] R3[632/1200] | LR: 0.016454 | E: -27.108608 | E_var:     0.2042 | E_err:   0.007060
[2025-10-02 20:33:08] [Iter  843/2250] R3[634/1200] | LR: 0.016389 | E: -27.115182 | E_var:     0.1888 | E_err:   0.006789
[2025-10-02 20:33:11] [Iter  844/2250] R3[636/1200] | LR: 0.016324 | E: -27.119917 | E_var:     0.2432 | E_err:   0.007706
[2025-10-02 20:33:15] [Iter  845/2250] R3[638/1200] | LR: 0.016259 | E: -27.112007 | E_var:     0.2506 | E_err:   0.007822
[2025-10-02 20:33:19] [Iter  846/2250] R3[640/1200] | LR: 0.016193 | E: -27.117087 | E_var:     0.2159 | E_err:   0.007260
[2025-10-02 20:33:22] [Iter  847/2250] R3[642/1200] | LR: 0.016128 | E: -27.119182 | E_var:     0.2916 | E_err:   0.008437
[2025-10-02 20:33:26] [Iter  848/2250] R3[644/1200] | LR: 0.016063 | E: -27.121106 | E_var:     0.2363 | E_err:   0.007596
[2025-10-02 20:33:30] [Iter  849/2250] R3[646/1200] | LR: 0.015998 | E: -27.115238 | E_var:     0.2204 | E_err:   0.007335
[2025-10-02 20:33:33] [Iter  850/2250] R3[648/1200] | LR: 0.015933 | E: -27.125870 | E_var:     0.3359 | E_err:   0.009055
[2025-10-02 20:33:37] [Iter  851/2250] R3[650/1200] | LR: 0.015868 | E: -27.114156 | E_var:     0.2746 | E_err:   0.008187
[2025-10-02 20:33:40] [Iter  852/2250] R3[652/1200] | LR: 0.015804 | E: -27.122065 | E_var:     0.2152 | E_err:   0.007248
[2025-10-02 20:33:44] [Iter  853/2250] R3[654/1200] | LR: 0.015739 | E: -27.116076 | E_var:     0.2176 | E_err:   0.007289
[2025-10-02 20:33:48] [Iter  854/2250] R3[656/1200] | LR: 0.015674 | E: -27.103492 | E_var:     0.2590 | E_err:   0.007952
[2025-10-02 20:33:51] [Iter  855/2250] R3[658/1200] | LR: 0.015609 | E: -27.115911 | E_var:     0.2096 | E_err:   0.007153
[2025-10-02 20:33:55] [Iter  856/2250] R3[660/1200] | LR: 0.015545 | E: -27.122535 | E_var:     0.1924 | E_err:   0.006853
[2025-10-02 20:33:59] [Iter  857/2250] R3[662/1200] | LR: 0.015480 | E: -27.109991 | E_var:     0.2011 | E_err:   0.007006
[2025-10-02 20:34:02] [Iter  858/2250] R3[664/1200] | LR: 0.015415 | E: -27.094027 | E_var:     0.2388 | E_err:   0.007635
[2025-10-02 20:34:06] [Iter  859/2250] R3[666/1200] | LR: 0.015351 | E: -27.132547 | E_var:     0.2627 | E_err:   0.008009
[2025-10-02 20:34:10] [Iter  860/2250] R3[668/1200] | LR: 0.015286 | E: -27.118515 | E_var:     0.1953 | E_err:   0.006906
[2025-10-02 20:34:13] [Iter  861/2250] R3[670/1200] | LR: 0.015222 | E: -27.118812 | E_var:     0.2039 | E_err:   0.007055
[2025-10-02 20:34:17] [Iter  862/2250] R3[672/1200] | LR: 0.015158 | E: -27.102325 | E_var:     0.3190 | E_err:   0.008825
[2025-10-02 20:34:21] [Iter  863/2250] R3[674/1200] | LR: 0.015093 | E: -27.118082 | E_var:     0.2311 | E_err:   0.007511
[2025-10-02 20:34:24] [Iter  864/2250] R3[676/1200] | LR: 0.015029 | E: -27.103507 | E_var:     0.2492 | E_err:   0.007800
[2025-10-02 20:34:28] [Iter  865/2250] R3[678/1200] | LR: 0.014965 | E: -27.113031 | E_var:     0.1709 | E_err:   0.006460
[2025-10-02 20:34:32] [Iter  866/2250] R3[680/1200] | LR: 0.014901 | E: -27.106478 | E_var:     0.2321 | E_err:   0.007527
[2025-10-02 20:34:35] [Iter  867/2250] R3[682/1200] | LR: 0.014837 | E: -27.091518 | E_var:     0.3114 | E_err:   0.008719
[2025-10-02 20:34:39] [Iter  868/2250] R3[684/1200] | LR: 0.014773 | E: -27.132266 | E_var:     0.1964 | E_err:   0.006924
[2025-10-02 20:34:43] [Iter  869/2250] R3[686/1200] | LR: 0.014709 | E: -27.120055 | E_var:     0.2310 | E_err:   0.007509
[2025-10-02 20:34:46] [Iter  870/2250] R3[688/1200] | LR: 0.014646 | E: -27.117182 | E_var:     0.2310 | E_err:   0.007510
[2025-10-02 20:34:50] [Iter  871/2250] R3[690/1200] | LR: 0.014582 | E: -27.112217 | E_var:     0.3835 | E_err:   0.009676
[2025-10-02 20:34:54] [Iter  872/2250] R3[692/1200] | LR: 0.014518 | E: -27.104619 | E_var:     0.2700 | E_err:   0.008120
[2025-10-02 20:34:57] [Iter  873/2250] R3[694/1200] | LR: 0.014455 | E: -27.115095 | E_var:     0.3413 | E_err:   0.009128
[2025-10-02 20:35:01] [Iter  874/2250] R3[696/1200] | LR: 0.014391 | E: -27.109925 | E_var:     0.2634 | E_err:   0.008020
[2025-10-02 20:35:05] [Iter  875/2250] R3[698/1200] | LR: 0.014328 | E: -27.116784 | E_var:     0.2054 | E_err:   0.007081
[2025-10-02 20:35:08] [Iter  876/2250] R3[700/1200] | LR: 0.014265 | E: -27.105752 | E_var:     0.3110 | E_err:   0.008713
[2025-10-02 20:35:12] [Iter  877/2250] R3[702/1200] | LR: 0.014202 | E: -27.126227 | E_var:     0.2041 | E_err:   0.007060
[2025-10-02 20:35:16] [Iter  878/2250] R3[704/1200] | LR: 0.014139 | E: -27.114046 | E_var:     0.2330 | E_err:   0.007543
[2025-10-02 20:35:19] [Iter  879/2250] R3[706/1200] | LR: 0.014076 | E: -27.118838 | E_var:     0.1826 | E_err:   0.006677
[2025-10-02 20:35:23] [Iter  880/2250] R3[708/1200] | LR: 0.014013 | E: -27.112760 | E_var:     0.2144 | E_err:   0.007235
[2025-10-02 20:35:26] [Iter  881/2250] R3[710/1200] | LR: 0.013950 | E: -27.107992 | E_var:     0.2505 | E_err:   0.007820
[2025-10-02 20:35:30] [Iter  882/2250] R3[712/1200] | LR: 0.013887 | E: -27.108971 | E_var:     0.2142 | E_err:   0.007231
[2025-10-02 20:35:34] [Iter  883/2250] R3[714/1200] | LR: 0.013824 | E: -27.109374 | E_var:     0.2263 | E_err:   0.007432
[2025-10-02 20:35:37] [Iter  884/2250] R3[716/1200] | LR: 0.013762 | E: -27.108966 | E_var:     0.2120 | E_err:   0.007195
[2025-10-02 20:35:41] [Iter  885/2250] R3[718/1200] | LR: 0.013700 | E: -27.115078 | E_var:     0.2104 | E_err:   0.007167
[2025-10-02 20:35:45] [Iter  886/2250] R3[720/1200] | LR: 0.013637 | E: -27.106599 | E_var:     0.2186 | E_err:   0.007305
[2025-10-02 20:35:48] [Iter  887/2250] R3[722/1200] | LR: 0.013575 | E: -27.103479 | E_var:     0.2111 | E_err:   0.007179
[2025-10-02 20:35:52] [Iter  888/2250] R3[724/1200] | LR: 0.013513 | E: -27.120141 | E_var:     0.1980 | E_err:   0.006953
[2025-10-02 20:35:56] [Iter  889/2250] R3[726/1200] | LR: 0.013451 | E: -27.115509 | E_var:     0.2137 | E_err:   0.007223
[2025-10-02 20:35:59] [Iter  890/2250] R3[728/1200] | LR: 0.013389 | E: -27.108021 | E_var:     0.2289 | E_err:   0.007475
[2025-10-02 20:36:03] [Iter  891/2250] R3[730/1200] | LR: 0.013327 | E: -27.112202 | E_var:     0.2193 | E_err:   0.007317
[2025-10-02 20:36:07] [Iter  892/2250] R3[732/1200] | LR: 0.013266 | E: -27.120271 | E_var:     0.1958 | E_err:   0.006915
[2025-10-02 20:36:10] [Iter  893/2250] R3[734/1200] | LR: 0.013204 | E: -27.118082 | E_var:     0.3093 | E_err:   0.008690
[2025-10-02 20:36:14] [Iter  894/2250] R3[736/1200] | LR: 0.013143 | E: -27.110650 | E_var:     0.2772 | E_err:   0.008226
[2025-10-02 20:36:18] [Iter  895/2250] R3[738/1200] | LR: 0.013082 | E: -27.125615 | E_var:     0.1951 | E_err:   0.006902
[2025-10-02 20:36:21] [Iter  896/2250] R3[740/1200] | LR: 0.013020 | E: -27.108195 | E_var:     0.2212 | E_err:   0.007349
[2025-10-02 20:36:25] [Iter  897/2250] R3[742/1200] | LR: 0.012959 | E: -27.113579 | E_var:     0.2023 | E_err:   0.007028
[2025-10-02 20:36:29] [Iter  898/2250] R3[744/1200] | LR: 0.012898 | E: -27.120317 | E_var:     0.2043 | E_err:   0.007063
[2025-10-02 20:36:32] [Iter  899/2250] R3[746/1200] | LR: 0.012838 | E: -27.096402 | E_var:     0.2648 | E_err:   0.008041
[2025-10-02 20:36:36] [Iter  900/2250] R3[748/1200] | LR: 0.012777 | E: -27.117507 | E_var:     0.1985 | E_err:   0.006961
[2025-10-02 20:36:40] [Iter  901/2250] R3[750/1200] | LR: 0.012716 | E: -27.118095 | E_var:     0.2149 | E_err:   0.007244
[2025-10-02 20:36:43] [Iter  902/2250] R3[752/1200] | LR: 0.012656 | E: -27.130523 | E_var:     0.2088 | E_err:   0.007140
[2025-10-02 20:36:47] [Iter  903/2250] R3[754/1200] | LR: 0.012596 | E: -27.126554 | E_var:     0.1944 | E_err:   0.006890
[2025-10-02 20:36:50] [Iter  904/2250] R3[756/1200] | LR: 0.012536 | E: -27.110163 | E_var:     0.2404 | E_err:   0.007662
[2025-10-02 20:36:54] [Iter  905/2250] R3[758/1200] | LR: 0.012476 | E: -27.106468 | E_var:     0.2349 | E_err:   0.007573
[2025-10-02 20:36:58] [Iter  906/2250] R3[760/1200] | LR: 0.012416 | E: -27.098682 | E_var:     0.2682 | E_err:   0.008092
[2025-10-02 20:37:01] [Iter  907/2250] R3[762/1200] | LR: 0.012356 | E: -27.118555 | E_var:     0.1893 | E_err:   0.006798
[2025-10-02 20:37:05] [Iter  908/2250] R3[764/1200] | LR: 0.012296 | E: -27.115811 | E_var:     0.1759 | E_err:   0.006553
[2025-10-02 20:37:09] [Iter  909/2250] R3[766/1200] | LR: 0.012237 | E: -27.100614 | E_var:     0.2274 | E_err:   0.007451
[2025-10-02 20:37:12] [Iter  910/2250] R3[768/1200] | LR: 0.012178 | E: -27.112459 | E_var:     0.2373 | E_err:   0.007611
[2025-10-02 20:37:16] [Iter  911/2250] R3[770/1200] | LR: 0.012119 | E: -27.128274 | E_var:     0.2069 | E_err:   0.007106
[2025-10-02 20:37:20] [Iter  912/2250] R3[772/1200] | LR: 0.012060 | E: -27.120297 | E_var:     0.1995 | E_err:   0.006978
[2025-10-02 20:37:23] [Iter  913/2250] R3[774/1200] | LR: 0.012001 | E: -27.127016 | E_var:     0.2267 | E_err:   0.007440
[2025-10-02 20:37:27] [Iter  914/2250] R3[776/1200] | LR: 0.011942 | E: -27.114450 | E_var:     0.2304 | E_err:   0.007500
[2025-10-02 20:37:31] [Iter  915/2250] R3[778/1200] | LR: 0.011884 | E: -27.126344 | E_var:     0.2130 | E_err:   0.007211
[2025-10-02 20:37:34] [Iter  916/2250] R3[780/1200] | LR: 0.011825 | E: -27.125292 | E_var:     0.2176 | E_err:   0.007289
[2025-10-02 20:37:38] [Iter  917/2250] R3[782/1200] | LR: 0.011767 | E: -27.122830 | E_var:     0.1867 | E_err:   0.006751
[2025-10-02 20:37:42] [Iter  918/2250] R3[784/1200] | LR: 0.011709 | E: -27.109463 | E_var:     0.2767 | E_err:   0.008219
[2025-10-02 20:37:45] [Iter  919/2250] R3[786/1200] | LR: 0.011651 | E: -27.123661 | E_var:     0.2974 | E_err:   0.008521
[2025-10-02 20:37:49] [Iter  920/2250] R3[788/1200] | LR: 0.011593 | E: -27.113603 | E_var:     0.1896 | E_err:   0.006803
[2025-10-02 20:37:53] [Iter  921/2250] R3[790/1200] | LR: 0.011536 | E: -27.122169 | E_var:     0.2289 | E_err:   0.007476
[2025-10-02 20:37:56] [Iter  922/2250] R3[792/1200] | LR: 0.011478 | E: -27.106860 | E_var:     0.2330 | E_err:   0.007542
[2025-10-02 20:38:00] [Iter  923/2250] R3[794/1200] | LR: 0.011421 | E: -27.112371 | E_var:     0.1978 | E_err:   0.006949
[2025-10-02 20:38:04] [Iter  924/2250] R3[796/1200] | LR: 0.011364 | E: -27.124541 | E_var:     0.1918 | E_err:   0.006844
[2025-10-02 20:38:07] [Iter  925/2250] R3[798/1200] | LR: 0.011307 | E: -27.115045 | E_var:     0.2570 | E_err:   0.007921
[2025-10-02 20:38:11] [Iter  926/2250] R3[800/1200] | LR: 0.011250 | E: -27.114919 | E_var:     0.1961 | E_err:   0.006920
[2025-10-02 20:38:14] [Iter  927/2250] R3[802/1200] | LR: 0.011193 | E: -27.118325 | E_var:     0.2508 | E_err:   0.007825
[2025-10-02 20:38:18] [Iter  928/2250] R3[804/1200] | LR: 0.011137 | E: -27.118337 | E_var:     0.1764 | E_err:   0.006562
[2025-10-02 20:38:22] [Iter  929/2250] R3[806/1200] | LR: 0.011081 | E: -27.120198 | E_var:     0.1938 | E_err:   0.006879
[2025-10-02 20:38:25] [Iter  930/2250] R3[808/1200] | LR: 0.011025 | E: -27.105737 | E_var:     0.2190 | E_err:   0.007312
[2025-10-02 20:38:29] [Iter  931/2250] R3[810/1200] | LR: 0.010969 | E: -27.123173 | E_var:     0.2822 | E_err:   0.008300
[2025-10-02 20:38:33] [Iter  932/2250] R3[812/1200] | LR: 0.010913 | E: -27.119888 | E_var:     0.2202 | E_err:   0.007332
[2025-10-02 20:38:36] [Iter  933/2250] R3[814/1200] | LR: 0.010858 | E: -27.112153 | E_var:     0.2606 | E_err:   0.007976
[2025-10-02 20:38:40] [Iter  934/2250] R3[816/1200] | LR: 0.010802 | E: -27.117883 | E_var:     0.2232 | E_err:   0.007382
[2025-10-02 20:38:44] [Iter  935/2250] R3[818/1200] | LR: 0.010747 | E: -27.115134 | E_var:     0.2218 | E_err:   0.007359
[2025-10-02 20:38:47] [Iter  936/2250] R3[820/1200] | LR: 0.010692 | E: -27.126259 | E_var:     0.2629 | E_err:   0.008012
[2025-10-02 20:38:51] [Iter  937/2250] R3[822/1200] | LR: 0.010637 | E: -27.117350 | E_var:     0.1857 | E_err:   0.006732
[2025-10-02 20:38:55] [Iter  938/2250] R3[824/1200] | LR: 0.010583 | E: -27.115750 | E_var:     0.2004 | E_err:   0.006994
[2025-10-02 20:38:58] [Iter  939/2250] R3[826/1200] | LR: 0.010528 | E: -27.112838 | E_var:     0.2571 | E_err:   0.007923
[2025-10-02 20:39:02] [Iter  940/2250] R3[828/1200] | LR: 0.010474 | E: -27.111865 | E_var:     0.1819 | E_err:   0.006664
[2025-10-02 20:39:06] [Iter  941/2250] R3[830/1200] | LR: 0.010420 | E: -27.126191 | E_var:     0.2192 | E_err:   0.007316
[2025-10-02 20:39:09] [Iter  942/2250] R3[832/1200] | LR: 0.010366 | E: -27.107573 | E_var:     0.2758 | E_err:   0.008205
[2025-10-02 20:39:13] [Iter  943/2250] R3[834/1200] | LR: 0.010312 | E: -27.121331 | E_var:     0.2059 | E_err:   0.007090
[2025-10-02 20:39:17] [Iter  944/2250] R3[836/1200] | LR: 0.010259 | E: -27.102708 | E_var:     0.3071 | E_err:   0.008658
[2025-10-02 20:39:20] [Iter  945/2250] R3[838/1200] | LR: 0.010206 | E: -27.112118 | E_var:     0.2379 | E_err:   0.007622
[2025-10-02 20:39:24] [Iter  946/2250] R3[840/1200] | LR: 0.010153 | E: -27.115468 | E_var:     0.2213 | E_err:   0.007350
[2025-10-02 20:39:28] [Iter  947/2250] R3[842/1200] | LR: 0.010100 | E: -27.116542 | E_var:     0.1707 | E_err:   0.006455
[2025-10-02 20:39:31] [Iter  948/2250] R3[844/1200] | LR: 0.010047 | E: -27.118394 | E_var:     0.2217 | E_err:   0.007358
[2025-10-02 20:39:35] [Iter  949/2250] R3[846/1200] | LR: 0.009995 | E: -27.114227 | E_var:     0.3139 | E_err:   0.008754
[2025-10-02 20:39:39] [Iter  950/2250] R3[848/1200] | LR: 0.009943 | E: -27.118690 | E_var:     0.2017 | E_err:   0.007017
[2025-10-02 20:39:42] [Iter  951/2250] R3[850/1200] | LR: 0.009890 | E: -27.119120 | E_var:     0.2715 | E_err:   0.008142
[2025-10-02 20:39:46] [Iter  952/2250] R3[852/1200] | LR: 0.009839 | E: -27.111054 | E_var:     0.2914 | E_err:   0.008434
[2025-10-02 20:39:49] [Iter  953/2250] R3[854/1200] | LR: 0.009787 | E: -27.123308 | E_var:     0.2082 | E_err:   0.007129
[2025-10-02 20:39:53] [Iter  954/2250] R3[856/1200] | LR: 0.009736 | E: -27.116591 | E_var:     0.2025 | E_err:   0.007031
[2025-10-02 20:39:57] [Iter  955/2250] R3[858/1200] | LR: 0.009684 | E: -27.124087 | E_var:     0.1915 | E_err:   0.006837
[2025-10-02 20:40:00] [Iter  956/2250] R3[860/1200] | LR: 0.009633 | E: -27.114179 | E_var:     0.2312 | E_err:   0.007512
[2025-10-02 20:40:04] [Iter  957/2250] R3[862/1200] | LR: 0.009583 | E: -27.124271 | E_var:     0.2240 | E_err:   0.007394
[2025-10-02 20:40:08] [Iter  958/2250] R3[864/1200] | LR: 0.009532 | E: -27.116643 | E_var:     0.2112 | E_err:   0.007181
[2025-10-02 20:40:11] [Iter  959/2250] R3[866/1200] | LR: 0.009482 | E: -27.123867 | E_var:     0.2169 | E_err:   0.007276
[2025-10-02 20:40:15] [Iter  960/2250] R3[868/1200] | LR: 0.009432 | E: -27.124193 | E_var:     0.1980 | E_err:   0.006953
[2025-10-02 20:40:19] [Iter  961/2250] R3[870/1200] | LR: 0.009382 | E: -27.130185 | E_var:     0.1891 | E_err:   0.006794
[2025-10-02 20:40:22] [Iter  962/2250] R3[872/1200] | LR: 0.009332 | E: -27.121235 | E_var:     0.2010 | E_err:   0.007005
[2025-10-02 20:40:26] [Iter  963/2250] R3[874/1200] | LR: 0.009283 | E: -27.102757 | E_var:     0.2150 | E_err:   0.007245
[2025-10-02 20:40:30] [Iter  964/2250] R3[876/1200] | LR: 0.009234 | E: -27.117926 | E_var:     0.2297 | E_err:   0.007488
[2025-10-02 20:40:33] [Iter  965/2250] R3[878/1200] | LR: 0.009185 | E: -27.113265 | E_var:     0.2114 | E_err:   0.007184
[2025-10-02 20:40:37] [Iter  966/2250] R3[880/1200] | LR: 0.009136 | E: -27.120722 | E_var:     0.1822 | E_err:   0.006670
[2025-10-02 20:40:41] [Iter  967/2250] R3[882/1200] | LR: 0.009087 | E: -27.125663 | E_var:     0.1781 | E_err:   0.006595
[2025-10-02 20:40:44] [Iter  968/2250] R3[884/1200] | LR: 0.009039 | E: -27.122746 | E_var:     0.1853 | E_err:   0.006726
[2025-10-02 20:40:48] [Iter  969/2250] R3[886/1200] | LR: 0.008991 | E: -27.115977 | E_var:     0.1668 | E_err:   0.006382
[2025-10-02 20:40:52] [Iter  970/2250] R3[888/1200] | LR: 0.008943 | E: -27.127519 | E_var:     0.1890 | E_err:   0.006793
[2025-10-02 20:40:55] [Iter  971/2250] R3[890/1200] | LR: 0.008896 | E: -27.120846 | E_var:     0.2109 | E_err:   0.007176
[2025-10-02 20:40:59] [Iter  972/2250] R3[892/1200] | LR: 0.008848 | E: -27.109244 | E_var:     0.2410 | E_err:   0.007670
[2025-10-02 20:41:02] [Iter  973/2250] R3[894/1200] | LR: 0.008801 | E: -27.108680 | E_var:     0.1942 | E_err:   0.006886
[2025-10-02 20:41:06] [Iter  974/2250] R3[896/1200] | LR: 0.008754 | E: -27.113523 | E_var:     0.1956 | E_err:   0.006910
[2025-10-02 20:41:10] [Iter  975/2250] R3[898/1200] | LR: 0.008708 | E: -27.122558 | E_var:     0.2279 | E_err:   0.007460
[2025-10-02 20:41:13] [Iter  976/2250] R3[900/1200] | LR: 0.008661 | E: -27.115708 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 20:41:17] [Iter  977/2250] R3[902/1200] | LR: 0.008615 | E: -27.120960 | E_var:     0.2314 | E_err:   0.007516
[2025-10-02 20:41:21] [Iter  978/2250] R3[904/1200] | LR: 0.008569 | E: -27.125676 | E_var:     0.2404 | E_err:   0.007661
[2025-10-02 20:41:24] [Iter  979/2250] R3[906/1200] | LR: 0.008523 | E: -27.117042 | E_var:     0.2068 | E_err:   0.007106
[2025-10-02 20:41:28] [Iter  980/2250] R3[908/1200] | LR: 0.008478 | E: -27.109758 | E_var:     0.2241 | E_err:   0.007397
[2025-10-02 20:41:32] [Iter  981/2250] R3[910/1200] | LR: 0.008433 | E: -27.124150 | E_var:     0.1857 | E_err:   0.006732
[2025-10-02 20:41:35] [Iter  982/2250] R3[912/1200] | LR: 0.008388 | E: -27.110731 | E_var:     0.2105 | E_err:   0.007169
[2025-10-02 20:41:39] [Iter  983/2250] R3[914/1200] | LR: 0.008343 | E: -27.107279 | E_var:     0.1986 | E_err:   0.006964
[2025-10-02 20:41:43] [Iter  984/2250] R3[916/1200] | LR: 0.008299 | E: -27.111898 | E_var:     0.1611 | E_err:   0.006272
[2025-10-02 20:41:46] [Iter  985/2250] R3[918/1200] | LR: 0.008255 | E: -27.124073 | E_var:     0.2122 | E_err:   0.007197
[2025-10-02 20:41:50] [Iter  986/2250] R3[920/1200] | LR: 0.008211 | E: -27.129727 | E_var:     0.1761 | E_err:   0.006556
[2025-10-02 20:41:54] [Iter  987/2250] R3[922/1200] | LR: 0.008167 | E: -27.118809 | E_var:     0.2264 | E_err:   0.007435
[2025-10-02 20:41:57] [Iter  988/2250] R3[924/1200] | LR: 0.008124 | E: -27.123119 | E_var:     0.2640 | E_err:   0.008028
[2025-10-02 20:42:01] [Iter  989/2250] R3[926/1200] | LR: 0.008080 | E: -27.111601 | E_var:     0.2391 | E_err:   0.007640
[2025-10-02 20:42:05] [Iter  990/2250] R3[928/1200] | LR: 0.008038 | E: -27.110638 | E_var:     0.2178 | E_err:   0.007292
[2025-10-02 20:42:08] [Iter  991/2250] R3[930/1200] | LR: 0.007995 | E: -27.122142 | E_var:     0.2168 | E_err:   0.007276
[2025-10-02 20:42:12] [Iter  992/2250] R3[932/1200] | LR: 0.007953 | E: -27.117623 | E_var:     0.1897 | E_err:   0.006806
[2025-10-02 20:42:16] [Iter  993/2250] R3[934/1200] | LR: 0.007910 | E: -27.108184 | E_var:     0.3325 | E_err:   0.009010
[2025-10-02 20:42:19] [Iter  994/2250] R3[936/1200] | LR: 0.007869 | E: -27.125956 | E_var:     0.2519 | E_err:   0.007842
[2025-10-02 20:42:23] [Iter  995/2250] R3[938/1200] | LR: 0.007827 | E: -27.110103 | E_var:     0.1755 | E_err:   0.006547
[2025-10-02 20:42:27] [Iter  996/2250] R3[940/1200] | LR: 0.007786 | E: -27.120005 | E_var:     0.2040 | E_err:   0.007057
[2025-10-02 20:42:30] [Iter  997/2250] R3[942/1200] | LR: 0.007745 | E: -27.122426 | E_var:     0.2324 | E_err:   0.007532
[2025-10-02 20:42:34] [Iter  998/2250] R3[944/1200] | LR: 0.007704 | E: -27.123206 | E_var:     0.2387 | E_err:   0.007634
[2025-10-02 20:42:38] [Iter  999/2250] R3[946/1200] | LR: 0.007663 | E: -27.107462 | E_var:     0.1892 | E_err:   0.006796
[2025-10-02 20:42:41] [Iter 1000/2250] R3[948/1200] | LR: 0.007623 | E: -27.127856 | E_var:     0.2018 | E_err:   0.007020
[2025-10-02 20:42:41] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-02 20:42:45] [Iter 1001/2250] R3[950/1200] | LR: 0.007583 | E: -27.124766 | E_var:     0.1878 | E_err:   0.006771
[2025-10-02 20:42:49] [Iter 1002/2250] R3[952/1200] | LR: 0.007543 | E: -27.136543 | E_var:     0.2092 | E_err:   0.007146
[2025-10-02 20:42:52] [Iter 1003/2250] R3[954/1200] | LR: 0.007504 | E: -27.120193 | E_var:     0.1974 | E_err:   0.006942
[2025-10-02 20:42:56] [Iter 1004/2250] R3[956/1200] | LR: 0.007465 | E: -27.121352 | E_var:     0.1941 | E_err:   0.006884
[2025-10-02 20:42:59] [Iter 1005/2250] R3[958/1200] | LR: 0.007426 | E: -27.115580 | E_var:     0.1966 | E_err:   0.006927
[2025-10-02 20:43:03] [Iter 1006/2250] R3[960/1200] | LR: 0.007387 | E: -27.124821 | E_var:     0.2208 | E_err:   0.007342
[2025-10-02 20:43:07] [Iter 1007/2250] R3[962/1200] | LR: 0.007349 | E: -27.125725 | E_var:     0.2035 | E_err:   0.007049
[2025-10-02 20:43:10] [Iter 1008/2250] R3[964/1200] | LR: 0.007311 | E: -27.114400 | E_var:     0.2284 | E_err:   0.007467
[2025-10-02 20:43:14] [Iter 1009/2250] R3[966/1200] | LR: 0.007273 | E: -27.126197 | E_var:     0.2562 | E_err:   0.007909
[2025-10-02 20:43:18] [Iter 1010/2250] R3[968/1200] | LR: 0.007236 | E: -27.120089 | E_var:     0.3022 | E_err:   0.008589
[2025-10-02 20:43:21] [Iter 1011/2250] R3[970/1200] | LR: 0.007198 | E: -27.113450 | E_var:     0.2886 | E_err:   0.008394
[2025-10-02 20:43:25] [Iter 1012/2250] R3[972/1200] | LR: 0.007161 | E: -27.115050 | E_var:     0.1795 | E_err:   0.006620
[2025-10-02 20:43:29] [Iter 1013/2250] R3[974/1200] | LR: 0.007125 | E: -27.127342 | E_var:     0.1862 | E_err:   0.006743
[2025-10-02 20:43:32] [Iter 1014/2250] R3[976/1200] | LR: 0.007088 | E: -27.112436 | E_var:     0.3029 | E_err:   0.008599
[2025-10-02 20:43:36] [Iter 1015/2250] R3[978/1200] | LR: 0.007052 | E: -27.119311 | E_var:     0.2801 | E_err:   0.008269
[2025-10-02 20:43:40] [Iter 1016/2250] R3[980/1200] | LR: 0.007017 | E: -27.119865 | E_var:     0.1976 | E_err:   0.006945
[2025-10-02 20:43:43] [Iter 1017/2250] R3[982/1200] | LR: 0.006981 | E: -27.111605 | E_var:     0.2388 | E_err:   0.007636
[2025-10-02 20:43:47] [Iter 1018/2250] R3[984/1200] | LR: 0.006946 | E: -27.128092 | E_var:     0.2226 | E_err:   0.007371
[2025-10-02 20:43:51] [Iter 1019/2250] R3[986/1200] | LR: 0.006911 | E: -27.117173 | E_var:     0.2358 | E_err:   0.007588
[2025-10-02 20:43:54] [Iter 1020/2250] R3[988/1200] | LR: 0.006876 | E: -27.115327 | E_var:     0.2188 | E_err:   0.007309
[2025-10-02 20:43:58] [Iter 1021/2250] R3[990/1200] | LR: 0.006842 | E: -27.120924 | E_var:     0.1976 | E_err:   0.006945
[2025-10-02 20:44:02] [Iter 1022/2250] R3[992/1200] | LR: 0.006808 | E: -27.113399 | E_var:     0.2263 | E_err:   0.007434
[2025-10-02 20:44:05] [Iter 1023/2250] R3[994/1200] | LR: 0.006774 | E: -27.128667 | E_var:     0.1892 | E_err:   0.006796
[2025-10-02 20:44:09] [Iter 1024/2250] R3[996/1200] | LR: 0.006741 | E: -27.120559 | E_var:     0.2018 | E_err:   0.007019
[2025-10-02 20:44:12] [Iter 1025/2250] R3[998/1200] | LR: 0.006708 | E: -27.115714 | E_var:     0.2163 | E_err:   0.007267
[2025-10-02 20:44:16] [Iter 1026/2250] R3[1000/1200] | LR: 0.006675 | E: -27.118569 | E_var:     0.2381 | E_err:   0.007625
[2025-10-02 20:44:20] [Iter 1027/2250] R3[1002/1200] | LR: 0.006642 | E: -27.121573 | E_var:     0.2542 | E_err:   0.007878
[2025-10-02 20:44:23] [Iter 1028/2250] R3[1004/1200] | LR: 0.006610 | E: -27.131725 | E_var:     0.2474 | E_err:   0.007772
[2025-10-02 20:44:27] [Iter 1029/2250] R3[1006/1200] | LR: 0.006578 | E: -27.125361 | E_var:     0.1995 | E_err:   0.006980
[2025-10-02 20:44:31] [Iter 1030/2250] R3[1008/1200] | LR: 0.006546 | E: -27.122920 | E_var:     0.2415 | E_err:   0.007679
[2025-10-02 20:44:34] [Iter 1031/2250] R3[1010/1200] | LR: 0.006515 | E: -27.117949 | E_var:     0.2261 | E_err:   0.007430
[2025-10-02 20:44:38] [Iter 1032/2250] R3[1012/1200] | LR: 0.006484 | E: -27.120304 | E_var:     0.1806 | E_err:   0.006641
[2025-10-02 20:44:42] [Iter 1033/2250] R3[1014/1200] | LR: 0.006453 | E: -27.113784 | E_var:     0.2427 | E_err:   0.007697
[2025-10-02 20:44:45] [Iter 1034/2250] R3[1016/1200] | LR: 0.006422 | E: -27.127152 | E_var:     0.1842 | E_err:   0.006706
[2025-10-02 20:44:49] [Iter 1035/2250] R3[1018/1200] | LR: 0.006392 | E: -27.116007 | E_var:     0.1978 | E_err:   0.006950
[2025-10-02 20:44:53] [Iter 1036/2250] R3[1020/1200] | LR: 0.006362 | E: -27.115322 | E_var:     0.2054 | E_err:   0.007082
[2025-10-02 20:44:56] [Iter 1037/2250] R3[1022/1200] | LR: 0.006333 | E: -27.115738 | E_var:     0.2685 | E_err:   0.008096
[2025-10-02 20:45:00] [Iter 1038/2250] R3[1024/1200] | LR: 0.006304 | E: -27.121885 | E_var:     0.3805 | E_err:   0.009638
[2025-10-02 20:45:04] [Iter 1039/2250] R3[1026/1200] | LR: 0.006275 | E: -27.112830 | E_var:     0.2577 | E_err:   0.007931
[2025-10-02 20:45:07] [Iter 1040/2250] R3[1028/1200] | LR: 0.006246 | E: -27.120282 | E_var:     0.2026 | E_err:   0.007032
[2025-10-02 20:45:11] [Iter 1041/2250] R3[1030/1200] | LR: 0.006218 | E: -27.127929 | E_var:     0.2084 | E_err:   0.007133
[2025-10-02 20:45:15] [Iter 1042/2250] R3[1032/1200] | LR: 0.006190 | E: -27.122384 | E_var:     0.2073 | E_err:   0.007114
[2025-10-02 20:45:18] [Iter 1043/2250] R3[1034/1200] | LR: 0.006162 | E: -27.123135 | E_var:     0.1678 | E_err:   0.006401
[2025-10-02 20:45:22] [Iter 1044/2250] R3[1036/1200] | LR: 0.006135 | E: -27.127238 | E_var:     0.2171 | E_err:   0.007281
[2025-10-02 20:45:26] [Iter 1045/2250] R3[1038/1200] | LR: 0.006107 | E: -27.134810 | E_var:     0.2256 | E_err:   0.007422
[2025-10-02 20:45:29] [Iter 1046/2250] R3[1040/1200] | LR: 0.006081 | E: -27.116143 | E_var:     0.1917 | E_err:   0.006840
[2025-10-02 20:45:33] [Iter 1047/2250] R3[1042/1200] | LR: 0.006054 | E: -27.136503 | E_var:     0.1975 | E_err:   0.006943
[2025-10-02 20:45:36] [Iter 1048/2250] R3[1044/1200] | LR: 0.006028 | E: -27.121092 | E_var:     0.1949 | E_err:   0.006898
[2025-10-02 20:45:40] [Iter 1049/2250] R3[1046/1200] | LR: 0.006002 | E: -27.129134 | E_var:     0.2024 | E_err:   0.007029
[2025-10-02 20:45:44] [Iter 1050/2250] R3[1048/1200] | LR: 0.005977 | E: -27.123456 | E_var:     0.2169 | E_err:   0.007276
[2025-10-02 20:45:47] [Iter 1051/2250] R3[1050/1200] | LR: 0.005952 | E: -27.127380 | E_var:     0.2706 | E_err:   0.008128
[2025-10-02 20:45:51] [Iter 1052/2250] R3[1052/1200] | LR: 0.005927 | E: -27.120071 | E_var:     0.2349 | E_err:   0.007573
[2025-10-02 20:45:55] [Iter 1053/2250] R3[1054/1200] | LR: 0.005902 | E: -27.119775 | E_var:     0.1710 | E_err:   0.006461
[2025-10-02 20:45:58] [Iter 1054/2250] R3[1056/1200] | LR: 0.005878 | E: -27.123035 | E_var:     0.1854 | E_err:   0.006728
[2025-10-02 20:46:02] [Iter 1055/2250] R3[1058/1200] | LR: 0.005854 | E: -27.127376 | E_var:     0.2031 | E_err:   0.007042
[2025-10-02 20:46:06] [Iter 1056/2250] R3[1060/1200] | LR: 0.005830 | E: -27.122496 | E_var:     0.1585 | E_err:   0.006220
[2025-10-02 20:46:09] [Iter 1057/2250] R3[1062/1200] | LR: 0.005807 | E: -27.115768 | E_var:     0.2796 | E_err:   0.008263
[2025-10-02 20:46:13] [Iter 1058/2250] R3[1064/1200] | LR: 0.005784 | E: -27.127292 | E_var:     0.2000 | E_err:   0.006988
[2025-10-02 20:46:17] [Iter 1059/2250] R3[1066/1200] | LR: 0.005761 | E: -27.126736 | E_var:     0.2175 | E_err:   0.007287
[2025-10-02 20:46:20] [Iter 1060/2250] R3[1068/1200] | LR: 0.005739 | E: -27.120571 | E_var:     0.2306 | E_err:   0.007504
[2025-10-02 20:46:24] [Iter 1061/2250] R3[1070/1200] | LR: 0.005717 | E: -27.135230 | E_var:     0.2415 | E_err:   0.007679
[2025-10-02 20:46:28] [Iter 1062/2250] R3[1072/1200] | LR: 0.005695 | E: -27.108970 | E_var:     0.2169 | E_err:   0.007277
[2025-10-02 20:46:31] [Iter 1063/2250] R3[1074/1200] | LR: 0.005674 | E: -27.126932 | E_var:     0.1719 | E_err:   0.006479
[2025-10-02 20:46:35] [Iter 1064/2250] R3[1076/1200] | LR: 0.005653 | E: -27.124721 | E_var:     0.1814 | E_err:   0.006654
[2025-10-02 20:46:39] [Iter 1065/2250] R3[1078/1200] | LR: 0.005632 | E: -27.118778 | E_var:     0.2597 | E_err:   0.007963
[2025-10-02 20:46:42] [Iter 1066/2250] R3[1080/1200] | LR: 0.005612 | E: -27.122222 | E_var:     0.3099 | E_err:   0.008699
[2025-10-02 20:46:46] [Iter 1067/2250] R3[1082/1200] | LR: 0.005592 | E: -27.126966 | E_var:     0.1778 | E_err:   0.006588
[2025-10-02 20:46:50] [Iter 1068/2250] R3[1084/1200] | LR: 0.005572 | E: -27.125874 | E_var:     0.2180 | E_err:   0.007295
[2025-10-02 20:46:53] [Iter 1069/2250] R3[1086/1200] | LR: 0.005553 | E: -27.118185 | E_var:     0.1878 | E_err:   0.006771
[2025-10-02 20:46:57] [Iter 1070/2250] R3[1088/1200] | LR: 0.005534 | E: -27.108633 | E_var:     0.2340 | E_err:   0.007558
[2025-10-02 20:47:00] [Iter 1071/2250] R3[1090/1200] | LR: 0.005515 | E: -27.122762 | E_var:     0.2183 | E_err:   0.007301
[2025-10-02 20:47:04] [Iter 1072/2250] R3[1092/1200] | LR: 0.005496 | E: -27.119035 | E_var:     0.1601 | E_err:   0.006252
[2025-10-02 20:47:08] [Iter 1073/2250] R3[1094/1200] | LR: 0.005478 | E: -27.123729 | E_var:     0.2071 | E_err:   0.007110
[2025-10-02 20:47:11] [Iter 1074/2250] R3[1096/1200] | LR: 0.005460 | E: -27.127360 | E_var:     0.2462 | E_err:   0.007752
[2025-10-02 20:47:15] [Iter 1075/2250] R3[1098/1200] | LR: 0.005443 | E: -27.122280 | E_var:     0.2227 | E_err:   0.007374
[2025-10-02 20:47:19] [Iter 1076/2250] R3[1100/1200] | LR: 0.005426 | E: -27.130668 | E_var:     0.2267 | E_err:   0.007440
[2025-10-02 20:47:22] [Iter 1077/2250] R3[1102/1200] | LR: 0.005409 | E: -27.117560 | E_var:     0.2651 | E_err:   0.008045
[2025-10-02 20:47:26] [Iter 1078/2250] R3[1104/1200] | LR: 0.005393 | E: -27.118947 | E_var:     0.1917 | E_err:   0.006841
[2025-10-02 20:47:30] [Iter 1079/2250] R3[1106/1200] | LR: 0.005377 | E: -27.118950 | E_var:     0.2367 | E_err:   0.007602
[2025-10-02 20:47:33] [Iter 1080/2250] R3[1108/1200] | LR: 0.005361 | E: -27.108326 | E_var:     0.1938 | E_err:   0.006878
[2025-10-02 20:47:37] [Iter 1081/2250] R3[1110/1200] | LR: 0.005345 | E: -27.114252 | E_var:     0.2109 | E_err:   0.007175
[2025-10-02 20:47:41] [Iter 1082/2250] R3[1112/1200] | LR: 0.005330 | E: -27.128569 | E_var:     0.1715 | E_err:   0.006470
[2025-10-02 20:47:44] [Iter 1083/2250] R3[1114/1200] | LR: 0.005315 | E: -27.104302 | E_var:     0.2191 | E_err:   0.007313
[2025-10-02 20:47:48] [Iter 1084/2250] R3[1116/1200] | LR: 0.005301 | E: -27.123418 | E_var:     0.2175 | E_err:   0.007287
[2025-10-02 20:47:52] [Iter 1085/2250] R3[1118/1200] | LR: 0.005287 | E: -27.122394 | E_var:     0.1814 | E_err:   0.006655
[2025-10-02 20:47:55] [Iter 1086/2250] R3[1120/1200] | LR: 0.005273 | E: -27.126507 | E_var:     0.2293 | E_err:   0.007482
[2025-10-02 20:47:59] [Iter 1087/2250] R3[1122/1200] | LR: 0.005260 | E: -27.114261 | E_var:     0.2063 | E_err:   0.007098
[2025-10-02 20:48:03] [Iter 1088/2250] R3[1124/1200] | LR: 0.005247 | E: -27.114382 | E_var:     0.2100 | E_err:   0.007159
[2025-10-02 20:48:06] [Iter 1089/2250] R3[1126/1200] | LR: 0.005234 | E: -27.113435 | E_var:     0.2118 | E_err:   0.007190
[2025-10-02 20:48:10] [Iter 1090/2250] R3[1128/1200] | LR: 0.005221 | E: -27.123693 | E_var:     0.1994 | E_err:   0.006978
[2025-10-02 20:48:14] [Iter 1091/2250] R3[1130/1200] | LR: 0.005209 | E: -27.126868 | E_var:     0.1870 | E_err:   0.006757
[2025-10-02 20:48:17] [Iter 1092/2250] R3[1132/1200] | LR: 0.005198 | E: -27.121646 | E_var:     0.1786 | E_err:   0.006603
[2025-10-02 20:48:21] [Iter 1093/2250] R3[1134/1200] | LR: 0.005186 | E: -27.119237 | E_var:     0.1698 | E_err:   0.006439
[2025-10-02 20:48:24] [Iter 1094/2250] R3[1136/1200] | LR: 0.005175 | E: -27.114044 | E_var:     0.2727 | E_err:   0.008160
[2025-10-02 20:48:28] [Iter 1095/2250] R3[1138/1200] | LR: 0.005164 | E: -27.134312 | E_var:     0.2262 | E_err:   0.007432
[2025-10-02 20:48:32] [Iter 1096/2250] R3[1140/1200] | LR: 0.005154 | E: -27.139029 | E_var:     0.2076 | E_err:   0.007119
[2025-10-02 20:48:35] [Iter 1097/2250] R3[1142/1200] | LR: 0.005144 | E: -27.117397 | E_var:     0.2624 | E_err:   0.008004
[2025-10-02 20:48:39] [Iter 1098/2250] R3[1144/1200] | LR: 0.005134 | E: -27.112919 | E_var:     0.2065 | E_err:   0.007100
[2025-10-02 20:48:43] [Iter 1099/2250] R3[1146/1200] | LR: 0.005125 | E: -27.114215 | E_var:     0.1927 | E_err:   0.006860
[2025-10-02 20:48:46] [Iter 1100/2250] R3[1148/1200] | LR: 0.005116 | E: -27.129906 | E_var:     0.1841 | E_err:   0.006703
[2025-10-02 20:48:50] [Iter 1101/2250] R3[1150/1200] | LR: 0.005107 | E: -27.127694 | E_var:     0.2325 | E_err:   0.007533
[2025-10-02 20:48:54] [Iter 1102/2250] R3[1152/1200] | LR: 0.005099 | E: -27.132243 | E_var:     0.1558 | E_err:   0.006168
[2025-10-02 20:48:57] [Iter 1103/2250] R3[1154/1200] | LR: 0.005091 | E: -27.110262 | E_var:     0.2980 | E_err:   0.008529
[2025-10-02 20:49:01] [Iter 1104/2250] R3[1156/1200] | LR: 0.005083 | E: -27.123905 | E_var:     0.2028 | E_err:   0.007037
[2025-10-02 20:49:05] [Iter 1105/2250] R3[1158/1200] | LR: 0.005075 | E: -27.117923 | E_var:     0.1861 | E_err:   0.006740
[2025-10-02 20:49:08] [Iter 1106/2250] R3[1160/1200] | LR: 0.005068 | E: -27.120822 | E_var:     0.2403 | E_err:   0.007660
[2025-10-02 20:49:12] [Iter 1107/2250] R3[1162/1200] | LR: 0.005062 | E: -27.128417 | E_var:     0.2009 | E_err:   0.007003
[2025-10-02 20:49:16] [Iter 1108/2250] R3[1164/1200] | LR: 0.005055 | E: -27.140808 | E_var:     0.2120 | E_err:   0.007195
[2025-10-02 20:49:19] [Iter 1109/2250] R3[1166/1200] | LR: 0.005049 | E: -27.120646 | E_var:     0.1769 | E_err:   0.006571
[2025-10-02 20:49:23] [Iter 1110/2250] R3[1168/1200] | LR: 0.005044 | E: -27.128773 | E_var:     0.1763 | E_err:   0.006561
[2025-10-02 20:49:27] [Iter 1111/2250] R3[1170/1200] | LR: 0.005039 | E: -27.121006 | E_var:     0.1965 | E_err:   0.006926
[2025-10-02 20:49:30] [Iter 1112/2250] R3[1172/1200] | LR: 0.005034 | E: -27.113032 | E_var:     0.2635 | E_err:   0.008021
[2025-10-02 20:49:34] [Iter 1113/2250] R3[1174/1200] | LR: 0.005029 | E: -27.124226 | E_var:     0.2322 | E_err:   0.007530
[2025-10-02 20:49:37] [Iter 1114/2250] R3[1176/1200] | LR: 0.005025 | E: -27.124074 | E_var:     0.2032 | E_err:   0.007043
[2025-10-02 20:49:41] [Iter 1115/2250] R3[1178/1200] | LR: 0.005021 | E: -27.121753 | E_var:     0.2029 | E_err:   0.007037
[2025-10-02 20:49:45] [Iter 1116/2250] R3[1180/1200] | LR: 0.005017 | E: -27.129096 | E_var:     0.1660 | E_err:   0.006366
[2025-10-02 20:49:48] [Iter 1117/2250] R3[1182/1200] | LR: 0.005014 | E: -27.132496 | E_var:     0.1801 | E_err:   0.006631
[2025-10-02 20:49:52] [Iter 1118/2250] R3[1184/1200] | LR: 0.005011 | E: -27.132496 | E_var:     0.2092 | E_err:   0.007147
[2025-10-02 20:49:56] [Iter 1119/2250] R3[1186/1200] | LR: 0.005008 | E: -27.130598 | E_var:     0.1596 | E_err:   0.006243
[2025-10-02 20:49:59] [Iter 1120/2250] R3[1188/1200] | LR: 0.005006 | E: -27.125609 | E_var:     0.2125 | E_err:   0.007202
[2025-10-02 20:50:03] [Iter 1121/2250] R3[1190/1200] | LR: 0.005004 | E: -27.124982 | E_var:     0.2252 | E_err:   0.007415
[2025-10-02 20:50:07] [Iter 1122/2250] R3[1192/1200] | LR: 0.005003 | E: -27.135683 | E_var:     0.1758 | E_err:   0.006552
[2025-10-02 20:50:10] [Iter 1123/2250] R3[1194/1200] | LR: 0.005002 | E: -27.124778 | E_var:     0.1827 | E_err:   0.006679
[2025-10-02 20:50:14] [Iter 1124/2250] R3[1196/1200] | LR: 0.005001 | E: -27.122657 | E_var:     0.2080 | E_err:   0.007126
[2025-10-02 20:50:18] [Iter 1125/2250] R3[1198/1200] | LR: 0.005000 | E: -27.131474 | E_var:     0.2102 | E_err:   0.007163
[2025-10-02 20:50:18] 🔄 RESTART #4 | Period: 2400
[2025-10-02 20:50:21] [Iter 1126/2250] R4[0/2400]   | LR: 0.030000 | E: -27.133010 | E_var:     0.2808 | E_err:   0.008280
[2025-10-02 20:50:25] [Iter 1127/2250] R4[2/2400]   | LR: 0.030000 | E: -27.110726 | E_var:     0.2054 | E_err:   0.007082
[2025-10-02 20:50:29] [Iter 1128/2250] R4[4/2400]   | LR: 0.030000 | E: -27.131135 | E_var:     0.1849 | E_err:   0.006720
[2025-10-02 20:50:32] [Iter 1129/2250] R4[6/2400]   | LR: 0.030000 | E: -27.121778 | E_var:     0.2130 | E_err:   0.007212
[2025-10-02 20:50:36] [Iter 1130/2250] R4[8/2400]   | LR: 0.029999 | E: -27.124064 | E_var:     0.2154 | E_err:   0.007252
[2025-10-02 20:50:40] [Iter 1131/2250] R4[10/2400]  | LR: 0.029999 | E: -27.122422 | E_var:     0.2707 | E_err:   0.008129
[2025-10-02 20:50:43] [Iter 1132/2250] R4[12/2400]  | LR: 0.029998 | E: -27.114193 | E_var:     0.2519 | E_err:   0.007842
[2025-10-02 20:50:47] [Iter 1133/2250] R4[14/2400]  | LR: 0.029998 | E: -27.120140 | E_var:     0.1840 | E_err:   0.006703
[2025-10-02 20:50:51] [Iter 1134/2250] R4[16/2400]  | LR: 0.029997 | E: -27.128487 | E_var:     0.1977 | E_err:   0.006948
[2025-10-02 20:50:54] [Iter 1135/2250] R4[18/2400]  | LR: 0.029997 | E: -27.114168 | E_var:     0.1827 | E_err:   0.006678
[2025-10-02 20:50:58] [Iter 1136/2250] R4[20/2400]  | LR: 0.029996 | E: -27.118614 | E_var:     0.2693 | E_err:   0.008108
[2025-10-02 20:51:02] [Iter 1137/2250] R4[22/2400]  | LR: 0.029995 | E: -27.117747 | E_var:     0.2384 | E_err:   0.007628
[2025-10-02 20:51:05] [Iter 1138/2250] R4[24/2400]  | LR: 0.029994 | E: -27.118020 | E_var:     0.1787 | E_err:   0.006606
[2025-10-02 20:51:09] [Iter 1139/2250] R4[26/2400]  | LR: 0.029993 | E: -27.125181 | E_var:     0.1657 | E_err:   0.006360
[2025-10-02 20:51:12] [Iter 1140/2250] R4[28/2400]  | LR: 0.029992 | E: -27.119619 | E_var:     0.2042 | E_err:   0.007061
[2025-10-02 20:51:16] [Iter 1141/2250] R4[30/2400]  | LR: 0.029990 | E: -27.117658 | E_var:     0.2073 | E_err:   0.007114
[2025-10-02 20:51:20] [Iter 1142/2250] R4[32/2400]  | LR: 0.029989 | E: -27.121121 | E_var:     0.1662 | E_err:   0.006371
[2025-10-02 20:51:23] [Iter 1143/2250] R4[34/2400]  | LR: 0.029988 | E: -27.120741 | E_var:     0.1560 | E_err:   0.006172
[2025-10-02 20:51:27] [Iter 1144/2250] R4[36/2400]  | LR: 0.029986 | E: -27.117290 | E_var:     0.1951 | E_err:   0.006902
[2025-10-02 20:51:31] [Iter 1145/2250] R4[38/2400]  | LR: 0.029985 | E: -27.128548 | E_var:     0.1718 | E_err:   0.006477
[2025-10-02 20:51:34] [Iter 1146/2250] R4[40/2400]  | LR: 0.029983 | E: -27.130410 | E_var:     0.2161 | E_err:   0.007264
[2025-10-02 20:51:38] [Iter 1147/2250] R4[42/2400]  | LR: 0.029981 | E: -27.122047 | E_var:     0.2134 | E_err:   0.007217
[2025-10-02 20:51:42] [Iter 1148/2250] R4[44/2400]  | LR: 0.029979 | E: -27.135761 | E_var:     0.1956 | E_err:   0.006910
[2025-10-02 20:51:45] [Iter 1149/2250] R4[46/2400]  | LR: 0.029977 | E: -27.127372 | E_var:     0.1850 | E_err:   0.006720
[2025-10-02 20:51:49] [Iter 1150/2250] R4[48/2400]  | LR: 0.029975 | E: -27.121105 | E_var:     0.2691 | E_err:   0.008105
[2025-10-02 20:51:53] [Iter 1151/2250] R4[50/2400]  | LR: 0.029973 | E: -27.115462 | E_var:     0.2536 | E_err:   0.007868
[2025-10-02 20:51:56] [Iter 1152/2250] R4[52/2400]  | LR: 0.029971 | E: -27.125072 | E_var:     0.1932 | E_err:   0.006868
[2025-10-02 20:52:00] [Iter 1153/2250] R4[54/2400]  | LR: 0.029969 | E: -27.122455 | E_var:     0.2490 | E_err:   0.007796
[2025-10-02 20:52:04] [Iter 1154/2250] R4[56/2400]  | LR: 0.029966 | E: -27.136699 | E_var:     0.2834 | E_err:   0.008318
[2025-10-02 20:52:07] [Iter 1155/2250] R4[58/2400]  | LR: 0.029964 | E: -27.128842 | E_var:     0.1580 | E_err:   0.006211
[2025-10-02 20:52:11] [Iter 1156/2250] R4[60/2400]  | LR: 0.029961 | E: -27.120294 | E_var:     0.1919 | E_err:   0.006846
[2025-10-02 20:52:15] [Iter 1157/2250] R4[62/2400]  | LR: 0.029959 | E: -27.123610 | E_var:     0.1752 | E_err:   0.006540
[2025-10-02 20:52:18] [Iter 1158/2250] R4[64/2400]  | LR: 0.029956 | E: -27.123781 | E_var:     0.1762 | E_err:   0.006560
[2025-10-02 20:52:22] [Iter 1159/2250] R4[66/2400]  | LR: 0.029953 | E: -27.129183 | E_var:     0.2386 | E_err:   0.007633
[2025-10-02 20:52:26] [Iter 1160/2250] R4[68/2400]  | LR: 0.029951 | E: -27.123497 | E_var:     0.1527 | E_err:   0.006105
[2025-10-02 20:52:29] [Iter 1161/2250] R4[70/2400]  | LR: 0.029948 | E: -27.123123 | E_var:     0.2096 | E_err:   0.007154
[2025-10-02 20:52:33] [Iter 1162/2250] R4[72/2400]  | LR: 0.029945 | E: -27.128828 | E_var:     0.1756 | E_err:   0.006547
[2025-10-02 20:52:36] [Iter 1163/2250] R4[74/2400]  | LR: 0.029941 | E: -27.132518 | E_var:     0.1986 | E_err:   0.006964
[2025-10-02 20:52:40] [Iter 1164/2250] R4[76/2400]  | LR: 0.029938 | E: -27.126966 | E_var:     0.2173 | E_err:   0.007283
[2025-10-02 20:52:44] [Iter 1165/2250] R4[78/2400]  | LR: 0.029935 | E: -27.127354 | E_var:     0.1887 | E_err:   0.006788
[2025-10-02 20:52:48] [Iter 1166/2250] R4[80/2400]  | LR: 0.029932 | E: -27.121787 | E_var:     0.2219 | E_err:   0.007361
[2025-10-02 20:52:51] [Iter 1167/2250] R4[82/2400]  | LR: 0.029928 | E: -27.134458 | E_var:     0.2565 | E_err:   0.007914
[2025-10-02 20:52:55] [Iter 1168/2250] R4[84/2400]  | LR: 0.029925 | E: -27.127241 | E_var:     0.1917 | E_err:   0.006841
[2025-10-02 20:52:58] [Iter 1169/2250] R4[86/2400]  | LR: 0.029921 | E: -27.114906 | E_var:     0.1745 | E_err:   0.006527
[2025-10-02 20:53:02] [Iter 1170/2250] R4[88/2400]  | LR: 0.029917 | E: -27.112650 | E_var:     0.2192 | E_err:   0.007315
[2025-10-02 20:53:06] [Iter 1171/2250] R4[90/2400]  | LR: 0.029913 | E: -27.117725 | E_var:     0.2065 | E_err:   0.007100
[2025-10-02 20:53:09] [Iter 1172/2250] R4[92/2400]  | LR: 0.029909 | E: -27.131892 | E_var:     0.2008 | E_err:   0.007002
[2025-10-02 20:53:13] [Iter 1173/2250] R4[94/2400]  | LR: 0.029905 | E: -27.120837 | E_var:     0.2474 | E_err:   0.007771
[2025-10-02 20:53:17] [Iter 1174/2250] R4[96/2400]  | LR: 0.029901 | E: -27.129900 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 20:53:20] [Iter 1175/2250] R4[98/2400]  | LR: 0.029897 | E: -27.135949 | E_var:     0.1956 | E_err:   0.006910
[2025-10-02 20:53:24] [Iter 1176/2250] R4[100/2400] | LR: 0.029893 | E: -27.127567 | E_var:     0.1705 | E_err:   0.006451
[2025-10-02 20:53:28] [Iter 1177/2250] R4[102/2400] | LR: 0.029889 | E: -27.128795 | E_var:     0.2157 | E_err:   0.007257
[2025-10-02 20:53:31] [Iter 1178/2250] R4[104/2400] | LR: 0.029884 | E: -27.112175 | E_var:     0.1946 | E_err:   0.006893
[2025-10-02 20:53:35] [Iter 1179/2250] R4[106/2400] | LR: 0.029880 | E: -27.122263 | E_var:     0.2095 | E_err:   0.007151
[2025-10-02 20:53:39] [Iter 1180/2250] R4[108/2400] | LR: 0.029875 | E: -27.130070 | E_var:     0.1930 | E_err:   0.006864
[2025-10-02 20:53:42] [Iter 1181/2250] R4[110/2400] | LR: 0.029871 | E: -27.126308 | E_var:     0.1624 | E_err:   0.006296
[2025-10-02 20:53:46] [Iter 1182/2250] R4[112/2400] | LR: 0.029866 | E: -27.126393 | E_var:     0.1741 | E_err:   0.006519
[2025-10-02 20:53:50] [Iter 1183/2250] R4[114/2400] | LR: 0.029861 | E: -27.122385 | E_var:     0.1767 | E_err:   0.006567
[2025-10-02 20:53:53] [Iter 1184/2250] R4[116/2400] | LR: 0.029856 | E: -27.131487 | E_var:     0.3200 | E_err:   0.008838
[2025-10-02 20:53:57] [Iter 1185/2250] R4[118/2400] | LR: 0.029851 | E: -27.104066 | E_var:     0.3190 | E_err:   0.008824
[2025-10-02 20:54:01] [Iter 1186/2250] R4[120/2400] | LR: 0.029846 | E: -27.119479 | E_var:     0.1999 | E_err:   0.006986
[2025-10-02 20:54:04] [Iter 1187/2250] R4[122/2400] | LR: 0.029841 | E: -27.126515 | E_var:     0.2198 | E_err:   0.007325
[2025-10-02 20:54:08] [Iter 1188/2250] R4[124/2400] | LR: 0.029836 | E: -27.127486 | E_var:     0.1837 | E_err:   0.006697
[2025-10-02 20:54:12] [Iter 1189/2250] R4[126/2400] | LR: 0.029830 | E: -27.146039 | E_var:     0.2006 | E_err:   0.006998
[2025-10-02 20:54:15] [Iter 1190/2250] R4[128/2400] | LR: 0.029825 | E: -27.115872 | E_var:     0.1698 | E_err:   0.006438
[2025-10-02 20:54:19] [Iter 1191/2250] R4[130/2400] | LR: 0.029819 | E: -27.120853 | E_var:     0.2646 | E_err:   0.008037
[2025-10-02 20:54:22] [Iter 1192/2250] R4[132/2400] | LR: 0.029814 | E: -27.130266 | E_var:     0.1797 | E_err:   0.006623
[2025-10-02 20:54:26] [Iter 1193/2250] R4[134/2400] | LR: 0.029808 | E: -27.124262 | E_var:     0.1881 | E_err:   0.006777
[2025-10-02 20:54:30] [Iter 1194/2250] R4[136/2400] | LR: 0.029802 | E: -27.131340 | E_var:     0.1981 | E_err:   0.006954
[2025-10-02 20:54:33] [Iter 1195/2250] R4[138/2400] | LR: 0.029797 | E: -27.118141 | E_var:     0.1623 | E_err:   0.006295
[2025-10-02 20:54:37] [Iter 1196/2250] R4[140/2400] | LR: 0.029791 | E: -27.116510 | E_var:     0.1854 | E_err:   0.006728
[2025-10-02 20:54:41] [Iter 1197/2250] R4[142/2400] | LR: 0.029785 | E: -27.115309 | E_var:     0.1726 | E_err:   0.006492
[2025-10-02 20:54:44] [Iter 1198/2250] R4[144/2400] | LR: 0.029779 | E: -27.127438 | E_var:     0.1677 | E_err:   0.006399
[2025-10-02 20:54:48] [Iter 1199/2250] R4[146/2400] | LR: 0.029772 | E: -27.137344 | E_var:     0.1772 | E_err:   0.006578
[2025-10-02 20:54:52] [Iter 1200/2250] R4[148/2400] | LR: 0.029766 | E: -27.117999 | E_var:     0.1866 | E_err:   0.006750
[2025-10-02 20:54:52] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-02 20:54:55] [Iter 1201/2250] R4[150/2400] | LR: 0.029760 | E: -27.127880 | E_var:     0.2932 | E_err:   0.008461
[2025-10-02 20:54:59] [Iter 1202/2250] R4[152/2400] | LR: 0.029753 | E: -27.121261 | E_var:     0.2068 | E_err:   0.007105
[2025-10-02 20:55:03] [Iter 1203/2250] R4[154/2400] | LR: 0.029747 | E: -27.121310 | E_var:     0.2052 | E_err:   0.007078
[2025-10-02 20:55:06] [Iter 1204/2250] R4[156/2400] | LR: 0.029740 | E: -27.123485 | E_var:     0.1979 | E_err:   0.006951
[2025-10-02 20:55:10] [Iter 1205/2250] R4[158/2400] | LR: 0.029734 | E: -27.116729 | E_var:     0.1616 | E_err:   0.006280
[2025-10-02 20:55:14] [Iter 1206/2250] R4[160/2400] | LR: 0.029727 | E: -27.127360 | E_var:     0.2164 | E_err:   0.007269
[2025-10-02 20:55:17] [Iter 1207/2250] R4[162/2400] | LR: 0.029720 | E: -27.125978 | E_var:     0.2076 | E_err:   0.007119
[2025-10-02 20:55:21] [Iter 1208/2250] R4[164/2400] | LR: 0.029713 | E: -27.121669 | E_var:     0.1922 | E_err:   0.006850
[2025-10-02 20:55:25] [Iter 1209/2250] R4[166/2400] | LR: 0.029706 | E: -27.129935 | E_var:     0.1844 | E_err:   0.006710
[2025-10-02 20:55:28] [Iter 1210/2250] R4[168/2400] | LR: 0.029699 | E: -27.123897 | E_var:     0.1750 | E_err:   0.006536
[2025-10-02 20:55:32] [Iter 1211/2250] R4[170/2400] | LR: 0.029692 | E: -27.126921 | E_var:     0.1593 | E_err:   0.006236
[2025-10-02 20:55:36] [Iter 1212/2250] R4[172/2400] | LR: 0.029685 | E: -27.116837 | E_var:     0.2613 | E_err:   0.007986
[2025-10-02 20:55:39] [Iter 1213/2250] R4[174/2400] | LR: 0.029677 | E: -27.113149 | E_var:     0.2120 | E_err:   0.007194
[2025-10-02 20:55:43] [Iter 1214/2250] R4[176/2400] | LR: 0.029670 | E: -27.124735 | E_var:     0.2490 | E_err:   0.007797
[2025-10-02 20:55:47] [Iter 1215/2250] R4[178/2400] | LR: 0.029662 | E: -27.126891 | E_var:     0.1697 | E_err:   0.006437
[2025-10-02 20:55:50] [Iter 1216/2250] R4[180/2400] | LR: 0.029655 | E: -27.123067 | E_var:     0.1985 | E_err:   0.006962
[2025-10-02 20:55:54] [Iter 1217/2250] R4[182/2400] | LR: 0.029647 | E: -27.128210 | E_var:     0.2015 | E_err:   0.007013
[2025-10-02 20:55:58] [Iter 1218/2250] R4[184/2400] | LR: 0.029639 | E: -27.123789 | E_var:     0.1943 | E_err:   0.006887
[2025-10-02 20:56:01] [Iter 1219/2250] R4[186/2400] | LR: 0.029631 | E: -27.131508 | E_var:     0.1833 | E_err:   0.006690
[2025-10-02 20:56:05] [Iter 1220/2250] R4[188/2400] | LR: 0.029623 | E: -27.134544 | E_var:     0.2428 | E_err:   0.007699
[2025-10-02 20:56:09] [Iter 1221/2250] R4[190/2400] | LR: 0.029615 | E: -27.119515 | E_var:     0.2345 | E_err:   0.007567
[2025-10-02 20:56:12] [Iter 1222/2250] R4[192/2400] | LR: 0.029607 | E: -27.115192 | E_var:     0.2522 | E_err:   0.007847
[2025-10-02 20:56:16] [Iter 1223/2250] R4[194/2400] | LR: 0.029599 | E: -27.127063 | E_var:     0.2759 | E_err:   0.008207
[2025-10-02 20:56:20] [Iter 1224/2250] R4[196/2400] | LR: 0.029591 | E: -27.131770 | E_var:     0.1781 | E_err:   0.006595
[2025-10-02 20:56:23] [Iter 1225/2250] R4[198/2400] | LR: 0.029583 | E: -27.120399 | E_var:     0.1742 | E_err:   0.006522
[2025-10-02 20:56:27] [Iter 1226/2250] R4[200/2400] | LR: 0.029574 | E: -27.116613 | E_var:     0.1853 | E_err:   0.006726
[2025-10-02 20:56:30] [Iter 1227/2250] R4[202/2400] | LR: 0.029566 | E: -27.123489 | E_var:     0.2061 | E_err:   0.007093
[2025-10-02 20:56:34] [Iter 1228/2250] R4[204/2400] | LR: 0.029557 | E: -27.124885 | E_var:     0.1809 | E_err:   0.006645
[2025-10-02 20:56:38] [Iter 1229/2250] R4[206/2400] | LR: 0.029548 | E: -27.131058 | E_var:     0.1782 | E_err:   0.006595
[2025-10-02 20:56:41] [Iter 1230/2250] R4[208/2400] | LR: 0.029540 | E: -27.125737 | E_var:     0.2464 | E_err:   0.007756
[2025-10-02 20:56:45] [Iter 1231/2250] R4[210/2400] | LR: 0.029531 | E: -27.117301 | E_var:     0.1885 | E_err:   0.006783
[2025-10-02 20:56:49] [Iter 1232/2250] R4[212/2400] | LR: 0.029522 | E: -27.132585 | E_var:     0.2308 | E_err:   0.007507
[2025-10-02 20:56:52] [Iter 1233/2250] R4[214/2400] | LR: 0.029513 | E: -27.130263 | E_var:     0.1609 | E_err:   0.006267
[2025-10-02 20:56:56] [Iter 1234/2250] R4[216/2400] | LR: 0.029504 | E: -27.126507 | E_var:     0.1775 | E_err:   0.006582
[2025-10-02 20:57:00] [Iter 1235/2250] R4[218/2400] | LR: 0.029494 | E: -27.121854 | E_var:     0.2073 | E_err:   0.007115
[2025-10-02 20:57:03] [Iter 1236/2250] R4[220/2400] | LR: 0.029485 | E: -27.119604 | E_var:     0.1737 | E_err:   0.006511
[2025-10-02 20:57:07] [Iter 1237/2250] R4[222/2400] | LR: 0.029476 | E: -27.135215 | E_var:     0.1657 | E_err:   0.006361
[2025-10-02 20:57:11] [Iter 1238/2250] R4[224/2400] | LR: 0.029466 | E: -27.124610 | E_var:     0.1944 | E_err:   0.006889
[2025-10-02 20:57:14] [Iter 1239/2250] R4[226/2400] | LR: 0.029457 | E: -27.114023 | E_var:     0.1677 | E_err:   0.006399
[2025-10-02 20:57:18] [Iter 1240/2250] R4[228/2400] | LR: 0.029447 | E: -27.126419 | E_var:     0.1678 | E_err:   0.006400
[2025-10-02 20:57:22] [Iter 1241/2250] R4[230/2400] | LR: 0.029438 | E: -27.124267 | E_var:     0.1664 | E_err:   0.006374
[2025-10-02 20:57:25] [Iter 1242/2250] R4[232/2400] | LR: 0.029428 | E: -27.123342 | E_var:     0.1864 | E_err:   0.006746
[2025-10-02 20:57:29] [Iter 1243/2250] R4[234/2400] | LR: 0.029418 | E: -27.140558 | E_var:     0.2174 | E_err:   0.007286
[2025-10-02 20:57:33] [Iter 1244/2250] R4[236/2400] | LR: 0.029408 | E: -27.116068 | E_var:     0.2187 | E_err:   0.007307
[2025-10-02 20:57:36] [Iter 1245/2250] R4[238/2400] | LR: 0.029398 | E: -27.124292 | E_var:     0.1770 | E_err:   0.006574
[2025-10-02 20:57:40] [Iter 1246/2250] R4[240/2400] | LR: 0.029388 | E: -27.139441 | E_var:     0.1736 | E_err:   0.006510
[2025-10-02 20:57:43] [Iter 1247/2250] R4[242/2400] | LR: 0.029378 | E: -27.116534 | E_var:     0.2092 | E_err:   0.007147
[2025-10-02 20:57:47] [Iter 1248/2250] R4[244/2400] | LR: 0.029368 | E: -27.128666 | E_var:     0.1546 | E_err:   0.006144
[2025-10-02 20:57:51] [Iter 1249/2250] R4[246/2400] | LR: 0.029358 | E: -27.135635 | E_var:     0.2323 | E_err:   0.007531
[2025-10-02 20:57:54] [Iter 1250/2250] R4[248/2400] | LR: 0.029347 | E: -27.116471 | E_var:     0.2605 | E_err:   0.007974
[2025-10-02 20:57:58] [Iter 1251/2250] R4[250/2400] | LR: 0.029337 | E: -27.118987 | E_var:     0.1819 | E_err:   0.006665
[2025-10-02 20:58:02] [Iter 1252/2250] R4[252/2400] | LR: 0.029326 | E: -27.118013 | E_var:     0.2270 | E_err:   0.007445
[2025-10-02 20:58:05] [Iter 1253/2250] R4[254/2400] | LR: 0.029315 | E: -27.115354 | E_var:     0.1652 | E_err:   0.006351
[2025-10-02 20:58:09] [Iter 1254/2250] R4[256/2400] | LR: 0.029305 | E: -27.127996 | E_var:     0.1736 | E_err:   0.006510
[2025-10-02 20:58:13] [Iter 1255/2250] R4[258/2400] | LR: 0.029294 | E: -27.123368 | E_var:     0.1877 | E_err:   0.006769
[2025-10-02 20:58:16] [Iter 1256/2250] R4[260/2400] | LR: 0.029283 | E: -27.135420 | E_var:     0.2212 | E_err:   0.007349
[2025-10-02 20:58:20] [Iter 1257/2250] R4[262/2400] | LR: 0.029272 | E: -27.131015 | E_var:     0.2414 | E_err:   0.007678
[2025-10-02 20:58:24] [Iter 1258/2250] R4[264/2400] | LR: 0.029261 | E: -27.117629 | E_var:     0.1723 | E_err:   0.006486
[2025-10-02 20:58:27] [Iter 1259/2250] R4[266/2400] | LR: 0.029250 | E: -27.134700 | E_var:     0.1864 | E_err:   0.006746
[2025-10-02 20:58:31] [Iter 1260/2250] R4[268/2400] | LR: 0.029239 | E: -27.127254 | E_var:     0.2140 | E_err:   0.007228
[2025-10-02 20:58:35] [Iter 1261/2250] R4[270/2400] | LR: 0.029227 | E: -27.122756 | E_var:     0.1783 | E_err:   0.006598
[2025-10-02 20:58:38] [Iter 1262/2250] R4[272/2400] | LR: 0.029216 | E: -27.130476 | E_var:     0.1757 | E_err:   0.006549
[2025-10-02 20:58:42] [Iter 1263/2250] R4[274/2400] | LR: 0.029205 | E: -27.133405 | E_var:     0.1742 | E_err:   0.006522
[2025-10-02 20:58:46] [Iter 1264/2250] R4[276/2400] | LR: 0.029193 | E: -27.133079 | E_var:     0.2025 | E_err:   0.007031
[2025-10-02 20:58:49] [Iter 1265/2250] R4[278/2400] | LR: 0.029181 | E: -27.123141 | E_var:     0.2168 | E_err:   0.007275
[2025-10-02 20:58:53] [Iter 1266/2250] R4[280/2400] | LR: 0.029170 | E: -27.129567 | E_var:     0.1897 | E_err:   0.006806
[2025-10-02 20:58:57] [Iter 1267/2250] R4[282/2400] | LR: 0.029158 | E: -27.133930 | E_var:     0.2108 | E_err:   0.007175
[2025-10-02 20:59:00] [Iter 1268/2250] R4[284/2400] | LR: 0.029146 | E: -27.125390 | E_var:     0.2702 | E_err:   0.008121
[2025-10-02 20:59:04] [Iter 1269/2250] R4[286/2400] | LR: 0.029134 | E: -27.117643 | E_var:     0.1643 | E_err:   0.006334
[2025-10-02 20:59:08] [Iter 1270/2250] R4[288/2400] | LR: 0.029122 | E: -27.125939 | E_var:     0.1902 | E_err:   0.006814
[2025-10-02 20:59:11] [Iter 1271/2250] R4[290/2400] | LR: 0.029110 | E: -27.121117 | E_var:     0.2196 | E_err:   0.007322
[2025-10-02 20:59:15] [Iter 1272/2250] R4[292/2400] | LR: 0.029098 | E: -27.123068 | E_var:     0.2098 | E_err:   0.007157
[2025-10-02 20:59:18] [Iter 1273/2250] R4[294/2400] | LR: 0.029086 | E: -27.131773 | E_var:     0.1706 | E_err:   0.006453
[2025-10-02 20:59:22] [Iter 1274/2250] R4[296/2400] | LR: 0.029073 | E: -27.132790 | E_var:     0.1892 | E_err:   0.006796
[2025-10-02 20:59:26] [Iter 1275/2250] R4[298/2400] | LR: 0.029061 | E: -27.128647 | E_var:     0.2006 | E_err:   0.006998
[2025-10-02 20:59:29] [Iter 1276/2250] R4[300/2400] | LR: 0.029048 | E: -27.123061 | E_var:     0.1540 | E_err:   0.006132
[2025-10-02 20:59:33] [Iter 1277/2250] R4[302/2400] | LR: 0.029036 | E: -27.126344 | E_var:     0.2102 | E_err:   0.007164
[2025-10-02 20:59:37] [Iter 1278/2250] R4[304/2400] | LR: 0.029023 | E: -27.125436 | E_var:     0.1766 | E_err:   0.006567
[2025-10-02 20:59:40] [Iter 1279/2250] R4[306/2400] | LR: 0.029011 | E: -27.124444 | E_var:     0.3039 | E_err:   0.008614
[2025-10-02 20:59:44] [Iter 1280/2250] R4[308/2400] | LR: 0.028998 | E: -27.133310 | E_var:     0.1680 | E_err:   0.006405
[2025-10-02 20:59:48] [Iter 1281/2250] R4[310/2400] | LR: 0.028985 | E: -27.117940 | E_var:     0.1745 | E_err:   0.006526
[2025-10-02 20:59:51] [Iter 1282/2250] R4[312/2400] | LR: 0.028972 | E: -27.123987 | E_var:     0.2511 | E_err:   0.007830
[2025-10-02 20:59:55] [Iter 1283/2250] R4[314/2400] | LR: 0.028959 | E: -27.126135 | E_var:     0.1763 | E_err:   0.006561
[2025-10-02 20:59:59] [Iter 1284/2250] R4[316/2400] | LR: 0.028946 | E: -27.132269 | E_var:     0.1844 | E_err:   0.006709
[2025-10-02 21:00:02] [Iter 1285/2250] R4[318/2400] | LR: 0.028933 | E: -27.124423 | E_var:     0.2048 | E_err:   0.007072
[2025-10-02 21:00:06] [Iter 1286/2250] R4[320/2400] | LR: 0.028919 | E: -27.125672 | E_var:     0.1994 | E_err:   0.006977
[2025-10-02 21:00:10] [Iter 1287/2250] R4[322/2400] | LR: 0.028906 | E: -27.126466 | E_var:     0.1873 | E_err:   0.006763
[2025-10-02 21:00:13] [Iter 1288/2250] R4[324/2400] | LR: 0.028893 | E: -27.122758 | E_var:     0.1807 | E_err:   0.006643
[2025-10-02 21:00:17] [Iter 1289/2250] R4[326/2400] | LR: 0.028879 | E: -27.142315 | E_var:     0.1914 | E_err:   0.006835
[2025-10-02 21:00:21] [Iter 1290/2250] R4[328/2400] | LR: 0.028865 | E: -27.130309 | E_var:     0.1866 | E_err:   0.006749
[2025-10-02 21:00:24] [Iter 1291/2250] R4[330/2400] | LR: 0.028852 | E: -27.126302 | E_var:     0.1718 | E_err:   0.006476
[2025-10-02 21:00:28] [Iter 1292/2250] R4[332/2400] | LR: 0.028838 | E: -27.133131 | E_var:     0.2043 | E_err:   0.007063
[2025-10-02 21:00:32] [Iter 1293/2250] R4[334/2400] | LR: 0.028824 | E: -27.121389 | E_var:     0.1785 | E_err:   0.006601
[2025-10-02 21:00:35] [Iter 1294/2250] R4[336/2400] | LR: 0.028810 | E: -27.126405 | E_var:     0.1651 | E_err:   0.006349
[2025-10-02 21:00:39] [Iter 1295/2250] R4[338/2400] | LR: 0.028796 | E: -27.129391 | E_var:     0.1669 | E_err:   0.006384
[2025-10-02 21:00:43] [Iter 1296/2250] R4[340/2400] | LR: 0.028782 | E: -27.139937 | E_var:     0.3855 | E_err:   0.009701
[2025-10-02 21:00:46] [Iter 1297/2250] R4[342/2400] | LR: 0.028768 | E: -27.136378 | E_var:     0.1458 | E_err:   0.005967
[2025-10-02 21:00:50] [Iter 1298/2250] R4[344/2400] | LR: 0.028754 | E: -27.124147 | E_var:     0.1667 | E_err:   0.006380
[2025-10-02 21:00:53] [Iter 1299/2250] R4[346/2400] | LR: 0.028740 | E: -27.117602 | E_var:     0.1706 | E_err:   0.006454
[2025-10-02 21:00:57] [Iter 1300/2250] R4[348/2400] | LR: 0.028725 | E: -27.121262 | E_var:     0.1522 | E_err:   0.006096
[2025-10-02 21:01:01] [Iter 1301/2250] R4[350/2400] | LR: 0.028711 | E: -27.134792 | E_var:     0.1941 | E_err:   0.006885
[2025-10-02 21:01:04] [Iter 1302/2250] R4[352/2400] | LR: 0.028696 | E: -27.132942 | E_var:     0.1699 | E_err:   0.006441
[2025-10-02 21:01:08] [Iter 1303/2250] R4[354/2400] | LR: 0.028682 | E: -27.117190 | E_var:     0.2088 | E_err:   0.007140
[2025-10-02 21:01:12] [Iter 1304/2250] R4[356/2400] | LR: 0.028667 | E: -27.128132 | E_var:     0.2333 | E_err:   0.007547
[2025-10-02 21:01:15] [Iter 1305/2250] R4[358/2400] | LR: 0.028652 | E: -27.122182 | E_var:     0.1839 | E_err:   0.006701
[2025-10-02 21:01:19] [Iter 1306/2250] R4[360/2400] | LR: 0.028638 | E: -27.135883 | E_var:     0.1717 | E_err:   0.006475
[2025-10-02 21:01:23] [Iter 1307/2250] R4[362/2400] | LR: 0.028623 | E: -27.136280 | E_var:     0.5845 | E_err:   0.011946
[2025-10-02 21:01:26] [Iter 1308/2250] R4[364/2400] | LR: 0.028608 | E: -27.129562 | E_var:     0.1735 | E_err:   0.006509
[2025-10-02 21:01:30] [Iter 1309/2250] R4[366/2400] | LR: 0.028593 | E: -27.120866 | E_var:     0.1971 | E_err:   0.006937
[2025-10-02 21:01:34] [Iter 1310/2250] R4[368/2400] | LR: 0.028578 | E: -27.124486 | E_var:     0.1605 | E_err:   0.006260
[2025-10-02 21:01:37] [Iter 1311/2250] R4[370/2400] | LR: 0.028562 | E: -27.125885 | E_var:     0.2202 | E_err:   0.007331
[2025-10-02 21:01:41] [Iter 1312/2250] R4[372/2400] | LR: 0.028547 | E: -27.131326 | E_var:     0.2992 | E_err:   0.008546
[2025-10-02 21:01:45] [Iter 1313/2250] R4[374/2400] | LR: 0.028532 | E: -27.143192 | E_var:     0.3044 | E_err:   0.008620
[2025-10-02 21:01:48] [Iter 1314/2250] R4[376/2400] | LR: 0.028516 | E: -27.120323 | E_var:     0.1625 | E_err:   0.006299
[2025-10-02 21:01:52] [Iter 1315/2250] R4[378/2400] | LR: 0.028501 | E: -27.137822 | E_var:     0.1892 | E_err:   0.006797
[2025-10-02 21:01:56] [Iter 1316/2250] R4[380/2400] | LR: 0.028485 | E: -27.126612 | E_var:     0.1645 | E_err:   0.006337
[2025-10-02 21:01:59] [Iter 1317/2250] R4[382/2400] | LR: 0.028470 | E: -27.124695 | E_var:     0.1519 | E_err:   0.006089
[2025-10-02 21:02:03] [Iter 1318/2250] R4[384/2400] | LR: 0.028454 | E: -27.111672 | E_var:     0.2187 | E_err:   0.007307
[2025-10-02 21:02:07] [Iter 1319/2250] R4[386/2400] | LR: 0.028438 | E: -27.129124 | E_var:     0.1694 | E_err:   0.006431
[2025-10-02 21:02:10] [Iter 1320/2250] R4[388/2400] | LR: 0.028422 | E: -27.121066 | E_var:     0.2058 | E_err:   0.007089
[2025-10-02 21:02:14] [Iter 1321/2250] R4[390/2400] | LR: 0.028406 | E: -27.129453 | E_var:     0.1996 | E_err:   0.006981
[2025-10-02 21:02:17] [Iter 1322/2250] R4[392/2400] | LR: 0.028390 | E: -27.120138 | E_var:     0.2582 | E_err:   0.007940
[2025-10-02 21:02:21] [Iter 1323/2250] R4[394/2400] | LR: 0.028374 | E: -27.130753 | E_var:     0.1392 | E_err:   0.005829
[2025-10-02 21:02:25] [Iter 1324/2250] R4[396/2400] | LR: 0.028358 | E: -27.132371 | E_var:     0.1616 | E_err:   0.006281
[2025-10-02 21:02:28] [Iter 1325/2250] R4[398/2400] | LR: 0.028342 | E: -27.114942 | E_var:     0.1742 | E_err:   0.006522
[2025-10-02 21:02:32] [Iter 1326/2250] R4[400/2400] | LR: 0.028325 | E: -27.128247 | E_var:     0.1735 | E_err:   0.006507
[2025-10-02 21:02:36] [Iter 1327/2250] R4[402/2400] | LR: 0.028309 | E: -27.124306 | E_var:     0.2323 | E_err:   0.007531
[2025-10-02 21:02:39] [Iter 1328/2250] R4[404/2400] | LR: 0.028292 | E: -27.125761 | E_var:     0.3624 | E_err:   0.009407
[2025-10-02 21:02:43] [Iter 1329/2250] R4[406/2400] | LR: 0.028276 | E: -27.126783 | E_var:     0.1687 | E_err:   0.006418
[2025-10-02 21:02:47] [Iter 1330/2250] R4[408/2400] | LR: 0.028259 | E: -27.130459 | E_var:     0.1692 | E_err:   0.006427
[2025-10-02 21:02:50] [Iter 1331/2250] R4[410/2400] | LR: 0.028243 | E: -27.120487 | E_var:     0.2074 | E_err:   0.007115
[2025-10-02 21:02:54] [Iter 1332/2250] R4[412/2400] | LR: 0.028226 | E: -27.128390 | E_var:     0.2343 | E_err:   0.007564
[2025-10-02 21:02:58] [Iter 1333/2250] R4[414/2400] | LR: 0.028209 | E: -27.127265 | E_var:     0.1838 | E_err:   0.006698
[2025-10-02 21:03:01] [Iter 1334/2250] R4[416/2400] | LR: 0.028192 | E: -27.145030 | E_var:     0.2316 | E_err:   0.007520
[2025-10-02 21:03:05] [Iter 1335/2250] R4[418/2400] | LR: 0.028175 | E: -27.125792 | E_var:     0.2363 | E_err:   0.007595
[2025-10-02 21:03:09] [Iter 1336/2250] R4[420/2400] | LR: 0.028158 | E: -27.125377 | E_var:     0.1956 | E_err:   0.006910
[2025-10-02 21:03:12] [Iter 1337/2250] R4[422/2400] | LR: 0.028141 | E: -27.133692 | E_var:     0.1725 | E_err:   0.006490
[2025-10-02 21:03:16] [Iter 1338/2250] R4[424/2400] | LR: 0.028124 | E: -27.124139 | E_var:     0.2342 | E_err:   0.007561
[2025-10-02 21:03:20] [Iter 1339/2250] R4[426/2400] | LR: 0.028106 | E: -27.136655 | E_var:     0.1365 | E_err:   0.005774
[2025-10-02 21:03:23] [Iter 1340/2250] R4[428/2400] | LR: 0.028089 | E: -27.127648 | E_var:     0.1661 | E_err:   0.006369
[2025-10-02 21:03:27] [Iter 1341/2250] R4[430/2400] | LR: 0.028072 | E: -27.121892 | E_var:     0.1768 | E_err:   0.006570
[2025-10-02 21:03:31] [Iter 1342/2250] R4[432/2400] | LR: 0.028054 | E: -27.132020 | E_var:     0.1784 | E_err:   0.006600
[2025-10-02 21:03:34] [Iter 1343/2250] R4[434/2400] | LR: 0.028037 | E: -27.136708 | E_var:     0.1808 | E_err:   0.006645
[2025-10-02 21:03:38] [Iter 1344/2250] R4[436/2400] | LR: 0.028019 | E: -27.124207 | E_var:     0.1942 | E_err:   0.006885
[2025-10-02 21:03:41] [Iter 1345/2250] R4[438/2400] | LR: 0.028001 | E: -27.132925 | E_var:     0.1409 | E_err:   0.005865
[2025-10-02 21:03:45] [Iter 1346/2250] R4[440/2400] | LR: 0.027983 | E: -27.116187 | E_var:     0.1901 | E_err:   0.006813
[2025-10-02 21:03:49] [Iter 1347/2250] R4[442/2400] | LR: 0.027966 | E: -27.118209 | E_var:     0.2731 | E_err:   0.008166
[2025-10-02 21:03:52] [Iter 1348/2250] R4[444/2400] | LR: 0.027948 | E: -27.135668 | E_var:     0.2370 | E_err:   0.007607
[2025-10-02 21:03:56] [Iter 1349/2250] R4[446/2400] | LR: 0.027930 | E: -27.139150 | E_var:     0.1740 | E_err:   0.006518
[2025-10-02 21:04:00] [Iter 1350/2250] R4[448/2400] | LR: 0.027912 | E: -27.130178 | E_var:     0.1902 | E_err:   0.006815
[2025-10-02 21:04:03] [Iter 1351/2250] R4[450/2400] | LR: 0.027893 | E: -27.125640 | E_var:     0.1705 | E_err:   0.006452
[2025-10-02 21:04:07] [Iter 1352/2250] R4[452/2400] | LR: 0.027875 | E: -27.127682 | E_var:     0.1813 | E_err:   0.006652
[2025-10-02 21:04:11] [Iter 1353/2250] R4[454/2400] | LR: 0.027857 | E: -27.140842 | E_var:     0.1804 | E_err:   0.006636
[2025-10-02 21:04:14] [Iter 1354/2250] R4[456/2400] | LR: 0.027839 | E: -27.119113 | E_var:     0.2004 | E_err:   0.006994
[2025-10-02 21:04:18] [Iter 1355/2250] R4[458/2400] | LR: 0.027820 | E: -27.121405 | E_var:     0.1709 | E_err:   0.006460
[2025-10-02 21:04:22] [Iter 1356/2250] R4[460/2400] | LR: 0.027802 | E: -27.121336 | E_var:     0.1785 | E_err:   0.006601
[2025-10-02 21:04:25] [Iter 1357/2250] R4[462/2400] | LR: 0.027783 | E: -27.122160 | E_var:     0.1698 | E_err:   0.006439
[2025-10-02 21:04:29] [Iter 1358/2250] R4[464/2400] | LR: 0.027764 | E: -27.122681 | E_var:     0.1836 | E_err:   0.006696
[2025-10-02 21:04:33] [Iter 1359/2250] R4[466/2400] | LR: 0.027746 | E: -27.133242 | E_var:     0.1792 | E_err:   0.006614
[2025-10-02 21:04:36] [Iter 1360/2250] R4[468/2400] | LR: 0.027727 | E: -27.128993 | E_var:     0.1832 | E_err:   0.006689
[2025-10-02 21:04:40] [Iter 1361/2250] R4[470/2400] | LR: 0.027708 | E: -27.120842 | E_var:     0.2125 | E_err:   0.007203
[2025-10-02 21:04:44] [Iter 1362/2250] R4[472/2400] | LR: 0.027689 | E: -27.122690 | E_var:     0.1700 | E_err:   0.006441
[2025-10-02 21:04:47] [Iter 1363/2250] R4[474/2400] | LR: 0.027670 | E: -27.123366 | E_var:     0.1864 | E_err:   0.006746
[2025-10-02 21:04:51] [Iter 1364/2250] R4[476/2400] | LR: 0.027651 | E: -27.129952 | E_var:     0.2060 | E_err:   0.007091
[2025-10-02 21:04:55] [Iter 1365/2250] R4[478/2400] | LR: 0.027632 | E: -27.121631 | E_var:     0.1870 | E_err:   0.006757
[2025-10-02 21:04:58] [Iter 1366/2250] R4[480/2400] | LR: 0.027613 | E: -27.124992 | E_var:     0.2638 | E_err:   0.008025
[2025-10-02 21:05:02] [Iter 1367/2250] R4[482/2400] | LR: 0.027593 | E: -27.124693 | E_var:     0.1810 | E_err:   0.006647
[2025-10-02 21:05:06] [Iter 1368/2250] R4[484/2400] | LR: 0.027574 | E: -27.121467 | E_var:     0.1982 | E_err:   0.006956
[2025-10-02 21:05:09] [Iter 1369/2250] R4[486/2400] | LR: 0.027555 | E: -27.128662 | E_var:     0.1587 | E_err:   0.006225
[2025-10-02 21:05:13] [Iter 1370/2250] R4[488/2400] | LR: 0.027535 | E: -27.135873 | E_var:     0.1532 | E_err:   0.006116
[2025-10-02 21:05:16] [Iter 1371/2250] R4[490/2400] | LR: 0.027516 | E: -27.126883 | E_var:     0.1706 | E_err:   0.006453
[2025-10-02 21:05:20] [Iter 1372/2250] R4[492/2400] | LR: 0.027496 | E: -27.120342 | E_var:     0.1543 | E_err:   0.006139
[2025-10-02 21:05:24] [Iter 1373/2250] R4[494/2400] | LR: 0.027476 | E: -27.133777 | E_var:     0.1984 | E_err:   0.006959
[2025-10-02 21:05:27] [Iter 1374/2250] R4[496/2400] | LR: 0.027457 | E: -27.125284 | E_var:     0.1847 | E_err:   0.006716
[2025-10-02 21:05:31] [Iter 1375/2250] R4[498/2400] | LR: 0.027437 | E: -27.127386 | E_var:     0.1615 | E_err:   0.006279
[2025-10-02 21:05:35] [Iter 1376/2250] R4[500/2400] | LR: 0.027417 | E: -27.122166 | E_var:     0.1957 | E_err:   0.006913
[2025-10-02 21:05:38] [Iter 1377/2250] R4[502/2400] | LR: 0.027397 | E: -27.133885 | E_var:     0.1535 | E_err:   0.006122
[2025-10-02 21:05:42] [Iter 1378/2250] R4[504/2400] | LR: 0.027377 | E: -27.122859 | E_var:     0.1845 | E_err:   0.006712
[2025-10-02 21:05:46] [Iter 1379/2250] R4[506/2400] | LR: 0.027357 | E: -27.136860 | E_var:     0.1899 | E_err:   0.006809
[2025-10-02 21:05:49] [Iter 1380/2250] R4[508/2400] | LR: 0.027337 | E: -27.127286 | E_var:     0.2012 | E_err:   0.007009
[2025-10-02 21:05:53] [Iter 1381/2250] R4[510/2400] | LR: 0.027316 | E: -27.124308 | E_var:     0.1788 | E_err:   0.006606
[2025-10-02 21:05:57] [Iter 1382/2250] R4[512/2400] | LR: 0.027296 | E: -27.122125 | E_var:     0.1664 | E_err:   0.006373
[2025-10-02 21:06:00] [Iter 1383/2250] R4[514/2400] | LR: 0.027276 | E: -27.131059 | E_var:     0.1424 | E_err:   0.005897
[2025-10-02 21:06:04] [Iter 1384/2250] R4[516/2400] | LR: 0.027255 | E: -27.125652 | E_var:     0.1838 | E_err:   0.006698
[2025-10-02 21:06:08] [Iter 1385/2250] R4[518/2400] | LR: 0.027235 | E: -27.118744 | E_var:     0.1620 | E_err:   0.006289
[2025-10-02 21:06:11] [Iter 1386/2250] R4[520/2400] | LR: 0.027214 | E: -27.140022 | E_var:     0.1844 | E_err:   0.006709
[2025-10-02 21:06:15] [Iter 1387/2250] R4[522/2400] | LR: 0.027194 | E: -27.140641 | E_var:     0.1636 | E_err:   0.006320
[2025-10-02 21:06:19] [Iter 1388/2250] R4[524/2400] | LR: 0.027173 | E: -27.136192 | E_var:     0.1703 | E_err:   0.006448
[2025-10-02 21:06:22] [Iter 1389/2250] R4[526/2400] | LR: 0.027152 | E: -27.119711 | E_var:     0.2818 | E_err:   0.008294
[2025-10-02 21:06:26] [Iter 1390/2250] R4[528/2400] | LR: 0.027131 | E: -27.125954 | E_var:     0.2351 | E_err:   0.007576
[2025-10-02 21:06:30] [Iter 1391/2250] R4[530/2400] | LR: 0.027111 | E: -27.132064 | E_var:     0.1773 | E_err:   0.006580
[2025-10-02 21:06:33] [Iter 1392/2250] R4[532/2400] | LR: 0.027090 | E: -27.118478 | E_var:     0.1645 | E_err:   0.006336
[2025-10-02 21:06:37] [Iter 1393/2250] R4[534/2400] | LR: 0.027069 | E: -27.139575 | E_var:     0.1763 | E_err:   0.006561
[2025-10-02 21:06:41] [Iter 1394/2250] R4[536/2400] | LR: 0.027047 | E: -27.122291 | E_var:     0.1734 | E_err:   0.006507
[2025-10-02 21:06:44] [Iter 1395/2250] R4[538/2400] | LR: 0.027026 | E: -27.120805 | E_var:     0.1920 | E_err:   0.006846
[2025-10-02 21:06:48] [Iter 1396/2250] R4[540/2400] | LR: 0.027005 | E: -27.137789 | E_var:     0.1628 | E_err:   0.006305
[2025-10-02 21:06:52] [Iter 1397/2250] R4[542/2400] | LR: 0.026984 | E: -27.122842 | E_var:     0.1575 | E_err:   0.006201
[2025-10-02 21:06:55] [Iter 1398/2250] R4[544/2400] | LR: 0.026962 | E: -27.122245 | E_var:     0.2139 | E_err:   0.007226
[2025-10-02 21:06:59] [Iter 1399/2250] R4[546/2400] | LR: 0.026941 | E: -27.132644 | E_var:     0.1862 | E_err:   0.006743
[2025-10-02 21:07:02] [Iter 1400/2250] R4[548/2400] | LR: 0.026920 | E: -27.134474 | E_var:     0.1726 | E_err:   0.006491
[2025-10-02 21:07:02] ✓ Checkpoint saved: checkpoint_iter_001400.pkl
[2025-10-02 21:07:06] [Iter 1401/2250] R4[550/2400] | LR: 0.026898 | E: -27.127666 | E_var:     0.2112 | E_err:   0.007180
[2025-10-02 21:07:10] [Iter 1402/2250] R4[552/2400] | LR: 0.026876 | E: -27.129688 | E_var:     0.2245 | E_err:   0.007403
[2025-10-02 21:07:13] [Iter 1403/2250] R4[554/2400] | LR: 0.026855 | E: -27.109540 | E_var:     0.4526 | E_err:   0.010512
[2025-10-02 21:07:17] [Iter 1404/2250] R4[556/2400] | LR: 0.026833 | E: -27.116266 | E_var:     0.1774 | E_err:   0.006581
[2025-10-02 21:07:21] [Iter 1405/2250] R4[558/2400] | LR: 0.026811 | E: -27.125966 | E_var:     0.1830 | E_err:   0.006685
[2025-10-02 21:07:24] [Iter 1406/2250] R4[560/2400] | LR: 0.026789 | E: -27.133443 | E_var:     0.1908 | E_err:   0.006825
[2025-10-02 21:07:28] [Iter 1407/2250] R4[562/2400] | LR: 0.026767 | E: -27.120016 | E_var:     0.2006 | E_err:   0.006999
[2025-10-02 21:07:32] [Iter 1408/2250] R4[564/2400] | LR: 0.026745 | E: -27.119087 | E_var:     0.1847 | E_err:   0.006715
[2025-10-02 21:07:35] [Iter 1409/2250] R4[566/2400] | LR: 0.026723 | E: -27.123002 | E_var:     0.1670 | E_err:   0.006385
[2025-10-02 21:07:39] [Iter 1410/2250] R4[568/2400] | LR: 0.026701 | E: -27.135950 | E_var:     0.1741 | E_err:   0.006520
[2025-10-02 21:07:43] [Iter 1411/2250] R4[570/2400] | LR: 0.026679 | E: -27.126781 | E_var:     0.1638 | E_err:   0.006324
[2025-10-02 21:07:46] [Iter 1412/2250] R4[572/2400] | LR: 0.026657 | E: -27.136195 | E_var:     0.1783 | E_err:   0.006598
[2025-10-02 21:07:50] [Iter 1413/2250] R4[574/2400] | LR: 0.026634 | E: -27.133538 | E_var:     0.1774 | E_err:   0.006581
[2025-10-02 21:07:54] [Iter 1414/2250] R4[576/2400] | LR: 0.026612 | E: -27.128916 | E_var:     0.1792 | E_err:   0.006615
[2025-10-02 21:07:57] [Iter 1415/2250] R4[578/2400] | LR: 0.026590 | E: -27.129230 | E_var:     0.1781 | E_err:   0.006593
[2025-10-02 21:08:01] [Iter 1416/2250] R4[580/2400] | LR: 0.026567 | E: -27.124033 | E_var:     0.1612 | E_err:   0.006273
[2025-10-02 21:08:05] [Iter 1417/2250] R4[582/2400] | LR: 0.026545 | E: -27.137176 | E_var:     0.1923 | E_err:   0.006852
[2025-10-02 21:08:08] [Iter 1418/2250] R4[584/2400] | LR: 0.026522 | E: -27.119723 | E_var:     0.1742 | E_err:   0.006521
[2025-10-02 21:08:12] [Iter 1419/2250] R4[586/2400] | LR: 0.026499 | E: -27.126496 | E_var:     0.1775 | E_err:   0.006582
[2025-10-02 21:08:15] [Iter 1420/2250] R4[588/2400] | LR: 0.026477 | E: -27.130679 | E_var:     0.2073 | E_err:   0.007115
[2025-10-02 21:08:19] [Iter 1421/2250] R4[590/2400] | LR: 0.026454 | E: -27.125762 | E_var:     0.2244 | E_err:   0.007402
[2025-10-02 21:08:23] [Iter 1422/2250] R4[592/2400] | LR: 0.026431 | E: -27.131909 | E_var:     0.2117 | E_err:   0.007190
[2025-10-02 21:08:26] [Iter 1423/2250] R4[594/2400] | LR: 0.026408 | E: -27.133110 | E_var:     0.2252 | E_err:   0.007415
[2025-10-02 21:08:30] [Iter 1424/2250] R4[596/2400] | LR: 0.026385 | E: -27.123416 | E_var:     0.1727 | E_err:   0.006492
[2025-10-02 21:08:34] [Iter 1425/2250] R4[598/2400] | LR: 0.026362 | E: -27.128957 | E_var:     0.1661 | E_err:   0.006368
[2025-10-02 21:08:37] [Iter 1426/2250] R4[600/2400] | LR: 0.026339 | E: -27.129220 | E_var:     0.2200 | E_err:   0.007329
[2025-10-02 21:08:41] [Iter 1427/2250] R4[602/2400] | LR: 0.026316 | E: -27.128215 | E_var:     0.1625 | E_err:   0.006299
[2025-10-02 21:08:45] [Iter 1428/2250] R4[604/2400] | LR: 0.026292 | E: -27.133498 | E_var:     0.1686 | E_err:   0.006415
[2025-10-02 21:08:48] [Iter 1429/2250] R4[606/2400] | LR: 0.026269 | E: -27.146381 | E_var:     0.2061 | E_err:   0.007093
[2025-10-02 21:08:52] [Iter 1430/2250] R4[608/2400] | LR: 0.026246 | E: -27.137475 | E_var:     0.1531 | E_err:   0.006115
[2025-10-02 21:08:56] [Iter 1431/2250] R4[610/2400] | LR: 0.026222 | E: -27.131117 | E_var:     0.1435 | E_err:   0.005918
[2025-10-02 21:08:59] [Iter 1432/2250] R4[612/2400] | LR: 0.026199 | E: -27.125899 | E_var:     0.1471 | E_err:   0.005992
[2025-10-02 21:09:03] [Iter 1433/2250] R4[614/2400] | LR: 0.026175 | E: -27.131370 | E_var:     0.1459 | E_err:   0.005968
[2025-10-02 21:09:07] [Iter 1434/2250] R4[616/2400] | LR: 0.026152 | E: -27.138711 | E_var:     0.2405 | E_err:   0.007662
[2025-10-02 21:09:10] [Iter 1435/2250] R4[618/2400] | LR: 0.026128 | E: -27.129998 | E_var:     0.1605 | E_err:   0.006259
[2025-10-02 21:09:14] [Iter 1436/2250] R4[620/2400] | LR: 0.026104 | E: -27.123655 | E_var:     0.2403 | E_err:   0.007660
[2025-10-02 21:09:18] [Iter 1437/2250] R4[622/2400] | LR: 0.026081 | E: -27.129656 | E_var:     0.1649 | E_err:   0.006346
[2025-10-02 21:09:21] [Iter 1438/2250] R4[624/2400] | LR: 0.026057 | E: -27.127988 | E_var:     0.1755 | E_err:   0.006545
[2025-10-02 21:09:25] [Iter 1439/2250] R4[626/2400] | LR: 0.026033 | E: -27.128249 | E_var:     0.2549 | E_err:   0.007888
[2025-10-02 21:09:29] [Iter 1440/2250] R4[628/2400] | LR: 0.026009 | E: -27.129208 | E_var:     0.1644 | E_err:   0.006336
[2025-10-02 21:09:32] [Iter 1441/2250] R4[630/2400] | LR: 0.025985 | E: -27.129823 | E_var:     0.1534 | E_err:   0.006121
[2025-10-02 21:09:36] [Iter 1442/2250] R4[632/2400] | LR: 0.025961 | E: -27.125881 | E_var:     0.2035 | E_err:   0.007048
[2025-10-02 21:09:39] [Iter 1443/2250] R4[634/2400] | LR: 0.025937 | E: -27.138261 | E_var:     0.1564 | E_err:   0.006180
[2025-10-02 21:09:43] [Iter 1444/2250] R4[636/2400] | LR: 0.025913 | E: -27.124405 | E_var:     0.2369 | E_err:   0.007605
[2025-10-02 21:09:47] [Iter 1445/2250] R4[638/2400] | LR: 0.025888 | E: -27.133657 | E_var:     0.1593 | E_err:   0.006236
[2025-10-02 21:09:50] [Iter 1446/2250] R4[640/2400] | LR: 0.025864 | E: -27.127493 | E_var:     0.1572 | E_err:   0.006195
[2025-10-02 21:09:54] [Iter 1447/2250] R4[642/2400] | LR: 0.025840 | E: -27.134568 | E_var:     0.1875 | E_err:   0.006765
[2025-10-02 21:09:58] [Iter 1448/2250] R4[644/2400] | LR: 0.025815 | E: -27.129042 | E_var:     0.1581 | E_err:   0.006213
[2025-10-02 21:10:01] [Iter 1449/2250] R4[646/2400] | LR: 0.025791 | E: -27.129290 | E_var:     0.2236 | E_err:   0.007389
[2025-10-02 21:10:05] [Iter 1450/2250] R4[648/2400] | LR: 0.025766 | E: -27.129111 | E_var:     0.1742 | E_err:   0.006522
[2025-10-02 21:10:09] [Iter 1451/2250] R4[650/2400] | LR: 0.025742 | E: -27.112439 | E_var:     0.1998 | E_err:   0.006983
[2025-10-02 21:10:12] [Iter 1452/2250] R4[652/2400] | LR: 0.025717 | E: -27.138408 | E_var:     0.1774 | E_err:   0.006581
[2025-10-02 21:10:16] [Iter 1453/2250] R4[654/2400] | LR: 0.025693 | E: -27.126170 | E_var:     0.1740 | E_err:   0.006517
[2025-10-02 21:10:20] [Iter 1454/2250] R4[656/2400] | LR: 0.025668 | E: -27.128399 | E_var:     0.2236 | E_err:   0.007389
[2025-10-02 21:10:23] [Iter 1455/2250] R4[658/2400] | LR: 0.025643 | E: -27.129714 | E_var:     0.2212 | E_err:   0.007348
[2025-10-02 21:10:27] [Iter 1456/2250] R4[660/2400] | LR: 0.025618 | E: -27.123536 | E_var:     0.2575 | E_err:   0.007929
[2025-10-02 21:10:31] [Iter 1457/2250] R4[662/2400] | LR: 0.025593 | E: -27.133168 | E_var:     0.1982 | E_err:   0.006956
[2025-10-02 21:10:34] [Iter 1458/2250] R4[664/2400] | LR: 0.025568 | E: -27.115057 | E_var:     0.1799 | E_err:   0.006628
[2025-10-02 21:10:38] [Iter 1459/2250] R4[666/2400] | LR: 0.025543 | E: -27.123693 | E_var:     0.2200 | E_err:   0.007328
[2025-10-02 21:10:42] [Iter 1460/2250] R4[668/2400] | LR: 0.025518 | E: -27.132134 | E_var:     0.1727 | E_err:   0.006492
[2025-10-02 21:10:45] [Iter 1461/2250] R4[670/2400] | LR: 0.025493 | E: -27.124084 | E_var:     0.1847 | E_err:   0.006715
[2025-10-02 21:10:49] [Iter 1462/2250] R4[672/2400] | LR: 0.025468 | E: -27.127980 | E_var:     0.2222 | E_err:   0.007366
[2025-10-02 21:10:53] [Iter 1463/2250] R4[674/2400] | LR: 0.025443 | E: -27.122432 | E_var:     0.1474 | E_err:   0.005999
[2025-10-02 21:10:56] [Iter 1464/2250] R4[676/2400] | LR: 0.025417 | E: -27.126833 | E_var:     0.2930 | E_err:   0.008458
[2025-10-02 21:11:00] [Iter 1465/2250] R4[678/2400] | LR: 0.025392 | E: -27.126581 | E_var:     0.1914 | E_err:   0.006837
[2025-10-02 21:11:03] [Iter 1466/2250] R4[680/2400] | LR: 0.025367 | E: -27.130214 | E_var:     0.1621 | E_err:   0.006291
[2025-10-02 21:11:07] [Iter 1467/2250] R4[682/2400] | LR: 0.025341 | E: -27.124411 | E_var:     0.1651 | E_err:   0.006348
[2025-10-02 21:11:11] [Iter 1468/2250] R4[684/2400] | LR: 0.025316 | E: -27.139806 | E_var:     0.1556 | E_err:   0.006164
[2025-10-02 21:11:14] [Iter 1469/2250] R4[686/2400] | LR: 0.025290 | E: -27.133579 | E_var:     0.4002 | E_err:   0.009885
[2025-10-02 21:11:18] [Iter 1470/2250] R4[688/2400] | LR: 0.025264 | E: -27.124894 | E_var:     0.1727 | E_err:   0.006493
[2025-10-02 21:11:22] [Iter 1471/2250] R4[690/2400] | LR: 0.025239 | E: -27.129712 | E_var:     0.2632 | E_err:   0.008016
[2025-10-02 21:11:25] [Iter 1472/2250] R4[692/2400] | LR: 0.025213 | E: -27.120535 | E_var:     0.1508 | E_err:   0.006069
[2025-10-02 21:11:29] [Iter 1473/2250] R4[694/2400] | LR: 0.025187 | E: -27.122492 | E_var:     0.2209 | E_err:   0.007344
[2025-10-02 21:11:33] [Iter 1474/2250] R4[696/2400] | LR: 0.025161 | E: -27.131712 | E_var:     0.1439 | E_err:   0.005928
[2025-10-02 21:11:36] [Iter 1475/2250] R4[698/2400] | LR: 0.025135 | E: -27.130986 | E_var:     0.1736 | E_err:   0.006509
[2025-10-02 21:11:40] [Iter 1476/2250] R4[700/2400] | LR: 0.025110 | E: -27.132582 | E_var:     0.1477 | E_err:   0.006005
[2025-10-02 21:11:44] [Iter 1477/2250] R4[702/2400] | LR: 0.025084 | E: -27.129897 | E_var:     0.2434 | E_err:   0.007709
[2025-10-02 21:11:47] [Iter 1478/2250] R4[704/2400] | LR: 0.025057 | E: -27.137807 | E_var:     0.2043 | E_err:   0.007063
[2025-10-02 21:11:51] [Iter 1479/2250] R4[706/2400] | LR: 0.025031 | E: -27.133140 | E_var:     0.1437 | E_err:   0.005923
[2025-10-02 21:11:55] [Iter 1480/2250] R4[708/2400] | LR: 0.025005 | E: -27.136356 | E_var:     0.1723 | E_err:   0.006486
[2025-10-02 21:11:58] [Iter 1481/2250] R4[710/2400] | LR: 0.024979 | E: -27.135483 | E_var:     0.1544 | E_err:   0.006140
[2025-10-02 21:12:02] [Iter 1482/2250] R4[712/2400] | LR: 0.024953 | E: -27.127388 | E_var:     0.1780 | E_err:   0.006592
[2025-10-02 21:12:06] [Iter 1483/2250] R4[714/2400] | LR: 0.024927 | E: -27.132081 | E_var:     0.1697 | E_err:   0.006436
[2025-10-02 21:12:09] [Iter 1484/2250] R4[716/2400] | LR: 0.024900 | E: -27.132694 | E_var:     0.1512 | E_err:   0.006076
[2025-10-02 21:12:13] [Iter 1485/2250] R4[718/2400] | LR: 0.024874 | E: -27.131313 | E_var:     0.1715 | E_err:   0.006470
[2025-10-02 21:12:17] [Iter 1486/2250] R4[720/2400] | LR: 0.024847 | E: -27.128166 | E_var:     0.1796 | E_err:   0.006622
[2025-10-02 21:12:20] [Iter 1487/2250] R4[722/2400] | LR: 0.024821 | E: -27.122690 | E_var:     0.1907 | E_err:   0.006823
[2025-10-02 21:12:24] [Iter 1488/2250] R4[724/2400] | LR: 0.024794 | E: -27.124306 | E_var:     0.1851 | E_err:   0.006723
[2025-10-02 21:12:27] [Iter 1489/2250] R4[726/2400] | LR: 0.024768 | E: -27.121912 | E_var:     0.1787 | E_err:   0.006605
[2025-10-02 21:12:31] [Iter 1490/2250] R4[728/2400] | LR: 0.024741 | E: -27.126680 | E_var:     0.2394 | E_err:   0.007646
[2025-10-02 21:12:35] [Iter 1491/2250] R4[730/2400] | LR: 0.024714 | E: -27.132057 | E_var:     0.1708 | E_err:   0.006458
[2025-10-02 21:12:38] [Iter 1492/2250] R4[732/2400] | LR: 0.024688 | E: -27.130826 | E_var:     0.1986 | E_err:   0.006964
[2025-10-02 21:12:42] [Iter 1493/2250] R4[734/2400] | LR: 0.024661 | E: -27.128999 | E_var:     0.2097 | E_err:   0.007156
[2025-10-02 21:12:46] [Iter 1494/2250] R4[736/2400] | LR: 0.024634 | E: -27.130901 | E_var:     0.1759 | E_err:   0.006553
[2025-10-02 21:12:49] [Iter 1495/2250] R4[738/2400] | LR: 0.024607 | E: -27.132508 | E_var:     0.1423 | E_err:   0.005893
[2025-10-02 21:12:53] [Iter 1496/2250] R4[740/2400] | LR: 0.024580 | E: -27.134697 | E_var:     0.2377 | E_err:   0.007619
[2025-10-02 21:12:57] [Iter 1497/2250] R4[742/2400] | LR: 0.024553 | E: -27.135144 | E_var:     0.1503 | E_err:   0.006057
[2025-10-02 21:13:00] [Iter 1498/2250] R4[744/2400] | LR: 0.024526 | E: -27.129168 | E_var:     0.1753 | E_err:   0.006542
[2025-10-02 21:13:04] [Iter 1499/2250] R4[746/2400] | LR: 0.024499 | E: -27.129644 | E_var:     0.1496 | E_err:   0.006043
[2025-10-02 21:13:08] [Iter 1500/2250] R4[748/2400] | LR: 0.024472 | E: -27.128521 | E_var:     0.2108 | E_err:   0.007174
[2025-10-02 21:13:11] [Iter 1501/2250] R4[750/2400] | LR: 0.024445 | E: -27.129367 | E_var:     0.1630 | E_err:   0.006308
[2025-10-02 21:13:15] [Iter 1502/2250] R4[752/2400] | LR: 0.024417 | E: -27.135290 | E_var:     0.1678 | E_err:   0.006400
[2025-10-02 21:13:19] [Iter 1503/2250] R4[754/2400] | LR: 0.024390 | E: -27.132354 | E_var:     0.1779 | E_err:   0.006591
[2025-10-02 21:13:22] [Iter 1504/2250] R4[756/2400] | LR: 0.024363 | E: -27.128030 | E_var:     0.1805 | E_err:   0.006638
[2025-10-02 21:13:26] [Iter 1505/2250] R4[758/2400] | LR: 0.024335 | E: -27.125424 | E_var:     0.2070 | E_err:   0.007109
[2025-10-02 21:13:30] [Iter 1506/2250] R4[760/2400] | LR: 0.024308 | E: -27.128212 | E_var:     0.2267 | E_err:   0.007440
[2025-10-02 21:13:33] [Iter 1507/2250] R4[762/2400] | LR: 0.024281 | E: -27.125154 | E_var:     0.2030 | E_err:   0.007039
[2025-10-02 21:13:37] [Iter 1508/2250] R4[764/2400] | LR: 0.024253 | E: -27.129513 | E_var:     0.1471 | E_err:   0.005992
[2025-10-02 21:13:41] [Iter 1509/2250] R4[766/2400] | LR: 0.024225 | E: -27.141494 | E_var:     0.1949 | E_err:   0.006898
[2025-10-02 21:13:44] [Iter 1510/2250] R4[768/2400] | LR: 0.024198 | E: -27.131210 | E_var:     0.1797 | E_err:   0.006624
[2025-10-02 21:13:48] [Iter 1511/2250] R4[770/2400] | LR: 0.024170 | E: -27.132613 | E_var:     0.1734 | E_err:   0.006507
[2025-10-02 21:13:51] [Iter 1512/2250] R4[772/2400] | LR: 0.024142 | E: -27.121863 | E_var:     0.2765 | E_err:   0.008217
[2025-10-02 21:13:55] [Iter 1513/2250] R4[774/2400] | LR: 0.024115 | E: -27.119238 | E_var:     0.1681 | E_err:   0.006406
[2025-10-02 21:13:59] [Iter 1514/2250] R4[776/2400] | LR: 0.024087 | E: -27.135218 | E_var:     0.1575 | E_err:   0.006201
[2025-10-02 21:14:02] [Iter 1515/2250] R4[778/2400] | LR: 0.024059 | E: -27.131186 | E_var:     0.1629 | E_err:   0.006306
[2025-10-02 21:14:06] [Iter 1516/2250] R4[780/2400] | LR: 0.024031 | E: -27.130145 | E_var:     0.2037 | E_err:   0.007051
[2025-10-02 21:14:10] [Iter 1517/2250] R4[782/2400] | LR: 0.024003 | E: -27.131528 | E_var:     0.1624 | E_err:   0.006296
[2025-10-02 21:14:13] [Iter 1518/2250] R4[784/2400] | LR: 0.023975 | E: -27.135811 | E_var:     0.2235 | E_err:   0.007387
[2025-10-02 21:14:17] [Iter 1519/2250] R4[786/2400] | LR: 0.023947 | E: -27.137937 | E_var:     0.1467 | E_err:   0.005984
[2025-10-02 21:14:21] [Iter 1520/2250] R4[788/2400] | LR: 0.023919 | E: -27.124910 | E_var:     0.1384 | E_err:   0.005813
[2025-10-02 21:14:24] [Iter 1521/2250] R4[790/2400] | LR: 0.023891 | E: -27.126998 | E_var:     0.1827 | E_err:   0.006678
[2025-10-02 21:14:28] [Iter 1522/2250] R4[792/2400] | LR: 0.023863 | E: -27.127674 | E_var:     0.1568 | E_err:   0.006188
[2025-10-02 21:14:32] [Iter 1523/2250] R4[794/2400] | LR: 0.023835 | E: -27.125686 | E_var:     0.1897 | E_err:   0.006805
[2025-10-02 21:14:35] [Iter 1524/2250] R4[796/2400] | LR: 0.023807 | E: -27.126642 | E_var:     0.1652 | E_err:   0.006351
[2025-10-02 21:14:39] [Iter 1525/2250] R4[798/2400] | LR: 0.023778 | E: -27.126520 | E_var:     0.2057 | E_err:   0.007087
[2025-10-02 21:14:43] [Iter 1526/2250] R4[800/2400] | LR: 0.023750 | E: -27.133349 | E_var:     0.1860 | E_err:   0.006739
[2025-10-02 21:14:46] [Iter 1527/2250] R4[802/2400] | LR: 0.023722 | E: -27.131060 | E_var:     0.1805 | E_err:   0.006639
[2025-10-02 21:14:50] [Iter 1528/2250] R4[804/2400] | LR: 0.023693 | E: -27.126152 | E_var:     0.1670 | E_err:   0.006385
[2025-10-02 21:14:54] [Iter 1529/2250] R4[806/2400] | LR: 0.023665 | E: -27.127104 | E_var:     0.1852 | E_err:   0.006724
[2025-10-02 21:14:57] [Iter 1530/2250] R4[808/2400] | LR: 0.023636 | E: -27.127307 | E_var:     0.1668 | E_err:   0.006381
[2025-10-02 21:15:01] [Iter 1531/2250] R4[810/2400] | LR: 0.023608 | E: -27.137621 | E_var:     0.2657 | E_err:   0.008054
[2025-10-02 21:15:05] [Iter 1532/2250] R4[812/2400] | LR: 0.023579 | E: -27.125527 | E_var:     0.1670 | E_err:   0.006384
[2025-10-02 21:15:08] [Iter 1533/2250] R4[814/2400] | LR: 0.023551 | E: -27.145910 | E_var:     0.1586 | E_err:   0.006223
[2025-10-02 21:15:12] [Iter 1534/2250] R4[816/2400] | LR: 0.023522 | E: -27.140539 | E_var:     0.1754 | E_err:   0.006545
[2025-10-02 21:15:15] [Iter 1535/2250] R4[818/2400] | LR: 0.023493 | E: -27.134743 | E_var:     0.1959 | E_err:   0.006916
[2025-10-02 21:15:19] [Iter 1536/2250] R4[820/2400] | LR: 0.023464 | E: -27.127715 | E_var:     0.2155 | E_err:   0.007253
[2025-10-02 21:15:23] [Iter 1537/2250] R4[822/2400] | LR: 0.023436 | E: -27.121494 | E_var:     0.2316 | E_err:   0.007520
[2025-10-02 21:15:26] [Iter 1538/2250] R4[824/2400] | LR: 0.023407 | E: -27.124319 | E_var:     0.2031 | E_err:   0.007042
[2025-10-02 21:15:30] [Iter 1539/2250] R4[826/2400] | LR: 0.023378 | E: -27.123345 | E_var:     0.1907 | E_err:   0.006823
[2025-10-02 21:15:34] [Iter 1540/2250] R4[828/2400] | LR: 0.023349 | E: -27.123663 | E_var:     0.1607 | E_err:   0.006265
[2025-10-02 21:15:37] [Iter 1541/2250] R4[830/2400] | LR: 0.023320 | E: -27.132874 | E_var:     0.2008 | E_err:   0.007003
[2025-10-02 21:15:41] [Iter 1542/2250] R4[832/2400] | LR: 0.023291 | E: -27.136459 | E_var:     0.1704 | E_err:   0.006451
[2025-10-02 21:15:45] [Iter 1543/2250] R4[834/2400] | LR: 0.023262 | E: -27.132752 | E_var:     0.1485 | E_err:   0.006022
[2025-10-02 21:15:48] [Iter 1544/2250] R4[836/2400] | LR: 0.023233 | E: -27.133309 | E_var:     0.1673 | E_err:   0.006392
[2025-10-02 21:15:52] [Iter 1545/2250] R4[838/2400] | LR: 0.023204 | E: -27.133353 | E_var:     0.2170 | E_err:   0.007279
[2025-10-02 21:15:56] [Iter 1546/2250] R4[840/2400] | LR: 0.023175 | E: -27.136276 | E_var:     0.1909 | E_err:   0.006826
[2025-10-02 21:15:59] [Iter 1547/2250] R4[842/2400] | LR: 0.023146 | E: -27.118621 | E_var:     0.1718 | E_err:   0.006477
[2025-10-02 21:16:03] [Iter 1548/2250] R4[844/2400] | LR: 0.023116 | E: -27.135119 | E_var:     0.1679 | E_err:   0.006402
[2025-10-02 21:16:07] [Iter 1549/2250] R4[846/2400] | LR: 0.023087 | E: -27.143051 | E_var:     0.2257 | E_err:   0.007423
[2025-10-02 21:16:10] [Iter 1550/2250] R4[848/2400] | LR: 0.023058 | E: -27.135219 | E_var:     0.1398 | E_err:   0.005843
[2025-10-02 21:16:14] [Iter 1551/2250] R4[850/2400] | LR: 0.023029 | E: -27.129659 | E_var:     0.1566 | E_err:   0.006184
[2025-10-02 21:16:18] [Iter 1552/2250] R4[852/2400] | LR: 0.022999 | E: -27.134655 | E_var:     0.1557 | E_err:   0.006166
[2025-10-02 21:16:21] [Iter 1553/2250] R4[854/2400] | LR: 0.022970 | E: -27.131455 | E_var:     0.1729 | E_err:   0.006498
[2025-10-02 21:16:25] [Iter 1554/2250] R4[856/2400] | LR: 0.022940 | E: -27.128322 | E_var:     0.1718 | E_err:   0.006476
[2025-10-02 21:16:29] [Iter 1555/2250] R4[858/2400] | LR: 0.022911 | E: -27.135427 | E_var:     0.1935 | E_err:   0.006874
[2025-10-02 21:16:32] [Iter 1556/2250] R4[860/2400] | LR: 0.022881 | E: -27.131984 | E_var:     0.1804 | E_err:   0.006636
[2025-10-02 21:16:36] [Iter 1557/2250] R4[862/2400] | LR: 0.022852 | E: -27.140646 | E_var:     0.2021 | E_err:   0.007024
[2025-10-02 21:16:40] [Iter 1558/2250] R4[864/2400] | LR: 0.022822 | E: -27.130359 | E_var:     0.1620 | E_err:   0.006289
[2025-10-02 21:16:43] [Iter 1559/2250] R4[866/2400] | LR: 0.022793 | E: -27.143517 | E_var:     0.1983 | E_err:   0.006957
[2025-10-02 21:16:47] [Iter 1560/2250] R4[868/2400] | LR: 0.022763 | E: -27.124275 | E_var:     0.1726 | E_err:   0.006491
[2025-10-02 21:16:51] [Iter 1561/2250] R4[870/2400] | LR: 0.022733 | E: -27.123357 | E_var:     0.2058 | E_err:   0.007088
[2025-10-02 21:16:54] [Iter 1562/2250] R4[872/2400] | LR: 0.022704 | E: -27.130055 | E_var:     0.1556 | E_err:   0.006164
[2025-10-02 21:16:58] [Iter 1563/2250] R4[874/2400] | LR: 0.022674 | E: -27.123202 | E_var:     0.2438 | E_err:   0.007715
[2025-10-02 21:17:01] [Iter 1564/2250] R4[876/2400] | LR: 0.022644 | E: -27.137648 | E_var:     0.1571 | E_err:   0.006193
[2025-10-02 21:17:05] [Iter 1565/2250] R4[878/2400] | LR: 0.022614 | E: -27.125924 | E_var:     0.1955 | E_err:   0.006909
[2025-10-02 21:17:09] [Iter 1566/2250] R4[880/2400] | LR: 0.022584 | E: -27.116036 | E_var:     0.1378 | E_err:   0.005800
[2025-10-02 21:17:12] [Iter 1567/2250] R4[882/2400] | LR: 0.022554 | E: -27.128228 | E_var:     0.1686 | E_err:   0.006417
[2025-10-02 21:17:16] [Iter 1568/2250] R4[884/2400] | LR: 0.022524 | E: -27.129324 | E_var:     0.1474 | E_err:   0.005999
[2025-10-02 21:17:20] [Iter 1569/2250] R4[886/2400] | LR: 0.022494 | E: -27.128971 | E_var:     0.1613 | E_err:   0.006276
[2025-10-02 21:17:23] [Iter 1570/2250] R4[888/2400] | LR: 0.022464 | E: -27.128188 | E_var:     0.1750 | E_err:   0.006537
[2025-10-02 21:17:27] [Iter 1571/2250] R4[890/2400] | LR: 0.022434 | E: -27.129040 | E_var:     0.1583 | E_err:   0.006217
[2025-10-02 21:17:31] [Iter 1572/2250] R4[892/2400] | LR: 0.022404 | E: -27.134511 | E_var:     0.1655 | E_err:   0.006357
[2025-10-02 21:17:34] [Iter 1573/2250] R4[894/2400] | LR: 0.022374 | E: -27.126831 | E_var:     0.1926 | E_err:   0.006857
[2025-10-02 21:17:38] [Iter 1574/2250] R4[896/2400] | LR: 0.022344 | E: -27.138465 | E_var:     0.1708 | E_err:   0.006458
[2025-10-02 21:17:42] [Iter 1575/2250] R4[898/2400] | LR: 0.022314 | E: -27.133731 | E_var:     0.1818 | E_err:   0.006662
[2025-10-02 21:17:45] [Iter 1576/2250] R4[900/2400] | LR: 0.022284 | E: -27.130023 | E_var:     0.1528 | E_err:   0.006107
[2025-10-02 21:17:49] [Iter 1577/2250] R4[902/2400] | LR: 0.022253 | E: -27.144679 | E_var:     0.1599 | E_err:   0.006249
[2025-10-02 21:17:53] [Iter 1578/2250] R4[904/2400] | LR: 0.022223 | E: -27.126774 | E_var:     0.1649 | E_err:   0.006346
[2025-10-02 21:17:56] [Iter 1579/2250] R4[906/2400] | LR: 0.022193 | E: -27.133190 | E_var:     0.1511 | E_err:   0.006074
[2025-10-02 21:18:00] [Iter 1580/2250] R4[908/2400] | LR: 0.022162 | E: -27.130078 | E_var:     0.1736 | E_err:   0.006510
[2025-10-02 21:18:04] [Iter 1581/2250] R4[910/2400] | LR: 0.022132 | E: -27.128656 | E_var:     0.1629 | E_err:   0.006306
[2025-10-02 21:18:07] [Iter 1582/2250] R4[912/2400] | LR: 0.022102 | E: -27.135788 | E_var:     0.1581 | E_err:   0.006214
[2025-10-02 21:18:11] [Iter 1583/2250] R4[914/2400] | LR: 0.022071 | E: -27.128530 | E_var:     0.1648 | E_err:   0.006343
[2025-10-02 21:18:15] [Iter 1584/2250] R4[916/2400] | LR: 0.022041 | E: -27.134141 | E_var:     0.1803 | E_err:   0.006635
[2025-10-02 21:18:18] [Iter 1585/2250] R4[918/2400] | LR: 0.022010 | E: -27.120350 | E_var:     0.1708 | E_err:   0.006457
[2025-10-02 21:18:22] [Iter 1586/2250] R4[920/2400] | LR: 0.021980 | E: -27.130087 | E_var:     0.1652 | E_err:   0.006351
[2025-10-02 21:18:26] [Iter 1587/2250] R4[922/2400] | LR: 0.021949 | E: -27.124114 | E_var:     0.1458 | E_err:   0.005966
[2025-10-02 21:18:29] [Iter 1588/2250] R4[924/2400] | LR: 0.021918 | E: -27.126528 | E_var:     0.1875 | E_err:   0.006766
[2025-10-02 21:18:33] [Iter 1589/2250] R4[926/2400] | LR: 0.021888 | E: -27.125427 | E_var:     0.1756 | E_err:   0.006547
[2025-10-02 21:18:36] [Iter 1590/2250] R4[928/2400] | LR: 0.021857 | E: -27.141036 | E_var:     0.1900 | E_err:   0.006810
[2025-10-02 21:18:40] [Iter 1591/2250] R4[930/2400] | LR: 0.021826 | E: -27.132247 | E_var:     0.1732 | E_err:   0.006502
[2025-10-02 21:18:44] [Iter 1592/2250] R4[932/2400] | LR: 0.021796 | E: -27.131392 | E_var:     0.1710 | E_err:   0.006462
[2025-10-02 21:18:47] [Iter 1593/2250] R4[934/2400] | LR: 0.021765 | E: -27.139507 | E_var:     0.1605 | E_err:   0.006260
[2025-10-02 21:18:51] [Iter 1594/2250] R4[936/2400] | LR: 0.021734 | E: -27.130335 | E_var:     0.1996 | E_err:   0.006981
[2025-10-02 21:18:55] [Iter 1595/2250] R4[938/2400] | LR: 0.021703 | E: -27.119842 | E_var:     0.1561 | E_err:   0.006173
[2025-10-02 21:18:58] [Iter 1596/2250] R4[940/2400] | LR: 0.021673 | E: -27.142725 | E_var:     0.1620 | E_err:   0.006288
[2025-10-02 21:19:02] [Iter 1597/2250] R4[942/2400] | LR: 0.021642 | E: -27.127484 | E_var:     0.1574 | E_err:   0.006199
[2025-10-02 21:19:06] [Iter 1598/2250] R4[944/2400] | LR: 0.021611 | E: -27.141093 | E_var:     0.1608 | E_err:   0.006266
[2025-10-02 21:19:09] [Iter 1599/2250] R4[946/2400] | LR: 0.021580 | E: -27.129948 | E_var:     0.1537 | E_err:   0.006125
[2025-10-02 21:19:13] [Iter 1600/2250] R4[948/2400] | LR: 0.021549 | E: -27.140365 | E_var:     0.1662 | E_err:   0.006370
[2025-10-02 21:19:13] ✓ Checkpoint saved: checkpoint_iter_001600.pkl
[2025-10-02 21:19:17] [Iter 1601/2250] R4[950/2400] | LR: 0.021518 | E: -27.121570 | E_var:     0.1511 | E_err:   0.006074
[2025-10-02 21:19:20] [Iter 1602/2250] R4[952/2400] | LR: 0.021487 | E: -27.124412 | E_var:     0.2157 | E_err:   0.007257
[2025-10-02 21:19:24] [Iter 1603/2250] R4[954/2400] | LR: 0.021456 | E: -27.129731 | E_var:     0.1561 | E_err:   0.006174
[2025-10-02 21:19:28] [Iter 1604/2250] R4[956/2400] | LR: 0.021425 | E: -27.132376 | E_var:     0.1652 | E_err:   0.006350
[2025-10-02 21:19:31] [Iter 1605/2250] R4[958/2400] | LR: 0.021394 | E: -27.128755 | E_var:     0.1980 | E_err:   0.006952
[2025-10-02 21:19:35] [Iter 1606/2250] R4[960/2400] | LR: 0.021363 | E: -27.146765 | E_var:     0.1799 | E_err:   0.006627
[2025-10-02 21:19:39] [Iter 1607/2250] R4[962/2400] | LR: 0.021332 | E: -27.127491 | E_var:     0.1617 | E_err:   0.006284
[2025-10-02 21:19:42] [Iter 1608/2250] R4[964/2400] | LR: 0.021300 | E: -27.142108 | E_var:     0.1979 | E_err:   0.006951
[2025-10-02 21:19:46] [Iter 1609/2250] R4[966/2400] | LR: 0.021269 | E: -27.130203 | E_var:     0.3018 | E_err:   0.008584
[2025-10-02 21:19:50] [Iter 1610/2250] R4[968/2400] | LR: 0.021238 | E: -27.124188 | E_var:     0.1542 | E_err:   0.006135
[2025-10-02 21:19:53] [Iter 1611/2250] R4[970/2400] | LR: 0.021207 | E: -27.124076 | E_var:     0.1636 | E_err:   0.006319
[2025-10-02 21:19:57] [Iter 1612/2250] R4[972/2400] | LR: 0.021176 | E: -27.135549 | E_var:     0.1891 | E_err:   0.006795
[2025-10-02 21:20:01] [Iter 1613/2250] R4[974/2400] | LR: 0.021144 | E: -27.123427 | E_var:     0.1759 | E_err:   0.006553
[2025-10-02 21:20:04] [Iter 1614/2250] R4[976/2400] | LR: 0.021113 | E: -27.132503 | E_var:     0.2682 | E_err:   0.008092
[2025-10-02 21:20:08] [Iter 1615/2250] R4[978/2400] | LR: 0.021082 | E: -27.143670 | E_var:     0.1634 | E_err:   0.006317
[2025-10-02 21:20:12] [Iter 1616/2250] R4[980/2400] | LR: 0.021050 | E: -27.123284 | E_var:     0.1918 | E_err:   0.006842
[2025-10-02 21:20:15] [Iter 1617/2250] R4[982/2400] | LR: 0.021019 | E: -27.123327 | E_var:     0.1583 | E_err:   0.006217
[2025-10-02 21:20:19] [Iter 1618/2250] R4[984/2400] | LR: 0.020987 | E: -27.127549 | E_var:     0.2340 | E_err:   0.007558
[2025-10-02 21:20:23] [Iter 1619/2250] R4[986/2400] | LR: 0.020956 | E: -27.122814 | E_var:     0.2291 | E_err:   0.007480
[2025-10-02 21:20:26] [Iter 1620/2250] R4[988/2400] | LR: 0.020924 | E: -27.135276 | E_var:     0.1704 | E_err:   0.006449
[2025-10-02 21:20:30] [Iter 1621/2250] R4[990/2400] | LR: 0.020893 | E: -27.130654 | E_var:     0.1591 | E_err:   0.006233
[2025-10-02 21:20:33] [Iter 1622/2250] R4[992/2400] | LR: 0.020861 | E: -27.136896 | E_var:     0.1373 | E_err:   0.005791
[2025-10-02 21:20:37] [Iter 1623/2250] R4[994/2400] | LR: 0.020830 | E: -27.126907 | E_var:     0.1688 | E_err:   0.006420
[2025-10-02 21:20:41] [Iter 1624/2250] R4[996/2400] | LR: 0.020798 | E: -27.129164 | E_var:     0.2296 | E_err:   0.007486
[2025-10-02 21:20:44] [Iter 1625/2250] R4[998/2400] | LR: 0.020767 | E: -27.139811 | E_var:     0.1877 | E_err:   0.006770
[2025-10-02 21:20:48] [Iter 1626/2250] R4[1000/2400] | LR: 0.020735 | E: -27.135625 | E_var:     0.1388 | E_err:   0.005820
[2025-10-02 21:20:52] [Iter 1627/2250] R4[1002/2400] | LR: 0.020704 | E: -27.123905 | E_var:     0.1478 | E_err:   0.006006
[2025-10-02 21:20:55] [Iter 1628/2250] R4[1004/2400] | LR: 0.020672 | E: -27.133939 | E_var:     0.1945 | E_err:   0.006890
[2025-10-02 21:20:59] [Iter 1629/2250] R4[1006/2400] | LR: 0.020640 | E: -27.127585 | E_var:     0.3635 | E_err:   0.009420
[2025-10-02 21:21:03] [Iter 1630/2250] R4[1008/2400] | LR: 0.020609 | E: -27.126420 | E_var:     0.1892 | E_err:   0.006797
[2025-10-02 21:21:06] [Iter 1631/2250] R4[1010/2400] | LR: 0.020577 | E: -27.127251 | E_var:     0.1628 | E_err:   0.006305
[2025-10-02 21:21:10] [Iter 1632/2250] R4[1012/2400] | LR: 0.020545 | E: -27.142082 | E_var:     0.1746 | E_err:   0.006528
[2025-10-02 21:21:14] [Iter 1633/2250] R4[1014/2400] | LR: 0.020513 | E: -27.136935 | E_var:     0.1726 | E_err:   0.006491
[2025-10-02 21:21:17] [Iter 1634/2250] R4[1016/2400] | LR: 0.020482 | E: -27.128326 | E_var:     0.1567 | E_err:   0.006186
[2025-10-02 21:21:21] [Iter 1635/2250] R4[1018/2400] | LR: 0.020450 | E: -27.122091 | E_var:     0.1903 | E_err:   0.006817
[2025-10-02 21:21:25] [Iter 1636/2250] R4[1020/2400] | LR: 0.020418 | E: -27.115943 | E_var:     0.2929 | E_err:   0.008456
[2025-10-02 21:21:28] [Iter 1637/2250] R4[1022/2400] | LR: 0.020386 | E: -27.125760 | E_var:     0.1522 | E_err:   0.006096
[2025-10-02 21:21:32] [Iter 1638/2250] R4[1024/2400] | LR: 0.020354 | E: -27.131219 | E_var:     0.1834 | E_err:   0.006691
[2025-10-02 21:21:36] [Iter 1639/2250] R4[1026/2400] | LR: 0.020323 | E: -27.124291 | E_var:     0.2336 | E_err:   0.007552
[2025-10-02 21:21:39] [Iter 1640/2250] R4[1028/2400] | LR: 0.020291 | E: -27.132739 | E_var:     0.1819 | E_err:   0.006664
[2025-10-02 21:21:43] [Iter 1641/2250] R4[1030/2400] | LR: 0.020259 | E: -27.124933 | E_var:     0.1434 | E_err:   0.005917
[2025-10-02 21:21:47] [Iter 1642/2250] R4[1032/2400] | LR: 0.020227 | E: -27.128415 | E_var:     0.2089 | E_err:   0.007142
[2025-10-02 21:21:50] [Iter 1643/2250] R4[1034/2400] | LR: 0.020195 | E: -27.120202 | E_var:     0.1802 | E_err:   0.006633
[2025-10-02 21:21:54] [Iter 1644/2250] R4[1036/2400] | LR: 0.020163 | E: -27.118858 | E_var:     0.2213 | E_err:   0.007350
[2025-10-02 21:21:58] [Iter 1645/2250] R4[1038/2400] | LR: 0.020131 | E: -27.136003 | E_var:     0.1737 | E_err:   0.006512
[2025-10-02 21:22:01] [Iter 1646/2250] R4[1040/2400] | LR: 0.020099 | E: -27.124681 | E_var:     0.1470 | E_err:   0.005991
[2025-10-02 21:22:05] [Iter 1647/2250] R4[1042/2400] | LR: 0.020067 | E: -27.131854 | E_var:     0.1738 | E_err:   0.006514
[2025-10-02 21:22:08] [Iter 1648/2250] R4[1044/2400] | LR: 0.020035 | E: -27.127246 | E_var:     0.1713 | E_err:   0.006468
[2025-10-02 21:22:12] [Iter 1649/2250] R4[1046/2400] | LR: 0.020003 | E: -27.132435 | E_var:     0.1419 | E_err:   0.005887
[2025-10-02 21:22:16] [Iter 1650/2250] R4[1048/2400] | LR: 0.019971 | E: -27.124323 | E_var:     0.1530 | E_err:   0.006112
[2025-10-02 21:22:19] [Iter 1651/2250] R4[1050/2400] | LR: 0.019939 | E: -27.124374 | E_var:     0.1548 | E_err:   0.006148
[2025-10-02 21:22:23] [Iter 1652/2250] R4[1052/2400] | LR: 0.019907 | E: -27.135218 | E_var:     0.1960 | E_err:   0.006918
[2025-10-02 21:22:27] [Iter 1653/2250] R4[1054/2400] | LR: 0.019874 | E: -27.128816 | E_var:     0.1745 | E_err:   0.006527
[2025-10-02 21:22:30] [Iter 1654/2250] R4[1056/2400] | LR: 0.019842 | E: -27.134511 | E_var:     0.1968 | E_err:   0.006931
[2025-10-02 21:22:34] [Iter 1655/2250] R4[1058/2400] | LR: 0.019810 | E: -27.139642 | E_var:     0.1803 | E_err:   0.006634
[2025-10-02 21:22:38] [Iter 1656/2250] R4[1060/2400] | LR: 0.019778 | E: -27.141336 | E_var:     0.1551 | E_err:   0.006153
[2025-10-02 21:22:41] [Iter 1657/2250] R4[1062/2400] | LR: 0.019746 | E: -27.138347 | E_var:     0.1644 | E_err:   0.006335
[2025-10-02 21:22:45] [Iter 1658/2250] R4[1064/2400] | LR: 0.019714 | E: -27.133996 | E_var:     0.1613 | E_err:   0.006275
[2025-10-02 21:22:49] [Iter 1659/2250] R4[1066/2400] | LR: 0.019681 | E: -27.138979 | E_var:     0.1457 | E_err:   0.005963
[2025-10-02 21:22:52] [Iter 1660/2250] R4[1068/2400] | LR: 0.019649 | E: -27.127590 | E_var:     0.2327 | E_err:   0.007537
[2025-10-02 21:22:56] [Iter 1661/2250] R4[1070/2400] | LR: 0.019617 | E: -27.130413 | E_var:     0.1470 | E_err:   0.005990
[2025-10-02 21:23:00] [Iter 1662/2250] R4[1072/2400] | LR: 0.019585 | E: -27.140077 | E_var:     0.1192 | E_err:   0.005394
[2025-10-02 21:23:03] [Iter 1663/2250] R4[1074/2400] | LR: 0.019552 | E: -27.123445 | E_var:     0.1531 | E_err:   0.006114
[2025-10-02 21:23:07] [Iter 1664/2250] R4[1076/2400] | LR: 0.019520 | E: -27.130100 | E_var:     0.2024 | E_err:   0.007029
[2025-10-02 21:23:11] [Iter 1665/2250] R4[1078/2400] | LR: 0.019488 | E: -27.137922 | E_var:     0.1545 | E_err:   0.006141
[2025-10-02 21:23:14] [Iter 1666/2250] R4[1080/2400] | LR: 0.019455 | E: -27.132239 | E_var:     0.1850 | E_err:   0.006721
[2025-10-02 21:23:18] [Iter 1667/2250] R4[1082/2400] | LR: 0.019423 | E: -27.124366 | E_var:     0.1597 | E_err:   0.006245
[2025-10-02 21:23:22] [Iter 1668/2250] R4[1084/2400] | LR: 0.019391 | E: -27.134693 | E_var:     0.1685 | E_err:   0.006415
[2025-10-02 21:23:25] [Iter 1669/2250] R4[1086/2400] | LR: 0.019358 | E: -27.125148 | E_var:     0.1598 | E_err:   0.006247
[2025-10-02 21:23:29] [Iter 1670/2250] R4[1088/2400] | LR: 0.019326 | E: -27.127617 | E_var:     0.1778 | E_err:   0.006589
[2025-10-02 21:23:32] [Iter 1671/2250] R4[1090/2400] | LR: 0.019294 | E: -27.138611 | E_var:     0.1773 | E_err:   0.006579
[2025-10-02 21:23:36] [Iter 1672/2250] R4[1092/2400] | LR: 0.019261 | E: -27.130822 | E_var:     0.1571 | E_err:   0.006193
[2025-10-02 21:23:40] [Iter 1673/2250] R4[1094/2400] | LR: 0.019229 | E: -27.127308 | E_var:     0.2001 | E_err:   0.006990
[2025-10-02 21:23:43] [Iter 1674/2250] R4[1096/2400] | LR: 0.019196 | E: -27.129690 | E_var:     0.1579 | E_err:   0.006210
[2025-10-02 21:23:47] [Iter 1675/2250] R4[1098/2400] | LR: 0.019164 | E: -27.131414 | E_var:     0.1521 | E_err:   0.006094
[2025-10-02 21:23:51] [Iter 1676/2250] R4[1100/2400] | LR: 0.019132 | E: -27.137277 | E_var:     0.1809 | E_err:   0.006646
[2025-10-02 21:23:54] [Iter 1677/2250] R4[1102/2400] | LR: 0.019099 | E: -27.128165 | E_var:     0.1700 | E_err:   0.006443
[2025-10-02 21:23:58] [Iter 1678/2250] R4[1104/2400] | LR: 0.019067 | E: -27.132165 | E_var:     0.1503 | E_err:   0.006058
[2025-10-02 21:24:02] [Iter 1679/2250] R4[1106/2400] | LR: 0.019034 | E: -27.130864 | E_var:     0.2038 | E_err:   0.007053
[2025-10-02 21:24:05] [Iter 1680/2250] R4[1108/2400] | LR: 0.019002 | E: -27.128445 | E_var:     0.1748 | E_err:   0.006534
[2025-10-02 21:24:09] [Iter 1681/2250] R4[1110/2400] | LR: 0.018969 | E: -27.128408 | E_var:     0.1733 | E_err:   0.006504
[2025-10-02 21:24:13] [Iter 1682/2250] R4[1112/2400] | LR: 0.018937 | E: -27.131184 | E_var:     0.1626 | E_err:   0.006301
[2025-10-02 21:24:16] [Iter 1683/2250] R4[1114/2400] | LR: 0.018904 | E: -27.125981 | E_var:     0.2075 | E_err:   0.007117
[2025-10-02 21:24:20] [Iter 1684/2250] R4[1116/2400] | LR: 0.018872 | E: -27.121918 | E_var:     0.2040 | E_err:   0.007057
[2025-10-02 21:24:24] [Iter 1685/2250] R4[1118/2400] | LR: 0.018839 | E: -27.131421 | E_var:     0.2999 | E_err:   0.008556
[2025-10-02 21:24:27] [Iter 1686/2250] R4[1120/2400] | LR: 0.018807 | E: -27.128352 | E_var:     0.1727 | E_err:   0.006493
[2025-10-02 21:24:31] [Iter 1687/2250] R4[1122/2400] | LR: 0.018774 | E: -27.125434 | E_var:     0.2106 | E_err:   0.007170
[2025-10-02 21:24:35] [Iter 1688/2250] R4[1124/2400] | LR: 0.018741 | E: -27.135946 | E_var:     0.1911 | E_err:   0.006830
[2025-10-02 21:24:38] [Iter 1689/2250] R4[1126/2400] | LR: 0.018709 | E: -27.132333 | E_var:     0.1478 | E_err:   0.006008
[2025-10-02 21:24:42] [Iter 1690/2250] R4[1128/2400] | LR: 0.018676 | E: -27.128741 | E_var:     0.1937 | E_err:   0.006876
[2025-10-02 21:24:46] [Iter 1691/2250] R4[1130/2400] | LR: 0.018644 | E: -27.130837 | E_var:     0.2126 | E_err:   0.007205
[2025-10-02 21:24:49] [Iter 1692/2250] R4[1132/2400] | LR: 0.018611 | E: -27.134524 | E_var:     0.1578 | E_err:   0.006207
[2025-10-02 21:24:53] [Iter 1693/2250] R4[1134/2400] | LR: 0.018579 | E: -27.128543 | E_var:     0.1382 | E_err:   0.005808
[2025-10-02 21:24:57] [Iter 1694/2250] R4[1136/2400] | LR: 0.018546 | E: -27.128339 | E_var:     0.1657 | E_err:   0.006359
[2025-10-02 21:25:00] [Iter 1695/2250] R4[1138/2400] | LR: 0.018513 | E: -27.138512 | E_var:     0.1349 | E_err:   0.005739
[2025-10-02 21:25:04] [Iter 1696/2250] R4[1140/2400] | LR: 0.018481 | E: -27.120524 | E_var:     0.2337 | E_err:   0.007554
[2025-10-02 21:25:08] [Iter 1697/2250] R4[1142/2400] | LR: 0.018448 | E: -27.138118 | E_var:     0.1498 | E_err:   0.006048
[2025-10-02 21:25:11] [Iter 1698/2250] R4[1144/2400] | LR: 0.018415 | E: -27.129141 | E_var:     0.1416 | E_err:   0.005880
[2025-10-02 21:25:15] [Iter 1699/2250] R4[1146/2400] | LR: 0.018383 | E: -27.138277 | E_var:     0.1587 | E_err:   0.006226
[2025-10-02 21:25:18] [Iter 1700/2250] R4[1148/2400] | LR: 0.018350 | E: -27.138469 | E_var:     0.2239 | E_err:   0.007393
[2025-10-02 21:25:22] [Iter 1701/2250] R4[1150/2400] | LR: 0.018318 | E: -27.113648 | E_var:     0.1603 | E_err:   0.006256
[2025-10-02 21:25:26] [Iter 1702/2250] R4[1152/2400] | LR: 0.018285 | E: -27.124834 | E_var:     0.1669 | E_err:   0.006383
[2025-10-02 21:25:29] [Iter 1703/2250] R4[1154/2400] | LR: 0.018252 | E: -27.134075 | E_var:     0.1693 | E_err:   0.006430
[2025-10-02 21:25:33] [Iter 1704/2250] R4[1156/2400] | LR: 0.018220 | E: -27.136838 | E_var:     0.1910 | E_err:   0.006828
[2025-10-02 21:25:37] [Iter 1705/2250] R4[1158/2400] | LR: 0.018187 | E: -27.125255 | E_var:     0.1548 | E_err:   0.006148
[2025-10-02 21:25:40] [Iter 1706/2250] R4[1160/2400] | LR: 0.018154 | E: -27.140077 | E_var:     0.1731 | E_err:   0.006501
[2025-10-02 21:25:44] [Iter 1707/2250] R4[1162/2400] | LR: 0.018122 | E: -27.122377 | E_var:     0.2241 | E_err:   0.007397
[2025-10-02 21:25:48] [Iter 1708/2250] R4[1164/2400] | LR: 0.018089 | E: -27.143116 | E_var:     0.1994 | E_err:   0.006978
[2025-10-02 21:25:51] [Iter 1709/2250] R4[1166/2400] | LR: 0.018056 | E: -27.125972 | E_var:     0.1905 | E_err:   0.006819
[2025-10-02 21:25:55] [Iter 1710/2250] R4[1168/2400] | LR: 0.018023 | E: -27.140425 | E_var:     0.1759 | E_err:   0.006554
[2025-10-02 21:25:59] [Iter 1711/2250] R4[1170/2400] | LR: 0.017991 | E: -27.120687 | E_var:     0.2196 | E_err:   0.007321
[2025-10-02 21:26:02] [Iter 1712/2250] R4[1172/2400] | LR: 0.017958 | E: -27.137799 | E_var:     0.1311 | E_err:   0.005658
[2025-10-02 21:26:06] [Iter 1713/2250] R4[1174/2400] | LR: 0.017925 | E: -27.135450 | E_var:     0.5518 | E_err:   0.011606
[2025-10-02 21:26:10] [Iter 1714/2250] R4[1176/2400] | LR: 0.017893 | E: -27.131047 | E_var:     0.1968 | E_err:   0.006932
[2025-10-02 21:26:13] [Iter 1715/2250] R4[1178/2400] | LR: 0.017860 | E: -27.135142 | E_var:     0.1676 | E_err:   0.006396
[2025-10-02 21:26:17] [Iter 1716/2250] R4[1180/2400] | LR: 0.017827 | E: -27.127715 | E_var:     0.1677 | E_err:   0.006399
[2025-10-02 21:26:21] [Iter 1717/2250] R4[1182/2400] | LR: 0.017794 | E: -27.131120 | E_var:     0.1733 | E_err:   0.006505
[2025-10-02 21:26:24] [Iter 1718/2250] R4[1184/2400] | LR: 0.017762 | E: -27.138954 | E_var:     0.1634 | E_err:   0.006315
[2025-10-02 21:26:28] [Iter 1719/2250] R4[1186/2400] | LR: 0.017729 | E: -27.137422 | E_var:     0.1452 | E_err:   0.005953
[2025-10-02 21:26:31] [Iter 1720/2250] R4[1188/2400] | LR: 0.017696 | E: -27.136513 | E_var:     0.1864 | E_err:   0.006747
[2025-10-02 21:26:35] [Iter 1721/2250] R4[1190/2400] | LR: 0.017664 | E: -27.123858 | E_var:     0.1564 | E_err:   0.006180
[2025-10-02 21:26:39] [Iter 1722/2250] R4[1192/2400] | LR: 0.017631 | E: -27.127206 | E_var:     0.2189 | E_err:   0.007310
[2025-10-02 21:26:42] [Iter 1723/2250] R4[1194/2400] | LR: 0.017598 | E: -27.125225 | E_var:     0.1721 | E_err:   0.006482
[2025-10-02 21:26:46] [Iter 1724/2250] R4[1196/2400] | LR: 0.017565 | E: -27.126281 | E_var:     0.1384 | E_err:   0.005813
[2025-10-02 21:26:50] [Iter 1725/2250] R4[1198/2400] | LR: 0.017533 | E: -27.123571 | E_var:     0.1530 | E_err:   0.006111
[2025-10-02 21:26:53] [Iter 1726/2250] R4[1200/2400] | LR: 0.017500 | E: -27.128209 | E_var:     0.1841 | E_err:   0.006705
[2025-10-02 21:26:57] [Iter 1727/2250] R4[1202/2400] | LR: 0.017467 | E: -27.122407 | E_var:     0.1778 | E_err:   0.006588
[2025-10-02 21:27:01] [Iter 1728/2250] R4[1204/2400] | LR: 0.017435 | E: -27.127473 | E_var:     0.1441 | E_err:   0.005931
[2025-10-02 21:27:04] [Iter 1729/2250] R4[1206/2400] | LR: 0.017402 | E: -27.129886 | E_var:     0.1684 | E_err:   0.006412
[2025-10-02 21:27:08] [Iter 1730/2250] R4[1208/2400] | LR: 0.017369 | E: -27.133416 | E_var:     0.1673 | E_err:   0.006391
[2025-10-02 21:27:12] [Iter 1731/2250] R4[1210/2400] | LR: 0.017336 | E: -27.128026 | E_var:     0.1463 | E_err:   0.005976
[2025-10-02 21:27:15] [Iter 1732/2250] R4[1212/2400] | LR: 0.017304 | E: -27.136989 | E_var:     0.1483 | E_err:   0.006018
[2025-10-02 21:27:19] [Iter 1733/2250] R4[1214/2400] | LR: 0.017271 | E: -27.127919 | E_var:     0.1598 | E_err:   0.006247
[2025-10-02 21:27:23] [Iter 1734/2250] R4[1216/2400] | LR: 0.017238 | E: -27.137411 | E_var:     0.1692 | E_err:   0.006428
[2025-10-02 21:27:26] [Iter 1735/2250] R4[1218/2400] | LR: 0.017206 | E: -27.132829 | E_var:     0.1522 | E_err:   0.006096
[2025-10-02 21:27:30] [Iter 1736/2250] R4[1220/2400] | LR: 0.017173 | E: -27.141370 | E_var:     0.1747 | E_err:   0.006532
[2025-10-02 21:27:34] [Iter 1737/2250] R4[1222/2400] | LR: 0.017140 | E: -27.132303 | E_var:     0.1420 | E_err:   0.005887
[2025-10-02 21:27:37] [Iter 1738/2250] R4[1224/2400] | LR: 0.017107 | E: -27.124385 | E_var:     0.1543 | E_err:   0.006138
[2025-10-02 21:27:41] [Iter 1739/2250] R4[1226/2400] | LR: 0.017075 | E: -27.126948 | E_var:     0.1588 | E_err:   0.006227
[2025-10-02 21:27:45] [Iter 1740/2250] R4[1228/2400] | LR: 0.017042 | E: -27.127480 | E_var:     0.2254 | E_err:   0.007418
[2025-10-02 21:27:48] [Iter 1741/2250] R4[1230/2400] | LR: 0.017009 | E: -27.120343 | E_var:     0.1543 | E_err:   0.006139
[2025-10-02 21:27:52] [Iter 1742/2250] R4[1232/2400] | LR: 0.016977 | E: -27.120983 | E_var:     0.1414 | E_err:   0.005875
[2025-10-02 21:27:56] [Iter 1743/2250] R4[1234/2400] | LR: 0.016944 | E: -27.126198 | E_var:     0.1705 | E_err:   0.006453
[2025-10-02 21:27:59] [Iter 1744/2250] R4[1236/2400] | LR: 0.016911 | E: -27.124577 | E_var:     0.1907 | E_err:   0.006824
[2025-10-02 21:28:03] [Iter 1745/2250] R4[1238/2400] | LR: 0.016878 | E: -27.132231 | E_var:     0.1479 | E_err:   0.006009
[2025-10-02 21:28:06] [Iter 1746/2250] R4[1240/2400] | LR: 0.016846 | E: -27.131458 | E_var:     0.1425 | E_err:   0.005898
[2025-10-02 21:28:10] [Iter 1747/2250] R4[1242/2400] | LR: 0.016813 | E: -27.133422 | E_var:     0.1755 | E_err:   0.006546
[2025-10-02 21:28:14] [Iter 1748/2250] R4[1244/2400] | LR: 0.016780 | E: -27.136356 | E_var:     0.1639 | E_err:   0.006326
[2025-10-02 21:28:17] [Iter 1749/2250] R4[1246/2400] | LR: 0.016748 | E: -27.131869 | E_var:     0.1378 | E_err:   0.005801
[2025-10-02 21:28:21] [Iter 1750/2250] R4[1248/2400] | LR: 0.016715 | E: -27.139680 | E_var:     0.2440 | E_err:   0.007719
[2025-10-02 21:28:25] [Iter 1751/2250] R4[1250/2400] | LR: 0.016682 | E: -27.137128 | E_var:     0.1461 | E_err:   0.005973
[2025-10-02 21:28:28] [Iter 1752/2250] R4[1252/2400] | LR: 0.016650 | E: -27.127923 | E_var:     0.1578 | E_err:   0.006207
[2025-10-02 21:28:32] [Iter 1753/2250] R4[1254/2400] | LR: 0.016617 | E: -27.127227 | E_var:     0.1640 | E_err:   0.006327
[2025-10-02 21:28:36] [Iter 1754/2250] R4[1256/2400] | LR: 0.016585 | E: -27.135654 | E_var:     0.1630 | E_err:   0.006308
[2025-10-02 21:28:39] [Iter 1755/2250] R4[1258/2400] | LR: 0.016552 | E: -27.139109 | E_var:     0.2302 | E_err:   0.007496
[2025-10-02 21:28:43] [Iter 1756/2250] R4[1260/2400] | LR: 0.016519 | E: -27.135041 | E_var:     0.2227 | E_err:   0.007374
[2025-10-02 21:28:47] [Iter 1757/2250] R4[1262/2400] | LR: 0.016487 | E: -27.122217 | E_var:     0.1917 | E_err:   0.006842
[2025-10-02 21:28:50] [Iter 1758/2250] R4[1264/2400] | LR: 0.016454 | E: -27.131699 | E_var:     0.1547 | E_err:   0.006145
[2025-10-02 21:28:54] [Iter 1759/2250] R4[1266/2400] | LR: 0.016421 | E: -27.128696 | E_var:     0.2071 | E_err:   0.007111
[2025-10-02 21:28:58] [Iter 1760/2250] R4[1268/2400] | LR: 0.016389 | E: -27.137805 | E_var:     0.1785 | E_err:   0.006601
[2025-10-02 21:29:01] [Iter 1761/2250] R4[1270/2400] | LR: 0.016356 | E: -27.128281 | E_var:     0.1545 | E_err:   0.006141
[2025-10-02 21:29:05] [Iter 1762/2250] R4[1272/2400] | LR: 0.016324 | E: -27.139859 | E_var:     0.1487 | E_err:   0.006025
[2025-10-02 21:29:09] [Iter 1763/2250] R4[1274/2400] | LR: 0.016291 | E: -27.130362 | E_var:     0.2905 | E_err:   0.008422
[2025-10-02 21:29:12] [Iter 1764/2250] R4[1276/2400] | LR: 0.016259 | E: -27.144446 | E_var:     0.2243 | E_err:   0.007399
[2025-10-02 21:29:16] [Iter 1765/2250] R4[1278/2400] | LR: 0.016226 | E: -27.123174 | E_var:     0.1534 | E_err:   0.006120
[2025-10-02 21:29:20] [Iter 1766/2250] R4[1280/2400] | LR: 0.016193 | E: -27.133239 | E_var:     0.3007 | E_err:   0.008568
[2025-10-02 21:29:23] [Iter 1767/2250] R4[1282/2400] | LR: 0.016161 | E: -27.127874 | E_var:     0.2053 | E_err:   0.007080
[2025-10-02 21:29:27] [Iter 1768/2250] R4[1284/2400] | LR: 0.016128 | E: -27.137092 | E_var:     0.1634 | E_err:   0.006316
[2025-10-02 21:29:30] [Iter 1769/2250] R4[1286/2400] | LR: 0.016096 | E: -27.149273 | E_var:     0.1691 | E_err:   0.006425
[2025-10-02 21:29:34] [Iter 1770/2250] R4[1288/2400] | LR: 0.016063 | E: -27.124122 | E_var:     0.1604 | E_err:   0.006258
[2025-10-02 21:29:38] [Iter 1771/2250] R4[1290/2400] | LR: 0.016031 | E: -27.131879 | E_var:     0.1551 | E_err:   0.006154
[2025-10-02 21:29:41] [Iter 1772/2250] R4[1292/2400] | LR: 0.015998 | E: -27.132582 | E_var:     0.1591 | E_err:   0.006233
[2025-10-02 21:29:45] [Iter 1773/2250] R4[1294/2400] | LR: 0.015966 | E: -27.127181 | E_var:     0.1823 | E_err:   0.006671
[2025-10-02 21:29:49] [Iter 1774/2250] R4[1296/2400] | LR: 0.015933 | E: -27.138808 | E_var:     0.1739 | E_err:   0.006515
[2025-10-02 21:29:52] [Iter 1775/2250] R4[1298/2400] | LR: 0.015901 | E: -27.134343 | E_var:     0.1546 | E_err:   0.006143
[2025-10-02 21:29:56] [Iter 1776/2250] R4[1300/2400] | LR: 0.015868 | E: -27.125727 | E_var:     0.1622 | E_err:   0.006293
[2025-10-02 21:30:00] [Iter 1777/2250] R4[1302/2400] | LR: 0.015836 | E: -27.122739 | E_var:     0.1637 | E_err:   0.006321
[2025-10-02 21:30:03] [Iter 1778/2250] R4[1304/2400] | LR: 0.015804 | E: -27.125885 | E_var:     0.1989 | E_err:   0.006968
[2025-10-02 21:30:07] [Iter 1779/2250] R4[1306/2400] | LR: 0.015771 | E: -27.129174 | E_var:     0.1882 | E_err:   0.006779
[2025-10-02 21:30:11] [Iter 1780/2250] R4[1308/2400] | LR: 0.015739 | E: -27.137803 | E_var:     0.1868 | E_err:   0.006754
[2025-10-02 21:30:14] [Iter 1781/2250] R4[1310/2400] | LR: 0.015706 | E: -27.120970 | E_var:     0.1472 | E_err:   0.005995
[2025-10-02 21:30:18] [Iter 1782/2250] R4[1312/2400] | LR: 0.015674 | E: -27.130353 | E_var:     0.1414 | E_err:   0.005875
[2025-10-02 21:30:22] [Iter 1783/2250] R4[1314/2400] | LR: 0.015642 | E: -27.128079 | E_var:     0.1557 | E_err:   0.006165
[2025-10-02 21:30:25] [Iter 1784/2250] R4[1316/2400] | LR: 0.015609 | E: -27.146483 | E_var:     0.1962 | E_err:   0.006922
[2025-10-02 21:30:29] [Iter 1785/2250] R4[1318/2400] | LR: 0.015577 | E: -27.145312 | E_var:     0.1542 | E_err:   0.006135
[2025-10-02 21:30:33] [Iter 1786/2250] R4[1320/2400] | LR: 0.015545 | E: -27.134405 | E_var:     0.2031 | E_err:   0.007042
[2025-10-02 21:30:36] [Iter 1787/2250] R4[1322/2400] | LR: 0.015512 | E: -27.126166 | E_var:     0.1557 | E_err:   0.006166
[2025-10-02 21:30:40] [Iter 1788/2250] R4[1324/2400] | LR: 0.015480 | E: -27.126393 | E_var:     0.1536 | E_err:   0.006123
[2025-10-02 21:30:44] [Iter 1789/2250] R4[1326/2400] | LR: 0.015448 | E: -27.136024 | E_var:     0.1704 | E_err:   0.006450
[2025-10-02 21:30:47] [Iter 1790/2250] R4[1328/2400] | LR: 0.015415 | E: -27.120402 | E_var:     0.1662 | E_err:   0.006370
[2025-10-02 21:30:51] [Iter 1791/2250] R4[1330/2400] | LR: 0.015383 | E: -27.134262 | E_var:     0.1877 | E_err:   0.006769
[2025-10-02 21:30:54] [Iter 1792/2250] R4[1332/2400] | LR: 0.015351 | E: -27.144778 | E_var:     0.1577 | E_err:   0.006204
[2025-10-02 21:30:58] [Iter 1793/2250] R4[1334/2400] | LR: 0.015319 | E: -27.121125 | E_var:     0.2022 | E_err:   0.007026
[2025-10-02 21:31:02] [Iter 1794/2250] R4[1336/2400] | LR: 0.015286 | E: -27.133144 | E_var:     0.1250 | E_err:   0.005524
[2025-10-02 21:31:05] [Iter 1795/2250] R4[1338/2400] | LR: 0.015254 | E: -27.134910 | E_var:     0.1600 | E_err:   0.006250
[2025-10-02 21:31:09] [Iter 1796/2250] R4[1340/2400] | LR: 0.015222 | E: -27.127265 | E_var:     0.1263 | E_err:   0.005552
[2025-10-02 21:31:13] [Iter 1797/2250] R4[1342/2400] | LR: 0.015190 | E: -27.135898 | E_var:     0.1197 | E_err:   0.005405
[2025-10-02 21:31:16] [Iter 1798/2250] R4[1344/2400] | LR: 0.015158 | E: -27.138431 | E_var:     0.1630 | E_err:   0.006309
[2025-10-02 21:31:20] [Iter 1799/2250] R4[1346/2400] | LR: 0.015126 | E: -27.136862 | E_var:     0.1679 | E_err:   0.006402
[2025-10-02 21:31:24] [Iter 1800/2250] R4[1348/2400] | LR: 0.015093 | E: -27.137400 | E_var:     0.1812 | E_err:   0.006651
[2025-10-02 21:31:24] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-02 21:31:27] [Iter 1801/2250] R4[1350/2400] | LR: 0.015061 | E: -27.135370 | E_var:     0.1564 | E_err:   0.006179
[2025-10-02 21:31:31] [Iter 1802/2250] R4[1352/2400] | LR: 0.015029 | E: -27.130036 | E_var:     0.1590 | E_err:   0.006230
[2025-10-02 21:31:35] [Iter 1803/2250] R4[1354/2400] | LR: 0.014997 | E: -27.134069 | E_var:     0.2211 | E_err:   0.007347
[2025-10-02 21:31:38] [Iter 1804/2250] R4[1356/2400] | LR: 0.014965 | E: -27.129819 | E_var:     0.1614 | E_err:   0.006277
[2025-10-02 21:31:42] [Iter 1805/2250] R4[1358/2400] | LR: 0.014933 | E: -27.132080 | E_var:     0.1334 | E_err:   0.005708
[2025-10-02 21:31:46] [Iter 1806/2250] R4[1360/2400] | LR: 0.014901 | E: -27.123120 | E_var:     0.1543 | E_err:   0.006139
[2025-10-02 21:31:49] [Iter 1807/2250] R4[1362/2400] | LR: 0.014869 | E: -27.124731 | E_var:     0.1555 | E_err:   0.006161
[2025-10-02 21:31:53] [Iter 1808/2250] R4[1364/2400] | LR: 0.014837 | E: -27.135256 | E_var:     0.1484 | E_err:   0.006020
[2025-10-02 21:31:57] [Iter 1809/2250] R4[1366/2400] | LR: 0.014805 | E: -27.132998 | E_var:     0.1509 | E_err:   0.006071
[2025-10-02 21:32:00] [Iter 1810/2250] R4[1368/2400] | LR: 0.014773 | E: -27.133218 | E_var:     0.3458 | E_err:   0.009188
[2025-10-02 21:32:04] [Iter 1811/2250] R4[1370/2400] | LR: 0.014741 | E: -27.130558 | E_var:     0.1228 | E_err:   0.005476
[2025-10-02 21:32:08] [Iter 1812/2250] R4[1372/2400] | LR: 0.014709 | E: -27.135025 | E_var:     0.1537 | E_err:   0.006127
[2025-10-02 21:32:11] [Iter 1813/2250] R4[1374/2400] | LR: 0.014677 | E: -27.130365 | E_var:     0.1321 | E_err:   0.005678
[2025-10-02 21:32:15] [Iter 1814/2250] R4[1376/2400] | LR: 0.014646 | E: -27.129388 | E_var:     0.1704 | E_err:   0.006451
[2025-10-02 21:32:19] [Iter 1815/2250] R4[1378/2400] | LR: 0.014614 | E: -27.142935 | E_var:     0.2445 | E_err:   0.007726
[2025-10-02 21:32:22] [Iter 1816/2250] R4[1380/2400] | LR: 0.014582 | E: -27.124457 | E_var:     0.2107 | E_err:   0.007173
[2025-10-02 21:32:26] [Iter 1817/2250] R4[1382/2400] | LR: 0.014550 | E: -27.130473 | E_var:     0.1462 | E_err:   0.005974
[2025-10-02 21:32:30] [Iter 1818/2250] R4[1384/2400] | LR: 0.014518 | E: -27.128995 | E_var:     0.2876 | E_err:   0.008379
[2025-10-02 21:32:33] [Iter 1819/2250] R4[1386/2400] | LR: 0.014487 | E: -27.133702 | E_var:     0.1702 | E_err:   0.006447
[2025-10-02 21:32:37] [Iter 1820/2250] R4[1388/2400] | LR: 0.014455 | E: -27.140417 | E_var:     0.1493 | E_err:   0.006036
[2025-10-02 21:32:40] [Iter 1821/2250] R4[1390/2400] | LR: 0.014423 | E: -27.127376 | E_var:     0.2037 | E_err:   0.007052
[2025-10-02 21:32:44] [Iter 1822/2250] R4[1392/2400] | LR: 0.014391 | E: -27.130852 | E_var:     0.1833 | E_err:   0.006690
[2025-10-02 21:32:48] [Iter 1823/2250] R4[1394/2400] | LR: 0.014360 | E: -27.116797 | E_var:     0.1793 | E_err:   0.006616
[2025-10-02 21:32:51] [Iter 1824/2250] R4[1396/2400] | LR: 0.014328 | E: -27.150584 | E_var:     0.1575 | E_err:   0.006201
[2025-10-02 21:32:55] [Iter 1825/2250] R4[1398/2400] | LR: 0.014296 | E: -27.139549 | E_var:     0.1656 | E_err:   0.006359
[2025-10-02 21:32:59] [Iter 1826/2250] R4[1400/2400] | LR: 0.014265 | E: -27.130155 | E_var:     0.1746 | E_err:   0.006529
[2025-10-02 21:33:02] [Iter 1827/2250] R4[1402/2400] | LR: 0.014233 | E: -27.123412 | E_var:     0.1745 | E_err:   0.006527
[2025-10-02 21:33:06] [Iter 1828/2250] R4[1404/2400] | LR: 0.014202 | E: -27.124249 | E_var:     0.1707 | E_err:   0.006456
[2025-10-02 21:33:10] [Iter 1829/2250] R4[1406/2400] | LR: 0.014170 | E: -27.122581 | E_var:     0.1530 | E_err:   0.006111
[2025-10-02 21:33:13] [Iter 1830/2250] R4[1408/2400] | LR: 0.014139 | E: -27.133689 | E_var:     0.1407 | E_err:   0.005861
[2025-10-02 21:33:17] [Iter 1831/2250] R4[1410/2400] | LR: 0.014107 | E: -27.136921 | E_var:     0.1697 | E_err:   0.006436
[2025-10-02 21:33:21] [Iter 1832/2250] R4[1412/2400] | LR: 0.014076 | E: -27.137499 | E_var:     0.1563 | E_err:   0.006177
[2025-10-02 21:33:24] [Iter 1833/2250] R4[1414/2400] | LR: 0.014044 | E: -27.125636 | E_var:     0.2318 | E_err:   0.007523
[2025-10-02 21:33:28] [Iter 1834/2250] R4[1416/2400] | LR: 0.014013 | E: -27.133810 | E_var:     0.2519 | E_err:   0.007843
[2025-10-02 21:33:32] [Iter 1835/2250] R4[1418/2400] | LR: 0.013981 | E: -27.138714 | E_var:     0.1471 | E_err:   0.005993
[2025-10-02 21:33:35] [Iter 1836/2250] R4[1420/2400] | LR: 0.013950 | E: -27.132893 | E_var:     0.1649 | E_err:   0.006345
[2025-10-02 21:33:39] [Iter 1837/2250] R4[1422/2400] | LR: 0.013918 | E: -27.125071 | E_var:     0.1749 | E_err:   0.006534
[2025-10-02 21:33:43] [Iter 1838/2250] R4[1424/2400] | LR: 0.013887 | E: -27.137453 | E_var:     0.1596 | E_err:   0.006243
[2025-10-02 21:33:46] [Iter 1839/2250] R4[1426/2400] | LR: 0.013856 | E: -27.121490 | E_var:     0.1830 | E_err:   0.006684
[2025-10-02 21:33:50] [Iter 1840/2250] R4[1428/2400] | LR: 0.013824 | E: -27.138414 | E_var:     0.1459 | E_err:   0.005967
[2025-10-02 21:33:54] [Iter 1841/2250] R4[1430/2400] | LR: 0.013793 | E: -27.138363 | E_var:     0.1407 | E_err:   0.005861
[2025-10-02 21:33:57] [Iter 1842/2250] R4[1432/2400] | LR: 0.013762 | E: -27.145613 | E_var:     0.2247 | E_err:   0.007407
[2025-10-02 21:34:01] [Iter 1843/2250] R4[1434/2400] | LR: 0.013731 | E: -27.134272 | E_var:     0.1452 | E_err:   0.005954
[2025-10-02 21:34:04] [Iter 1844/2250] R4[1436/2400] | LR: 0.013700 | E: -27.132891 | E_var:     0.1395 | E_err:   0.005836
[2025-10-02 21:34:08] [Iter 1845/2250] R4[1438/2400] | LR: 0.013668 | E: -27.134797 | E_var:     0.1412 | E_err:   0.005871
[2025-10-02 21:34:12] [Iter 1846/2250] R4[1440/2400] | LR: 0.013637 | E: -27.128026 | E_var:     0.1520 | E_err:   0.006092
[2025-10-02 21:34:15] [Iter 1847/2250] R4[1442/2400] | LR: 0.013606 | E: -27.137809 | E_var:     0.1484 | E_err:   0.006020
[2025-10-02 21:34:19] [Iter 1848/2250] R4[1444/2400] | LR: 0.013575 | E: -27.129061 | E_var:     0.1933 | E_err:   0.006870
[2025-10-02 21:34:23] [Iter 1849/2250] R4[1446/2400] | LR: 0.013544 | E: -27.130240 | E_var:     0.1381 | E_err:   0.005806
[2025-10-02 21:34:26] [Iter 1850/2250] R4[1448/2400] | LR: 0.013513 | E: -27.127155 | E_var:     0.1499 | E_err:   0.006050
[2025-10-02 21:34:30] [Iter 1851/2250] R4[1450/2400] | LR: 0.013482 | E: -27.140889 | E_var:     0.1771 | E_err:   0.006575
[2025-10-02 21:34:34] [Iter 1852/2250] R4[1452/2400] | LR: 0.013451 | E: -27.132149 | E_var:     0.2848 | E_err:   0.008339
[2025-10-02 21:34:37] [Iter 1853/2250] R4[1454/2400] | LR: 0.013420 | E: -27.125909 | E_var:     0.2025 | E_err:   0.007031
[2025-10-02 21:34:41] [Iter 1854/2250] R4[1456/2400] | LR: 0.013389 | E: -27.137960 | E_var:     0.1317 | E_err:   0.005671
[2025-10-02 21:34:45] [Iter 1855/2250] R4[1458/2400] | LR: 0.013358 | E: -27.130461 | E_var:     0.1701 | E_err:   0.006445
[2025-10-02 21:34:48] [Iter 1856/2250] R4[1460/2400] | LR: 0.013327 | E: -27.136584 | E_var:     0.1962 | E_err:   0.006922
[2025-10-02 21:34:52] [Iter 1857/2250] R4[1462/2400] | LR: 0.013297 | E: -27.143357 | E_var:     0.1676 | E_err:   0.006397
[2025-10-02 21:34:56] [Iter 1858/2250] R4[1464/2400] | LR: 0.013266 | E: -27.141841 | E_var:     0.1948 | E_err:   0.006896
[2025-10-02 21:34:59] [Iter 1859/2250] R4[1466/2400] | LR: 0.013235 | E: -27.135965 | E_var:     0.1601 | E_err:   0.006252
[2025-10-02 21:35:03] [Iter 1860/2250] R4[1468/2400] | LR: 0.013204 | E: -27.126956 | E_var:     0.1685 | E_err:   0.006414
[2025-10-02 21:35:07] [Iter 1861/2250] R4[1470/2400] | LR: 0.013174 | E: -27.130490 | E_var:     0.2219 | E_err:   0.007361
[2025-10-02 21:35:10] [Iter 1862/2250] R4[1472/2400] | LR: 0.013143 | E: -27.124623 | E_var:     0.1753 | E_err:   0.006542
[2025-10-02 21:35:14] [Iter 1863/2250] R4[1474/2400] | LR: 0.013112 | E: -27.132213 | E_var:     0.1616 | E_err:   0.006282
[2025-10-02 21:35:18] [Iter 1864/2250] R4[1476/2400] | LR: 0.013082 | E: -27.138532 | E_var:     0.1306 | E_err:   0.005646
[2025-10-02 21:35:21] [Iter 1865/2250] R4[1478/2400] | LR: 0.013051 | E: -27.138391 | E_var:     0.2230 | E_err:   0.007379
[2025-10-02 21:35:25] [Iter 1866/2250] R4[1480/2400] | LR: 0.013020 | E: -27.143797 | E_var:     0.1585 | E_err:   0.006220
[2025-10-02 21:35:28] [Iter 1867/2250] R4[1482/2400] | LR: 0.012990 | E: -27.130161 | E_var:     0.2103 | E_err:   0.007166
[2025-10-02 21:35:32] [Iter 1868/2250] R4[1484/2400] | LR: 0.012959 | E: -27.128704 | E_var:     0.2016 | E_err:   0.007015
[2025-10-02 21:35:36] [Iter 1869/2250] R4[1486/2400] | LR: 0.012929 | E: -27.136945 | E_var:     0.1660 | E_err:   0.006366
[2025-10-02 21:35:39] [Iter 1870/2250] R4[1488/2400] | LR: 0.012898 | E: -27.124041 | E_var:     0.1402 | E_err:   0.005850
[2025-10-02 21:35:43] [Iter 1871/2250] R4[1490/2400] | LR: 0.012868 | E: -27.145235 | E_var:     0.1532 | E_err:   0.006115
[2025-10-02 21:35:47] [Iter 1872/2250] R4[1492/2400] | LR: 0.012838 | E: -27.132818 | E_var:     0.1268 | E_err:   0.005564
[2025-10-02 21:35:50] [Iter 1873/2250] R4[1494/2400] | LR: 0.012807 | E: -27.129589 | E_var:     0.1420 | E_err:   0.005888
[2025-10-02 21:35:54] [Iter 1874/2250] R4[1496/2400] | LR: 0.012777 | E: -27.137664 | E_var:     0.1527 | E_err:   0.006106
[2025-10-02 21:35:58] [Iter 1875/2250] R4[1498/2400] | LR: 0.012747 | E: -27.135567 | E_var:     0.1656 | E_err:   0.006359
[2025-10-02 21:36:01] [Iter 1876/2250] R4[1500/2400] | LR: 0.012716 | E: -27.126985 | E_var:     0.1879 | E_err:   0.006773
[2025-10-02 21:36:05] [Iter 1877/2250] R4[1502/2400] | LR: 0.012686 | E: -27.138801 | E_var:     0.1767 | E_err:   0.006568
[2025-10-02 21:36:09] [Iter 1878/2250] R4[1504/2400] | LR: 0.012656 | E: -27.126790 | E_var:     0.1525 | E_err:   0.006103
[2025-10-02 21:36:12] [Iter 1879/2250] R4[1506/2400] | LR: 0.012626 | E: -27.128267 | E_var:     0.1445 | E_err:   0.005940
[2025-10-02 21:36:16] [Iter 1880/2250] R4[1508/2400] | LR: 0.012596 | E: -27.118632 | E_var:     0.1605 | E_err:   0.006260
[2025-10-02 21:36:20] [Iter 1881/2250] R4[1510/2400] | LR: 0.012566 | E: -27.127158 | E_var:     0.1306 | E_err:   0.005647
[2025-10-02 21:36:23] [Iter 1882/2250] R4[1512/2400] | LR: 0.012536 | E: -27.132304 | E_var:     0.2042 | E_err:   0.007060
[2025-10-02 21:36:27] [Iter 1883/2250] R4[1514/2400] | LR: 0.012506 | E: -27.134526 | E_var:     0.1580 | E_err:   0.006210
[2025-10-02 21:36:31] [Iter 1884/2250] R4[1516/2400] | LR: 0.012476 | E: -27.130487 | E_var:     0.2025 | E_err:   0.007031
[2025-10-02 21:36:34] [Iter 1885/2250] R4[1518/2400] | LR: 0.012446 | E: -27.136508 | E_var:     0.1334 | E_err:   0.005707
[2025-10-02 21:36:38] [Iter 1886/2250] R4[1520/2400] | LR: 0.012416 | E: -27.121526 | E_var:     0.1371 | E_err:   0.005786
[2025-10-02 21:36:42] [Iter 1887/2250] R4[1522/2400] | LR: 0.012386 | E: -27.121602 | E_var:     0.1752 | E_err:   0.006540
[2025-10-02 21:36:45] [Iter 1888/2250] R4[1524/2400] | LR: 0.012356 | E: -27.135378 | E_var:     0.1254 | E_err:   0.005533
[2025-10-02 21:36:49] [Iter 1889/2250] R4[1526/2400] | LR: 0.012326 | E: -27.133247 | E_var:     0.1673 | E_err:   0.006392
[2025-10-02 21:36:53] [Iter 1890/2250] R4[1528/2400] | LR: 0.012296 | E: -27.131445 | E_var:     0.1956 | E_err:   0.006911
[2025-10-02 21:36:56] [Iter 1891/2250] R4[1530/2400] | LR: 0.012267 | E: -27.132612 | E_var:     0.1746 | E_err:   0.006530
[2025-10-02 21:37:00] [Iter 1892/2250] R4[1532/2400] | LR: 0.012237 | E: -27.125726 | E_var:     0.1270 | E_err:   0.005569
[2025-10-02 21:37:03] [Iter 1893/2250] R4[1534/2400] | LR: 0.012207 | E: -27.127342 | E_var:     0.1254 | E_err:   0.005533
[2025-10-02 21:37:07] [Iter 1894/2250] R4[1536/2400] | LR: 0.012178 | E: -27.130954 | E_var:     0.1326 | E_err:   0.005690
[2025-10-02 21:37:11] [Iter 1895/2250] R4[1538/2400] | LR: 0.012148 | E: -27.132464 | E_var:     0.4011 | E_err:   0.009895
[2025-10-02 21:37:14] [Iter 1896/2250] R4[1540/2400] | LR: 0.012119 | E: -27.134572 | E_var:     0.1860 | E_err:   0.006738
[2025-10-02 21:37:18] [Iter 1897/2250] R4[1542/2400] | LR: 0.012089 | E: -27.136263 | E_var:     0.1540 | E_err:   0.006133
[2025-10-02 21:37:22] [Iter 1898/2250] R4[1544/2400] | LR: 0.012060 | E: -27.134816 | E_var:     0.1657 | E_err:   0.006361
[2025-10-02 21:37:25] [Iter 1899/2250] R4[1546/2400] | LR: 0.012030 | E: -27.134778 | E_var:     0.1303 | E_err:   0.005641
[2025-10-02 21:37:29] [Iter 1900/2250] R4[1548/2400] | LR: 0.012001 | E: -27.129265 | E_var:     0.1678 | E_err:   0.006401
[2025-10-02 21:37:33] [Iter 1901/2250] R4[1550/2400] | LR: 0.011971 | E: -27.130446 | E_var:     0.1180 | E_err:   0.005368
[2025-10-02 21:37:36] [Iter 1902/2250] R4[1552/2400] | LR: 0.011942 | E: -27.142711 | E_var:     0.2495 | E_err:   0.007805
[2025-10-02 21:37:40] [Iter 1903/2250] R4[1554/2400] | LR: 0.011913 | E: -27.134905 | E_var:     0.2004 | E_err:   0.006995
[2025-10-02 21:37:44] [Iter 1904/2250] R4[1556/2400] | LR: 0.011884 | E: -27.125001 | E_var:     0.1327 | E_err:   0.005692
[2025-10-02 21:37:47] [Iter 1905/2250] R4[1558/2400] | LR: 0.011854 | E: -27.127764 | E_var:     0.1443 | E_err:   0.005935
[2025-10-02 21:37:51] [Iter 1906/2250] R4[1560/2400] | LR: 0.011825 | E: -27.121043 | E_var:     0.1966 | E_err:   0.006928
[2025-10-02 21:37:55] [Iter 1907/2250] R4[1562/2400] | LR: 0.011796 | E: -27.131095 | E_var:     0.1503 | E_err:   0.006057
[2025-10-02 21:37:58] [Iter 1908/2250] R4[1564/2400] | LR: 0.011767 | E: -27.146040 | E_var:     0.1920 | E_err:   0.006847
[2025-10-02 21:38:02] [Iter 1909/2250] R4[1566/2400] | LR: 0.011738 | E: -27.133428 | E_var:     0.1658 | E_err:   0.006362
[2025-10-02 21:38:06] [Iter 1910/2250] R4[1568/2400] | LR: 0.011709 | E: -27.136605 | E_var:     0.1523 | E_err:   0.006098
[2025-10-02 21:38:09] [Iter 1911/2250] R4[1570/2400] | LR: 0.011680 | E: -27.131894 | E_var:     0.1848 | E_err:   0.006717
[2025-10-02 21:38:13] [Iter 1912/2250] R4[1572/2400] | LR: 0.011651 | E: -27.131895 | E_var:     0.1729 | E_err:   0.006498
[2025-10-02 21:38:17] [Iter 1913/2250] R4[1574/2400] | LR: 0.011622 | E: -27.142010 | E_var:     0.1283 | E_err:   0.005597
[2025-10-02 21:38:20] [Iter 1914/2250] R4[1576/2400] | LR: 0.011593 | E: -27.118168 | E_var:     0.1917 | E_err:   0.006842
[2025-10-02 21:38:24] [Iter 1915/2250] R4[1578/2400] | LR: 0.011564 | E: -27.138797 | E_var:     0.2237 | E_err:   0.007391
[2025-10-02 21:38:28] [Iter 1916/2250] R4[1580/2400] | LR: 0.011536 | E: -27.139887 | E_var:     0.1387 | E_err:   0.005819
[2025-10-02 21:38:31] [Iter 1917/2250] R4[1582/2400] | LR: 0.011507 | E: -27.137782 | E_var:     0.1647 | E_err:   0.006342
[2025-10-02 21:38:35] [Iter 1918/2250] R4[1584/2400] | LR: 0.011478 | E: -27.124993 | E_var:     0.2356 | E_err:   0.007584
[2025-10-02 21:38:39] [Iter 1919/2250] R4[1586/2400] | LR: 0.011449 | E: -27.132440 | E_var:     0.1953 | E_err:   0.006904
[2025-10-02 21:38:42] [Iter 1920/2250] R4[1588/2400] | LR: 0.011421 | E: -27.138358 | E_var:     0.1704 | E_err:   0.006450
[2025-10-02 21:38:46] [Iter 1921/2250] R4[1590/2400] | LR: 0.011392 | E: -27.133454 | E_var:     0.1886 | E_err:   0.006786
[2025-10-02 21:38:50] [Iter 1922/2250] R4[1592/2400] | LR: 0.011364 | E: -27.133220 | E_var:     0.1648 | E_err:   0.006343
[2025-10-02 21:38:53] [Iter 1923/2250] R4[1594/2400] | LR: 0.011335 | E: -27.146945 | E_var:     0.2472 | E_err:   0.007768
[2025-10-02 21:38:57] [Iter 1924/2250] R4[1596/2400] | LR: 0.011307 | E: -27.135972 | E_var:     0.1632 | E_err:   0.006313
[2025-10-02 21:39:00] [Iter 1925/2250] R4[1598/2400] | LR: 0.011278 | E: -27.124766 | E_var:     0.1817 | E_err:   0.006661
[2025-10-02 21:39:04] [Iter 1926/2250] R4[1600/2400] | LR: 0.011250 | E: -27.136611 | E_var:     0.4008 | E_err:   0.009891
[2025-10-02 21:39:08] [Iter 1927/2250] R4[1602/2400] | LR: 0.011222 | E: -27.136746 | E_var:     0.1285 | E_err:   0.005601
[2025-10-02 21:39:11] [Iter 1928/2250] R4[1604/2400] | LR: 0.011193 | E: -27.133147 | E_var:     0.1970 | E_err:   0.006935
[2025-10-02 21:39:15] [Iter 1929/2250] R4[1606/2400] | LR: 0.011165 | E: -27.132530 | E_var:     0.1667 | E_err:   0.006380
[2025-10-02 21:39:19] [Iter 1930/2250] R4[1608/2400] | LR: 0.011137 | E: -27.146254 | E_var:     0.2114 | E_err:   0.007184
[2025-10-02 21:39:22] [Iter 1931/2250] R4[1610/2400] | LR: 0.011109 | E: -27.134354 | E_var:     0.1551 | E_err:   0.006153
[2025-10-02 21:39:26] [Iter 1932/2250] R4[1612/2400] | LR: 0.011081 | E: -27.135309 | E_var:     0.1504 | E_err:   0.006059
[2025-10-02 21:39:30] [Iter 1933/2250] R4[1614/2400] | LR: 0.011053 | E: -27.143063 | E_var:     0.2049 | E_err:   0.007072
[2025-10-02 21:39:33] [Iter 1934/2250] R4[1616/2400] | LR: 0.011025 | E: -27.133512 | E_var:     0.1567 | E_err:   0.006185
[2025-10-02 21:39:37] [Iter 1935/2250] R4[1618/2400] | LR: 0.010997 | E: -27.131972 | E_var:     0.1631 | E_err:   0.006310
[2025-10-02 21:39:41] [Iter 1936/2250] R4[1620/2400] | LR: 0.010969 | E: -27.136864 | E_var:     0.1559 | E_err:   0.006169
[2025-10-02 21:39:44] [Iter 1937/2250] R4[1622/2400] | LR: 0.010941 | E: -27.129470 | E_var:     0.1569 | E_err:   0.006190
[2025-10-02 21:39:48] [Iter 1938/2250] R4[1624/2400] | LR: 0.010913 | E: -27.128823 | E_var:     0.1299 | E_err:   0.005631
[2025-10-02 21:39:52] [Iter 1939/2250] R4[1626/2400] | LR: 0.010885 | E: -27.136961 | E_var:     0.1439 | E_err:   0.005928
[2025-10-02 21:39:55] [Iter 1940/2250] R4[1628/2400] | LR: 0.010858 | E: -27.139922 | E_var:     0.1918 | E_err:   0.006843
[2025-10-02 21:39:59] [Iter 1941/2250] R4[1630/2400] | LR: 0.010830 | E: -27.133667 | E_var:     0.1258 | E_err:   0.005543
[2025-10-02 21:40:03] [Iter 1942/2250] R4[1632/2400] | LR: 0.010802 | E: -27.133474 | E_var:     0.1604 | E_err:   0.006258
[2025-10-02 21:40:06] [Iter 1943/2250] R4[1634/2400] | LR: 0.010775 | E: -27.133834 | E_var:     0.1593 | E_err:   0.006235
[2025-10-02 21:40:10] [Iter 1944/2250] R4[1636/2400] | LR: 0.010747 | E: -27.125768 | E_var:     0.2046 | E_err:   0.007068
[2025-10-02 21:40:14] [Iter 1945/2250] R4[1638/2400] | LR: 0.010719 | E: -27.137650 | E_var:     0.1584 | E_err:   0.006218
[2025-10-02 21:40:17] [Iter 1946/2250] R4[1640/2400] | LR: 0.010692 | E: -27.138605 | E_var:     0.1827 | E_err:   0.006679
[2025-10-02 21:40:21] [Iter 1947/2250] R4[1642/2400] | LR: 0.010665 | E: -27.126777 | E_var:     0.1850 | E_err:   0.006721
[2025-10-02 21:40:24] [Iter 1948/2250] R4[1644/2400] | LR: 0.010637 | E: -27.137561 | E_var:     0.1588 | E_err:   0.006227
[2025-10-02 21:40:28] [Iter 1949/2250] R4[1646/2400] | LR: 0.010610 | E: -27.128145 | E_var:     0.1416 | E_err:   0.005880
[2025-10-02 21:40:32] [Iter 1950/2250] R4[1648/2400] | LR: 0.010583 | E: -27.139051 | E_var:     0.1516 | E_err:   0.006084
[2025-10-02 21:40:35] [Iter 1951/2250] R4[1650/2400] | LR: 0.010555 | E: -27.138752 | E_var:     0.1848 | E_err:   0.006718
[2025-10-02 21:40:39] [Iter 1952/2250] R4[1652/2400] | LR: 0.010528 | E: -27.138459 | E_var:     0.2617 | E_err:   0.007994
[2025-10-02 21:40:43] [Iter 1953/2250] R4[1654/2400] | LR: 0.010501 | E: -27.132614 | E_var:     0.1345 | E_err:   0.005731
[2025-10-02 21:40:46] [Iter 1954/2250] R4[1656/2400] | LR: 0.010474 | E: -27.133559 | E_var:     0.1648 | E_err:   0.006344
[2025-10-02 21:40:50] [Iter 1955/2250] R4[1658/2400] | LR: 0.010447 | E: -27.124194 | E_var:     0.1534 | E_err:   0.006119
[2025-10-02 21:40:54] [Iter 1956/2250] R4[1660/2400] | LR: 0.010420 | E: -27.134652 | E_var:     0.1532 | E_err:   0.006117
[2025-10-02 21:40:57] [Iter 1957/2250] R4[1662/2400] | LR: 0.010393 | E: -27.139599 | E_var:     0.1966 | E_err:   0.006928
[2025-10-02 21:41:01] [Iter 1958/2250] R4[1664/2400] | LR: 0.010366 | E: -27.145243 | E_var:     0.1405 | E_err:   0.005856
[2025-10-02 21:41:05] [Iter 1959/2250] R4[1666/2400] | LR: 0.010339 | E: -27.126797 | E_var:     0.1783 | E_err:   0.006597
[2025-10-02 21:41:08] [Iter 1960/2250] R4[1668/2400] | LR: 0.010312 | E: -27.130944 | E_var:     0.1628 | E_err:   0.006304
[2025-10-02 21:41:12] [Iter 1961/2250] R4[1670/2400] | LR: 0.010286 | E: -27.134687 | E_var:     0.1213 | E_err:   0.005442
[2025-10-02 21:41:16] [Iter 1962/2250] R4[1672/2400] | LR: 0.010259 | E: -27.133111 | E_var:     0.1780 | E_err:   0.006592
[2025-10-02 21:41:19] [Iter 1963/2250] R4[1674/2400] | LR: 0.010232 | E: -27.134064 | E_var:     0.1234 | E_err:   0.005489
[2025-10-02 21:41:23] [Iter 1964/2250] R4[1676/2400] | LR: 0.010206 | E: -27.138868 | E_var:     0.2233 | E_err:   0.007383
[2025-10-02 21:41:27] [Iter 1965/2250] R4[1678/2400] | LR: 0.010179 | E: -27.117545 | E_var:     0.1409 | E_err:   0.005865
[2025-10-02 21:41:30] [Iter 1966/2250] R4[1680/2400] | LR: 0.010153 | E: -27.129223 | E_var:     0.1764 | E_err:   0.006563
[2025-10-02 21:41:34] [Iter 1967/2250] R4[1682/2400] | LR: 0.010126 | E: -27.137407 | E_var:     0.1481 | E_err:   0.006014
[2025-10-02 21:41:38] [Iter 1968/2250] R4[1684/2400] | LR: 0.010100 | E: -27.129849 | E_var:     0.1652 | E_err:   0.006351
[2025-10-02 21:41:41] [Iter 1969/2250] R4[1686/2400] | LR: 0.010073 | E: -27.128441 | E_var:     0.1455 | E_err:   0.005960
[2025-10-02 21:41:45] [Iter 1970/2250] R4[1688/2400] | LR: 0.010047 | E: -27.122256 | E_var:     0.5473 | E_err:   0.011560
[2025-10-02 21:41:49] [Iter 1971/2250] R4[1690/2400] | LR: 0.010021 | E: -27.135383 | E_var:     0.1251 | E_err:   0.005525
[2025-10-02 21:41:52] [Iter 1972/2250] R4[1692/2400] | LR: 0.009995 | E: -27.134838 | E_var:     0.1678 | E_err:   0.006401
[2025-10-02 21:41:56] [Iter 1973/2250] R4[1694/2400] | LR: 0.009969 | E: -27.134139 | E_var:     0.1201 | E_err:   0.005416
[2025-10-02 21:41:59] [Iter 1974/2250] R4[1696/2400] | LR: 0.009943 | E: -27.129355 | E_var:     0.1720 | E_err:   0.006480
[2025-10-02 21:42:03] [Iter 1975/2250] R4[1698/2400] | LR: 0.009916 | E: -27.130273 | E_var:     0.1825 | E_err:   0.006676
[2025-10-02 21:42:07] [Iter 1976/2250] R4[1700/2400] | LR: 0.009890 | E: -27.141567 | E_var:     0.1634 | E_err:   0.006316
[2025-10-02 21:42:10] [Iter 1977/2250] R4[1702/2400] | LR: 0.009865 | E: -27.135492 | E_var:     0.1855 | E_err:   0.006729
[2025-10-02 21:42:14] [Iter 1978/2250] R4[1704/2400] | LR: 0.009839 | E: -27.135846 | E_var:     0.1739 | E_err:   0.006515
[2025-10-02 21:42:18] [Iter 1979/2250] R4[1706/2400] | LR: 0.009813 | E: -27.133930 | E_var:     0.1344 | E_err:   0.005728
[2025-10-02 21:42:21] [Iter 1980/2250] R4[1708/2400] | LR: 0.009787 | E: -27.131632 | E_var:     0.1614 | E_err:   0.006276
[2025-10-02 21:42:25] [Iter 1981/2250] R4[1710/2400] | LR: 0.009761 | E: -27.133542 | E_var:     0.1313 | E_err:   0.005662
[2025-10-02 21:42:29] [Iter 1982/2250] R4[1712/2400] | LR: 0.009736 | E: -27.132929 | E_var:     0.1288 | E_err:   0.005607
[2025-10-02 21:42:32] [Iter 1983/2250] R4[1714/2400] | LR: 0.009710 | E: -27.127310 | E_var:     0.1910 | E_err:   0.006829
[2025-10-02 21:42:36] [Iter 1984/2250] R4[1716/2400] | LR: 0.009684 | E: -27.143309 | E_var:     0.2070 | E_err:   0.007109
[2025-10-02 21:42:40] [Iter 1985/2250] R4[1718/2400] | LR: 0.009659 | E: -27.128313 | E_var:     0.1638 | E_err:   0.006323
[2025-10-02 21:42:43] [Iter 1986/2250] R4[1720/2400] | LR: 0.009633 | E: -27.128212 | E_var:     0.1541 | E_err:   0.006133
[2025-10-02 21:42:47] [Iter 1987/2250] R4[1722/2400] | LR: 0.009608 | E: -27.150492 | E_var:     0.2339 | E_err:   0.007556
[2025-10-02 21:42:51] [Iter 1988/2250] R4[1724/2400] | LR: 0.009583 | E: -27.122471 | E_var:     0.1972 | E_err:   0.006939
[2025-10-02 21:42:54] [Iter 1989/2250] R4[1726/2400] | LR: 0.009557 | E: -27.139837 | E_var:     0.1455 | E_err:   0.005959
[2025-10-02 21:42:58] [Iter 1990/2250] R4[1728/2400] | LR: 0.009532 | E: -27.134348 | E_var:     0.1764 | E_err:   0.006563
[2025-10-02 21:43:02] [Iter 1991/2250] R4[1730/2400] | LR: 0.009507 | E: -27.128750 | E_var:     0.1553 | E_err:   0.006158
[2025-10-02 21:43:05] [Iter 1992/2250] R4[1732/2400] | LR: 0.009482 | E: -27.136804 | E_var:     0.1526 | E_err:   0.006104
[2025-10-02 21:43:09] [Iter 1993/2250] R4[1734/2400] | LR: 0.009457 | E: -27.138085 | E_var:     0.1344 | E_err:   0.005729
[2025-10-02 21:43:13] [Iter 1994/2250] R4[1736/2400] | LR: 0.009432 | E: -27.131144 | E_var:     0.1732 | E_err:   0.006503
[2025-10-02 21:43:16] [Iter 1995/2250] R4[1738/2400] | LR: 0.009407 | E: -27.138362 | E_var:     0.1586 | E_err:   0.006222
[2025-10-02 21:43:20] [Iter 1996/2250] R4[1740/2400] | LR: 0.009382 | E: -27.130988 | E_var:     0.1397 | E_err:   0.005839
[2025-10-02 21:43:24] [Iter 1997/2250] R4[1742/2400] | LR: 0.009357 | E: -27.135289 | E_var:     0.1454 | E_err:   0.005958
[2025-10-02 21:43:27] [Iter 1998/2250] R4[1744/2400] | LR: 0.009332 | E: -27.138719 | E_var:     0.1932 | E_err:   0.006868
[2025-10-02 21:43:31] [Iter 1999/2250] R4[1746/2400] | LR: 0.009307 | E: -27.138642 | E_var:     0.1559 | E_err:   0.006169
[2025-10-02 21:43:34] [Iter 2000/2250] R4[1748/2400] | LR: 0.009283 | E: -27.121697 | E_var:     0.1680 | E_err:   0.006404
[2025-10-02 21:43:35] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-10-02 21:43:38] [Iter 2001/2250] R4[1750/2400] | LR: 0.009258 | E: -27.125355 | E_var:     0.1280 | E_err:   0.005590
[2025-10-02 21:43:42] [Iter 2002/2250] R4[1752/2400] | LR: 0.009234 | E: -27.135783 | E_var:     0.1245 | E_err:   0.005512
[2025-10-02 21:43:45] [Iter 2003/2250] R4[1754/2400] | LR: 0.009209 | E: -27.131441 | E_var:     0.1573 | E_err:   0.006196
[2025-10-02 21:43:49] [Iter 2004/2250] R4[1756/2400] | LR: 0.009185 | E: -27.132820 | E_var:     0.1711 | E_err:   0.006464
[2025-10-02 21:43:53] [Iter 2005/2250] R4[1758/2400] | LR: 0.009160 | E: -27.125651 | E_var:     0.1550 | E_err:   0.006151
[2025-10-02 21:43:56] [Iter 2006/2250] R4[1760/2400] | LR: 0.009136 | E: -27.127208 | E_var:     0.1399 | E_err:   0.005844
[2025-10-02 21:44:00] [Iter 2007/2250] R4[1762/2400] | LR: 0.009112 | E: -27.130111 | E_var:     0.1565 | E_err:   0.006181
[2025-10-02 21:44:04] [Iter 2008/2250] R4[1764/2400] | LR: 0.009087 | E: -27.129080 | E_var:     0.1516 | E_err:   0.006085
[2025-10-02 21:44:07] [Iter 2009/2250] R4[1766/2400] | LR: 0.009063 | E: -27.136636 | E_var:     0.2160 | E_err:   0.007262
[2025-10-02 21:44:11] [Iter 2010/2250] R4[1768/2400] | LR: 0.009039 | E: -27.121859 | E_var:     0.1323 | E_err:   0.005683
[2025-10-02 21:44:15] [Iter 2011/2250] R4[1770/2400] | LR: 0.009015 | E: -27.139012 | E_var:     0.1721 | E_err:   0.006483
[2025-10-02 21:44:18] [Iter 2012/2250] R4[1772/2400] | LR: 0.008991 | E: -27.134958 | E_var:     0.1819 | E_err:   0.006663
[2025-10-02 21:44:22] [Iter 2013/2250] R4[1774/2400] | LR: 0.008967 | E: -27.126973 | E_var:     0.1981 | E_err:   0.006954
[2025-10-02 21:44:26] [Iter 2014/2250] R4[1776/2400] | LR: 0.008943 | E: -27.143004 | E_var:     0.1499 | E_err:   0.006051
[2025-10-02 21:44:29] [Iter 2015/2250] R4[1778/2400] | LR: 0.008919 | E: -27.132996 | E_var:     0.1592 | E_err:   0.006235
[2025-10-02 21:44:33] [Iter 2016/2250] R4[1780/2400] | LR: 0.008896 | E: -27.136463 | E_var:     0.1860 | E_err:   0.006739
[2025-10-02 21:44:37] [Iter 2017/2250] R4[1782/2400] | LR: 0.008872 | E: -27.134810 | E_var:     0.1285 | E_err:   0.005600
[2025-10-02 21:44:40] [Iter 2018/2250] R4[1784/2400] | LR: 0.008848 | E: -27.137775 | E_var:     0.1729 | E_err:   0.006497
[2025-10-02 21:44:44] [Iter 2019/2250] R4[1786/2400] | LR: 0.008825 | E: -27.137441 | E_var:     0.1449 | E_err:   0.005948
[2025-10-02 21:44:48] [Iter 2020/2250] R4[1788/2400] | LR: 0.008801 | E: -27.144485 | E_var:     0.1481 | E_err:   0.006012
[2025-10-02 21:44:51] [Iter 2021/2250] R4[1790/2400] | LR: 0.008778 | E: -27.130034 | E_var:     0.1754 | E_err:   0.006543
[2025-10-02 21:44:55] [Iter 2022/2250] R4[1792/2400] | LR: 0.008754 | E: -27.144660 | E_var:     0.1795 | E_err:   0.006620
[2025-10-02 21:44:59] [Iter 2023/2250] R4[1794/2400] | LR: 0.008731 | E: -27.131601 | E_var:     0.1570 | E_err:   0.006191
[2025-10-02 21:45:02] [Iter 2024/2250] R4[1796/2400] | LR: 0.008708 | E: -27.127529 | E_var:     0.1491 | E_err:   0.006033
[2025-10-02 21:45:06] [Iter 2025/2250] R4[1798/2400] | LR: 0.008684 | E: -27.142910 | E_var:     0.1764 | E_err:   0.006562
[2025-10-02 21:45:10] [Iter 2026/2250] R4[1800/2400] | LR: 0.008661 | E: -27.136679 | E_var:     0.1465 | E_err:   0.005981
[2025-10-02 21:45:13] [Iter 2027/2250] R4[1802/2400] | LR: 0.008638 | E: -27.140720 | E_var:     0.1522 | E_err:   0.006096
[2025-10-02 21:45:17] [Iter 2028/2250] R4[1804/2400] | LR: 0.008615 | E: -27.123695 | E_var:     0.1436 | E_err:   0.005920
[2025-10-02 21:45:21] [Iter 2029/2250] R4[1806/2400] | LR: 0.008592 | E: -27.137486 | E_var:     0.2100 | E_err:   0.007161
[2025-10-02 21:45:24] [Iter 2030/2250] R4[1808/2400] | LR: 0.008569 | E: -27.132847 | E_var:     0.1407 | E_err:   0.005860
[2025-10-02 21:45:28] [Iter 2031/2250] R4[1810/2400] | LR: 0.008546 | E: -27.140498 | E_var:     0.2796 | E_err:   0.008263
[2025-10-02 21:45:31] [Iter 2032/2250] R4[1812/2400] | LR: 0.008523 | E: -27.134846 | E_var:     0.1447 | E_err:   0.005943
[2025-10-02 21:45:35] [Iter 2033/2250] R4[1814/2400] | LR: 0.008501 | E: -27.136943 | E_var:     0.1521 | E_err:   0.006093
[2025-10-02 21:45:39] [Iter 2034/2250] R4[1816/2400] | LR: 0.008478 | E: -27.134111 | E_var:     0.1663 | E_err:   0.006371
[2025-10-02 21:45:42] [Iter 2035/2250] R4[1818/2400] | LR: 0.008455 | E: -27.146470 | E_var:     0.1396 | E_err:   0.005838
[2025-10-02 21:45:46] [Iter 2036/2250] R4[1820/2400] | LR: 0.008433 | E: -27.139475 | E_var:     0.1702 | E_err:   0.006446
[2025-10-02 21:45:50] [Iter 2037/2250] R4[1822/2400] | LR: 0.008410 | E: -27.132106 | E_var:     0.1408 | E_err:   0.005863
[2025-10-02 21:45:53] [Iter 2038/2250] R4[1824/2400] | LR: 0.008388 | E: -27.134232 | E_var:     0.1739 | E_err:   0.006516
[2025-10-02 21:45:57] [Iter 2039/2250] R4[1826/2400] | LR: 0.008366 | E: -27.133656 | E_var:     0.2137 | E_err:   0.007222
[2025-10-02 21:46:01] [Iter 2040/2250] R4[1828/2400] | LR: 0.008343 | E: -27.143581 | E_var:     0.1638 | E_err:   0.006325
[2025-10-02 21:46:04] [Iter 2041/2250] R4[1830/2400] | LR: 0.008321 | E: -27.144028 | E_var:     0.2733 | E_err:   0.008168
[2025-10-02 21:46:08] [Iter 2042/2250] R4[1832/2400] | LR: 0.008299 | E: -27.140617 | E_var:     0.3099 | E_err:   0.008698
[2025-10-02 21:46:12] [Iter 2043/2250] R4[1834/2400] | LR: 0.008277 | E: -27.135506 | E_var:     0.1544 | E_err:   0.006139
[2025-10-02 21:46:15] [Iter 2044/2250] R4[1836/2400] | LR: 0.008255 | E: -27.129823 | E_var:     0.1379 | E_err:   0.005802
[2025-10-02 21:46:19] [Iter 2045/2250] R4[1838/2400] | LR: 0.008233 | E: -27.130329 | E_var:     0.1907 | E_err:   0.006823
[2025-10-02 21:46:23] [Iter 2046/2250] R4[1840/2400] | LR: 0.008211 | E: -27.139622 | E_var:     0.1438 | E_err:   0.005925
[2025-10-02 21:46:26] [Iter 2047/2250] R4[1842/2400] | LR: 0.008189 | E: -27.134178 | E_var:     0.1447 | E_err:   0.005944
[2025-10-02 21:46:30] [Iter 2048/2250] R4[1844/2400] | LR: 0.008167 | E: -27.135417 | E_var:     0.1478 | E_err:   0.006007
[2025-10-02 21:46:34] [Iter 2049/2250] R4[1846/2400] | LR: 0.008145 | E: -27.130310 | E_var:     0.1825 | E_err:   0.006675
[2025-10-02 21:46:37] [Iter 2050/2250] R4[1848/2400] | LR: 0.008124 | E: -27.137659 | E_var:     0.1753 | E_err:   0.006543
[2025-10-02 21:46:41] [Iter 2051/2250] R4[1850/2400] | LR: 0.008102 | E: -27.134161 | E_var:     0.1368 | E_err:   0.005779
[2025-10-02 21:46:45] [Iter 2052/2250] R4[1852/2400] | LR: 0.008080 | E: -27.137212 | E_var:     0.1508 | E_err:   0.006067
[2025-10-02 21:46:48] [Iter 2053/2250] R4[1854/2400] | LR: 0.008059 | E: -27.129197 | E_var:     0.1912 | E_err:   0.006832
[2025-10-02 21:46:52] [Iter 2054/2250] R4[1856/2400] | LR: 0.008038 | E: -27.136299 | E_var:     0.1960 | E_err:   0.006917
[2025-10-02 21:46:55] [Iter 2055/2250] R4[1858/2400] | LR: 0.008016 | E: -27.139609 | E_var:     0.1419 | E_err:   0.005886
[2025-10-02 21:46:59] [Iter 2056/2250] R4[1860/2400] | LR: 0.007995 | E: -27.132288 | E_var:     0.1589 | E_err:   0.006228
[2025-10-02 21:47:03] [Iter 2057/2250] R4[1862/2400] | LR: 0.007974 | E: -27.136413 | E_var:     0.1309 | E_err:   0.005654
[2025-10-02 21:47:06] [Iter 2058/2250] R4[1864/2400] | LR: 0.007953 | E: -27.131451 | E_var:     0.1550 | E_err:   0.006152
[2025-10-02 21:47:10] [Iter 2059/2250] R4[1866/2400] | LR: 0.007931 | E: -27.129385 | E_var:     0.1721 | E_err:   0.006483
[2025-10-02 21:47:14] [Iter 2060/2250] R4[1868/2400] | LR: 0.007910 | E: -27.140386 | E_var:     0.1645 | E_err:   0.006338
[2025-10-02 21:47:17] [Iter 2061/2250] R4[1870/2400] | LR: 0.007889 | E: -27.129280 | E_var:     0.1373 | E_err:   0.005789
[2025-10-02 21:47:21] [Iter 2062/2250] R4[1872/2400] | LR: 0.007869 | E: -27.140069 | E_var:     0.1894 | E_err:   0.006800
[2025-10-02 21:47:25] [Iter 2063/2250] R4[1874/2400] | LR: 0.007848 | E: -27.134455 | E_var:     0.1800 | E_err:   0.006628
[2025-10-02 21:47:28] [Iter 2064/2250] R4[1876/2400] | LR: 0.007827 | E: -27.125656 | E_var:     0.1242 | E_err:   0.005506
[2025-10-02 21:47:32] [Iter 2065/2250] R4[1878/2400] | LR: 0.007806 | E: -27.132455 | E_var:     0.1671 | E_err:   0.006388
[2025-10-02 21:47:36] [Iter 2066/2250] R4[1880/2400] | LR: 0.007786 | E: -27.121985 | E_var:     0.1544 | E_err:   0.006140
[2025-10-02 21:47:39] [Iter 2067/2250] R4[1882/2400] | LR: 0.007765 | E: -27.139647 | E_var:     0.1520 | E_err:   0.006091
[2025-10-02 21:47:43] [Iter 2068/2250] R4[1884/2400] | LR: 0.007745 | E: -27.141335 | E_var:     0.1378 | E_err:   0.005801
[2025-10-02 21:47:47] [Iter 2069/2250] R4[1886/2400] | LR: 0.007724 | E: -27.132868 | E_var:     0.1815 | E_err:   0.006657
[2025-10-02 21:47:50] [Iter 2070/2250] R4[1888/2400] | LR: 0.007704 | E: -27.130310 | E_var:     0.1340 | E_err:   0.005719
[2025-10-02 21:47:54] [Iter 2071/2250] R4[1890/2400] | LR: 0.007684 | E: -27.134702 | E_var:     0.2380 | E_err:   0.007622
[2025-10-02 21:47:58] [Iter 2072/2250] R4[1892/2400] | LR: 0.007663 | E: -27.120730 | E_var:     0.1848 | E_err:   0.006717
[2025-10-02 21:48:01] [Iter 2073/2250] R4[1894/2400] | LR: 0.007643 | E: -27.133117 | E_var:     0.1489 | E_err:   0.006030
[2025-10-02 21:48:05] [Iter 2074/2250] R4[1896/2400] | LR: 0.007623 | E: -27.139063 | E_var:     0.1858 | E_err:   0.006736
[2025-10-02 21:48:09] [Iter 2075/2250] R4[1898/2400] | LR: 0.007603 | E: -27.128300 | E_var:     0.1869 | E_err:   0.006755
[2025-10-02 21:48:12] [Iter 2076/2250] R4[1900/2400] | LR: 0.007583 | E: -27.133036 | E_var:     0.1943 | E_err:   0.006888
[2025-10-02 21:48:16] [Iter 2077/2250] R4[1902/2400] | LR: 0.007563 | E: -27.138905 | E_var:     0.1636 | E_err:   0.006320
[2025-10-02 21:48:20] [Iter 2078/2250] R4[1904/2400] | LR: 0.007543 | E: -27.134935 | E_var:     0.1704 | E_err:   0.006449
[2025-10-02 21:48:23] [Iter 2079/2250] R4[1906/2400] | LR: 0.007524 | E: -27.144035 | E_var:     0.1369 | E_err:   0.005782
[2025-10-02 21:48:27] [Iter 2080/2250] R4[1908/2400] | LR: 0.007504 | E: -27.138245 | E_var:     0.1525 | E_err:   0.006101
[2025-10-02 21:48:31] [Iter 2081/2250] R4[1910/2400] | LR: 0.007484 | E: -27.138302 | E_var:     0.1390 | E_err:   0.005826
[2025-10-02 21:48:34] [Iter 2082/2250] R4[1912/2400] | LR: 0.007465 | E: -27.140316 | E_var:     0.1217 | E_err:   0.005450
[2025-10-02 21:48:38] [Iter 2083/2250] R4[1914/2400] | LR: 0.007445 | E: -27.132529 | E_var:     0.1222 | E_err:   0.005461
[2025-10-02 21:48:41] [Iter 2084/2250] R4[1916/2400] | LR: 0.007426 | E: -27.129451 | E_var:     0.1788 | E_err:   0.006607
[2025-10-02 21:48:45] [Iter 2085/2250] R4[1918/2400] | LR: 0.007407 | E: -27.127784 | E_var:     0.1358 | E_err:   0.005757
[2025-10-02 21:48:49] [Iter 2086/2250] R4[1920/2400] | LR: 0.007387 | E: -27.119967 | E_var:     0.1559 | E_err:   0.006170
[2025-10-02 21:48:52] [Iter 2087/2250] R4[1922/2400] | LR: 0.007368 | E: -27.130727 | E_var:     0.1254 | E_err:   0.005534
[2025-10-02 21:48:56] [Iter 2088/2250] R4[1924/2400] | LR: 0.007349 | E: -27.135060 | E_var:     0.1544 | E_err:   0.006140
[2025-10-02 21:49:00] [Iter 2089/2250] R4[1926/2400] | LR: 0.007330 | E: -27.127352 | E_var:     0.1612 | E_err:   0.006274
[2025-10-02 21:49:03] [Iter 2090/2250] R4[1928/2400] | LR: 0.007311 | E: -27.131647 | E_var:     0.1634 | E_err:   0.006316
[2025-10-02 21:49:07] [Iter 2091/2250] R4[1930/2400] | LR: 0.007292 | E: -27.138658 | E_var:     0.1307 | E_err:   0.005649
[2025-10-02 21:49:11] [Iter 2092/2250] R4[1932/2400] | LR: 0.007273 | E: -27.143937 | E_var:     0.2128 | E_err:   0.007209
[2025-10-02 21:49:14] [Iter 2093/2250] R4[1934/2400] | LR: 0.007254 | E: -27.138051 | E_var:     0.1753 | E_err:   0.006543
[2025-10-02 21:49:18] [Iter 2094/2250] R4[1936/2400] | LR: 0.007236 | E: -27.140800 | E_var:     0.1621 | E_err:   0.006292
[2025-10-02 21:49:22] [Iter 2095/2250] R4[1938/2400] | LR: 0.007217 | E: -27.139979 | E_var:     0.1634 | E_err:   0.006317
[2025-10-02 21:49:25] [Iter 2096/2250] R4[1940/2400] | LR: 0.007198 | E: -27.139933 | E_var:     0.2350 | E_err:   0.007575
[2025-10-02 21:49:29] [Iter 2097/2250] R4[1942/2400] | LR: 0.007180 | E: -27.138603 | E_var:     0.2037 | E_err:   0.007051
[2025-10-02 21:49:33] [Iter 2098/2250] R4[1944/2400] | LR: 0.007161 | E: -27.134517 | E_var:     0.1433 | E_err:   0.005914
[2025-10-02 21:49:36] [Iter 2099/2250] R4[1946/2400] | LR: 0.007143 | E: -27.131398 | E_var:     0.1736 | E_err:   0.006510
[2025-10-02 21:49:40] [Iter 2100/2250] R4[1948/2400] | LR: 0.007125 | E: -27.137338 | E_var:     0.1670 | E_err:   0.006386
[2025-10-02 21:49:44] [Iter 2101/2250] R4[1950/2400] | LR: 0.007107 | E: -27.130104 | E_var:     0.1214 | E_err:   0.005443
[2025-10-02 21:49:47] [Iter 2102/2250] R4[1952/2400] | LR: 0.007088 | E: -27.136087 | E_var:     0.1615 | E_err:   0.006280
[2025-10-02 21:49:51] [Iter 2103/2250] R4[1954/2400] | LR: 0.007070 | E: -27.129131 | E_var:     0.1836 | E_err:   0.006696
[2025-10-02 21:49:55] [Iter 2104/2250] R4[1956/2400] | LR: 0.007052 | E: -27.131099 | E_var:     0.1489 | E_err:   0.006030
[2025-10-02 21:49:58] [Iter 2105/2250] R4[1958/2400] | LR: 0.007034 | E: -27.140626 | E_var:     0.2659 | E_err:   0.008058
[2025-10-02 21:50:02] [Iter 2106/2250] R4[1960/2400] | LR: 0.007017 | E: -27.136101 | E_var:     0.1345 | E_err:   0.005730
[2025-10-02 21:50:05] [Iter 2107/2250] R4[1962/2400] | LR: 0.006999 | E: -27.134293 | E_var:     0.1346 | E_err:   0.005733
[2025-10-02 21:50:09] [Iter 2108/2250] R4[1964/2400] | LR: 0.006981 | E: -27.135834 | E_var:     0.1408 | E_err:   0.005863
[2025-10-02 21:50:13] [Iter 2109/2250] R4[1966/2400] | LR: 0.006963 | E: -27.131951 | E_var:     0.1681 | E_err:   0.006406
[2025-10-02 21:50:16] [Iter 2110/2250] R4[1968/2400] | LR: 0.006946 | E: -27.139558 | E_var:     0.1619 | E_err:   0.006287
[2025-10-02 21:50:20] [Iter 2111/2250] R4[1970/2400] | LR: 0.006928 | E: -27.149064 | E_var:     0.1488 | E_err:   0.006028
[2025-10-02 21:50:24] [Iter 2112/2250] R4[1972/2400] | LR: 0.006911 | E: -27.126040 | E_var:     0.4969 | E_err:   0.011014
[2025-10-02 21:50:27] [Iter 2113/2250] R4[1974/2400] | LR: 0.006894 | E: -27.122846 | E_var:     0.1432 | E_err:   0.005913
[2025-10-02 21:50:31] [Iter 2114/2250] R4[1976/2400] | LR: 0.006876 | E: -27.127123 | E_var:     0.1521 | E_err:   0.006094
[2025-10-02 21:50:35] [Iter 2115/2250] R4[1978/2400] | LR: 0.006859 | E: -27.135637 | E_var:     0.1808 | E_err:   0.006644
[2025-10-02 21:50:38] [Iter 2116/2250] R4[1980/2400] | LR: 0.006842 | E: -27.139024 | E_var:     0.1324 | E_err:   0.005686
[2025-10-02 21:50:42] [Iter 2117/2250] R4[1982/2400] | LR: 0.006825 | E: -27.125037 | E_var:     0.2106 | E_err:   0.007171
[2025-10-02 21:50:46] [Iter 2118/2250] R4[1984/2400] | LR: 0.006808 | E: -27.128871 | E_var:     0.1842 | E_err:   0.006705
[2025-10-02 21:50:49] [Iter 2119/2250] R4[1986/2400] | LR: 0.006791 | E: -27.137578 | E_var:     0.1595 | E_err:   0.006241
[2025-10-02 21:50:53] [Iter 2120/2250] R4[1988/2400] | LR: 0.006774 | E: -27.142063 | E_var:     0.1764 | E_err:   0.006562
[2025-10-02 21:50:57] [Iter 2121/2250] R4[1990/2400] | LR: 0.006757 | E: -27.125673 | E_var:     0.1464 | E_err:   0.005978
[2025-10-02 21:51:00] [Iter 2122/2250] R4[1992/2400] | LR: 0.006741 | E: -27.141083 | E_var:     0.1550 | E_err:   0.006152
[2025-10-02 21:51:04] [Iter 2123/2250] R4[1994/2400] | LR: 0.006724 | E: -27.142586 | E_var:     0.1493 | E_err:   0.006038
[2025-10-02 21:51:08] [Iter 2124/2250] R4[1996/2400] | LR: 0.006708 | E: -27.131325 | E_var:     0.1624 | E_err:   0.006298
[2025-10-02 21:51:11] [Iter 2125/2250] R4[1998/2400] | LR: 0.006691 | E: -27.134770 | E_var:     0.1323 | E_err:   0.005684
[2025-10-02 21:51:15] [Iter 2126/2250] R4[2000/2400] | LR: 0.006675 | E: -27.147642 | E_var:     0.1473 | E_err:   0.005996
[2025-10-02 21:51:19] [Iter 2127/2250] R4[2002/2400] | LR: 0.006658 | E: -27.123966 | E_var:     0.1799 | E_err:   0.006628
[2025-10-02 21:51:22] [Iter 2128/2250] R4[2004/2400] | LR: 0.006642 | E: -27.138586 | E_var:     0.1304 | E_err:   0.005641
[2025-10-02 21:51:26] [Iter 2129/2250] R4[2006/2400] | LR: 0.006626 | E: -27.138722 | E_var:     0.2695 | E_err:   0.008111
[2025-10-02 21:51:29] [Iter 2130/2250] R4[2008/2400] | LR: 0.006610 | E: -27.140373 | E_var:     0.1482 | E_err:   0.006015
[2025-10-02 21:51:33] [Iter 2131/2250] R4[2010/2400] | LR: 0.006594 | E: -27.130438 | E_var:     0.1492 | E_err:   0.006035
[2025-10-02 21:51:37] [Iter 2132/2250] R4[2012/2400] | LR: 0.006578 | E: -27.128437 | E_var:     0.1299 | E_err:   0.005631
[2025-10-02 21:51:40] [Iter 2133/2250] R4[2014/2400] | LR: 0.006562 | E: -27.125822 | E_var:     0.1575 | E_err:   0.006201
[2025-10-02 21:51:44] [Iter 2134/2250] R4[2016/2400] | LR: 0.006546 | E: -27.141652 | E_var:     0.1442 | E_err:   0.005934
[2025-10-02 21:51:48] [Iter 2135/2250] R4[2018/2400] | LR: 0.006530 | E: -27.147945 | E_var:     0.1267 | E_err:   0.005561
[2025-10-02 21:51:51] [Iter 2136/2250] R4[2020/2400] | LR: 0.006515 | E: -27.146772 | E_var:     0.1477 | E_err:   0.006005
[2025-10-02 21:51:55] [Iter 2137/2250] R4[2022/2400] | LR: 0.006499 | E: -27.143999 | E_var:     0.1454 | E_err:   0.005957
[2025-10-02 21:51:59] [Iter 2138/2250] R4[2024/2400] | LR: 0.006484 | E: -27.130288 | E_var:     0.1923 | E_err:   0.006851
[2025-10-02 21:52:02] [Iter 2139/2250] R4[2026/2400] | LR: 0.006468 | E: -27.132844 | E_var:     0.1297 | E_err:   0.005627
[2025-10-02 21:52:06] [Iter 2140/2250] R4[2028/2400] | LR: 0.006453 | E: -27.131581 | E_var:     0.1332 | E_err:   0.005702
[2025-10-02 21:52:10] [Iter 2141/2250] R4[2030/2400] | LR: 0.006438 | E: -27.125142 | E_var:     0.1768 | E_err:   0.006570
[2025-10-02 21:52:13] [Iter 2142/2250] R4[2032/2400] | LR: 0.006422 | E: -27.135969 | E_var:     0.2402 | E_err:   0.007658
[2025-10-02 21:52:17] [Iter 2143/2250] R4[2034/2400] | LR: 0.006407 | E: -27.141794 | E_var:     0.1314 | E_err:   0.005665
[2025-10-02 21:52:21] [Iter 2144/2250] R4[2036/2400] | LR: 0.006392 | E: -27.147803 | E_var:     0.1159 | E_err:   0.005319
[2025-10-02 21:52:24] [Iter 2145/2250] R4[2038/2400] | LR: 0.006377 | E: -27.136887 | E_var:     0.1599 | E_err:   0.006247
[2025-10-02 21:52:28] [Iter 2146/2250] R4[2040/2400] | LR: 0.006362 | E: -27.131032 | E_var:     0.1921 | E_err:   0.006848
[2025-10-02 21:52:32] [Iter 2147/2250] R4[2042/2400] | LR: 0.006348 | E: -27.131583 | E_var:     0.1579 | E_err:   0.006210
[2025-10-02 21:52:35] [Iter 2148/2250] R4[2044/2400] | LR: 0.006333 | E: -27.139680 | E_var:     0.1299 | E_err:   0.005632
[2025-10-02 21:52:39] [Iter 2149/2250] R4[2046/2400] | LR: 0.006318 | E: -27.132299 | E_var:     0.1452 | E_err:   0.005954
[2025-10-02 21:52:43] [Iter 2150/2250] R4[2048/2400] | LR: 0.006304 | E: -27.126115 | E_var:     0.1498 | E_err:   0.006047
[2025-10-02 21:52:46] [Iter 2151/2250] R4[2050/2400] | LR: 0.006289 | E: -27.130360 | E_var:     0.1353 | E_err:   0.005748
[2025-10-02 21:52:50] [Iter 2152/2250] R4[2052/2400] | LR: 0.006275 | E: -27.133696 | E_var:     0.1252 | E_err:   0.005529
[2025-10-02 21:52:54] [Iter 2153/2250] R4[2054/2400] | LR: 0.006260 | E: -27.144632 | E_var:     0.1647 | E_err:   0.006340
[2025-10-02 21:52:57] [Iter 2154/2250] R4[2056/2400] | LR: 0.006246 | E: -27.144815 | E_var:     0.1488 | E_err:   0.006026
[2025-10-02 21:53:01] [Iter 2155/2250] R4[2058/2400] | LR: 0.006232 | E: -27.140644 | E_var:     0.1574 | E_err:   0.006200
[2025-10-02 21:53:04] [Iter 2156/2250] R4[2060/2400] | LR: 0.006218 | E: -27.133397 | E_var:     0.1680 | E_err:   0.006404
[2025-10-02 21:53:08] [Iter 2157/2250] R4[2062/2400] | LR: 0.006204 | E: -27.140970 | E_var:     0.1192 | E_err:   0.005394
[2025-10-02 21:53:12] [Iter 2158/2250] R4[2064/2400] | LR: 0.006190 | E: -27.144265 | E_var:     0.1373 | E_err:   0.005789
[2025-10-02 21:53:15] [Iter 2159/2250] R4[2066/2400] | LR: 0.006176 | E: -27.139316 | E_var:     0.3993 | E_err:   0.009874
[2025-10-02 21:53:19] [Iter 2160/2250] R4[2068/2400] | LR: 0.006162 | E: -27.144345 | E_var:     0.1793 | E_err:   0.006615
[2025-10-02 21:53:23] [Iter 2161/2250] R4[2070/2400] | LR: 0.006148 | E: -27.141215 | E_var:     0.1749 | E_err:   0.006534
[2025-10-02 21:53:26] [Iter 2162/2250] R4[2072/2400] | LR: 0.006135 | E: -27.129786 | E_var:     0.1804 | E_err:   0.006637
[2025-10-02 21:53:30] [Iter 2163/2250] R4[2074/2400] | LR: 0.006121 | E: -27.137672 | E_var:     0.1327 | E_err:   0.005691
[2025-10-02 21:53:34] [Iter 2164/2250] R4[2076/2400] | LR: 0.006107 | E: -27.123918 | E_var:     0.1377 | E_err:   0.005799
[2025-10-02 21:53:37] [Iter 2165/2250] R4[2078/2400] | LR: 0.006094 | E: -27.139681 | E_var:     0.1635 | E_err:   0.006318
[2025-10-02 21:53:41] [Iter 2166/2250] R4[2080/2400] | LR: 0.006081 | E: -27.141999 | E_var:     0.1339 | E_err:   0.005718
[2025-10-02 21:53:45] [Iter 2167/2250] R4[2082/2400] | LR: 0.006067 | E: -27.131694 | E_var:     0.1671 | E_err:   0.006388
[2025-10-02 21:53:48] [Iter 2168/2250] R4[2084/2400] | LR: 0.006054 | E: -27.136748 | E_var:     0.1384 | E_err:   0.005813
[2025-10-02 21:53:52] [Iter 2169/2250] R4[2086/2400] | LR: 0.006041 | E: -27.136308 | E_var:     0.1478 | E_err:   0.006007
[2025-10-02 21:53:56] [Iter 2170/2250] R4[2088/2400] | LR: 0.006028 | E: -27.133827 | E_var:     0.1485 | E_err:   0.006021
[2025-10-02 21:53:59] [Iter 2171/2250] R4[2090/2400] | LR: 0.006015 | E: -27.146874 | E_var:     0.2681 | E_err:   0.008091
[2025-10-02 21:54:03] [Iter 2172/2250] R4[2092/2400] | LR: 0.006002 | E: -27.132294 | E_var:     0.1340 | E_err:   0.005720
[2025-10-02 21:54:07] [Iter 2173/2250] R4[2094/2400] | LR: 0.005989 | E: -27.130228 | E_var:     0.2865 | E_err:   0.008364
[2025-10-02 21:54:10] [Iter 2174/2250] R4[2096/2400] | LR: 0.005977 | E: -27.141112 | E_var:     0.1713 | E_err:   0.006468
[2025-10-02 21:54:14] [Iter 2175/2250] R4[2098/2400] | LR: 0.005964 | E: -27.140578 | E_var:     0.1932 | E_err:   0.006868
[2025-10-02 21:54:18] [Iter 2176/2250] R4[2100/2400] | LR: 0.005952 | E: -27.146831 | E_var:     0.1575 | E_err:   0.006200
[2025-10-02 21:54:21] [Iter 2177/2250] R4[2102/2400] | LR: 0.005939 | E: -27.142075 | E_var:     0.1591 | E_err:   0.006233
[2025-10-02 21:54:25] [Iter 2178/2250] R4[2104/2400] | LR: 0.005927 | E: -27.127932 | E_var:     0.1418 | E_err:   0.005883
[2025-10-02 21:54:29] [Iter 2179/2250] R4[2106/2400] | LR: 0.005914 | E: -27.137372 | E_var:     0.1370 | E_err:   0.005783
[2025-10-02 21:54:32] [Iter 2180/2250] R4[2108/2400] | LR: 0.005902 | E: -27.135222 | E_var:     0.1278 | E_err:   0.005586
[2025-10-02 21:54:36] [Iter 2181/2250] R4[2110/2400] | LR: 0.005890 | E: -27.134586 | E_var:     0.1305 | E_err:   0.005643
[2025-10-02 21:54:39] [Iter 2182/2250] R4[2112/2400] | LR: 0.005878 | E: -27.138421 | E_var:     0.1717 | E_err:   0.006475
[2025-10-02 21:54:43] [Iter 2183/2250] R4[2114/2400] | LR: 0.005866 | E: -27.130645 | E_var:     0.3134 | E_err:   0.008747
[2025-10-02 21:54:47] [Iter 2184/2250] R4[2116/2400] | LR: 0.005854 | E: -27.129497 | E_var:     0.1194 | E_err:   0.005400
[2025-10-02 21:54:50] [Iter 2185/2250] R4[2118/2400] | LR: 0.005842 | E: -27.128774 | E_var:     0.1450 | E_err:   0.005951
[2025-10-02 21:54:54] [Iter 2186/2250] R4[2120/2400] | LR: 0.005830 | E: -27.134683 | E_var:     0.1620 | E_err:   0.006288
[2025-10-02 21:54:58] [Iter 2187/2250] R4[2122/2400] | LR: 0.005819 | E: -27.135614 | E_var:     0.1237 | E_err:   0.005496
[2025-10-02 21:55:01] [Iter 2188/2250] R4[2124/2400] | LR: 0.005807 | E: -27.142485 | E_var:     0.2221 | E_err:   0.007363
[2025-10-02 21:55:05] [Iter 2189/2250] R4[2126/2400] | LR: 0.005795 | E: -27.141239 | E_var:     0.1361 | E_err:   0.005765
[2025-10-02 21:55:09] [Iter 2190/2250] R4[2128/2400] | LR: 0.005784 | E: -27.145302 | E_var:     0.1994 | E_err:   0.006977
[2025-10-02 21:55:12] [Iter 2191/2250] R4[2130/2400] | LR: 0.005773 | E: -27.141948 | E_var:     0.7004 | E_err:   0.013076
[2025-10-02 21:55:16] [Iter 2192/2250] R4[2132/2400] | LR: 0.005761 | E: -27.135365 | E_var:     0.2049 | E_err:   0.007072
[2025-10-02 21:55:20] [Iter 2193/2250] R4[2134/2400] | LR: 0.005750 | E: -27.138865 | E_var:     0.1455 | E_err:   0.005960
[2025-10-02 21:55:23] [Iter 2194/2250] R4[2136/2400] | LR: 0.005739 | E: -27.139221 | E_var:     0.1708 | E_err:   0.006457
[2025-10-02 21:55:27] [Iter 2195/2250] R4[2138/2400] | LR: 0.005728 | E: -27.138464 | E_var:     0.1848 | E_err:   0.006717
[2025-10-02 21:55:31] [Iter 2196/2250] R4[2140/2400] | LR: 0.005717 | E: -27.131325 | E_var:     0.1291 | E_err:   0.005614
[2025-10-02 21:55:34] [Iter 2197/2250] R4[2142/2400] | LR: 0.005706 | E: -27.135062 | E_var:     0.1552 | E_err:   0.006156
[2025-10-02 21:55:38] [Iter 2198/2250] R4[2144/2400] | LR: 0.005695 | E: -27.139423 | E_var:     0.1342 | E_err:   0.005723
[2025-10-02 21:55:42] [Iter 2199/2250] R4[2146/2400] | LR: 0.005685 | E: -27.130251 | E_var:     0.1157 | E_err:   0.005314
[2025-10-02 21:55:45] [Iter 2200/2250] R4[2148/2400] | LR: 0.005674 | E: -27.136717 | E_var:     0.1511 | E_err:   0.006075
[2025-10-02 21:55:46] ✓ Checkpoint saved: checkpoint_iter_002200.pkl
[2025-10-02 21:55:49] [Iter 2201/2250] R4[2150/2400] | LR: 0.005663 | E: -27.143039 | E_var:     0.1203 | E_err:   0.005420
[2025-10-02 21:55:53] [Iter 2202/2250] R4[2152/2400] | LR: 0.005653 | E: -27.124701 | E_var:     0.1737 | E_err:   0.006511
[2025-10-02 21:55:56] [Iter 2203/2250] R4[2154/2400] | LR: 0.005642 | E: -27.136057 | E_var:     0.1213 | E_err:   0.005441
[2025-10-02 21:56:00] [Iter 2204/2250] R4[2156/2400] | LR: 0.005632 | E: -27.126805 | E_var:     0.1599 | E_err:   0.006247
[2025-10-02 21:56:04] [Iter 2205/2250] R4[2158/2400] | LR: 0.005622 | E: -27.133009 | E_var:     0.1469 | E_err:   0.005990
[2025-10-02 21:56:07] [Iter 2206/2250] R4[2160/2400] | LR: 0.005612 | E: -27.139256 | E_var:     0.2056 | E_err:   0.007085
[2025-10-02 21:56:11] [Iter 2207/2250] R4[2162/2400] | LR: 0.005602 | E: -27.131768 | E_var:     0.1521 | E_err:   0.006094
[2025-10-02 21:56:15] [Iter 2208/2250] R4[2164/2400] | LR: 0.005592 | E: -27.143768 | E_var:     0.1439 | E_err:   0.005928
[2025-10-02 21:56:18] [Iter 2209/2250] R4[2166/2400] | LR: 0.005582 | E: -27.125899 | E_var:     0.1572 | E_err:   0.006195
[2025-10-02 21:56:22] [Iter 2210/2250] R4[2168/2400] | LR: 0.005572 | E: -27.136491 | E_var:     0.1261 | E_err:   0.005549
[2025-10-02 21:56:26] [Iter 2211/2250] R4[2170/2400] | LR: 0.005562 | E: -27.141021 | E_var:     0.1446 | E_err:   0.005942
[2025-10-02 21:56:29] [Iter 2212/2250] R4[2172/2400] | LR: 0.005553 | E: -27.132499 | E_var:     0.1321 | E_err:   0.005679
[2025-10-02 21:56:33] [Iter 2213/2250] R4[2174/2400] | LR: 0.005543 | E: -27.136270 | E_var:     0.1525 | E_err:   0.006101
[2025-10-02 21:56:37] [Iter 2214/2250] R4[2176/2400] | LR: 0.005534 | E: -27.140078 | E_var:     0.1822 | E_err:   0.006669
[2025-10-02 21:56:40] [Iter 2215/2250] R4[2178/2400] | LR: 0.005524 | E: -27.134572 | E_var:     0.2945 | E_err:   0.008479
[2025-10-02 21:56:44] [Iter 2216/2250] R4[2180/2400] | LR: 0.005515 | E: -27.139427 | E_var:     0.1144 | E_err:   0.005286
[2025-10-02 21:56:48] [Iter 2217/2250] R4[2182/2400] | LR: 0.005506 | E: -27.134834 | E_var:     0.1477 | E_err:   0.006006
[2025-10-02 21:56:51] [Iter 2218/2250] R4[2184/2400] | LR: 0.005496 | E: -27.134724 | E_var:     0.1363 | E_err:   0.005768
[2025-10-02 21:56:55] [Iter 2219/2250] R4[2186/2400] | LR: 0.005487 | E: -27.128882 | E_var:     0.1556 | E_err:   0.006164
[2025-10-02 21:56:59] [Iter 2220/2250] R4[2188/2400] | LR: 0.005478 | E: -27.146875 | E_var:     0.1605 | E_err:   0.006260
[2025-10-02 21:57:02] [Iter 2221/2250] R4[2190/2400] | LR: 0.005469 | E: -27.141316 | E_var:     0.1696 | E_err:   0.006436
[2025-10-02 21:57:06] [Iter 2222/2250] R4[2192/2400] | LR: 0.005460 | E: -27.140588 | E_var:     0.1458 | E_err:   0.005966
[2025-10-02 21:57:09] [Iter 2223/2250] R4[2194/2400] | LR: 0.005452 | E: -27.133535 | E_var:     0.1173 | E_err:   0.005351
[2025-10-02 21:57:13] [Iter 2224/2250] R4[2196/2400] | LR: 0.005443 | E: -27.143381 | E_var:     0.2292 | E_err:   0.007480
[2025-10-02 21:57:17] [Iter 2225/2250] R4[2198/2400] | LR: 0.005434 | E: -27.142594 | E_var:     0.1721 | E_err:   0.006482
[2025-10-02 21:57:20] [Iter 2226/2250] R4[2200/2400] | LR: 0.005426 | E: -27.140583 | E_var:     0.1815 | E_err:   0.006656
[2025-10-02 21:57:24] [Iter 2227/2250] R4[2202/2400] | LR: 0.005417 | E: -27.144827 | E_var:     0.1440 | E_err:   0.005929
[2025-10-02 21:57:28] [Iter 2228/2250] R4[2204/2400] | LR: 0.005409 | E: -27.139089 | E_var:     0.1561 | E_err:   0.006173
[2025-10-02 21:57:31] [Iter 2229/2250] R4[2206/2400] | LR: 0.005401 | E: -27.134443 | E_var:     0.1188 | E_err:   0.005385
[2025-10-02 21:57:35] [Iter 2230/2250] R4[2208/2400] | LR: 0.005393 | E: -27.136878 | E_var:     0.1251 | E_err:   0.005526
[2025-10-02 21:57:39] [Iter 2231/2250] R4[2210/2400] | LR: 0.005385 | E: -27.137199 | E_var:     0.1473 | E_err:   0.005997
[2025-10-02 21:57:42] [Iter 2232/2250] R4[2212/2400] | LR: 0.005377 | E: -27.143148 | E_var:     0.2325 | E_err:   0.007535
[2025-10-02 21:57:46] [Iter 2233/2250] R4[2214/2400] | LR: 0.005369 | E: -27.130264 | E_var:     0.1398 | E_err:   0.005842
[2025-10-02 21:57:50] [Iter 2234/2250] R4[2216/2400] | LR: 0.005361 | E: -27.138067 | E_var:     0.1375 | E_err:   0.005793
[2025-10-02 21:57:53] [Iter 2235/2250] R4[2218/2400] | LR: 0.005353 | E: -27.137457 | E_var:     0.1711 | E_err:   0.006464
[2025-10-02 21:57:57] [Iter 2236/2250] R4[2220/2400] | LR: 0.005345 | E: -27.141655 | E_var:     0.1260 | E_err:   0.005547
[2025-10-02 21:58:01] [Iter 2237/2250] R4[2222/2400] | LR: 0.005338 | E: -27.132595 | E_var:     0.1365 | E_err:   0.005772
[2025-10-02 21:58:04] [Iter 2238/2250] R4[2224/2400] | LR: 0.005330 | E: -27.133557 | E_var:     0.1539 | E_err:   0.006130
[2025-10-02 21:58:08] [Iter 2239/2250] R4[2226/2400] | LR: 0.005323 | E: -27.147641 | E_var:     0.1574 | E_err:   0.006200
[2025-10-02 21:58:12] [Iter 2240/2250] R4[2228/2400] | LR: 0.005315 | E: -27.137798 | E_var:     0.1875 | E_err:   0.006767
[2025-10-02 21:58:15] [Iter 2241/2250] R4[2230/2400] | LR: 0.005308 | E: -27.141289 | E_var:     0.1717 | E_err:   0.006475
[2025-10-02 21:58:19] [Iter 2242/2250] R4[2232/2400] | LR: 0.005301 | E: -27.137195 | E_var:     0.1776 | E_err:   0.006585
[2025-10-02 21:58:23] [Iter 2243/2250] R4[2234/2400] | LR: 0.005294 | E: -27.150478 | E_var:     0.1714 | E_err:   0.006468
[2025-10-02 21:58:26] [Iter 2244/2250] R4[2236/2400] | LR: 0.005287 | E: -27.133005 | E_var:     0.1394 | E_err:   0.005834
[2025-10-02 21:58:30] [Iter 2245/2250] R4[2238/2400] | LR: 0.005280 | E: -27.132560 | E_var:     0.1463 | E_err:   0.005976
[2025-10-02 21:58:33] [Iter 2246/2250] R4[2240/2400] | LR: 0.005273 | E: -27.133180 | E_var:     0.1476 | E_err:   0.006002
[2025-10-02 21:58:37] [Iter 2247/2250] R4[2242/2400] | LR: 0.005266 | E: -27.135733 | E_var:     0.1324 | E_err:   0.005686
[2025-10-02 21:58:41] [Iter 2248/2250] R4[2244/2400] | LR: 0.005260 | E: -27.130116 | E_var:     0.3190 | E_err:   0.008824
[2025-10-02 21:58:44] [Iter 2249/2250] R4[2246/2400] | LR: 0.005253 | E: -27.141677 | E_var:     0.1334 | E_err:   0.005706
[2025-10-02 21:58:48] [Iter 2250/2250] R4[2248/2400] | LR: 0.005247 | E: -27.129715 | E_var:     0.1938 | E_err:   0.006878
[2025-10-02 21:58:48] ================================================================================
[2025-10-02 21:58:48] ✅ Training completed successfully
[2025-10-02 21:58:48] Total restarts: 4
[2025-10-02 21:58:49] Final Energy: -27.12971458 ± 0.00687804
[2025-10-02 21:58:49] Final Variance: 0.193771
[2025-10-02 21:58:49] ================================================================================
[2025-10-02 21:58:49] ============================================================
[2025-10-02 21:58:49] Training completed | Runtime: 8260.2s
[2025-10-02 21:58:51] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-02 21:58:51] ============================================================
