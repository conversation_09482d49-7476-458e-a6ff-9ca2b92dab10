[2025-10-06 05:22:06] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.83/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 05:22:06]   - 迭代次数: final
[2025-10-06 05:22:06]   - 能量: -30.002829-0.000458j ± 0.004636, Var: 0.088041
[2025-10-06 05:22:06]   - 时间戳: 2025-10-06T05:21:53.916956+08:00
[2025-10-06 05:22:20] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 05:22:20] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 05:22:20] ======================================================================================================
[2025-10-06 05:22:20] GCNN for Shastry-Sutherland Model
[2025-10-06 05:22:20] ======================================================================================================
[2025-10-06 05:22:20] System parameters:
[2025-10-06 05:22:20]   - System size: L=4, N=64
[2025-10-06 05:22:20]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-10-06 05:22:20] ------------------------------------------------------------------------------------------------------
[2025-10-06 05:22:20] Model parameters:
[2025-10-06 05:22:20]   - Number of layers = 4
[2025-10-06 05:22:20]   - Number of features = 4
[2025-10-06 05:22:20]   - Total parameters = 12572
[2025-10-06 05:22:20] ------------------------------------------------------------------------------------------------------
[2025-10-06 05:22:20] Training parameters:
[2025-10-06 05:22:20]   - Total iterations: 1050
[2025-10-06 05:22:20]   - Annealing cycles: 3
[2025-10-06 05:22:20]   - Initial period: 150
[2025-10-06 05:22:20]   - Period multiplier: 2.0
[2025-10-06 05:22:20]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 05:22:20]   - Samples: 4096
[2025-10-06 05:22:20]   - Discarded samples: 0
[2025-10-06 05:22:20]   - Chunk size: 4096
[2025-10-06 05:22:20]   - Diagonal shift: 0.15
[2025-10-06 05:22:20]   - Gradient clipping: 1.0
[2025-10-06 05:22:20]   - Checkpoint enabled: interval=100
[2025-10-06 05:22:20]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.84/model_L4F4/training/checkpoints
[2025-10-06 05:22:20] ------------------------------------------------------------------------------------------------------
[2025-10-06 05:22:20] Device status:
[2025-10-06 05:22:20]   - Devices model: NVIDIA H200 NVL
[2025-10-06 05:22:20]   - Number of devices: 1
[2025-10-06 05:22:21]   - Sharding: True
[2025-10-06 05:22:21] ======================================================================================================
[2025-10-06 05:22:55] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -30.424407 | E_var:     0.1810 | E_err:   0.006647
[2025-10-06 05:23:14] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -30.417631 | E_var:     0.1169 | E_err:   0.005343
[2025-10-06 05:23:16] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -30.420051 | E_var:     0.1044 | E_err:   0.005049
[2025-10-06 05:23:19] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -30.418370 | E_var:     0.0934 | E_err:   0.004775
[2025-10-06 05:23:21] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -30.416372 | E_var:     0.1831 | E_err:   0.006687
[2025-10-06 05:23:23] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -30.422730 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 05:23:26] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -30.419475 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 05:23:28] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -30.421087 | E_var:     0.0861 | E_err:   0.004586
[2025-10-06 05:23:31] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -30.417181 | E_var:     0.0841 | E_err:   0.004530
[2025-10-06 05:23:33] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -30.429257 | E_var:     0.0757 | E_err:   0.004299
[2025-10-06 05:23:36] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -30.420274 | E_var:     0.0838 | E_err:   0.004523
[2025-10-06 05:23:38] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -30.418177 | E_var:     0.0757 | E_err:   0.004300
[2025-10-06 05:23:40] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -30.412013 | E_var:     0.0861 | E_err:   0.004586
[2025-10-06 05:23:43] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -30.418329 | E_var:     0.0887 | E_err:   0.004652
[2025-10-06 05:23:45] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -30.417926 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 05:23:48] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -30.419916 | E_var:     0.0886 | E_err:   0.004650
[2025-10-06 05:23:50] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -30.423267 | E_var:     0.1052 | E_err:   0.005067
[2025-10-06 05:23:52] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -30.421738 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 05:23:55] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -30.432009 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 05:23:57] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -30.426237 | E_var:     0.1162 | E_err:   0.005326
[2025-10-06 05:24:00] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -30.413703 | E_var:     0.0784 | E_err:   0.004374
[2025-10-06 05:24:02] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -30.420678 | E_var:     0.0703 | E_err:   0.004142
[2025-10-06 05:24:05] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -30.415355 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 05:24:07] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -30.415949 | E_var:     0.0808 | E_err:   0.004442
[2025-10-06 05:24:09] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -30.422398 | E_var:     0.0761 | E_err:   0.004312
[2025-10-06 05:24:12] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -30.416853 | E_var:     0.1079 | E_err:   0.005133
[2025-10-06 05:24:14] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -30.418830 | E_var:     0.0634 | E_err:   0.003935
[2025-10-06 05:24:17] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -30.419046 | E_var:     0.0666 | E_err:   0.004032
[2025-10-06 05:24:19] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -30.427528 | E_var:     0.0768 | E_err:   0.004330
[2025-10-06 05:24:21] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -30.422734 | E_var:     0.0736 | E_err:   0.004238
[2025-10-06 05:24:24] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -30.422055 | E_var:     0.0737 | E_err:   0.004242
[2025-10-06 05:24:26] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -30.425036 | E_var:     0.0772 | E_err:   0.004341
[2025-10-06 05:24:29] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -30.421503 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 05:24:31] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -30.414899 | E_var:     0.0696 | E_err:   0.004123
[2025-10-06 05:24:33] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -30.426267 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 05:24:36] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -30.421564 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 05:24:38] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -30.419005 | E_var:     0.0758 | E_err:   0.004302
[2025-10-06 05:24:41] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -30.415336 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 05:24:43] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -30.420389 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 05:24:46] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -30.419790 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 05:24:48] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -30.429970 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 05:24:50] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -30.418436 | E_var:     0.0741 | E_err:   0.004253
[2025-10-06 05:24:53] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -30.422802 | E_var:     0.1628 | E_err:   0.006305
[2025-10-06 05:24:55] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -30.422673 | E_var:     0.1380 | E_err:   0.005805
[2025-10-06 05:24:58] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -30.416513 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 05:25:00] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -30.422867 | E_var:     0.0638 | E_err:   0.003946
[2025-10-06 05:25:02] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -30.434015 | E_var:     0.0975 | E_err:   0.004878
[2025-10-06 05:25:05] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -30.429824 | E_var:     0.0685 | E_err:   0.004089
[2025-10-06 05:25:07] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -30.428771 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 05:25:10] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -30.423140 | E_var:     0.0832 | E_err:   0.004508
[2025-10-06 05:25:12] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -30.424625 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 05:25:15] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -30.419009 | E_var:     0.0729 | E_err:   0.004218
[2025-10-06 05:25:17] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -30.426310 | E_var:     0.1056 | E_err:   0.005078
[2025-10-06 05:25:19] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -30.421932 | E_var:     0.0768 | E_err:   0.004330
[2025-10-06 05:25:22] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -30.422576 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 05:25:24] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -30.432733 | E_var:     0.0756 | E_err:   0.004296
[2025-10-06 05:25:27] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -30.425469 | E_var:     0.0725 | E_err:   0.004207
[2025-10-06 05:25:29] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -30.424775 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 05:25:31] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -30.420263 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 05:25:34] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -30.415339 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 05:25:36] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -30.419811 | E_var:     0.0636 | E_err:   0.003942
[2025-10-06 05:25:39] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -30.415118 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 05:25:41] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -30.419633 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 05:25:44] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -30.419681 | E_var:     0.0596 | E_err:   0.003815
[2025-10-06 05:25:46] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -30.419733 | E_var:     0.0638 | E_err:   0.003946
[2025-10-06 05:25:48] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -30.426320 | E_var:     0.0776 | E_err:   0.004353
[2025-10-06 05:25:51] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -30.425607 | E_var:     0.0926 | E_err:   0.004756
[2025-10-06 05:25:53] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -30.420176 | E_var:     0.0688 | E_err:   0.004098
[2025-10-06 05:25:56] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -30.419375 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 05:25:58] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -30.430746 | E_var:     0.4378 | E_err:   0.010339
[2025-10-06 05:26:00] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -30.421852 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 05:26:03] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -30.421756 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 05:26:05] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -30.418840 | E_var:     0.0586 | E_err:   0.003783
[2025-10-06 05:26:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -30.421755 | E_var:     0.0764 | E_err:   0.004318
[2025-10-06 05:26:10] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -30.419405 | E_var:     0.0777 | E_err:   0.004357
[2025-10-06 05:26:13] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -30.428420 | E_var:     0.0885 | E_err:   0.004648
[2025-10-06 05:26:15] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -30.425236 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 05:26:17] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -30.422888 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 05:26:20] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -30.425775 | E_var:     0.0826 | E_err:   0.004489
[2025-10-06 05:26:22] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -30.423173 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 05:26:25] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -30.435733 | E_var:     0.1813 | E_err:   0.006653
[2025-10-06 05:26:27] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -30.422239 | E_var:     0.0816 | E_err:   0.004462
[2025-10-06 05:26:30] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -30.429391 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 05:26:32] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -30.420651 | E_var:     0.0680 | E_err:   0.004075
[2025-10-06 05:26:34] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -30.418594 | E_var:     0.0943 | E_err:   0.004797
[2025-10-06 05:26:37] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -30.425463 | E_var:     0.0805 | E_err:   0.004432
[2025-10-06 05:26:39] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -30.428925 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 05:26:42] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -30.418840 | E_var:     0.0865 | E_err:   0.004594
[2025-10-06 05:26:44] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -30.418585 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 05:26:46] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -30.427610 | E_var:     0.0636 | E_err:   0.003940
[2025-10-06 05:26:49] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -30.427815 | E_var:     0.0751 | E_err:   0.004283
[2025-10-06 05:26:51] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -30.423536 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 05:26:54] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -30.421812 | E_var:     0.0627 | E_err:   0.003912
[2025-10-06 05:26:56] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -30.425791 | E_var:     0.0698 | E_err:   0.004129
[2025-10-06 05:26:59] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -30.432381 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 05:27:01] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -30.422750 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 05:27:03] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -30.420155 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 05:27:06] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -30.418560 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 05:27:08] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -30.424628 | E_var:     0.0733 | E_err:   0.004231
[2025-10-06 05:27:11] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -30.423880 | E_var:     0.0814 | E_err:   0.004459
[2025-10-06 05:27:11] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 05:27:13] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -30.422148 | E_var:     0.0798 | E_err:   0.004414
[2025-10-06 05:27:15] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -30.424664 | E_var:     0.0568 | E_err:   0.003723
[2025-10-06 05:27:18] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -30.420905 | E_var:     0.0617 | E_err:   0.003881
[2025-10-06 05:27:20] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -30.421657 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 05:27:23] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -30.416084 | E_var:     0.0778 | E_err:   0.004360
[2025-10-06 05:27:25] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -30.417751 | E_var:     0.0796 | E_err:   0.004409
[2025-10-06 05:27:28] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -30.413339 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 05:27:30] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -30.427008 | E_var:     0.0787 | E_err:   0.004382
[2025-10-06 05:27:32] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -30.420991 | E_var:     0.0688 | E_err:   0.004099
[2025-10-06 05:27:35] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -30.413416 | E_var:     0.0739 | E_err:   0.004246
[2025-10-06 05:27:37] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -30.416122 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 05:27:40] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -30.413582 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 05:27:42] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -30.412692 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 05:27:44] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -30.426583 | E_var:     0.0789 | E_err:   0.004388
[2025-10-06 05:27:47] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -30.420385 | E_var:     0.1093 | E_err:   0.005167
[2025-10-06 05:27:49] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -30.419741 | E_var:     0.0777 | E_err:   0.004357
[2025-10-06 05:27:52] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -30.423292 | E_var:     0.0670 | E_err:   0.004043
[2025-10-06 05:27:54] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -30.424753 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 05:27:56] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -30.427030 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 05:27:59] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -30.421274 | E_var:     0.0855 | E_err:   0.004568
[2025-10-06 05:28:01] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -30.419397 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 05:28:04] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -30.417280 | E_var:     0.0752 | E_err:   0.004285
[2025-10-06 05:28:06] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -30.413870 | E_var:     0.1047 | E_err:   0.005057
[2025-10-06 05:28:09] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -30.418514 | E_var:     0.0625 | E_err:   0.003906
[2025-10-06 05:28:11] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -30.421847 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 05:28:13] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -30.416493 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 05:28:16] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -30.422015 | E_var:     0.0663 | E_err:   0.004023
[2025-10-06 05:28:18] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -30.415608 | E_var:     0.0708 | E_err:   0.004158
[2025-10-06 05:28:21] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -30.420842 | E_var:     0.0614 | E_err:   0.003870
[2025-10-06 05:28:23] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -30.420965 | E_var:     0.0636 | E_err:   0.003939
[2025-10-06 05:28:25] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -30.417401 | E_var:     0.0655 | E_err:   0.003998
[2025-10-06 05:28:28] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -30.422184 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 05:28:30] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -30.418473 | E_var:     0.0657 | E_err:   0.004005
[2025-10-06 05:28:33] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -30.427621 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 05:28:35] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -30.423931 | E_var:     0.0673 | E_err:   0.004054
[2025-10-06 05:28:38] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -30.423684 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 05:28:40] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -30.421823 | E_var:     0.0945 | E_err:   0.004803
[2025-10-06 05:28:42] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -30.420425 | E_var:     0.0717 | E_err:   0.004185
[2025-10-06 05:28:45] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -30.415712 | E_var:     0.0699 | E_err:   0.004130
[2025-10-06 05:28:47] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -30.429223 | E_var:     0.0695 | E_err:   0.004121
[2025-10-06 05:28:50] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -30.423549 | E_var:     0.0662 | E_err:   0.004019
[2025-10-06 05:28:52] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -30.421202 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 05:28:54] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -30.425755 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 05:28:57] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -30.422647 | E_var:     0.0659 | E_err:   0.004012
[2025-10-06 05:28:59] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -30.424910 | E_var:     0.1277 | E_err:   0.005583
[2025-10-06 05:29:02] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -30.428033 | E_var:     0.0804 | E_err:   0.004429
[2025-10-06 05:29:04] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -30.415472 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 05:29:07] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -30.413989 | E_var:     0.1831 | E_err:   0.006686
[2025-10-06 05:29:09] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -30.417536 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 05:29:11] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -30.422801 | E_var:     0.0706 | E_err:   0.004152
[2025-10-06 05:29:11] 🔄 RESTART #1 | Period: 300
[2025-10-06 05:29:14] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -30.427950 | E_var:     0.0827 | E_err:   0.004492
[2025-10-06 05:29:16] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -30.420091 | E_var:     0.0753 | E_err:   0.004287
[2025-10-06 05:29:19] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -30.428694 | E_var:     0.0691 | E_err:   0.004108
[2025-10-06 05:29:21] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -30.424079 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 05:29:24] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -30.424428 | E_var:     0.0724 | E_err:   0.004205
[2025-10-06 05:29:26] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -30.420324 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 05:29:28] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -30.423991 | E_var:     0.0916 | E_err:   0.004728
[2025-10-06 05:29:31] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -30.420214 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 05:29:33] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -30.420956 | E_var:     0.0634 | E_err:   0.003934
[2025-10-06 05:29:36] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -30.418226 | E_var:     0.0841 | E_err:   0.004530
[2025-10-06 05:29:38] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -30.428647 | E_var:     0.0618 | E_err:   0.003885
[2025-10-06 05:29:40] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -30.416216 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 05:29:43] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -30.423477 | E_var:     0.0800 | E_err:   0.004419
[2025-10-06 05:29:45] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -30.423395 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 05:29:48] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -30.426523 | E_var:     0.0801 | E_err:   0.004422
[2025-10-06 05:29:50] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -30.418168 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 05:29:52] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -30.423089 | E_var:     0.0722 | E_err:   0.004198
[2025-10-06 05:29:55] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -30.422101 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 05:29:57] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -30.431677 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 05:30:00] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -30.418184 | E_var:     0.0677 | E_err:   0.004065
[2025-10-06 05:30:02] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -30.424312 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 05:30:05] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -30.418808 | E_var:     0.0658 | E_err:   0.004008
[2025-10-06 05:30:07] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -30.422138 | E_var:     0.0593 | E_err:   0.003806
[2025-10-06 05:30:09] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -30.423381 | E_var:     0.0767 | E_err:   0.004328
[2025-10-06 05:30:12] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -30.423837 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 05:30:14] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -30.419582 | E_var:     0.0664 | E_err:   0.004028
[2025-10-06 05:30:17] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -30.415113 | E_var:     0.1064 | E_err:   0.005097
[2025-10-06 05:30:19] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -30.420633 | E_var:     0.0684 | E_err:   0.004088
[2025-10-06 05:30:22] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -30.423736 | E_var:     0.0709 | E_err:   0.004161
[2025-10-06 05:30:24] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -30.419547 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 05:30:26] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -30.416755 | E_var:     0.0929 | E_err:   0.004761
[2025-10-06 05:30:29] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -30.423622 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 05:30:31] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -30.420520 | E_var:     0.1046 | E_err:   0.005052
[2025-10-06 05:30:34] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -30.420273 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 05:30:36] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -30.428694 | E_var:     0.0975 | E_err:   0.004878
[2025-10-06 05:30:38] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -30.429564 | E_var:     0.0615 | E_err:   0.003875
[2025-10-06 05:30:41] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -30.421789 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 05:30:43] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -30.422183 | E_var:     0.0688 | E_err:   0.004099
[2025-10-06 05:30:46] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -30.423987 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 05:30:48] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -30.421335 | E_var:     0.0855 | E_err:   0.004568
[2025-10-06 05:30:50] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -30.421286 | E_var:     0.0636 | E_err:   0.003941
[2025-10-06 05:30:53] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -30.428886 | E_var:     0.0671 | E_err:   0.004049
[2025-10-06 05:30:55] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -30.421746 | E_var:     0.0730 | E_err:   0.004220
[2025-10-06 05:30:58] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -30.426799 | E_var:     0.0689 | E_err:   0.004103
[2025-10-06 05:31:00] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -30.415428 | E_var:     0.0800 | E_err:   0.004420
[2025-10-06 05:31:03] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -30.421431 | E_var:     0.0715 | E_err:   0.004179
[2025-10-06 05:31:05] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -30.423940 | E_var:     0.0647 | E_err:   0.003975
[2025-10-06 05:31:07] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -30.415187 | E_var:     0.0979 | E_err:   0.004889
[2025-10-06 05:31:10] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -30.425905 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 05:31:12] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -30.422779 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 05:31:12] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 05:31:15] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -30.426568 | E_var:     0.0688 | E_err:   0.004099
[2025-10-06 05:31:17] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -30.420496 | E_var:     0.1101 | E_err:   0.005185
[2025-10-06 05:31:19] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -30.430431 | E_var:     0.0594 | E_err:   0.003808
[2025-10-06 05:31:22] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -30.409912 | E_var:     0.1288 | E_err:   0.005607
[2025-10-06 05:31:24] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -30.433625 | E_var:     0.1100 | E_err:   0.005182
[2025-10-06 05:31:27] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -30.420216 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 05:31:29] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -30.423902 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 05:31:32] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -30.422899 | E_var:     0.0744 | E_err:   0.004262
[2025-10-06 05:31:34] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -30.423810 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 05:31:36] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -30.424346 | E_var:     0.0763 | E_err:   0.004316
[2025-10-06 05:31:39] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -30.421520 | E_var:     0.0670 | E_err:   0.004045
[2025-10-06 05:31:41] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -30.412397 | E_var:     0.1842 | E_err:   0.006705
[2025-10-06 05:31:44] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -30.428743 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 05:31:46] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -30.426126 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 05:31:48] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -30.422200 | E_var:     0.0736 | E_err:   0.004239
[2025-10-06 05:31:51] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -30.415295 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 05:31:53] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -30.424425 | E_var:     0.0709 | E_err:   0.004162
[2025-10-06 05:31:56] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -30.421764 | E_var:     0.0679 | E_err:   0.004072
[2025-10-06 05:31:58] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -30.420177 | E_var:     0.0616 | E_err:   0.003879
[2025-10-06 05:32:01] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -30.422943 | E_var:     0.0785 | E_err:   0.004377
[2025-10-06 05:32:03] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -30.421491 | E_var:     0.0703 | E_err:   0.004141
[2025-10-06 05:32:05] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -30.427213 | E_var:     0.0785 | E_err:   0.004379
[2025-10-06 05:32:08] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -30.425440 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 05:32:10] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -30.418803 | E_var:     0.0832 | E_err:   0.004508
[2025-10-06 05:32:13] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -30.413050 | E_var:     0.0710 | E_err:   0.004165
[2025-10-06 05:32:15] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -30.420273 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 05:32:18] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -30.420909 | E_var:     0.0821 | E_err:   0.004477
[2025-10-06 05:32:20] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -30.417769 | E_var:     0.1017 | E_err:   0.004982
[2025-10-06 05:32:22] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -30.418917 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 05:32:25] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -30.423594 | E_var:     0.1064 | E_err:   0.005098
[2025-10-06 05:32:27] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -30.424486 | E_var:     0.2389 | E_err:   0.007638
[2025-10-06 05:32:30] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -30.411049 | E_var:     0.0642 | E_err:   0.003958
[2025-10-06 05:32:32] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -30.425791 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 05:32:34] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -30.414691 | E_var:     0.0869 | E_err:   0.004607
[2025-10-06 05:32:37] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -30.429119 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 05:32:39] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -30.418753 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 05:32:42] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -30.427351 | E_var:     0.0764 | E_err:   0.004317
[2025-10-06 05:32:44] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -30.416318 | E_var:     0.2201 | E_err:   0.007330
[2025-10-06 05:32:47] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -30.415609 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 05:32:49] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -30.412791 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 05:32:51] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -30.420747 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 05:32:54] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -30.421830 | E_var:     0.0867 | E_err:   0.004600
[2025-10-06 05:32:56] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -30.414917 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 05:32:59] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -30.424836 | E_var:     0.0782 | E_err:   0.004369
[2025-10-06 05:33:01] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -30.421274 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 05:33:04] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -30.424250 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 05:33:06] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -30.419054 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 05:33:08] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -30.423887 | E_var:     0.0901 | E_err:   0.004689
[2025-10-06 05:33:11] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -30.422508 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 05:33:13] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -30.410486 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 05:33:16] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -30.415739 | E_var:     0.0670 | E_err:   0.004046
[2025-10-06 05:33:18] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -30.424478 | E_var:     0.0945 | E_err:   0.004803
[2025-10-06 05:33:20] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -30.423239 | E_var:     0.1145 | E_err:   0.005286
[2025-10-06 05:33:23] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -30.424045 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 05:33:25] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -30.421042 | E_var:     0.1573 | E_err:   0.006197
[2025-10-06 05:33:28] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -30.411753 | E_var:     0.1004 | E_err:   0.004950
[2025-10-06 05:33:30] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -30.415420 | E_var:     0.0661 | E_err:   0.004018
[2025-10-06 05:33:32] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -30.423668 | E_var:     0.0976 | E_err:   0.004882
[2025-10-06 05:33:35] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -30.424554 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 05:33:37] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -30.425176 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 05:33:40] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -30.422430 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 05:33:42] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -30.424723 | E_var:     0.0776 | E_err:   0.004352
[2025-10-06 05:33:45] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -30.417407 | E_var:     0.0705 | E_err:   0.004149
[2025-10-06 05:33:47] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -30.417553 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 05:33:49] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -30.419931 | E_var:     0.0906 | E_err:   0.004702
[2025-10-06 05:33:52] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -30.411543 | E_var:     0.0734 | E_err:   0.004232
[2025-10-06 05:33:54] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -30.423416 | E_var:     0.1193 | E_err:   0.005396
[2025-10-06 05:33:57] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -30.415881 | E_var:     0.1183 | E_err:   0.005375
[2025-10-06 05:33:59] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -30.424889 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 05:34:01] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -30.415019 | E_var:     0.1444 | E_err:   0.005938
[2025-10-06 05:34:04] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -30.431626 | E_var:     0.0770 | E_err:   0.004334
[2025-10-06 05:34:06] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -30.428406 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 05:34:09] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -30.424938 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 05:34:11] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -30.423828 | E_var:     0.1081 | E_err:   0.005137
[2025-10-06 05:34:14] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -30.423841 | E_var:     0.0761 | E_err:   0.004311
[2025-10-06 05:34:16] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -30.428452 | E_var:     0.1279 | E_err:   0.005587
[2025-10-06 05:34:18] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -30.423189 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 05:34:21] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -30.418396 | E_var:     0.0771 | E_err:   0.004338
[2025-10-06 05:34:23] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -30.431289 | E_var:     0.0701 | E_err:   0.004137
[2025-10-06 05:34:26] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -30.420246 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 05:34:28] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -30.426424 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 05:34:30] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -30.421386 | E_var:     0.0712 | E_err:   0.004168
[2025-10-06 05:34:33] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -30.420768 | E_var:     0.0835 | E_err:   0.004516
[2025-10-06 05:34:35] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -30.427206 | E_var:     0.1254 | E_err:   0.005532
[2025-10-06 05:34:38] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -30.420886 | E_var:     0.0880 | E_err:   0.004636
[2025-10-06 05:34:40] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -30.419285 | E_var:     0.0760 | E_err:   0.004307
[2025-10-06 05:34:42] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -30.421515 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 05:34:45] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -30.414900 | E_var:     0.0994 | E_err:   0.004927
[2025-10-06 05:34:47] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -30.425467 | E_var:     0.1560 | E_err:   0.006171
[2025-10-06 05:34:50] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -30.431579 | E_var:     0.0807 | E_err:   0.004439
[2025-10-06 05:34:52] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -30.419721 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 05:34:55] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -30.425551 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 05:34:57] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -30.421392 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 05:34:59] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -30.423616 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 05:35:02] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -30.421348 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 05:35:04] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -30.419307 | E_var:     0.0760 | E_err:   0.004308
[2025-10-06 05:35:07] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -30.424969 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 05:35:09] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -30.426221 | E_var:     0.0699 | E_err:   0.004132
[2025-10-06 05:35:11] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -30.418977 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 05:35:14] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -30.421425 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 05:35:14] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 05:35:16] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -30.419633 | E_var:     0.0830 | E_err:   0.004502
[2025-10-06 05:35:19] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -30.424289 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 05:35:21] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -30.417413 | E_var:     0.0773 | E_err:   0.004344
[2025-10-06 05:35:23] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -30.418050 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 05:35:26] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -30.420992 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 05:35:28] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -30.424201 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 05:35:31] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -30.421192 | E_var:     0.0706 | E_err:   0.004151
[2025-10-06 05:35:33] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -30.420207 | E_var:     0.1269 | E_err:   0.005566
[2025-10-06 05:35:36] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -30.425995 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 05:35:38] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -30.429518 | E_var:     0.1251 | E_err:   0.005527
[2025-10-06 05:35:40] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -30.428818 | E_var:     0.0840 | E_err:   0.004530
[2025-10-06 05:35:43] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -30.425738 | E_var:     0.0693 | E_err:   0.004114
[2025-10-06 05:35:45] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -30.427176 | E_var:     0.0827 | E_err:   0.004495
[2025-10-06 05:35:48] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -30.421199 | E_var:     0.0820 | E_err:   0.004475
[2025-10-06 05:35:50] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -30.421958 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 05:35:52] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -30.421628 | E_var:     0.0737 | E_err:   0.004242
[2025-10-06 05:35:55] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -30.429528 | E_var:     0.0853 | E_err:   0.004562
[2025-10-06 05:35:57] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -30.422024 | E_var:     0.0693 | E_err:   0.004112
[2025-10-06 05:36:00] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -30.436236 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 05:36:02] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -30.416611 | E_var:     0.0746 | E_err:   0.004268
[2025-10-06 05:36:05] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -30.417358 | E_var:     0.0699 | E_err:   0.004133
[2025-10-06 05:36:07] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -30.426230 | E_var:     0.0760 | E_err:   0.004306
[2025-10-06 05:36:09] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -30.425466 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 05:36:12] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -30.412893 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 05:36:14] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -30.421727 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 05:36:17] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -30.419390 | E_var:     0.0741 | E_err:   0.004252
[2025-10-06 05:36:19] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -30.424865 | E_var:     0.0751 | E_err:   0.004282
[2025-10-06 05:36:21] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -30.417391 | E_var:     0.0889 | E_err:   0.004660
[2025-10-06 05:36:24] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -30.425874 | E_var:     0.0870 | E_err:   0.004610
[2025-10-06 05:36:26] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -30.419535 | E_var:     0.1026 | E_err:   0.005006
[2025-10-06 05:36:29] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -30.422633 | E_var:     0.0818 | E_err:   0.004470
[2025-10-06 05:36:31] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -30.425806 | E_var:     0.1121 | E_err:   0.005233
[2025-10-06 05:36:33] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -30.419505 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 05:36:36] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -30.425260 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 05:36:38] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -30.427473 | E_var:     0.0653 | E_err:   0.003993
[2025-10-06 05:36:41] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -30.418939 | E_var:     0.0655 | E_err:   0.004000
[2025-10-06 05:36:43] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -30.421346 | E_var:     0.0654 | E_err:   0.003996
[2025-10-06 05:36:46] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -30.421180 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 05:36:48] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -30.421154 | E_var:     0.0820 | E_err:   0.004475
[2025-10-06 05:36:50] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -30.412335 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 05:36:53] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -30.417417 | E_var:     0.0710 | E_err:   0.004165
[2025-10-06 05:36:55] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -30.431139 | E_var:     0.0670 | E_err:   0.004045
[2025-10-06 05:36:58] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -30.415500 | E_var:     0.2164 | E_err:   0.007269
[2025-10-06 05:37:00] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -30.419125 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 05:37:02] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -30.421561 | E_var:     0.0661 | E_err:   0.004017
[2025-10-06 05:37:05] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -30.420748 | E_var:     0.0631 | E_err:   0.003925
[2025-10-06 05:37:07] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -30.421457 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 05:37:10] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -30.421125 | E_var:     0.0718 | E_err:   0.004186
[2025-10-06 05:37:12] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -30.423302 | E_var:     0.0836 | E_err:   0.004518
[2025-10-06 05:37:15] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -30.421995 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 05:37:17] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -30.416880 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 05:37:19] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -30.427502 | E_var:     0.0589 | E_err:   0.003792
[2025-10-06 05:37:22] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -30.415912 | E_var:     0.0776 | E_err:   0.004353
[2025-10-06 05:37:24] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -30.421300 | E_var:     0.0663 | E_err:   0.004022
[2025-10-06 05:37:27] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -30.409579 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 05:37:29] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -30.421046 | E_var:     0.0755 | E_err:   0.004293
[2025-10-06 05:37:31] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -30.422855 | E_var:     0.0724 | E_err:   0.004203
[2025-10-06 05:37:34] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -30.424424 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 05:37:36] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -30.424609 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 05:37:39] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -30.422810 | E_var:     0.1012 | E_err:   0.004972
[2025-10-06 05:37:41] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -30.423382 | E_var:     0.0622 | E_err:   0.003898
[2025-10-06 05:37:44] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -30.427745 | E_var:     0.2289 | E_err:   0.007475
[2025-10-06 05:37:46] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -30.422508 | E_var:     0.0897 | E_err:   0.004681
[2025-10-06 05:37:48] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -30.421429 | E_var:     0.0683 | E_err:   0.004083
[2025-10-06 05:37:51] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -30.419128 | E_var:     0.0971 | E_err:   0.004869
[2025-10-06 05:37:53] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -30.421714 | E_var:     0.0689 | E_err:   0.004100
[2025-10-06 05:37:56] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -30.413293 | E_var:     0.0640 | E_err:   0.003952
[2025-10-06 05:37:58] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -30.421906 | E_var:     0.0664 | E_err:   0.004025
[2025-10-06 05:38:00] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -30.422051 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 05:38:03] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -30.415379 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 05:38:05] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -30.421815 | E_var:     0.0706 | E_err:   0.004151
[2025-10-06 05:38:08] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -30.418055 | E_var:     0.0920 | E_err:   0.004740
[2025-10-06 05:38:10] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -30.419990 | E_var:     0.1238 | E_err:   0.005497
[2025-10-06 05:38:13] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -30.428428 | E_var:     0.0735 | E_err:   0.004235
[2025-10-06 05:38:15] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -30.419528 | E_var:     0.0665 | E_err:   0.004028
[2025-10-06 05:38:17] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -30.414034 | E_var:     0.0888 | E_err:   0.004656
[2025-10-06 05:38:20] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -30.419380 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 05:38:22] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -30.429950 | E_var:     0.0764 | E_err:   0.004320
[2025-10-06 05:38:25] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -30.419132 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 05:38:27] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -30.421185 | E_var:     0.0715 | E_err:   0.004178
[2025-10-06 05:38:29] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -30.412951 | E_var:     0.1226 | E_err:   0.005472
[2025-10-06 05:38:32] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -30.419018 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 05:38:34] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -30.429821 | E_var:     0.0745 | E_err:   0.004264
[2025-10-06 05:38:37] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -30.417870 | E_var:     0.0872 | E_err:   0.004615
[2025-10-06 05:38:39] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -30.419805 | E_var:     0.0919 | E_err:   0.004736
[2025-10-06 05:38:42] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -30.424036 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 05:38:44] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -30.420704 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 05:38:46] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -30.427980 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 05:38:49] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -30.424013 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 05:38:51] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -30.422576 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 05:38:54] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -30.423048 | E_var:     0.0717 | E_err:   0.004185
[2025-10-06 05:38:56] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -30.421690 | E_var:     0.0735 | E_err:   0.004237
[2025-10-06 05:38:58] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -30.421610 | E_var:     0.0598 | E_err:   0.003822
[2025-10-06 05:39:01] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -30.419932 | E_var:     0.0650 | E_err:   0.003983
[2025-10-06 05:39:03] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -30.421439 | E_var:     0.1020 | E_err:   0.004989
[2025-10-06 05:39:06] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -30.417235 | E_var:     0.0627 | E_err:   0.003913
[2025-10-06 05:39:08] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -30.417428 | E_var:     0.0636 | E_err:   0.003939
[2025-10-06 05:39:10] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -30.425411 | E_var:     0.0797 | E_err:   0.004412
[2025-10-06 05:39:13] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -30.424547 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 05:39:15] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -30.428265 | E_var:     0.0957 | E_err:   0.004832
[2025-10-06 05:39:15] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 05:39:18] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -30.411064 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 05:39:20] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -30.427295 | E_var:     0.0621 | E_err:   0.003893
[2025-10-06 05:39:23] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -30.423680 | E_var:     0.0691 | E_err:   0.004108
[2025-10-06 05:39:25] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -30.419373 | E_var:     0.0855 | E_err:   0.004568
[2025-10-06 05:39:27] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -30.416658 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 05:39:30] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -30.416484 | E_var:     0.0970 | E_err:   0.004866
[2025-10-06 05:39:32] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -30.415801 | E_var:     0.0613 | E_err:   0.003869
[2025-10-06 05:39:35] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -30.419317 | E_var:     0.0627 | E_err:   0.003912
[2025-10-06 05:39:37] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -30.431568 | E_var:     0.2184 | E_err:   0.007303
[2025-10-06 05:39:39] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -30.419900 | E_var:     0.0687 | E_err:   0.004097
[2025-10-06 05:39:42] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -30.425059 | E_var:     0.1253 | E_err:   0.005531
[2025-10-06 05:39:44] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -30.422665 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 05:39:47] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -30.419235 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 05:39:49] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -30.419892 | E_var:     0.1268 | E_err:   0.005565
[2025-10-06 05:39:52] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -30.423449 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 05:39:54] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -30.423047 | E_var:     0.0916 | E_err:   0.004730
[2025-10-06 05:39:56] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -30.415037 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 05:39:59] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -30.422998 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 05:40:01] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -30.421969 | E_var:     0.0739 | E_err:   0.004249
[2025-10-06 05:40:04] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -30.417164 | E_var:     0.0758 | E_err:   0.004302
[2025-10-06 05:40:06] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -30.418250 | E_var:     0.0757 | E_err:   0.004298
[2025-10-06 05:40:08] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -30.426814 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 05:40:11] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -30.422546 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 05:40:13] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -30.422996 | E_var:     0.0673 | E_err:   0.004052
[2025-10-06 05:40:16] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -30.417821 | E_var:     0.0889 | E_err:   0.004660
[2025-10-06 05:40:18] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -30.421899 | E_var:     0.0725 | E_err:   0.004208
[2025-10-06 05:40:20] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -30.423378 | E_var:     0.0698 | E_err:   0.004128
[2025-10-06 05:40:23] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -30.418420 | E_var:     0.0806 | E_err:   0.004437
[2025-10-06 05:40:25] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -30.418553 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 05:40:28] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -30.431102 | E_var:     0.1476 | E_err:   0.006004
[2025-10-06 05:40:30] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -30.423332 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 05:40:33] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -30.419198 | E_var:     0.0647 | E_err:   0.003975
[2025-10-06 05:40:35] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -30.417939 | E_var:     0.0661 | E_err:   0.004017
[2025-10-06 05:40:37] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -30.424283 | E_var:     0.0718 | E_err:   0.004186
[2025-10-06 05:40:40] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -30.421693 | E_var:     0.0618 | E_err:   0.003884
[2025-10-06 05:40:42] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -30.428011 | E_var:     0.0817 | E_err:   0.004467
[2025-10-06 05:40:45] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -30.414193 | E_var:     0.0747 | E_err:   0.004271
[2025-10-06 05:40:47] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -30.411989 | E_var:     0.1642 | E_err:   0.006331
[2025-10-06 05:40:49] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -30.424341 | E_var:     0.0736 | E_err:   0.004239
[2025-10-06 05:40:52] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -30.420000 | E_var:     0.0616 | E_err:   0.003877
[2025-10-06 05:40:54] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -30.419765 | E_var:     0.0731 | E_err:   0.004226
[2025-10-06 05:40:57] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -30.423434 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 05:40:59] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -30.425686 | E_var:     0.0649 | E_err:   0.003979
[2025-10-06 05:41:01] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -30.421233 | E_var:     0.0854 | E_err:   0.004566
[2025-10-06 05:41:04] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -30.422582 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 05:41:06] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -30.428650 | E_var:     0.0737 | E_err:   0.004243
[2025-10-06 05:41:09] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -30.411981 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 05:41:11] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -30.421041 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 05:41:14] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -30.421588 | E_var:     0.0909 | E_err:   0.004711
[2025-10-06 05:41:16] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -30.419625 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 05:41:16] 🔄 RESTART #2 | Period: 600
[2025-10-06 05:41:18] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -30.426471 | E_var:     0.0733 | E_err:   0.004230
[2025-10-06 05:41:21] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -30.417619 | E_var:     0.0695 | E_err:   0.004120
[2025-10-06 05:41:23] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -30.416990 | E_var:     0.0781 | E_err:   0.004366
[2025-10-06 05:41:26] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -30.417400 | E_var:     0.0683 | E_err:   0.004082
[2025-10-06 05:41:28] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -30.419823 | E_var:     0.1096 | E_err:   0.005174
[2025-10-06 05:41:30] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -30.421487 | E_var:     0.0827 | E_err:   0.004492
[2025-10-06 05:41:33] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -30.422225 | E_var:     0.0689 | E_err:   0.004103
[2025-10-06 05:41:35] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -30.422316 | E_var:     0.0618 | E_err:   0.003883
[2025-10-06 05:41:38] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -30.421926 | E_var:     0.0750 | E_err:   0.004280
[2025-10-06 05:41:40] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -30.420901 | E_var:     0.0651 | E_err:   0.003987
[2025-10-06 05:41:42] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -30.418682 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 05:41:45] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -30.420168 | E_var:     0.0776 | E_err:   0.004353
[2025-10-06 05:41:47] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -30.417819 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 05:41:50] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -30.425950 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 05:41:52] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -30.421572 | E_var:     0.0689 | E_err:   0.004101
[2025-10-06 05:41:55] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -30.419617 | E_var:     0.3253 | E_err:   0.008912
[2025-10-06 05:41:57] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -30.423911 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 05:41:59] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -30.415661 | E_var:     0.1005 | E_err:   0.004953
[2025-10-06 05:42:02] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -30.428235 | E_var:     0.0656 | E_err:   0.004001
[2025-10-06 05:42:04] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -30.425645 | E_var:     0.0939 | E_err:   0.004787
[2025-10-06 05:42:07] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -30.425229 | E_var:     0.0601 | E_err:   0.003831
[2025-10-06 05:42:09] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -30.423084 | E_var:     0.1264 | E_err:   0.005555
[2025-10-06 05:42:11] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -30.421356 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 05:42:14] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -30.421117 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 05:42:16] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -30.421621 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 05:42:19] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -30.425663 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 05:42:21] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -30.423371 | E_var:     0.0615 | E_err:   0.003876
[2025-10-06 05:42:24] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -30.420776 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 05:42:26] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -30.423029 | E_var:     0.0636 | E_err:   0.003940
[2025-10-06 05:42:28] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -30.419381 | E_var:     0.0845 | E_err:   0.004543
[2025-10-06 05:42:31] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -30.425497 | E_var:     0.0808 | E_err:   0.004442
[2025-10-06 05:42:33] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -30.417991 | E_var:     0.0697 | E_err:   0.004126
[2025-10-06 05:42:36] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -30.423870 | E_var:     0.0664 | E_err:   0.004025
[2025-10-06 05:42:38] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -30.412957 | E_var:     0.0695 | E_err:   0.004120
[2025-10-06 05:42:40] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -30.423792 | E_var:     0.0707 | E_err:   0.004156
[2025-10-06 05:42:43] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -30.417507 | E_var:     0.0730 | E_err:   0.004222
[2025-10-06 05:42:45] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -30.425651 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 05:42:48] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -30.413797 | E_var:     0.1216 | E_err:   0.005449
[2025-10-06 05:42:50] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -30.418798 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 05:42:52] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -30.416457 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 05:42:55] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -30.434460 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 05:42:57] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -30.422539 | E_var:     0.0843 | E_err:   0.004536
[2025-10-06 05:43:00] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -30.427150 | E_var:     0.0719 | E_err:   0.004189
[2025-10-06 05:43:02] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -30.419477 | E_var:     0.0631 | E_err:   0.003926
[2025-10-06 05:43:05] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -30.433679 | E_var:     0.0828 | E_err:   0.004495
[2025-10-06 05:43:07] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -30.421570 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 05:43:09] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -30.429476 | E_var:     0.1584 | E_err:   0.006219
[2025-10-06 05:43:12] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -30.422416 | E_var:     0.0715 | E_err:   0.004177
[2025-10-06 05:43:14] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -30.430882 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 05:43:17] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -30.425117 | E_var:     0.0648 | E_err:   0.003976
[2025-10-06 05:43:17] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 05:43:19] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -30.420587 | E_var:     0.0566 | E_err:   0.003717
[2025-10-06 05:43:21] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -30.419575 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 05:43:24] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -30.412271 | E_var:     0.0792 | E_err:   0.004398
[2025-10-06 05:43:26] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -30.419358 | E_var:     0.0919 | E_err:   0.004738
[2025-10-06 05:43:29] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -30.427511 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 05:43:31] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -30.416439 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 05:43:33] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -30.421050 | E_var:     0.0588 | E_err:   0.003790
[2025-10-06 05:43:36] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -30.424925 | E_var:     0.0856 | E_err:   0.004573
[2025-10-06 05:43:38] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -30.431534 | E_var:     0.1021 | E_err:   0.004994
[2025-10-06 05:43:41] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -30.416195 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 05:43:43] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -30.424353 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 05:43:45] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -30.420059 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 05:43:48] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -30.418596 | E_var:     0.0846 | E_err:   0.004546
[2025-10-06 05:43:50] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -30.426362 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 05:43:53] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -30.417939 | E_var:     0.0629 | E_err:   0.003918
[2025-10-06 05:43:55] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -30.421829 | E_var:     0.0718 | E_err:   0.004186
[2025-10-06 05:43:58] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -30.424258 | E_var:     0.0709 | E_err:   0.004159
[2025-10-06 05:44:00] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -30.412748 | E_var:     0.0726 | E_err:   0.004210
[2025-10-06 05:44:02] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -30.424510 | E_var:     0.0632 | E_err:   0.003929
[2025-10-06 05:44:05] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -30.420813 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 05:44:07] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -30.416821 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 05:44:10] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -30.422596 | E_var:     0.0759 | E_err:   0.004303
[2025-10-06 05:44:12] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -30.422347 | E_var:     0.0928 | E_err:   0.004761
[2025-10-06 05:44:14] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -30.417641 | E_var:     0.0890 | E_err:   0.004660
[2025-10-06 05:44:17] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -30.428913 | E_var:     0.0795 | E_err:   0.004404
[2025-10-06 05:44:19] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -30.427621 | E_var:     0.0771 | E_err:   0.004340
[2025-10-06 05:44:22] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -30.417990 | E_var:     0.0605 | E_err:   0.003843
[2025-10-06 05:44:24] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -30.426383 | E_var:     0.0624 | E_err:   0.003902
[2025-10-06 05:44:26] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -30.422768 | E_var:     0.0803 | E_err:   0.004428
[2025-10-06 05:44:29] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -30.418609 | E_var:     0.0854 | E_err:   0.004566
[2025-10-06 05:44:31] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -30.426416 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 05:44:34] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -30.425914 | E_var:     0.0664 | E_err:   0.004027
[2025-10-06 05:44:36] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -30.419950 | E_var:     0.0646 | E_err:   0.003973
[2025-10-06 05:44:39] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -30.421026 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 05:44:41] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -30.424201 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 05:44:43] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -30.416121 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 05:44:46] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -30.418602 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 05:44:48] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -30.419345 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 05:44:51] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -30.420545 | E_var:     0.0721 | E_err:   0.004194
[2025-10-06 05:44:53] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -30.418428 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 05:44:55] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -30.426099 | E_var:     0.0725 | E_err:   0.004207
[2025-10-06 05:44:58] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -30.420886 | E_var:     0.0778 | E_err:   0.004359
[2025-10-06 05:45:00] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -30.436601 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 05:45:03] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -30.423687 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 05:45:05] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -30.418238 | E_var:     0.0783 | E_err:   0.004372
[2025-10-06 05:45:08] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -30.422616 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 05:45:10] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -30.423118 | E_var:     0.0615 | E_err:   0.003875
[2025-10-06 05:45:12] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -30.421151 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 05:45:15] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -30.420060 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 05:45:17] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -30.423586 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 05:45:20] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -30.421050 | E_var:     0.0625 | E_err:   0.003906
[2025-10-06 05:45:22] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -30.427364 | E_var:     0.0712 | E_err:   0.004170
[2025-10-06 05:45:24] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -30.418072 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 05:45:27] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -30.422019 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 05:45:29] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -30.422051 | E_var:     0.0815 | E_err:   0.004460
[2025-10-06 05:45:32] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -30.419336 | E_var:     0.0801 | E_err:   0.004421
[2025-10-06 05:45:34] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -30.419902 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 05:45:36] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -30.420402 | E_var:     0.0699 | E_err:   0.004131
[2025-10-06 05:45:39] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -30.420148 | E_var:     0.0732 | E_err:   0.004228
[2025-10-06 05:45:41] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -30.420327 | E_var:     0.0633 | E_err:   0.003930
[2025-10-06 05:45:44] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -30.420919 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 05:45:46] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -30.419272 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 05:45:49] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -30.418153 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 05:45:51] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -30.423678 | E_var:     0.0621 | E_err:   0.003894
[2025-10-06 05:45:53] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -30.419430 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 05:45:56] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -30.426554 | E_var:     0.0709 | E_err:   0.004160
[2025-10-06 05:45:58] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -30.422427 | E_var:     0.1029 | E_err:   0.005011
[2025-10-06 05:46:01] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -30.427963 | E_var:     0.0685 | E_err:   0.004090
[2025-10-06 05:46:03] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -30.417084 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 05:46:05] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -30.420002 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 05:46:08] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -30.434797 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 05:46:10] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -30.425945 | E_var:     0.0653 | E_err:   0.003993
[2025-10-06 05:46:13] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -30.430350 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 05:46:15] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -30.423870 | E_var:     0.1038 | E_err:   0.005035
[2025-10-06 05:46:18] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -30.429945 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 05:46:20] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -30.420532 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 05:46:22] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -30.428611 | E_var:     0.0737 | E_err:   0.004242
[2025-10-06 05:46:25] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -30.423676 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 05:46:27] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -30.417260 | E_var:     0.0682 | E_err:   0.004080
[2025-10-06 05:46:30] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -30.420742 | E_var:     0.1311 | E_err:   0.005657
[2025-10-06 05:46:32] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -30.415961 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 05:46:34] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -30.424034 | E_var:     0.0732 | E_err:   0.004226
[2025-10-06 05:46:37] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -30.425467 | E_var:     0.1954 | E_err:   0.006906
[2025-10-06 05:46:39] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -30.415839 | E_var:     0.0767 | E_err:   0.004327
[2025-10-06 05:46:42] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -30.420966 | E_var:     0.0679 | E_err:   0.004070
[2025-10-06 05:46:44] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -30.429112 | E_var:     0.0566 | E_err:   0.003716
[2025-10-06 05:46:46] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -30.420844 | E_var:     0.0652 | E_err:   0.003990
[2025-10-06 05:46:49] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -30.420442 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 05:46:51] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -30.420962 | E_var:     0.0746 | E_err:   0.004266
[2025-10-06 05:46:54] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -30.416431 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 05:46:56] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -30.416046 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 05:46:59] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -30.413752 | E_var:     0.0655 | E_err:   0.003998
[2025-10-06 05:47:01] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -30.424108 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 05:47:03] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -30.418721 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 05:47:06] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -30.426113 | E_var:     0.1062 | E_err:   0.005093
[2025-10-06 05:47:08] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -30.427089 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 05:47:11] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -30.416459 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 05:47:13] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -30.424812 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 05:47:15] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -30.428132 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 05:47:18] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -30.417318 | E_var:     0.0787 | E_err:   0.004384
[2025-10-06 05:47:18] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 05:47:20] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -30.415548 | E_var:     0.0559 | E_err:   0.003694
[2025-10-06 05:47:23] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -30.422082 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 05:47:25] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -30.416019 | E_var:     0.0650 | E_err:   0.003984
[2025-10-06 05:47:27] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -30.425244 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 05:47:30] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -30.426699 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 05:47:32] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -30.425149 | E_var:     0.0629 | E_err:   0.003919
[2025-10-06 05:47:35] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -30.420978 | E_var:     0.0750 | E_err:   0.004280
[2025-10-06 05:47:37] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -30.422495 | E_var:     0.0790 | E_err:   0.004391
[2025-10-06 05:47:40] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -30.422747 | E_var:     0.0654 | E_err:   0.003997
[2025-10-06 05:47:42] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -30.423396 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 05:47:44] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -30.427554 | E_var:     0.0775 | E_err:   0.004349
[2025-10-06 05:47:47] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -30.419865 | E_var:     0.0644 | E_err:   0.003965
[2025-10-06 05:47:49] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -30.428267 | E_var:     0.0700 | E_err:   0.004135
[2025-10-06 05:47:52] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -30.426601 | E_var:     0.0959 | E_err:   0.004838
[2025-10-06 05:47:54] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -30.415420 | E_var:     0.0787 | E_err:   0.004382
[2025-10-06 05:47:56] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -30.422313 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 05:47:59] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -30.422166 | E_var:     0.0705 | E_err:   0.004148
[2025-10-06 05:48:01] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -30.414769 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 05:48:04] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -30.422487 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 05:48:06] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -30.427298 | E_var:     0.0733 | E_err:   0.004229
[2025-10-06 05:48:08] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -30.425178 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 05:48:11] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -30.420856 | E_var:     0.0683 | E_err:   0.004082
[2025-10-06 05:48:13] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -30.421714 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 05:48:16] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -30.426910 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 05:48:18] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -30.414742 | E_var:     0.1068 | E_err:   0.005106
[2025-10-06 05:48:21] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -30.418586 | E_var:     0.0603 | E_err:   0.003836
[2025-10-06 05:48:23] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -30.419005 | E_var:     0.0705 | E_err:   0.004149
[2025-10-06 05:48:25] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -30.406531 | E_var:     0.0917 | E_err:   0.004731
[2025-10-06 05:48:28] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -30.414852 | E_var:     0.2210 | E_err:   0.007346
[2025-10-06 05:48:30] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -30.419445 | E_var:     0.0785 | E_err:   0.004378
[2025-10-06 05:48:33] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -30.430085 | E_var:     0.0671 | E_err:   0.004046
[2025-10-06 05:48:35] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -30.427676 | E_var:     0.0714 | E_err:   0.004176
[2025-10-06 05:48:37] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -30.422220 | E_var:     0.0739 | E_err:   0.004248
[2025-10-06 05:48:40] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -30.426915 | E_var:     0.0692 | E_err:   0.004110
[2025-10-06 05:48:42] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -30.417946 | E_var:     0.2278 | E_err:   0.007457
[2025-10-06 05:48:45] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -30.426178 | E_var:     0.0878 | E_err:   0.004630
[2025-10-06 05:48:47] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -30.421822 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 05:48:50] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -30.418852 | E_var:     0.0719 | E_err:   0.004189
[2025-10-06 05:48:52] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -30.428137 | E_var:     0.0570 | E_err:   0.003729
[2025-10-06 05:48:54] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -30.419914 | E_var:     0.0639 | E_err:   0.003950
[2025-10-06 05:48:57] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -30.422549 | E_var:     0.0726 | E_err:   0.004210
[2025-10-06 05:48:59] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -30.421303 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 05:49:02] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -30.418473 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 05:49:04] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -30.415800 | E_var:     0.0730 | E_err:   0.004222
[2025-10-06 05:49:06] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -30.424990 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 05:49:09] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -30.420158 | E_var:     0.0602 | E_err:   0.003835
[2025-10-06 05:49:11] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -30.422727 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 05:49:14] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -30.430774 | E_var:     0.0732 | E_err:   0.004228
[2025-10-06 05:49:16] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -30.417573 | E_var:     0.0693 | E_err:   0.004115
[2025-10-06 05:49:19] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -30.417227 | E_var:     0.0731 | E_err:   0.004225
[2025-10-06 05:49:21] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -30.415109 | E_var:     0.0680 | E_err:   0.004074
[2025-10-06 05:49:23] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -30.420802 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 05:49:26] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -30.424389 | E_var:     0.0945 | E_err:   0.004803
[2025-10-06 05:49:28] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -30.428655 | E_var:     0.0709 | E_err:   0.004161
[2025-10-06 05:49:31] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -30.426141 | E_var:     0.0699 | E_err:   0.004131
[2025-10-06 05:49:33] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -30.421073 | E_var:     0.0736 | E_err:   0.004240
[2025-10-06 05:49:35] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -30.423465 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 05:49:38] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -30.429180 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 05:49:40] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -30.427854 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 05:49:43] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -30.417551 | E_var:     0.0634 | E_err:   0.003935
[2025-10-06 05:49:45] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -30.414527 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 05:49:47] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -30.412399 | E_var:     0.1255 | E_err:   0.005535
[2025-10-06 05:49:50] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -30.417626 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 05:49:52] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -30.420023 | E_var:     0.0608 | E_err:   0.003853
[2025-10-06 05:49:55] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -30.427146 | E_var:     0.1796 | E_err:   0.006621
[2025-10-06 05:49:57] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -30.417990 | E_var:     0.0564 | E_err:   0.003712
[2025-10-06 05:50:00] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -30.429204 | E_var:     0.0855 | E_err:   0.004569
[2025-10-06 05:50:02] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -30.425125 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 05:50:04] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -30.429288 | E_var:     0.0768 | E_err:   0.004331
[2025-10-06 05:50:07] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -30.423504 | E_var:     0.0695 | E_err:   0.004121
[2025-10-06 05:50:09] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -30.424290 | E_var:     0.0668 | E_err:   0.004039
[2025-10-06 05:50:12] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -30.423313 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 05:50:14] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -30.419679 | E_var:     0.0834 | E_err:   0.004511
[2025-10-06 05:50:16] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -30.420424 | E_var:     0.0726 | E_err:   0.004209
[2025-10-06 05:50:19] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -30.431911 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 05:50:21] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -30.425620 | E_var:     0.0878 | E_err:   0.004631
[2025-10-06 05:50:24] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -30.428795 | E_var:     0.0670 | E_err:   0.004045
[2025-10-06 05:50:26] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -30.421278 | E_var:     0.0634 | E_err:   0.003934
[2025-10-06 05:50:29] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -30.417062 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 05:50:31] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -30.421824 | E_var:     0.0702 | E_err:   0.004141
[2025-10-06 05:50:33] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -30.420505 | E_var:     0.0622 | E_err:   0.003898
[2025-10-06 05:50:36] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -30.425574 | E_var:     0.1233 | E_err:   0.005488
[2025-10-06 05:50:38] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -30.424671 | E_var:     0.0726 | E_err:   0.004210
[2025-10-06 05:50:41] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -30.422088 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 05:50:43] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -30.422545 | E_var:     0.0616 | E_err:   0.003879
[2025-10-06 05:50:45] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -30.426096 | E_var:     0.0757 | E_err:   0.004299
[2025-10-06 05:50:48] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -30.425411 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 05:50:50] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -30.420188 | E_var:     0.0966 | E_err:   0.004855
[2025-10-06 05:50:53] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -30.423308 | E_var:     0.0691 | E_err:   0.004108
[2025-10-06 05:50:55] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -30.426895 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 05:50:57] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -30.422187 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 05:51:00] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -30.422737 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 05:51:02] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -30.416859 | E_var:     0.0963 | E_err:   0.004850
[2025-10-06 05:51:05] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -30.415633 | E_var:     0.0773 | E_err:   0.004344
[2025-10-06 05:51:07] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -30.420398 | E_var:     0.0936 | E_err:   0.004779
[2025-10-06 05:51:10] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -30.422725 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 05:51:12] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -30.430673 | E_var:     0.1460 | E_err:   0.005969
[2025-10-06 05:51:14] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -30.433292 | E_var:     0.0676 | E_err:   0.004063
[2025-10-06 05:51:17] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -30.421114 | E_var:     0.0658 | E_err:   0.004009
[2025-10-06 05:51:19] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -30.424287 | E_var:     0.0776 | E_err:   0.004354
[2025-10-06 05:51:19] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 05:51:22] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -30.420372 | E_var:     0.0878 | E_err:   0.004631
[2025-10-06 05:51:24] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -30.433356 | E_var:     0.0638 | E_err:   0.003948
[2025-10-06 05:51:26] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -30.425054 | E_var:     0.0747 | E_err:   0.004270
[2025-10-06 05:51:29] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -30.426701 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 05:51:31] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -30.419125 | E_var:     0.0643 | E_err:   0.003963
[2025-10-06 05:51:34] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -30.425321 | E_var:     0.0670 | E_err:   0.004043
[2025-10-06 05:51:36] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -30.423813 | E_var:     0.0666 | E_err:   0.004032
[2025-10-06 05:51:39] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -30.422194 | E_var:     0.0787 | E_err:   0.004385
[2025-10-06 05:51:41] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -30.416916 | E_var:     0.1190 | E_err:   0.005391
[2025-10-06 05:51:43] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -30.430092 | E_var:     0.0994 | E_err:   0.004926
[2025-10-06 05:51:46] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -30.422913 | E_var:     0.0685 | E_err:   0.004088
[2025-10-06 05:51:48] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -30.422071 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 05:51:51] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -30.423142 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 05:51:53] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -30.422297 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 05:51:55] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -30.427219 | E_var:     0.0715 | E_err:   0.004178
[2025-10-06 05:51:58] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -30.423955 | E_var:     0.1041 | E_err:   0.005041
[2025-10-06 05:52:00] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -30.428259 | E_var:     0.0648 | E_err:   0.003977
[2025-10-06 05:52:03] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -30.419600 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 05:52:05] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -30.425655 | E_var:     0.0801 | E_err:   0.004423
[2025-10-06 05:52:08] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -30.421438 | E_var:     0.0858 | E_err:   0.004578
[2025-10-06 05:52:10] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -30.421232 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 05:52:12] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -30.424461 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 05:52:15] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -30.416858 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 05:52:17] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -30.419411 | E_var:     0.0878 | E_err:   0.004630
[2025-10-06 05:52:20] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -30.426770 | E_var:     0.0733 | E_err:   0.004231
[2025-10-06 05:52:22] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -30.428511 | E_var:     0.0657 | E_err:   0.004004
[2025-10-06 05:52:24] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -30.417936 | E_var:     0.0702 | E_err:   0.004140
[2025-10-06 05:52:27] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -30.418800 | E_var:     0.0684 | E_err:   0.004087
[2025-10-06 05:52:29] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -30.416113 | E_var:     0.0805 | E_err:   0.004432
[2025-10-06 05:52:32] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -30.427045 | E_var:     0.0677 | E_err:   0.004066
[2025-10-06 05:52:34] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -30.428795 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 05:52:36] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -30.421814 | E_var:     0.0779 | E_err:   0.004360
[2025-10-06 05:52:39] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -30.423890 | E_var:     0.1248 | E_err:   0.005521
[2025-10-06 05:52:41] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -30.420624 | E_var:     0.0720 | E_err:   0.004192
[2025-10-06 05:52:44] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -30.412976 | E_var:     0.0799 | E_err:   0.004418
[2025-10-06 05:52:46] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -30.424962 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 05:52:48] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -30.419512 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 05:52:51] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -30.421216 | E_var:     0.0631 | E_err:   0.003926
[2025-10-06 05:52:53] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -30.417856 | E_var:     0.0674 | E_err:   0.004056
[2025-10-06 05:52:56] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -30.421020 | E_var:     0.0832 | E_err:   0.004508
[2025-10-06 05:52:58] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -30.410292 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 05:53:01] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -30.418052 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 05:53:03] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -30.419044 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 05:53:05] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -30.417271 | E_var:     0.0744 | E_err:   0.004263
[2025-10-06 05:53:08] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -30.424882 | E_var:     0.0705 | E_err:   0.004149
[2025-10-06 05:53:10] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -30.423936 | E_var:     0.0723 | E_err:   0.004201
[2025-10-06 05:53:13] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -30.421403 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 05:53:15] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -30.426357 | E_var:     0.0643 | E_err:   0.003962
[2025-10-06 05:53:17] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -30.417922 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 05:53:20] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -30.412512 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 05:53:22] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -30.418193 | E_var:     0.0884 | E_err:   0.004647
[2025-10-06 05:53:25] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -30.417631 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 05:53:27] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -30.424163 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 05:53:29] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -30.423745 | E_var:     0.0815 | E_err:   0.004459
[2025-10-06 05:53:32] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -30.417719 | E_var:     0.0975 | E_err:   0.004879
[2025-10-06 05:53:34] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -30.426317 | E_var:     0.0568 | E_err:   0.003725
[2025-10-06 05:53:37] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -30.420188 | E_var:     0.0760 | E_err:   0.004309
[2025-10-06 05:53:39] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -30.425029 | E_var:     0.0694 | E_err:   0.004115
[2025-10-06 05:53:42] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -30.414996 | E_var:     0.0677 | E_err:   0.004067
[2025-10-06 05:53:44] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -30.417989 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 05:53:46] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -30.427075 | E_var:     0.0704 | E_err:   0.004145
[2025-10-06 05:53:49] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -30.420518 | E_var:     0.1238 | E_err:   0.005498
[2025-10-06 05:53:51] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -30.429745 | E_var:     0.0838 | E_err:   0.004522
[2025-10-06 05:53:54] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -30.420209 | E_var:     0.0589 | E_err:   0.003792
[2025-10-06 05:53:56] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -30.421109 | E_var:     0.0822 | E_err:   0.004481
[2025-10-06 05:53:58] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -30.425522 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 05:54:01] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -30.420939 | E_var:     0.0692 | E_err:   0.004109
[2025-10-06 05:54:03] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -30.424299 | E_var:     0.0712 | E_err:   0.004169
[2025-10-06 05:54:06] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -30.433149 | E_var:     0.0875 | E_err:   0.004621
[2025-10-06 05:54:08] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -30.416041 | E_var:     0.0671 | E_err:   0.004049
[2025-10-06 05:54:11] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -30.420097 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 05:54:13] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -30.421357 | E_var:     0.0882 | E_err:   0.004639
[2025-10-06 05:54:15] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -30.421103 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 05:54:18] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -30.427158 | E_var:     0.0626 | E_err:   0.003911
[2025-10-06 05:54:20] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -30.419387 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 05:54:23] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -30.424394 | E_var:     0.0741 | E_err:   0.004254
[2025-10-06 05:54:25] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -30.425115 | E_var:     0.0835 | E_err:   0.004515
[2025-10-06 05:54:27] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -30.418201 | E_var:     0.1330 | E_err:   0.005698
[2025-10-06 05:54:30] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -30.424360 | E_var:     0.0699 | E_err:   0.004130
[2025-10-06 05:54:32] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -30.421105 | E_var:     0.0715 | E_err:   0.004177
[2025-10-06 05:54:35] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -30.430579 | E_var:     0.0994 | E_err:   0.004925
[2025-10-06 05:54:37] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -30.416424 | E_var:     0.0790 | E_err:   0.004392
[2025-10-06 05:54:39] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -30.423265 | E_var:     0.0823 | E_err:   0.004484
[2025-10-06 05:54:42] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -30.422430 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 05:54:44] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -30.418195 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 05:54:47] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -30.423357 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 05:54:49] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -30.416655 | E_var:     0.0897 | E_err:   0.004681
[2025-10-06 05:54:52] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -30.419036 | E_var:     0.0964 | E_err:   0.004850
[2025-10-06 05:54:54] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -30.430471 | E_var:     0.0722 | E_err:   0.004199
[2025-10-06 05:54:56] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -30.416625 | E_var:     0.0692 | E_err:   0.004109
[2025-10-06 05:54:59] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -30.423124 | E_var:     0.0663 | E_err:   0.004023
[2025-10-06 05:55:01] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -30.421774 | E_var:     0.0693 | E_err:   0.004112
[2025-10-06 05:55:04] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -30.421904 | E_var:     0.0809 | E_err:   0.004445
[2025-10-06 05:55:06] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -30.423843 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 05:55:08] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -30.422925 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 05:55:11] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -30.423403 | E_var:     0.0595 | E_err:   0.003812
[2025-10-06 05:55:13] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -30.417481 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 05:55:16] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -30.423695 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 05:55:18] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -30.417530 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 05:55:20] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -30.424351 | E_var:     0.0722 | E_err:   0.004198
[2025-10-06 05:55:21] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 05:55:23] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -30.423909 | E_var:     0.0782 | E_err:   0.004368
[2025-10-06 05:55:25] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -30.422812 | E_var:     0.0960 | E_err:   0.004841
[2025-10-06 05:55:28] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -30.422538 | E_var:     0.0652 | E_err:   0.003989
[2025-10-06 05:55:30] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -30.424602 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 05:55:33] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -30.418182 | E_var:     0.0750 | E_err:   0.004278
[2025-10-06 05:55:35] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -30.417544 | E_var:     0.0773 | E_err:   0.004343
[2025-10-06 05:55:37] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -30.421089 | E_var:     0.0608 | E_err:   0.003853
[2025-10-06 05:55:40] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -30.422726 | E_var:     0.0600 | E_err:   0.003828
[2025-10-06 05:55:42] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -30.415658 | E_var:     0.0894 | E_err:   0.004673
[2025-10-06 05:55:45] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -30.421578 | E_var:     0.1624 | E_err:   0.006298
[2025-10-06 05:55:47] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -30.424377 | E_var:     0.0694 | E_err:   0.004117
[2025-10-06 05:55:50] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -30.425788 | E_var:     0.0721 | E_err:   0.004194
[2025-10-06 05:55:52] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -30.422011 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 05:55:54] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -30.421390 | E_var:     0.0809 | E_err:   0.004443
[2025-10-06 05:55:57] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -30.426808 | E_var:     0.0679 | E_err:   0.004073
[2025-10-06 05:55:59] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -30.431674 | E_var:     0.0726 | E_err:   0.004210
[2025-10-06 05:56:02] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -30.425422 | E_var:     0.0695 | E_err:   0.004119
[2025-10-06 05:56:04] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -30.420563 | E_var:     0.0761 | E_err:   0.004311
[2025-10-06 05:56:06] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -30.424477 | E_var:     0.1220 | E_err:   0.005458
[2025-10-06 05:56:09] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -30.420395 | E_var:     0.0790 | E_err:   0.004391
[2025-10-06 05:56:11] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -30.428246 | E_var:     0.0766 | E_err:   0.004323
[2025-10-06 05:56:14] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -30.422340 | E_var:     0.0610 | E_err:   0.003859
[2025-10-06 05:56:16] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -30.419662 | E_var:     0.0724 | E_err:   0.004206
[2025-10-06 05:56:18] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -30.426116 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 05:56:21] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -30.412745 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 05:56:23] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -30.423101 | E_var:     0.0768 | E_err:   0.004331
[2025-10-06 05:56:26] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -30.424265 | E_var:     0.0665 | E_err:   0.004030
[2025-10-06 05:56:28] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -30.426380 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 05:56:31] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -30.422078 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 05:56:33] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -30.419370 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 05:56:35] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -30.420958 | E_var:     0.0684 | E_err:   0.004086
[2025-10-06 05:56:38] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -30.419464 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 05:56:40] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -30.426857 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 05:56:43] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -30.426215 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 05:56:45] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -30.423728 | E_var:     0.0647 | E_err:   0.003975
[2025-10-06 05:56:47] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -30.422725 | E_var:     0.0919 | E_err:   0.004736
[2025-10-06 05:56:50] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -30.413305 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 05:56:52] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -30.424114 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 05:56:55] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -30.420946 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 05:56:57] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -30.416102 | E_var:     0.0678 | E_err:   0.004068
[2025-10-06 05:56:59] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -30.420072 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 05:57:02] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -30.426687 | E_var:     0.0725 | E_err:   0.004207
[2025-10-06 05:57:04] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -30.425461 | E_var:     0.0619 | E_err:   0.003887
[2025-10-06 05:57:07] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -30.419403 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 05:57:09] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -30.420306 | E_var:     0.0705 | E_err:   0.004147
[2025-10-06 05:57:12] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -30.420922 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 05:57:14] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -30.427009 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 05:57:16] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -30.428640 | E_var:     0.1985 | E_err:   0.006962
[2025-10-06 05:57:19] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -30.420921 | E_var:     0.0699 | E_err:   0.004132
[2025-10-06 05:57:21] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -30.420682 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 05:57:24] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -30.422279 | E_var:     0.0620 | E_err:   0.003890
[2025-10-06 05:57:26] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -30.415088 | E_var:     0.0737 | E_err:   0.004243
[2025-10-06 05:57:28] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -30.429548 | E_var:     0.0611 | E_err:   0.003861
[2025-10-06 05:57:31] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -30.418712 | E_var:     0.0608 | E_err:   0.003853
[2025-10-06 05:57:33] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -30.426370 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 05:57:36] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -30.419707 | E_var:     0.0752 | E_err:   0.004285
[2025-10-06 05:57:38] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -30.423397 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 05:57:41] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -30.422884 | E_var:     0.0765 | E_err:   0.004322
[2025-10-06 05:57:43] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -30.422927 | E_var:     0.0674 | E_err:   0.004057
[2025-10-06 05:57:45] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -30.426053 | E_var:     0.0776 | E_err:   0.004352
[2025-10-06 05:57:48] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -30.427826 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 05:57:50] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -30.423950 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 05:57:53] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -30.418138 | E_var:     0.1140 | E_err:   0.005277
[2025-10-06 05:57:55] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -30.422608 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 05:57:57] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -30.423795 | E_var:     0.0726 | E_err:   0.004210
[2025-10-06 05:58:00] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -30.429371 | E_var:     0.0886 | E_err:   0.004650
[2025-10-06 05:58:02] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -30.417057 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 05:58:05] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -30.422607 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 05:58:07] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -30.426159 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 05:58:09] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -30.424518 | E_var:     0.1016 | E_err:   0.004980
[2025-10-06 05:58:12] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -30.425179 | E_var:     0.0743 | E_err:   0.004259
[2025-10-06 05:58:14] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -30.416267 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 05:58:17] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -30.422592 | E_var:     0.0649 | E_err:   0.003982
[2025-10-06 05:58:19] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -30.418500 | E_var:     0.0805 | E_err:   0.004432
[2025-10-06 05:58:22] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -30.425977 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 05:58:24] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -30.417252 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 05:58:26] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -30.423534 | E_var:     0.0717 | E_err:   0.004185
[2025-10-06 05:58:29] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -30.414357 | E_var:     0.0774 | E_err:   0.004347
[2025-10-06 05:58:31] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -30.423682 | E_var:     0.0870 | E_err:   0.004608
[2025-10-06 05:58:34] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -30.424259 | E_var:     0.1790 | E_err:   0.006610
[2025-10-06 05:58:36] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -30.430089 | E_var:     0.0640 | E_err:   0.003951
[2025-10-06 05:58:38] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -30.424768 | E_var:     0.1046 | E_err:   0.005053
[2025-10-06 05:58:41] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -30.418509 | E_var:     0.0632 | E_err:   0.003927
[2025-10-06 05:58:43] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -30.419347 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 05:58:46] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -30.428357 | E_var:     0.0782 | E_err:   0.004369
[2025-10-06 05:58:48] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -30.423914 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 05:58:50] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -30.428997 | E_var:     0.0665 | E_err:   0.004030
[2025-10-06 05:58:53] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -30.423181 | E_var:     0.0768 | E_err:   0.004329
[2025-10-06 05:58:55] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -30.419039 | E_var:     0.0741 | E_err:   0.004253
[2025-10-06 05:58:58] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -30.420930 | E_var:     0.0695 | E_err:   0.004119
[2025-10-06 05:59:00] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -30.417231 | E_var:     0.0790 | E_err:   0.004391
[2025-10-06 05:59:03] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -30.426456 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 05:59:05] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -30.416667 | E_var:     0.0792 | E_err:   0.004398
[2025-10-06 05:59:07] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -30.418566 | E_var:     0.0741 | E_err:   0.004255
[2025-10-06 05:59:10] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -30.427961 | E_var:     0.0838 | E_err:   0.004522
[2025-10-06 05:59:12] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -30.423460 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 05:59:15] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -30.426941 | E_var:     0.0715 | E_err:   0.004179
[2025-10-06 05:59:17] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -30.430432 | E_var:     0.0698 | E_err:   0.004128
[2025-10-06 05:59:19] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -30.427428 | E_var:     0.0670 | E_err:   0.004044
[2025-10-06 05:59:22] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -30.418672 | E_var:     0.0637 | E_err:   0.003944
[2025-10-06 05:59:22] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 05:59:24] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -30.423945 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 05:59:27] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -30.418603 | E_var:     0.0650 | E_err:   0.003983
[2025-10-06 05:59:29] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -30.426784 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 05:59:32] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -30.416616 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 05:59:34] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -30.427421 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 05:59:36] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -30.421398 | E_var:     0.0986 | E_err:   0.004908
[2025-10-06 05:59:39] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -30.425717 | E_var:     0.0648 | E_err:   0.003976
[2025-10-06 05:59:41] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -30.418669 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 05:59:44] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -30.420311 | E_var:     0.0845 | E_err:   0.004541
[2025-10-06 05:59:46] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -30.417961 | E_var:     0.0608 | E_err:   0.003853
[2025-10-06 05:59:48] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -30.421052 | E_var:     0.0803 | E_err:   0.004427
[2025-10-06 05:59:51] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -30.419843 | E_var:     0.0717 | E_err:   0.004184
[2025-10-06 05:59:53] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -30.419993 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 05:59:56] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -30.425038 | E_var:     0.0621 | E_err:   0.003893
[2025-10-06 05:59:58] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -30.417465 | E_var:     0.0973 | E_err:   0.004874
[2025-10-06 06:00:01] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -30.420127 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 06:00:03] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -30.424794 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 06:00:05] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -30.424373 | E_var:     0.0715 | E_err:   0.004179
[2025-10-06 06:00:08] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -30.422597 | E_var:     0.0695 | E_err:   0.004119
[2025-10-06 06:00:10] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -30.424090 | E_var:     0.0730 | E_err:   0.004223
[2025-10-06 06:00:13] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -30.418645 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 06:00:15] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -30.418723 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 06:00:17] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -30.423251 | E_var:     0.0613 | E_err:   0.003869
[2025-10-06 06:00:20] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -30.412356 | E_var:     0.1129 | E_err:   0.005251
[2025-10-06 06:00:22] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -30.420338 | E_var:     0.0760 | E_err:   0.004307
[2025-10-06 06:00:25] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -30.425623 | E_var:     0.0768 | E_err:   0.004330
[2025-10-06 06:00:27] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -30.416719 | E_var:     0.0750 | E_err:   0.004280
[2025-10-06 06:00:30] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -30.421000 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 06:00:32] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -30.421675 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 06:00:34] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -30.422509 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 06:00:37] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -30.418878 | E_var:     0.0947 | E_err:   0.004808
[2025-10-06 06:00:39] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -30.435300 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 06:00:42] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -30.420057 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 06:00:44] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -30.424058 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 06:00:46] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -30.415561 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 06:00:49] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -30.419325 | E_var:     0.0638 | E_err:   0.003946
[2025-10-06 06:00:51] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -30.418050 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 06:00:54] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -30.414279 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 06:00:56] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -30.414252 | E_var:     0.0981 | E_err:   0.004893
[2025-10-06 06:00:59] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -30.422295 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 06:01:01] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -30.417321 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 06:01:03] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -30.416801 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 06:01:06] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -30.426356 | E_var:     0.0623 | E_err:   0.003899
[2025-10-06 06:01:08] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -30.420880 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 06:01:11] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -30.422871 | E_var:     0.0715 | E_err:   0.004178
[2025-10-06 06:01:13] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -30.420555 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 06:01:15] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -30.423474 | E_var:     0.1186 | E_err:   0.005382
[2025-10-06 06:01:18] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -30.424251 | E_var:     0.0685 | E_err:   0.004090
[2025-10-06 06:01:20] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -30.424868 | E_var:     0.0568 | E_err:   0.003724
[2025-10-06 06:01:23] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -30.426629 | E_var:     0.0656 | E_err:   0.004002
[2025-10-06 06:01:25] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -30.414052 | E_var:     0.0689 | E_err:   0.004101
[2025-10-06 06:01:28] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -30.424454 | E_var:     0.0777 | E_err:   0.004354
[2025-10-06 06:01:30] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -30.418743 | E_var:     0.1593 | E_err:   0.006236
[2025-10-06 06:01:32] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -30.419861 | E_var:     0.1126 | E_err:   0.005243
[2025-10-06 06:01:35] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -30.428795 | E_var:     0.1319 | E_err:   0.005675
[2025-10-06 06:01:37] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -30.428695 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 06:01:40] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -30.426566 | E_var:     0.0873 | E_err:   0.004616
[2025-10-06 06:01:42] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -30.411673 | E_var:     0.1085 | E_err:   0.005147
[2025-10-06 06:01:44] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -30.426118 | E_var:     0.0578 | E_err:   0.003756
[2025-10-06 06:01:47] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -30.416319 | E_var:     0.1154 | E_err:   0.005307
[2025-10-06 06:01:49] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -30.420967 | E_var:     0.0675 | E_err:   0.004061
[2025-10-06 06:01:52] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -30.421656 | E_var:     0.0689 | E_err:   0.004103
[2025-10-06 06:01:54] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -30.417343 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 06:01:56] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -30.419197 | E_var:     0.0933 | E_err:   0.004772
[2025-10-06 06:01:59] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -30.425053 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 06:02:01] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -30.419352 | E_var:     0.0796 | E_err:   0.004409
[2025-10-06 06:02:04] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -30.423844 | E_var:     0.0658 | E_err:   0.004007
[2025-10-06 06:02:06] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -30.420418 | E_var:     0.1181 | E_err:   0.005370
[2025-10-06 06:02:08] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -30.423956 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 06:02:11] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -30.421982 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 06:02:13] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -30.417479 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 06:02:16] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -30.424367 | E_var:     0.1061 | E_err:   0.005091
[2025-10-06 06:02:18] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -30.421286 | E_var:     0.0669 | E_err:   0.004040
[2025-10-06 06:02:21] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -30.427309 | E_var:     0.1101 | E_err:   0.005186
[2025-10-06 06:02:23] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -30.416562 | E_var:     0.0729 | E_err:   0.004220
[2025-10-06 06:02:25] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -30.422324 | E_var:     0.0887 | E_err:   0.004652
[2025-10-06 06:02:28] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -30.432388 | E_var:     0.1558 | E_err:   0.006168
[2025-10-06 06:02:30] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -30.419404 | E_var:     0.0703 | E_err:   0.004142
[2025-10-06 06:02:33] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -30.408343 | E_var:     0.0681 | E_err:   0.004078
[2025-10-06 06:02:35] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -30.428598 | E_var:     0.0668 | E_err:   0.004040
[2025-10-06 06:02:37] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -30.429756 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 06:02:40] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -30.415890 | E_var:     0.0733 | E_err:   0.004231
[2025-10-06 06:02:42] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -30.429076 | E_var:     0.0560 | E_err:   0.003699
[2025-10-06 06:02:45] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -30.427352 | E_var:     0.0800 | E_err:   0.004421
[2025-10-06 06:02:47] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -30.421070 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 06:02:49] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -30.426452 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 06:02:52] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -30.422617 | E_var:     0.0917 | E_err:   0.004731
[2025-10-06 06:02:54] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -30.416915 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 06:02:57] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -30.421329 | E_var:     0.0661 | E_err:   0.004017
[2025-10-06 06:02:59] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -30.424114 | E_var:     0.0596 | E_err:   0.003814
[2025-10-06 06:03:01] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -30.422440 | E_var:     0.0756 | E_err:   0.004296
[2025-10-06 06:03:04] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -30.420397 | E_var:     0.0619 | E_err:   0.003888
[2025-10-06 06:03:06] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -30.422482 | E_var:     0.0654 | E_err:   0.003996
[2025-10-06 06:03:09] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -30.416449 | E_var:     0.1241 | E_err:   0.005505
[2025-10-06 06:03:11] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -30.422967 | E_var:     0.1282 | E_err:   0.005595
[2025-10-06 06:03:13] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -30.424726 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 06:03:16] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -30.423495 | E_var:     0.0757 | E_err:   0.004300
[2025-10-06 06:03:18] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -30.430231 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 06:03:21] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -30.421558 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 06:03:23] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -30.425229 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 06:03:23] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 06:03:25] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -30.419137 | E_var:     0.0800 | E_err:   0.004421
[2025-10-06 06:03:28] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -30.422314 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 06:03:30] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -30.428882 | E_var:     0.1127 | E_err:   0.005245
[2025-10-06 06:03:33] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -30.425547 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 06:03:35] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -30.424289 | E_var:     0.0791 | E_err:   0.004393
[2025-10-06 06:03:37] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -30.427000 | E_var:     0.0564 | E_err:   0.003711
[2025-10-06 06:03:40] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -30.414107 | E_var:     0.0618 | E_err:   0.003885
[2025-10-06 06:03:42] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -30.421879 | E_var:     0.0621 | E_err:   0.003895
[2025-10-06 06:03:45] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -30.424391 | E_var:     0.0735 | E_err:   0.004236
[2025-10-06 06:03:47] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -30.426963 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 06:03:50] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -30.415818 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 06:03:52] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -30.423788 | E_var:     0.0630 | E_err:   0.003923
[2025-10-06 06:03:54] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -30.423600 | E_var:     0.1147 | E_err:   0.005292
[2025-10-06 06:03:57] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -30.421448 | E_var:     0.0587 | E_err:   0.003787
[2025-10-06 06:03:59] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -30.422550 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 06:04:02] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -30.416390 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 06:04:04] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -30.425729 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 06:04:06] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -30.423829 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 06:04:09] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -30.421214 | E_var:     0.0645 | E_err:   0.003969
[2025-10-06 06:04:11] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -30.422102 | E_var:     0.0743 | E_err:   0.004259
[2025-10-06 06:04:14] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -30.420129 | E_var:     0.0741 | E_err:   0.004252
[2025-10-06 06:04:16] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -30.419222 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 06:04:18] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -30.426449 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 06:04:21] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -30.431906 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 06:04:23] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -30.420586 | E_var:     0.0600 | E_err:   0.003826
[2025-10-06 06:04:26] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -30.419870 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 06:04:28] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -30.414607 | E_var:     0.0801 | E_err:   0.004423
[2025-10-06 06:04:30] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -30.421766 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 06:04:33] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -30.428220 | E_var:     0.0765 | E_err:   0.004323
[2025-10-06 06:04:35] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -30.415520 | E_var:     0.0851 | E_err:   0.004558
[2025-10-06 06:04:38] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -30.412426 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 06:04:40] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -30.421788 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 06:04:42] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -30.424383 | E_var:     0.0650 | E_err:   0.003985
[2025-10-06 06:04:45] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -30.424083 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 06:04:47] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -30.415133 | E_var:     0.0694 | E_err:   0.004117
[2025-10-06 06:04:50] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -30.423206 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 06:04:52] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -30.422246 | E_var:     0.0692 | E_err:   0.004111
[2025-10-06 06:04:54] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -30.417989 | E_var:     0.0710 | E_err:   0.004164
[2025-10-06 06:04:57] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -30.418689 | E_var:     0.0626 | E_err:   0.003910
[2025-10-06 06:04:59] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -30.421332 | E_var:     0.1258 | E_err:   0.005543
[2025-10-06 06:05:02] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -30.418905 | E_var:     0.0801 | E_err:   0.004421
[2025-10-06 06:05:04] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -30.420529 | E_var:     0.0630 | E_err:   0.003921
[2025-10-06 06:05:06] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -30.428100 | E_var:     0.1433 | E_err:   0.005915
[2025-10-06 06:05:09] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -30.421480 | E_var:     0.0634 | E_err:   0.003934
[2025-10-06 06:05:11] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -30.422205 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 06:05:14] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -30.425100 | E_var:     0.0976 | E_err:   0.004881
[2025-10-06 06:05:16] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -30.429730 | E_var:     0.0648 | E_err:   0.003977
[2025-10-06 06:05:18] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -30.417460 | E_var:     0.0785 | E_err:   0.004377
[2025-10-06 06:05:21] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -30.419887 | E_var:     0.0829 | E_err:   0.004500
[2025-10-06 06:05:23] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -30.426395 | E_var:     0.0703 | E_err:   0.004143
[2025-10-06 06:05:23] ======================================================================================================
[2025-10-06 06:05:23] ✅ Training completed successfully
[2025-10-06 06:05:23] Total restarts: 2
[2025-10-06 06:05:24] Final Energy: -30.42639495 ± 0.00414335
[2025-10-06 06:05:24] Final Variance: 0.070318
[2025-10-06 06:05:24] ======================================================================================================
[2025-10-06 06:05:24] ======================================================================================================
[2025-10-06 06:05:24] Training completed | Runtime: 2583.5s
[2025-10-06 06:05:25] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 06:05:25] ======================================================================================================
