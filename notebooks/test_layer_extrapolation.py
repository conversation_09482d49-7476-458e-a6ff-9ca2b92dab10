#!/usr/bin/env python3
"""
测试层数外推分析功能

这个脚本用于测试notebook中的层数外推分析功能，
确保数据提取和绘图功能正常工作。
"""

import numpy as np
import matplotlib.pyplot as plt
import pickle
import pandas as pd
from pathlib import Path
import re
from scipy.optimize import curve_fit
from collections import defaultdict

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (10, 8)
plt.rcParams['font.size'] = 12

def extract_layer_info_from_model_name(model_name):
    """从模型名称中提取层数信息"""
    match = re.search(r'model_L(\d+)F\d+', model_name)
    if match:
        return int(match.group(1))
    return None

def extract_layer_data_from_results_directory(results_dir="../results", target_J2=1.00, target_J1=0.76):
    """直接从results目录结构中提取层数数据"""
    results_path = Path(results_dir)
    layer_data = defaultdict(list)
    
    print(f"正在扫描目录: {results_path}")
    
    # 遍历L=4和L=5目录
    for L in [4, 5]:
        L_dir = results_path / f"L={L}"
        if not L_dir.exists():
            print(f"目录 {L_dir} 不存在")
            continue
            
        J2_dir = L_dir / f"J2={target_J2:.2f}"
        if not J2_dir.exists():
            print(f"目录 {J2_dir} 不存在")
            continue
            
        J1_dir = J2_dir / f"J1={target_J1:.2f}"
        if not J1_dir.exists():
            print(f"目录 {J1_dir} 不存在")
            continue
            
        print(f"处理 L={L} 的数据...")
        
        # 查找不同层数的模型目录
        for model_dir in J1_dir.iterdir():
            if model_dir.is_dir() and model_dir.name.startswith('model_L'):
                layers = extract_layer_info_from_model_name(model_dir.name)
                if layers is not None:
                    print(f"  找到模型: {model_dir.name}, 层数: {layers}")
                    
                    # 查找分析结果
                    analysis_dir = model_dir / "analysis" / "final_GCNN"
                    if analysis_dir.exists():
                        # 查找spin数据文件
                        spin_data_file = analysis_dir / "spin" / "spin_data.npy"
                        if spin_data_file.exists():
                            try:
                                # 加载spin数据并计算序参量
                                spin_data = np.load(spin_data_file, allow_pickle=True).item()
                                
                                # 提取k点和结构因子
                                k_points_x = spin_data['metadata']['k_grid']['kx']
                                k_points_y = spin_data['metadata']['k_grid']['ky']
                                structure_factor = spin_data['structure_factor']['values']
                                
                                # 计算AF序参量 (S(π,π))
                                pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
                                pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
                                af_order_param = structure_factor[pi_idx_y, pi_idx_x].real
                                
                                # 计算Neel关联比
                                adjacent_indices = []
                                for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                                    ni, nj = pi_idx_y + di, pi_idx_x + dj
                                    if 0 <= ni < len(k_points_y) and 0 <= nj < len(k_points_x):
                                        adjacent_indices.append((ni, nj))
                                
                                if adjacent_indices:
                                    adjacent_values = [structure_factor[ni, nj].real for ni, nj in adjacent_indices]
                                    adjacent_avg = np.mean(adjacent_values)
                                    neel_ratio = (af_order_param - adjacent_avg) / af_order_param if af_order_param > 0 else 0
                                else:
                                    neel_ratio = 0
                                
                                layer_data[L].append({
                                    'layers': layers,
                                    'inv_layers': 1.0 / layers,
                                    'neel_ratio': neel_ratio,
                                    'af_order_param': af_order_param,
                                    'model_name': model_dir.name
                                })
                                
                                print(f"    层数={layers}: AF序参量={af_order_param:.6f}, Neel比={neel_ratio:.6f}")
                                
                            except Exception as e:
                                print(f"    处理 {spin_data_file} 时出错: {e}")
                        else:
                            print(f"    未找到spin数据文件: {spin_data_file}")
                    else:
                        print(f"    未找到分析目录: {analysis_dir}")
    
    # 按层数排序
    for L in layer_data:
        layer_data[L].sort(key=lambda x: x['layers'])
    
    return dict(layer_data)

def linear_function(x, a, b):
    """线性函数用于拟合"""
    return a * x + b

def plot_layer_extrapolation(layer_data, parameter='af_order_param', title_suffix='AF序参量'):
    """绘制层数外推图"""
    plt.figure(figsize=(12, 8))
    
    colors = ['blue', 'red', 'green', 'orange']
    markers = ['o', 's', '^', 'D']
    
    extrapolated_values = {}
    
    for i, (L, data_list) in enumerate(layer_data.items()):
        if not data_list:
            continue
            
        # 提取数据
        inv_layers = [d['inv_layers'] for d in data_list]
        values = [d[parameter] for d in data_list]
        layers = [d['layers'] for d in data_list]
        
        color = colors[i % len(colors)]
        marker = markers[i % len(markers)]
        
        # 绘制数据点
        plt.scatter(inv_layers, values, color=color, marker=marker, s=100, 
                   label=f'L={L} 数据点', alpha=0.8, edgecolors='black', linewidth=1)
        
        # 添加层数标签
        for j, (x, y, layer) in enumerate(zip(inv_layers, values, layers)):
            plt.annotate(f'{layer}层', (x, y), xytext=(5, 5), textcoords='offset points',
                        fontsize=10, alpha=0.7)
        
        # 线性拟合
        if len(inv_layers) >= 2:
            try:
                popt, pcov = curve_fit(linear_function, inv_layers, values)
                a, b = popt
                
                # 生成拟合线
                x_fit = np.linspace(0, max(inv_layers) * 1.1, 100)
                y_fit = linear_function(x_fit, a, b)
                
                plt.plot(x_fit, y_fit, color=color, linestyle='--', alpha=0.7,
                        label=f'L={L} 拟合线: y={a:.4f}x+{b:.4f}')
                
                # 计算外推值 (1/layers -> 0)
                extrapolated_value = b
                extrapolated_values[L] = extrapolated_value
                
                # 标记外推点
                plt.scatter([0], [extrapolated_value], color=color, marker='*', s=200,
                           edgecolors='black', linewidth=2, alpha=0.9)
                plt.annotate(f'L={L}外推值\n{extrapolated_value:.6f}', 
                           (0, extrapolated_value), xytext=(10, 10), 
                           textcoords='offset points', fontsize=10, 
                           bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3))
                
                print(f"L={L} 拟合结果: 斜率={a:.6f}, 截距={b:.6f}, 外推值={extrapolated_value:.6f}")
                
            except Exception as e:
                print(f"L={L} 拟合失败: {e}")
    
    plt.xlabel('1/层数', fontsize=14)
    plt.ylabel(f'{title_suffix}', fontsize=14)
    plt.title(f'神经网络层数外推分析 - {title_suffix}\n(J2=1.00, J1=0.76)', fontsize=16)
    plt.legend(fontsize=12, loc='best')
    plt.grid(True, alpha=0.3)
    
    # 设置坐标轴范围
    if layer_data:
        max_inv_layers = max([max([d['inv_layers'] for d in data_list]) for data_list in layer_data.values()])
        plt.xlim(-0.02, max_inv_layers * 1.1)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'layer_extrapolation_{parameter}_J2_1.00_J1_0.76.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"图片已保存为: {filename}")
    
    plt.show()
    
    return extrapolated_values

def main():
    """主函数"""
    print("开始层数外推分析测试...")
    
    # 提取层数数据
    layer_data = extract_layer_data_from_results_directory()
    
    if not layer_data:
        print("未能提取到层数数据，请检查results目录结构")
        return
    
    print(f"\n成功提取到层数数据:")
    for L, data_list in layer_data.items():
        print(f"  L={L}: {len(data_list)} 个层数配置")
    
    # 绘制AF序参量的层数外推图
    print("\n绘制AF序参量的层数外推图...")
    af_extrapolated = plot_layer_extrapolation(layer_data, 'af_order_param', 'AF序参量')
    
    print("\n绘制Neel关联比的层数外推图...")
    neel_extrapolated = plot_layer_extrapolation(layer_data, 'neel_ratio', 'Neel关联比')
    
    # 总结结果
    print("\n" + "="*60)
    print("层数外推分析结果总结")
    print("="*60)
    
    if af_extrapolated:
        print("\nAF序参量外推结果 (层数→∞):")
        for L, value in af_extrapolated.items():
            print(f"  L={L}: {value:.6f}")
    
    if neel_extrapolated:
        print("\nNeel关联比外推结果 (层数→∞):")
        for L, value in neel_extrapolated.items():
            print(f"  L={L}: {value:.6f}")

if __name__ == "__main__":
    main()
