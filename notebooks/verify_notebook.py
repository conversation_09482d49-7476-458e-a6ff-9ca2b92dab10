#!/usr/bin/env python3
"""
验证notebook功能的简单脚本

这个脚本用于验证layer_extrapolation_analysis.ipynb中的主要功能
是否可以正常工作，包括数据加载、处理和绘图。
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = ['numpy', 'matplotlib', 'scipy', 'pandas']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    return len(missing_packages) == 0

def check_data_structure():
    """检查数据目录结构"""
    results_dir = Path("../results")
    
    if not results_dir.exists():
        print(f"✗ Results目录不存在: {results_dir}")
        return False
    
    print(f"✓ Results目录存在: {results_dir}")
    
    # 检查L=4和L=5目录
    required_paths = [
        "L=4/J2=1.00/J1=0.76",
        "L=5/J2=1.00/J1=0.76"
    ]
    
    all_exist = True
    for path in required_paths:
        full_path = results_dir / path
        if full_path.exists():
            print(f"✓ 路径存在: {path}")
            
            # 检查模型目录
            model_dirs = [d for d in full_path.iterdir() if d.is_dir() and d.name.startswith('model_L')]
            print(f"  找到 {len(model_dirs)} 个模型目录: {[d.name for d in model_dirs]}")
            
            # 检查分析数据
            for model_dir in model_dirs:
                spin_data_file = model_dir / "analysis" / "final_GCNN" / "spin" / "spin_data.npy"
                if spin_data_file.exists():
                    print(f"  ✓ {model_dir.name}: spin数据存在")
                else:
                    print(f"  ✗ {model_dir.name}: spin数据缺失")
                    all_exist = False
        else:
            print(f"✗ 路径不存在: {path}")
            all_exist = False
    
    return all_exist

def check_notebook_files():
    """检查notebook相关文件"""
    files_to_check = [
        "layer_extrapolation_analysis.ipynb",
        "test_layer_extrapolation.py",
        "README_layer_extrapolation.md"
    ]
    
    all_exist = True
    for filename in files_to_check:
        if Path(filename).exists():
            print(f"✓ 文件存在: {filename}")
        else:
            print(f"✗ 文件缺失: {filename}")
            all_exist = False
    
    return all_exist

def run_basic_test():
    """运行基本功能测试"""
    try:
        print("运行基本功能测试...")
        
        # 导入必要模块
        import numpy as np
        import matplotlib.pyplot as plt
        from pathlib import Path
        import re
        
        # 测试数据提取函数
        def extract_layer_info_from_model_name(model_name):
            match = re.search(r'model_L(\d+)F\d+', model_name)
            if match:
                return int(match.group(1))
            return None
        
        # 测试几个模型名称
        test_names = ['model_L4F4', 'model_L6F4', 'model_L8F4']
        for name in test_names:
            layers = extract_layer_info_from_model_name(name)
            print(f"  {name} -> {layers} 层")
        
        # 测试matplotlib
        plt.figure(figsize=(6, 4))
        x = np.linspace(0, 1, 10)
        y = x**2
        plt.plot(x, y, 'o-')
        plt.title('Test Plot')
        plt.close()  # 关闭图形，不显示
        
        print("✓ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("="*60)
    print("Jupyter Notebook 层数外推分析验证")
    print("="*60)
    
    # 检查依赖包
    print("\n1. 检查依赖包...")
    deps_ok = check_dependencies()
    
    # 检查文件
    print("\n2. 检查notebook文件...")
    files_ok = check_notebook_files()
    
    # 检查数据结构
    print("\n3. 检查数据目录结构...")
    data_ok = check_data_structure()
    
    # 运行基本测试
    print("\n4. 运行基本功能测试...")
    test_ok = run_basic_test()
    
    # 总结
    print("\n" + "="*60)
    print("验证结果总结")
    print("="*60)
    
    results = {
        "依赖包": deps_ok,
        "文件检查": files_ok,
        "数据结构": data_ok,
        "功能测试": test_ok
    }
    
    all_ok = all(results.values())
    
    for check, status in results.items():
        status_str = "✓ 通过" if status else "✗ 失败"
        print(f"{check}: {status_str}")
    
    print("\n总体状态:", "✓ 所有检查通过，可以运行notebook" if all_ok else "✗ 存在问题，请检查上述失败项")
    
    if all_ok:
        print("\n建议的下一步:")
        print("1. 运行: jupyter notebook layer_extrapolation_analysis.ipynb")
        print("2. 或运行: python test_layer_extrapolation.py")
    else:
        print("\n请解决上述问题后再运行notebook")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
