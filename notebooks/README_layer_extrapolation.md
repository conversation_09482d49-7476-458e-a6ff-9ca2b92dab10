# 神经网络层数外推分析

本目录包含用于分析不同神经网络层数对Neel序参量影响的工具和notebook。

## 文件说明

### 主要文件
- `layer_extrapolation_analysis.ipynb` - 主要的Jupyter notebook，包含完整的层数外推分析
- `test_layer_extrapolation.py` - 独立的Python脚本，用于测试和验证分析功能
- `README_layer_extrapolation.md` - 本说明文件

### 生成的结果文件
- `layer_extrapolation_af_order_param_J2_1.00_J1_0.76.png` - AF序参量层数外推图
- `layer_extrapolation_neel_ratio_J2_1.00_J1_0.76.png` - Neel关联比层数外推图
- `layer_extrapolation_data_J2_1.00_J1_0.76.csv` - 原始数据CSV文件
- `layer_extrapolation_results_J2_1.00_J1_0.76.json` - 外推结果JSON文件

## 分析目标

本分析的主要目标是：

1. **数据来源**：使用已训练的L=4和L=5系统下J2=1，J1=0.76的不同神经网络层数数据
2. **分析方法**：创建Neel序参量的层数外推图
   - X轴：1/Layer数量 (层数的倒数)
   - Y轴：Neel序参量值或AF序参量值
   - 将L=4和L=5的数据点绘制在同一张图上，用不同颜色或标记区分
3. **外推分析**：通过线性拟合获得无限层数极限下的序参量估计值

## 使用方法

### 方法1：使用Jupyter Notebook（推荐）

1. 启动Jupyter notebook：
   ```bash
   cd notebooks
   jupyter notebook layer_extrapolation_analysis.ipynb
   ```

2. 按顺序运行notebook中的所有cell

### 方法2：使用Python脚本

1. 直接运行测试脚本：
   ```bash
   cd notebooks
   python test_layer_extrapolation.py
   ```

### 方法3：在conda netket环境中运行

如果需要使用特定的conda环境：
```bash
cd notebooks
conda activate netket
python test_layer_extrapolation.py
```

## 数据结构要求

分析脚本期望以下目录结构：

```
../results/
├── L=4/
│   └── J2=1.00/
│       └── J1=0.76/
│           ├── model_L4F4/
│           │   └── analysis/final_GCNN/spin/spin_data.npy
│           ├── model_L6F4/
│           │   └── analysis/final_GCNN/spin/spin_data.npy
│           └── model_L8F4/
│               └── analysis/final_GCNN/spin/spin_data.npy
└── L=5/
    └── J2=1.00/
        └── J1=0.76/
            ├── model_L4F4/
            ├── model_L6F4/
            └── model_L8F4/
```

## 分析结果

### 当前结果（基于测试运行）

**L=4系统：**
- 4层：AF序参量=0.094756, Neel比=0.625205
- 6层：AF序参量=0.092425, Neel比=0.613271  
- 8层：AF序参量=0.071894, Neel比=0.595847

**L=5系统：**
- 4层：AF序参量=0.074335, Neel比=0.633207
- 6层：AF序参量=0.072619, Neel比=0.621076
- 8层：AF序参量=0.068676, Neel比=0.594585

**外推结果（层数→∞）：**
- L=4: AF序参量=0.057332, Neel关联比=0.571399
- L=5: AF序参量=0.064340, Neel关联比=0.564718

### 结果解释

1. **趋势分析**：随着神经网络层数增加，序参量值呈现下降趋势
2. **外推意义**：外推值代表在无限层数极限下的序参量估计值
3. **系统比较**：L=5系统的AF序参量外推值略高于L=4系统

## 技术细节

### 序参量计算
- **AF序参量**：取结构因子在(π,π)点的实部值
- **Neel关联比**：计算主峰与相邻点的比值差异

### 拟合方法
- 使用线性函数 y = ax + b 拟合 1/层数 vs 序参量
- 外推值为拟合直线的截距（当1/层数→0时的y值）

### 图表特性
- 数据点用不同颜色和标记区分L=4和L=5系统
- 虚线表示线性拟合结果
- 星号标记表示外推值
- 包含拟合方程和外推数值标注

## 依赖包

- numpy
- matplotlib
- scipy
- pandas
- pathlib
- re
- collections

## 注意事项

1. 确保results目录结构正确
2. 检查spin_data.npy文件是否存在且格式正确
3. 如果遇到中文字体显示问题，图表会自动使用英文标签
4. 生成的图片保存为高分辨率PNG格式（300 DPI）

## 故障排除

### 常见问题

1. **找不到数据文件**：检查results目录路径和文件结构
2. **字体警告**：这是正常现象，不影响分析结果
3. **拟合失败**：可能是数据点太少，需要至少2个不同层数的数据点

### 调试建议

1. 运行test_layer_extrapolation.py查看详细输出
2. 检查控制台输出中的数据提取信息
3. 确认生成的图片文件是否正确保存
