{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 神经网络层数外推分析\n", "\n", "本notebook用于分析不同神经网络层数对Neel序参量的影响，并进行层数外推分析。\n", "\n", "## 分析目标\n", "- 使用已训练的L=4和L=5系统下J2=1，J1=0.76的不同神经网络层数数据\n", "- 创建Neel序参量的层数外推图\n", "- X轴：1/Layer数量 (层数的倒数)\n", "- Y轴：Neel序参量值\n", "- 将L=4和L=5的数据点绘制在同一张图上"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pickle\n", "import pandas as pd\n", "from pathlib import Path\n", "import re\n", "from scipy.optimize import curve_fit\n", "from collections import defaultdict\n", "\n", "# 设置matplotlib参数\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['figure.figsize'] = (10, 8)\n", "plt.rcParams['font.size'] = 12"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载分析数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载已保存的分析结果\n", "def load_analysis_data(filename=\"analysis_results.pkl\"):\n", "    \"\"\"加载分析结果数据\"\"\"\n", "    data_path = Path(filename)\n", "    if data_path.exists():\n", "        with open(data_path, 'rb') as f:\n", "            data = pickle.load(f)\n", "        print(f\"成功加载数据文件: {data_path}\")\n", "        return data\n", "    else:\n", "        print(f\"数据文件 {data_path} 不存在\")\n", "        return None\n", "\n", "# 加载数据\n", "analysis_data = load_analysis_data()\n", "if analysis_data:\n", "    print(f\"数据包含的系统配置: {list(analysis_data.keys())}\")\n", "else:\n", "    print(\"无法加载数据，请先运行order_analysis.py生成分析结果\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 提取层数信息和序参量数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_layer_info_from_model_name(model_name):\n", "    \"\"\"从模型名称中提取层数信息\n", "    \n", "    Args:\n", "        model_name: 模型名称，如 'model_L4F4', 'model_L6F4', 'model_L8F4'\n", "    \n", "    Returns:\n", "        int: 层数，如果无法解析则返回None\n", "    \"\"\"\n", "    # 匹配模式：model_L{layers}F{features}\n", "    match = re.search(r'model_L(\\d+)F\\d+', model_name)\n", "    if match:\n", "        return int(match.group(1))\n", "    return None\n", "\n", "def extract_layer_data_for_extrapolation(analysis_data, target_J2=1.00, target_J1=0.76):\n", "    \"\"\"提取用于层数外推的数据\n", "    \n", "    Args:\n", "        analysis_data: 分析数据字典\n", "        target_J2: 目标J2值\n", "        target_J1: 目标J1值\n", "    \n", "    Returns:\n", "        dict: 按L值组织的层数外推数据\n", "    \"\"\"\n", "    layer_data = defaultdict(list)\n", "    \n", "    for (L, J2), data_list in analysis_data.items():\n", "        if abs(J2 - target_J2) < 1e-6:  # 匹配J2值\n", "            for data_point in data_list:\n", "                if abs(data_point['J1'] - target_J1) < 1e-6:  # 匹配J1值\n", "                    # 检查是否有模型信息\n", "                    if 'model_results' in data_point:\n", "                        for model_name, model_data in data_point['model_results'].items():\n", "                            layers = extract_layer_info_from_model_name(model_name)\n", "                            if layers is not None:\n", "                                layer_data[L].append({\n", "                                    'layers': layers,\n", "                                    'inv_layers': 1.0 / layers,\n", "                                    'neel_ratio': model_data.get('neel_ratio', 0.0),\n", "                                    'af_order_param': model_data.get('af_order_param', 0.0),\n", "                                    'neel_ratio_std': model_data.get('neel_ratio_std', 0.0),\n", "                                    'af_order_param_std': model_data.get('af_order_param_std', 0.0),\n", "                                    'model_name': model_name\n", "                                })\n", "                    else:\n", "                        # 如果没有模型信息，可能是旧格式数据\n", "                        print(f\"警告: L={L}, J2={J2}, J1={data_point['J1']} 的数据没有模型信息\")\n", "    \n", "    # 按层数排序\n", "    for L in layer_data:\n", "        layer_data[L].sort(key=lambda x: x['layers'])\n", "    \n", "    return dict(layer_data)\n", "\n", "# 提取层数外推数据\n", "if analysis_data:\n", "    layer_extrapolation_data = extract_layer_data_for_extrapolation(analysis_data)\n", "    print(f\"提取到的层数外推数据:\")\n", "    for L, data_list in layer_extrapolation_data.items():\n", "        print(f\"  L={L}: {len(data_list)} 个层数配置\")\n", "        for data in data_list:\n", "            print(f\"    层数={data['layers']}, Neel比={data['neel_ratio']:.6f}, AF序参量={data['af_order_param']:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 检查数据结构并手动提取数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果上面的自动提取没有成功，我们手动检查数据结构\n", "if analysis_data:\n", "    print(\"数据结构检查:\")\n", "    for key, data_list in analysis_data.items():\n", "        L, J2 = key\n", "        if J2 == 1.00:  # 只看J2=1.00的数据\n", "            print(f\"\\nL={L}, J2={J2}:\")\n", "            for i, data_point in enumerate(data_list):\n", "                if abs(data_point['J1'] - 0.76) < 1e-6:  # 只看J1=0.76的数据\n", "                    print(f\"  数据点 {i}: J1={data_point['J1']}\")\n", "                    print(f\"    可用字段: {list(data_point.keys())}\")\n", "                    if 'neel_ratio' in data_point:\n", "                        print(f\"    neel_ratio: {data_point['neel_ratio']}\")\n", "                    if 'af_order_param' in data_point:\n", "                        print(f\"    af_order_param: {data_point['af_order_param']}\")\n", "                    # 检查是否有checkpoint相关信息\n", "                    if 'checkpoint' in data_point:\n", "                        print(f\"    checkpoint: {data_point['checkpoint']}\")\n", "                    break  # 只看第一个匹配的数据点"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 直接从results目录提取层数数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_layer_data_from_results_directory(results_dir=\"../results\", target_J2=1.00, target_J1=0.76):\n", "    \"\"\"直接从results目录结构中提取层数数据\"\"\"\n", "    results_path = Path(results_dir)\n", "    layer_data = defaultdict(list)\n", "    \n", "    # 遍历L=4和L=5目录\n", "    for L in [4, 5]:\n", "        L_dir = results_path / f\"L={L}\"\n", "        if not L_dir.exists():\n", "            continue\n", "            \n", "        J2_dir = L_dir / f\"J2={target_J2:.2f}\"\n", "        if not J2_dir.exists():\n", "            continue\n", "            \n", "        J1_dir = J2_dir / f\"J1={target_J1:.2f}\"\n", "        if not J1_dir.exists():\n", "            continue\n", "            \n", "        # 查找不同层数的模型目录\n", "        for model_dir in J1_dir.iterdir():\n", "            if model_dir.is_dir() and model_dir.name.startswith('model_L'):\n", "                layers = extract_layer_info_from_model_name(model_dir.name)\n", "                if layers is not None:\n", "                    # 查找分析结果\n", "                    analysis_dir = model_dir / \"analysis\" / \"final_GCNN\"\n", "                    if analysis_dir.exists():\n", "                        # 查找spin数据文件\n", "                        spin_data_file = analysis_dir / \"spin\" / \"spin_data.npy\"\n", "                        if spin_data_file.exists():\n", "                            try:\n", "                                # 加载spin数据并计算序参量\n", "                                spin_data = np.load(spin_data_file, allow_pickle=True).item()\n", "                                \n", "                                # 提取k点和结构因子\n", "                                k_points_x = spin_data['metadata']['k_grid']['kx']\n", "                                k_points_y = spin_data['metadata']['k_grid']['ky']\n", "                                structure_factor = spin_data['structure_factor']['values']\n", "                                \n", "                                # 计算AF序参量 (S(π,π))\n", "                                pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))\n", "                                pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))\n", "                                af_order_param = structure_factor[pi_idx_y, pi_idx_x].real\n", "                                \n", "                                # 计算Neel关联比\n", "                                # 找到相邻点的平均值\n", "                                adjacent_indices = []\n", "                                for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:\n", "                                    ni, nj = pi_idx_y + di, pi_idx_x + dj\n", "                                    if 0 <= ni < len(k_points_y) and 0 <= nj < len(k_points_x):\n", "                                        adjacent_indices.append((ni, nj))\n", "                                \n", "                                if adjacent_indices:\n", "                                    adjacent_values = [structure_factor[ni, nj].real for ni, nj in adjacent_indices]\n", "                                    adjacent_avg = np.mean(adjacent_values)\n", "                                    neel_ratio = (af_order_param - adjacent_avg) / af_order_param if af_order_param > 0 else 0\n", "                                else:\n", "                                    neel_ratio = 0\n", "                                \n", "                                layer_data[L].append({\n", "                                    'layers': layers,\n", "                                    'inv_layers': 1.0 / layers,\n", "                                    'neel_ratio': neel_ratio,\n", "                                    'af_order_param': af_order_param,\n", "                                    'model_name': model_dir.name\n", "                                })\n", "                                \n", "                                print(f\"L={L}, 层数={layers}: AF序参量={af_order_param:.6f}, Neel比={neel_ratio:.6f}\")\n", "                                \n", "                            except Exception as e:\n", "                                print(f\"处理 {spin_data_file} 时出错: {e}\")\n", "    \n", "    # 按层数排序\n", "    for L in layer_data:\n", "        layer_data[L].sort(key=lambda x: x['layers'])\n", "    \n", "    return dict(layer_data)\n", "\n", "# 直接从results目录提取数据\n", "print(\"直接从results目录提取层数数据...\")\n", "layer_data_direct = extract_layer_data_from_results_directory()\n", "\n", "if layer_data_direct:\n", "    print(f\"\\n成功提取到层数数据:\")\n", "    for L, data_list in layer_data_direct.items():\n", "        print(f\"  L={L}: {len(data_list)} 个层数配置\")\n", "else:\n", "    print(\"未能提取到层数数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 绘制层数外推图"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def linear_function(x, a, b):\n", "    \"\"\"线性函数用于拟合\"\"\"\n", "    return a * x + b\n", "\n", "def plot_layer_extrapolation(layer_data, parameter='af_order_param', title_suffix='AF Order Parameter'):\n", "    \"\"\"绘制层数外推图\n", "    \n", "    Args:\n", "        layer_data: 层数数据字典\n", "        parameter: 要绘制的参数名称\n", "        title_suffix: 图标题后缀\n", "    \"\"\"\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    colors = ['blue', 'red', 'green', 'orange']\n", "    markers = ['o', 's', '^', 'D']\n", "    \n", "    extrapolated_values = {}\n", "    \n", "    for i, (L, data_list) in enumerate(layer_data.items()):\n", "        if not data_list:\n", "            continue\n", "            \n", "        # 提取数据\n", "        inv_layers = [d['inv_layers'] for d in data_list]\n", "        values = [d[parameter] for d in data_list]\n", "        layers = [d['layers'] for d in data_list]\n", "        \n", "        color = colors[i % len(colors)]\n", "        marker = markers[i % len(markers)]\n", "        \n", "        # 绘制数据点\n", "        plt.scatter(inv_layers, values, color=color, marker=marker, s=100, \n", "                   label=f'L={L} Data Points', alpha=0.8, edgecolors='black', linewidth=1)\n", "        \n", "        # 添加层数标签\n", "        for j, (x, y, layer) in enumerate(zip(inv_layers, values, layers)):\n", "            plt.annotate(f'{layer} layers', (x, y), xytext=(5, 5), textcoords='offset points',\n", "                        fontsize=10, alpha=0.7)\n", "        \n", "        # 线性拟合\n", "        if len(inv_layers) >= 2:\n", "            try:\n", "                popt, pcov = curve_fit(linear_function, inv_layers, values)\n", "                a, b = popt\n", "                \n", "                # 生成拟合线\n", "                x_fit = np.linspace(0, max(inv_layers) * 1.1, 100)\n", "                y_fit = linear_function(x_fit, a, b)\n", "                \n", "                plt.plot(x_fit, y_fit, color=color, linestyle='--', alpha=0.7,\n", "                        label=f'L={L} Fit: y={a:.4f}x+{b:.4f}')\n", "                \n", "                # 计算外推值 (1/layers -> 0)\n", "                extrapolated_value = b\n", "                extrapolated_values[L] = extrapolated_value\n", "                \n", "                # 标记外推点\n", "                plt.scatter([0], [extrapolated_value], color=color, marker='*', s=200,\n", "                           edgecolors='black', linewidth=2, alpha=0.9)\n", "                plt.annotate(f'L={L} Extrapolated\\n{extrapolated_value:.6f}', \n", "                           (0, extrapolated_value), xytext=(10, 10), \n", "                           textcoords='offset points', fontsize=10, \n", "                           bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3))\n", "                \n", "                print(f\"L={L} Fit Results: slope={a:.6f}, intercept={b:.6f}, extrapolated={extrapolated_value:.6f}\")\n", "                \n", "            except Exception as e:\n", "                print(f\"L={L} Fit Failed: {e}\")\n", "    \n", "    plt.xlabel('1/Number of Layers', fontsize=14)\n", "    plt.ylabel(f'{title_suffix}', fontsize=14)\n", "    plt.title(f'Neural Network Layer Extrapolation Analysis - {title_suffix}\\n(J2=1.00, J1=0.76)', fontsize=16)\n", "    plt.legend(fontsize=12, loc='best')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 设置坐标轴范围\n", "    plt.xlim(-0.02, max([max([d['inv_layers'] for d in data_list]) for data_list in layer_data.values()]) * 1.1)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 保存图片\n", "    filename = f'layer_extrapolation_{parameter}_J2_1.00_J1_0.76.png'\n", "    plt.savefig(filename, dpi=300, bbox_inches='tight')\n", "    print(f\"Figure saved as: {filename}\")\n", "    \n", "    plt.show()\n", "    \n", "    return extrapolated_values\n", "\n", "# 绘制AF序参量的层数外推图\n", "if layer_data_direct:\n", "    print(\"Plotting AF order parameter layer extrapolation...\")\n", "    af_extrapolated = plot_layer_extrapolation(layer_data_direct, 'af_order_param', 'AF Order Parameter')\n", "    \n", "    print(\"\\nPlotting Neel correlation ratio layer extrapolation...\")\n", "    neel_extrapolated = plot_layer_extrapolation(layer_data_direct, 'neel_ratio', 'Neel Correlation Ratio')\n", "else:\n", "    print(\"No layer data available for plotting\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 外推结果总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 总结外推结果\n", "if layer_data_direct:\n", "    print(\"=\"*60)\n", "    print(\"层数外推分析结果总结\")\n", "    print(\"=\"*60)\n", "    print(f\"分析参数: J2=1.00, J1=0.76\")\n", "    print()\n", "    \n", "    # 显示原始数据\n", "    print(\"原始数据:\")\n", "    for L, data_list in layer_data_direct.items():\n", "        print(f\"\\nL={L}系统:\")\n", "        for data in data_list:\n", "            print(f\"  {data['layers']}层: AF序参量={data['af_order_param']:.6f}, Neel比={data['neel_ratio']:.6f}\")\n", "    \n", "    # 显示外推结果\n", "    if 'af_extrapolated' in locals() and af_extrapolated:\n", "        print(\"\\nAF序参量外推结果 (层数→∞):\")\n", "        for L, value in af_extrapolated.items():\n", "            print(f\"  L={L}: {value:.6f}\")\n", "    \n", "    if 'neel_extrapolated' in locals() and neel_extrapolated:\n", "        print(\"\\nNeel关联比外推结果 (层数→∞):\")\n", "        for L, value in neel_extrapolated.items():\n", "            print(f\"  L={L}: {value:.6f}\")\n", "    \n", "    print(\"\\n注意: 外推值是通过线性拟合1/层数 vs 序参量得到的截距值\")\n", "    print(\"这代表了在无限层数极限下的序参量估计值\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 数据导出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将结果保存为CSV文件以便进一步分析\n", "if layer_data_direct:\n", "    # 创建DataFrame\n", "    all_data = []\n", "    for L, data_list in layer_data_direct.items():\n", "        for data in data_list:\n", "            all_data.append({\n", "                'L': L,\n", "                'layers': data['layers'],\n", "                'inv_layers': data['inv_layers'],\n", "                'af_order_param': data['af_order_param'],\n", "                'neel_ratio': data['neel_ratio'],\n", "                'model_name': data['model_name']\n", "            })\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # 保存CSV文件\n", "    csv_filename = 'layer_extrapolation_data_J2_1.00_J1_0.76.csv'\n", "    df.to_csv(csv_filename, index=False)\n", "    print(f\"数据已保存为CSV文件: {csv_filename}\")\n", "    \n", "    # 显示DataFrame\n", "    print(\"\\n数据表格:\")\n", "    print(df.to_string(index=False))\n", "    \n", "    # 保存外推结果\n", "    extrapolation_results = {}\n", "    if 'af_extrapolated' in locals():\n", "        extrapolation_results['af_order_param'] = af_extrapolated\n", "    if 'neel_extrapolated' in locals():\n", "        extrapolation_results['neel_ratio'] = neel_extrapolated\n", "    \n", "    if extrapolation_results:\n", "        import json\n", "        json_filename = 'layer_extrapolation_results_J2_1.00_J1_0.76.json'\n", "        with open(json_filename, 'w') as f:\n", "            json.dump(extrapolation_results, f, indent=2)\n", "        print(f\"外推结果已保存为JSON文件: {json_filename}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}