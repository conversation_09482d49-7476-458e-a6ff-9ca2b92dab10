#!/usr/bin/env python3
"""
分析脚本,用于分析Shastry-Sutherland模型的GCNN量子态。
"""

# 设置环境变量
import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"  # 保留NetKet的分片功能
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
os.environ["JAX_PLATFORM_NAME"] = "gpu"

import argparse
import traceback
import numpy as np
import pickle

# 导入自定义模块
from src.utils.logging import log_message
from src.utils.plotting import plot_structure_factor
from src.models.gcnn import create_quantum_state
from src.analysis.structure_factors import (
    calculate_spin_structure_factor,
    calculate_plaquette_structure_factor,
    calculate_dimer_structure_factor,
    calculate_diag_dimer_structure_factor,
    generate_shared_samples,
)

def load_quantum_state_from_checkpoint(file_path, L, J2, J1, n_samples=2**14, n_discard=0, chunk_size=2**10, num_layers=4, num_features=4, log_file=None):
    """
    从checkpoint格式加载量子态参数
    
    参数:
    file_path: checkpoint文件路径
    L: 晶格大小
    J2: J2耦合强度
    J1: J1耦合强度
    n_samples: 采样数量
    n_discard: 丢弃的样本数
    chunk_size: 批处理大小
    num_layers: 模型层数
    num_features: 模型特征数
    log_file: 日志文件路径（可选）
    
    返回:
    vqs: 变分量子态
    lattice: 晶格
    hilbert: 希尔伯特空间
    hamiltonian: 哈密顿量
    """
    # 创建量子态
    vqs, lattice, hilbert, hamiltonian = create_quantum_state(
        L, J2, J1, 
        n_samples=n_samples, 
        n_discard=n_discard, 
        chunk_size=chunk_size,
        num_layers=num_layers,
        num_features=num_features
    )
    
    # 加载checkpoint数据
    with open(file_path, "rb") as f:
        checkpoint_data = pickle.load(f)
    
    # 从checkpoint数据中提取参数
    if not isinstance(checkpoint_data, dict) or 'parameters' not in checkpoint_data:
        raise ValueError(f"无效的checkpoint格式: {file_path}")
    
    parameters = checkpoint_data['parameters']
    if log_file:
        log_message(log_file, f"✓ 从checkpoint加载参数: {checkpoint_data.get('iteration', 'unknown')}")
        if checkpoint_data.get('energy'):
            energy = checkpoint_data['energy']
            log_message(log_file, f"  - 能量: {energy['mean']:.6f} ± {energy['error']:.6f}")
    
    # 设置参数
    vqs.parameters = parameters
    
    return vqs, lattice, hilbert, hamiltonian

def main(args=None):
    """主函数,接受命令行参数L, J2, J1, num_features, num_layers, checkpoint"""
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='分析量子态结构因子')
    parser.add_argument('--L', type=int, required=True, help='晶格大小')
    parser.add_argument('--J2', type=float, required=True, help='J2耦合强度')
    parser.add_argument('--J1', type=float, required=True, help='J1耦合强度')
    parser.add_argument('--num_features', type=int, default=4, help='模型特征数')
    parser.add_argument('--num_layers', type=int, default=4, help='模型层数')
    parser.add_argument('--checkpoint', type=str, default='final_GCNN', help='checkpoint文件名（不含.pkl扩展名）')
    parser.add_argument('--n_samples', type=int, default=1048576, help='采样数目（默认2^20=1048576）')
    args = parser.parse_args(args)

    # 获取参数
    L = args.L
    J2 = args.J2
    J1 = args.J1
    num_features = args.num_features
    num_layers = args.num_layers
    checkpoint_name = args.checkpoint
    n_samples = args.n_samples

    # 创建结果目录，包含模型参数
    model_dir = f"model_L{num_layers}F{num_features}"
    result_dir = f"results/L={L}/J2={J2:.2f}/J1={J1:.2f}/{model_dir}"
    # 为每个checkpoint创建独立的分析目录
    analysis_dir = os.path.join(result_dir, "analysis", checkpoint_name)
    os.makedirs(analysis_dir, exist_ok=True)

    # 创建日志文件
    analyze_log = os.path.join(analysis_dir, f"analyze_L={L}_J2={J2:.2f}_J1={J1:.2f}_L{num_layers}F{num_features}_{checkpoint_name}.log")

    try:
        # 构建checkpoint文件路径
        checkpoint_file = os.path.join(result_dir, "training", "checkpoints", f"{checkpoint_name}.pkl")
        
        # 检查文件存在性
        if not os.path.exists(checkpoint_file):
            log_message(analyze_log, f"错误: 未找到checkpoint文件 {checkpoint_file}")
            return

        log_message(analyze_log, f"使用checkpoint文件: {checkpoint_file}")

        # 加载量子态 - 使用较小的采样数初始化
        vqs, lattice, _, _ = load_quantum_state_from_checkpoint(
            checkpoint_file, L, J2, J1,
            n_samples=2**12,  # 初始使用较小的采样数
            n_discard=0,
            chunk_size=2**10,  # 使用较小的chunk_size
            num_layers=num_layers,
            num_features=num_features,
            log_file=analyze_log
        )

        # 在加载量子态后立即生成共享样本,供后续所有结构因子计算使用
        log_message(analyze_log, "="*80)
        log_message(analyze_log, f"加载量子态: L={L}, J2={J2:.2f}, J1={J1:.2f}, checkpoint={checkpoint_name}")
        log_message(analyze_log, f"使用采样数目: {n_samples}")
        generate_shared_samples(vqs, n_samples=n_samples, log_file=analyze_log)

        # 创建子目录
        spin_dir = os.path.join(analysis_dir, "spin")
        plaquette_dir = os.path.join(analysis_dir, "plaquette")
        dimer_dir = os.path.join(analysis_dir, "dimer")
        diag_dimer_dir = os.path.join(analysis_dir, "diag_dimer")

        os.makedirs(spin_dir, exist_ok=True)
        os.makedirs(plaquette_dir, exist_ok=True)
        os.makedirs(dimer_dir, exist_ok=True)
        os.makedirs(diag_dimer_dir, exist_ok=True)          

        # 计算自旋因子
        log_message(analyze_log, "="*80)
        k_points_tuple, (spin_sf_real, spin_sf_imag) = calculate_spin_structure_factor(vqs, lattice, L, spin_dir, analyze_log)
        plot_structure_factor(k_points_tuple, spin_sf_real, L, J2, J1, "Spin", spin_dir)
        # 绘制虚部结构因子  
        spin_data_storage = np.load(os.path.join(spin_dir, "spin_data.npy"), allow_pickle=True).item()
        # 使用新的数据结构读取误差数据
        errors_data = spin_data_storage['correlations']['errors']
        # 计算复数误差的幅度
        error_magnitudes = np.abs(errors_data)
        log_message(analyze_log, f"自旋相关函数平均误差: {np.mean(error_magnitudes):.6f}")
                
        # # 计算二聚体结构因子
        # log_message(analyze_log, "="*80)
        # k_points_tuple, (dimer_sf_real, dimer_sf_imag) = calculate_dimer_structure_factor(vqs, lattice, L, dimer_dir, analyze_log)
        # plot_structure_factor(k_points_tuple, dimer_sf_real, L, J2, J1, "Dimer", dimer_dir)
        # # 从存储结构中加载数据
        # dimer_data_storage = np.load(os.path.join(dimer_dir, "dimer_data.npy"), allow_pickle=True).item()
        # # 使用新的数据结构读取误差数据
        # dimer_errors = dimer_data_storage['correlations']['errors']
        # dimer_error_magnitudes = np.abs(dimer_errors)
        # log_message(analyze_log, f"二聚体相关函数平均误差: {np.mean(dimer_error_magnitudes):.6f}")

        # # 计算对角二聚体结构因子
        # log_message(analyze_log, "="*80)
        # k_points_tuple, (diag_dimer_sf_real, diag_dimer_sf_imag) = calculate_diag_dimer_structure_factor(vqs, lattice, L, diag_dimer_dir, analyze_log)
        # plot_structure_factor(k_points_tuple, diag_dimer_sf_real, L, J2, J1, "Diag Dimer", diag_dimer_dir)
        # # 从存储结构中加载数据
        # diag_dimer_data_storage = np.load(os.path.join(diag_dimer_dir, "diag_dimer_data.npy"), allow_pickle=True).item()
        # # 使用新的数据结构读取误差数据
        # diag_dimer_errors = diag_dimer_data_storage['correlations']['errors']
        # diag_dimer_error_magnitudes = np.abs(diag_dimer_errors)
        # log_message(analyze_log, f"对角二聚体相关函数平均误差: {np.mean(diag_dimer_error_magnitudes):.6f}")
        
        # # 计算简盘因子
        # log_message(analyze_log, "="*80)
        # k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor(vqs, lattice, L, plaquette_dir, analyze_log)
        # plot_structure_factor(k_points_tuple, plaq_sf_real, L, J2, J1, "Plaquette", plaquette_dir)
        # # 从存储结构中加载数据
        # plaquette_data_storage = np.load(os.path.join(plaquette_dir, "plaquette_data.npy"), allow_pickle=True).item()
        # # 使用新的数据结构读取误差数据
        # plaquette_errors = plaquette_data_storage['correlations']['errors']
        # plaquette_error_magnitudes = np.abs(plaquette_errors)
        # log_message(analyze_log, f"简盘相关函数平均误差: {np.mean(plaquette_error_magnitudes):.6f}")

        # log_message(analyze_log, "="*80)
        # log_message(analyze_log, "所有分析完成")

    except Exception as e:
        log_message(analyze_log, "!"*80)
        log_message(analyze_log, f"处理 L={L}, J2={J2:.2f}, J1={J1:.2f} 时出错: {str(e)}")
        log_message(analyze_log, traceback.format_exc())
        log_message(analyze_log, "!"*80)

if __name__ == "__main__":
    main()
